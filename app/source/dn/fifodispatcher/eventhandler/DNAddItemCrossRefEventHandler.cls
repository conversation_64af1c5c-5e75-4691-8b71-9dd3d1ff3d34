<?php

/**
 * Event Handler for Add ItemCrossRef
 *
 * FILE:			 DNAddItemCrossRefEventHandler.cls
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Intacct Corporation, All Rights Reserved
 */
class DNAddItemCrossRefEventHandler extends DNItemCrossRefCommonEventHandler
{
    /** @var string $orgId */
    protected $orgId;


    public function __construct()
    {
       parent::__construct();
    }

    /**
     * @return string
     */
    protected function getEventSubjectStr()
    {
        return '_ITEMCROSSREF_';
    }

    /**
     * @param array $job
     * @param array $jobDetails
     *
     * @return bool
     */
    public function processEvent(&$job, $jobDetails) : bool
    {
        $metricPOAutomationItemCrossRef = new MetricPOAutomationItemCrossRefSync();
        $metricPOAutomationItemCrossRef->startTime();
        $metricPOAutomationItemCrossRef->setJobRecord($job['RECORDNO']);
        $metricPOAutomationItemCrossRef->setJobAction($job['TOPIC']);
        $metricPOAutomationItemCrossRef->setWhenCreated($job['WHENCREATED']);
        $metricPOAutomationItemCrossRef->setCUser($job['CREATEDBY']);

        XACT_BEGIN(__METHOD__);
        $startTime = time();

        // org/compmany is nont subscribe
        if (WPBSetupManager::isSubscribed()) {
            $objData = $job['OBJECT']['dataMap'];

            $metricPOAutomationItemCrossRef->setICRAction('Add');
            $metricPOAutomationItemCrossRef->setICRRecord($objData['RECORDNO']);
            $metricPOAutomationItemCrossRef->setICRRefType($objData['REFTYPE']);
            $metricPOAutomationItemCrossRef->setICRItemAliasId($objData['ITEMALIASID']);
            $metricPOAutomationItemCrossRef->setICRItemId($objData['ITEMID']);
            $metricPOAutomationItemCrossRef->setICRVendorId($objData['VENDORID']);
            // Company GUID
            $guid = Profile::getCompanyCacheProperty('COMPANYPREF', 'GUID');
            $metricPOAutomationItemCrossRef->setLocation(0);
            $metricPOAutomationItemCrossRef->setGuId($guid);

            //We donot have mega restriction on Item Cross reffereence object
            //So no need off applying it. The Item Cross Ref object should be visible at all root and entites exist.
            $this->buildItemCrossRefCompanyVisibility($guid, $objData['RECORDNO']);

            $subjectStr = $this->getEventSubjectStr();

            // Set Subject ID -- FORMAT - GUID_VEN_Record#
            $subjectID = $guid . $subjectStr . $objData['RECORDNO'];

            // set the idemPotencyKey
            $idemPotencyKey = $guid . '_' . $job['RECORDNO'] . '_' . $job['TOPIC'];

            // set subject
            $this->setSubjectID($subjectID);

            // set idempotencyKey
            $this->setIdemPotencyKey($idemPotencyKey);

            // 2. Filter Black List
            $objData = $this->filterBlackListField($objData, DNEventHandlerConstaint::ITEMCROSSREF_TYPE);

            // Reformat Date
            $objData = $this->reformatDateFields($objData, DNEventHandlerConstaint::ITEMCROSSREF_TYPE);

            $objData = $this->changeToLowerCase($objData);

            //4. Populate "companyids"
            $objData = $this->populateExternalIDMap($objData);

            //5. Prepare Final Sync Map
            $syncData[] = $this->prepareEntitySyncDataMap($objData);

            $ok = $this->putEvents($syncData, $respHttpCode, $response);

        } else {
            $errMsg = "DigitalNetworkSync Processor :: Organization/Company not subscribed ";
            $metricPOAutomationItemCrossRef->setError($errMsg);
            Globals::$g->gErr->addError('PO-0129', __FILE__ . ':' . __LINE__, $errMsg);
            $ok = false;
        }

        if($ok){
            $this->setSyncStatus(self::STATUS_COMPLETE);
            $metricPOAutomationItemCrossRef->setStatus(self::STATUS_COMPLETE);
        }
        $ok = $ok && XACT_COMMIT(__METHOD__);

        if ( ! $ok ) {
            XACT_ABORT(__METHOD__);
            logToFileCritical("DigitalNetworkSync Processor :: Error in processing Add Item cross ref Event");
            $errMsg = "DigitalNetworkSync Processor :: Error processing Add Item cross ref ";
            $metricPOAutomationItemCrossRef->setError($errMsg);
            Globals::$g->gErr->addError('PO-0130', __FILE__ . ':' . __LINE__, $errMsg);

        }

        $timeProcess = time() - $startTime;
        $metricPOAutomationItemCrossRef->setTimeForProcess($timeProcess);
        $metricPOAutomationItemCrossRef->stopTime();
        $metricPOAutomationItemCrossRef->publish();
        
        return $ok;
    }

    /**
     * @return string
     */
    protected function getObjectSyncEventName()
    {
        return DNEventHandlerConstaint::ITEMCROSSREFCREATE_EVENT;
    }

    /**
     * @return string
     */
    protected function getObjectIDField()
    {
        return 'recordno';
    }
}