<?php
    //=============================================================================
    //
    //	FILE:        set_up_builder_baseline.tpl.inc
    //	AUTHOR:      <PERSON><PERSON><PERSON> <<EMAIL>>
    //	DESCRIPTION: create setup from baseline template
    //
    //	(C)2021, Intacct Corporation, All Rights Reserved
    //
    //	Intacct Corporation Proprietary Information.
    //	This document contains trade secret data that belongs to Intacct
    //	corporation and is protected by the copyright laws. Information herein
    //	may not be used, copied or disclosed in whole or part without prior
    //	written consent from Intacct Corporation.
    //
    //=============================================================================

    // just to keep inspections happy
    $t_page_name = $t_page_name ?? "";
    $t_pod_labels = $t_pod_labels ?? array();
    $t_schema_labels = $t_schema_labels ?? array();
    $t_oLister = $t_oLister ?? null;
    $t_del_selected_companies = $t_del_selected_companies ?? null;
    $t_remove_comp_val = $t_remove_comp_val ?? null;
    $t_email = $t_email ?? "";

    $t_alerts = $t_alerts ?? array();
    // end inspections
?>

<div class="alerts">
    <div id="error_group" class="alert alert-error" style="display:<?=count($t_alerts[ALERT_TYPE_ERROR]) > 0 ? "block" : "none";?>;">
        <span id="email_error" class="alert-message" style="display:<?=isset($t_alerts[ALERT_TYPE_ERROR]["email"]) ? "block" : "none";?>;">
            A value for <a href='#email'>Email</a> is required.
        </span>
        <span id="email_format_error" class="alert-message" style="display:<?=isset($t_alerts[ALERT_TYPE_ERROR]["email_format"]) ? "block" : "none";?>;">
            <a href='#email'>Email</a> format is wrong.
        </span>
        <span id="companies_error" class="alert-message" style="display:<?=isset($t_alerts[ALERT_TYPE_ERROR]["companies_error"]) ? "block" : "none";?>;">
            You did not select any company.
        </span>
        <span id="setup_not_created" class="alert-message" style="display:<?=isset($t_alerts[ALERT_TYPE_ERROR]["setup_not_created"]) ? "block" : "none";?>;">
            The setup was not created.
        </span>
        <span id="err_blocked_company" class="alert-message" style="display:<?= isset($t_alerts[ALERT_TYPE_ERROR]["blocked_company"]) ? "block" : "none"; ?>;">
        <? foreach ($t_alerts[ALERT_TYPE_ERROR]["blocked_company"] as $error) { ?>
            <span><?=$error;?></span><br/>
        <? } ?>
        </span>
    </div>
</div>

<div class="page-row">
    <div class="page-col1">
        <div class="box-parent">
            <form id="selected_companies_form" action="?page=baseline" method="post" class="box" style="padding: 0;" onsubmit="return formValidationBaseline();">
                <div class="box-list-header">Selected companies</div>
                <input id="companies_tree" type="hidden" name="companies_tree"/>
                <ul id="box_list" class="box-list">
                    <p id="info_text" class="info-box-text">No companies selected.</p>
                </ul>
                <div id="email_block" class="form-row-props form-row">
                    <label for="email" class="form-row-text">
                        <span>Email</span>
                        <span class="required">*</span>
                    </label>
                    <input id="email" name="email" type="text" class="form-input required-value" value="<?=$t_email;?>"  onchange="addToLocalStorage('cs_baseline_email', this.value)" />
                </div>
                <div class="form-row-props form-row">
                    <label class="checkbox">
                        <input id="del_selected_companies" name="del_selected_companies" type="checkbox" <?= $t_del_selected_companies ? " checked" : "";?> onchange="addToLocalStorage('cs_baseline_checkbox', this.checked)" />
                        <span class="fal"></span>
                        <div class="form-row-text">Remove selected companies from list (after environment is created)</div>
                    </label>
                </div>
                <input type="hidden" value="" name="ids" id="ids">
                <div class="list-buttons" style="display: flex;">
                    <input id="build" name="create" type="submit" form="selected_companies_form" value="Build" class="button flex-right"/>
                    <button type="button" class="button flex-left" onclick="cleanBaselineForm(event);">Clear</button>
                </div>
            </form>

            <li id="li_row_INDEX" style="display: none;">
                <input type="radio" name="li_radio_btn" class="select-row hide" id="radio_INDEX">
                <label class="radio-btn-label list-item" for="radio_INDEX">
                    <span style="padding-left: 0.001001px;">LABEL_SPAN_TEXT</span>
                    &nbsp;&nbsp;
                    <div id="select-pod-INDEX">
                        <div class="select-group">
                            <button type="button" class="select-btn" onclick="showDropdown(event,'company_INDEX_pods_selector');">
                                <span id="company_INDEX_pod_label" class="select-label"><?=reset($t_pod_labels);?></span>
                                <span class="select-arrow-box"><span class="select-arrow"></span></span>
                            </button>
                            <div id="company_INDEX_pods_selector" class="select-dropdown">
                                <? foreach ($t_pod_labels as $item => $value) { ?>
                                <input id="company_INDEX_pod_<?=$item;?>" name="company_INDEX_pod" type="radio" value="<?=$item;?>" <?=(reset($t_pod_labels) === $value) ? " checked" : "";?> class="select-option" onclick="setSelector('company_INDEX_pod_label','<?=$value;?>'); changeOption('<?=$item?>', 'company_INDEX', 'selector_option_generic')"/>
                                <label for="company_INDEX_pod_<?=$item;?>" class="select-item"><?=$value;?></label>
                                <? } ?>
                            </div>
                        </div>
                    </div>
                    &nbsp;&nbsp;
                    <div id="select-schema-INDEX">
                        <div class="select-group">
                            <button type="button" class="select-btn" onclick="showDropdown(event,'company_INDEX_selector');">
                                <span id="company_INDEX_selector_label" class="select-label"><?=reset($t_schema_labels[array_key_first($t_pod_labels)]);?></span>
                                <span class="select-arrow-box"><span class="select-arrow"></span></span>
                            </button>
                            <div id="company_INDEX_selector" class="select-dropdown">
                                <? foreach ($t_schema_labels[array_key_first($t_pod_labels)] as $schema_key => $value) { ?>
                                <input id="company_INDEX_<?=$schema_key;?>" name="company_INDEX_schema" type="radio" value="<?=$schema_key;?>" <?=(reset($t_schema_labels[array_key_first($t_pod_labels)]) === $value) ? " checked" : "";?> class="select-option" onclick="setSelector('company_INDEX_selector_label','<?=$value;?>');"/>
                                <label for="company_INDEX_<?=$schema_key;?>" class="select-item"><?=$value;?></label>
                                <? } ?>
                            </div>
                        </div>
                    </div>
                    &nbsp;&nbsp;
                    <button class="btn-icon" style="margin-left: auto;" onclick="removeRow(event, 'li_row_INDEX')">
                        <span class="far i-remove"></span>
                    </button>
                </label>
            </li>
        </div>
    </div>
    <div class="page-col1">
        <? if ($t_oLister !== null) { ?>
        <?=$t_oLister->Render();?>
        <? } ?>
    </div>
</div>

<template id="selector_option_generic">
    <input id="BLOCK_KEY" name="BLOCK" type="radio" value="KEY" class="select-option" onclick="setSelector('BLOCK_selector_label', 'VALUE');"/>
    <label for="BLOCK_KEY" title="VALUE" class="select-item" tabindex="-1">VALUE</label>
</template>

<script>
    <? if (!empty($t_schema_labels)) { ?>
    var option_labels = <?= json_encode($t_schema_labels, true); ?>;
    <? } ?>
    <? if ($t_remove_comp_val === true) { ?>
    localStorage.removeItem('companies_tree');
    <? } ?>
    const page = "create_setup_from_baseline";
</script>

