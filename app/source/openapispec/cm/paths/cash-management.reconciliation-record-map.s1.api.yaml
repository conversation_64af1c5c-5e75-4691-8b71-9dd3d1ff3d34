openapi: 3.0.0
info:
  title: cash-management-reconciliation-record-map
  description: Account reconciliation transaction mapping
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Reconciliation record maps
    description: A reconciliation record map associates a bank transaction with an account reconciliation source record for reconciliation.
servers:
  - url: 'https://dev09.intacct.com/users/neema.shetty/projects.bankFeedRestApi/api/v0'
paths:
  /objects/cash-management/reconciliation-record-map:
    get:
      summary: List reconciliation record maps
      description: Returns a collection with a key, ID, and link for each reconciliation record map. This operation is mostly for use in testing; use query to find objects that meet certain criteria and to specify properties that are returned.
      x-documentationFlags:
        subscription: Cash Management
        userPermissions:
          - userType: Business
            permissions: List, View Reconcile bank
      tags:
        - Reconciliation record maps
      operationId: list-cash-management-reconciliation-record-map
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Account reconciliation transaction map list
                properties:
                  'ia::result':
                    type: array
                    description: Account reconciliation transaction maps
                    items:
                      type: object
                      properties:
                        key:
                          type: string
                        id:
                          type: string
                        href:
                          type: string
                          readOnly: true
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List reconciliation record maps:
                  value:
                    'ia::result':
                      - key: '51'
                        id: '51'
                        href: /objects/cash-management/reconciliation-record-map/51
                      - key: '52'
                        id: '52'
                        href: /objects/cash-management/reconciliation-record-map/52
                      - key: '200'
                        id: '200'
                        href: /objects/cash-management/reconciliation-record-map/200
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  /objects/cash-management/reconciliation-record-map/{key}:
    parameters:
      - name: key
        description: System-assigned key for the reconciliation record map
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a reconciliation record map
      description: Returns detailed information for a specified reconciliation record map.
      x-documentationFlags:
        subscription: Cash Management
        userPermissions:
          - userType: Business
            permissions: List, View Reconcile bank
      tags:
        - Reconciliation record maps
      operationId: get-cash-management-reconciliation-record-map-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Account reconciliation record map details
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/cash-management-reconciliation-record-map'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a reconciliation record map:
                  value:
                    'ia::result':
                      key: '267'
                      id: '267'
                      reconciliationSourceRecord:
                        key: '2672'
                        id: '2672'
                        href: /objects/cash-management/reconciliation-source-record/2672
                      bankTxnRecord:
                        key: '365'
                        id: '365'
                        href: /objects/cash-management/bank-transaction/365
                      matchRuleKey: null
                      bankTxnMatchRuleKey: null
                      matchedAmount: '1200.00'
                      matchType: automatch
                      matchedStatus: matched
                      audit:
                        createdDateTime: '2021-10-15T17:18:16Z'
                        modifiedDateTime: '2021-10-15T17:18:16Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      href: /objects/cash-management/reconciliation-record-map/267
                    'ia::meta':
                      totalCount: 1
        '404':
          description: Not Found
components:
  schemas:
    cash-management-reconciliation-record-map:
      $ref: ../models/cash-management.reconciliation-record-map.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
