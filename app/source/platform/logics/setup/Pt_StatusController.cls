<?
/**
 * Manages HTTP requests to create/modify workflow statuses.
 */
require_once 'Pt_Includes.inc';

require_once "Pt_StatusManager.cls";
require_once "Pt_WorkflowManager.cls";

class Pt_StatusController
{

    /**
     * Create new workflow status.
     */
    public static function create() 
    {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0410', "Object definition with id $objDefId not found",
                                       [ 'OBJDEFID' => $objDefId ]);
        }

        $customLabels = Pt_I18nLabelsController::getIdAndLabels(Pt_Status::STATUS_LABELS_INDEXES);
        $statusName = http_getParameter(FIELD_NAME);
        $systemCode = http_getParameter(FIELD_SYS_CODE);
        $createAction = http_isBoxChecked(FIELD_CREATE_ACTION);

        $orderNo = Pt_StatusManager::getNextOrderNo($objDefId);
        $processes = Pt_ProcessManager::getByObjectDef($objDefId);

        $props = self::getProperties($customLabels);

        XACT_BEGIN('PLATFORM');
        $s = null;
        $a = null;
        try {
            $s = Pt_StatusManager::create(0, $objDefId, $orderNo, $customLabels, $statusName, $systemCode, $props);

            foreach ($processes as $p) {
                $actionIds = http_getIntArray(FIELD_ASSIGNED.$p->getId());
                Pt_WorkflowManager::setActions($p, $s, $actionIds);
            }

            if ($createAction) {
                $actionName = http_getParameter(FIELD_NAME_LABEL);
                if (strlen($actionName) == 0) {
                    $actionName = $statusName; 
                }

                $orderNo2 = Pt_ActionManager::getNextOrderNo($objDefId);
                $pageId = Pt_WebUtil::getPageId($objDefId, TYPE_STATUS);
                $a = Pt_ActionManager::create(
                    0, $objDefId, $orderNo2, Pt_ActionManager::getCustomLabelsDefaults($actionName), $actionName,
                    Pt_Action::ACTION_STATUS, $pageId, $s->getId(), false, null);
            }
            Pt_Cache::loadWorkflow();

            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        if ($a != null) {
            Pt_ActionController::grantPermissions($a->getId(), $objDefId); 
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.STATUS_HAS_BEEN_CREATED",
                'placeHolders' => [
                    [ 'name' => 'LINK_TEXT', 'value' => util_encode($s->__toString())],
                    [ 'name' => 'LINK_HREF', 'value' => Pt_WebUtil::url("pt_statusView.phtml?statusId=".$s->getId())]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.STATUS_HAS_BEEN_CREATED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Clone existing workflow status.
     */
    public static function cloneStatus() 
    {
        $id = http_getIntParameter(FIELD_STATUS_ID);
        $sOld = Pt_StatusManager::getById($id);
        if ($sOld == null) {
            throw new Pt_I18nException('PAAS-0411', "Status with id $id not found", [ 'ID' => $id ]);
        }
        $objDefId = $sOld->getObjectDefId();
    
        $sourceCustomLabels = Pt_I18nLabelsController::getIdAndLabels(Pt_Status::STATUS_LABELS_INDEXES);
        $customLabels = $sOld->initializeLabels($sourceCustomLabels, Pt_Status::STATUS_LABEL_IDX,
                                                Pt_Status::STATUS_LABEL_IDX);
        $statusName = http_getParameter(FIELD_NAME);
        $systemCode = http_getParameter(FIELD_SYS_CODE);

        $orderNo = Pt_StatusManager::getNextOrderNo($objDefId);
        $processes = Pt_ProcessManager::getByObjectDef($objDefId);

        $props = self::getProperties($customLabels);

        XACT_BEGIN('PLATFORM');
        $sNew = null;
        try {
            $sNew = Pt_StatusManager::create(0, $objDefId, $orderNo, $customLabels, $statusName, $systemCode, $props);

            foreach ($processes as $p) {
                $actionIds = http_getIntArray(FIELD_ASSIGNED.$p->getId());
                Pt_WorkflowManager::setActions($p, $sNew, $actionIds);
            }
            Pt_Cache::loadWorkflow();

            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N: TODO
            db_rollback($ex);
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.STATUS_HAS_BEEN_CLONED",
                'placeHolders' => [
                    [ 'name' => 'LINK_TEXT', 'value' => util_encode($sNew->__toString()) ],
                    [ 'name' => 'LINK_HREF', 'value' => Pt_WebUtil::url("pt_statusView.phtml?statusId=".$sNew->getId())]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.STATUS_HAS_BEEN_CLONED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Update existing workflow status.
     */
    public static function update() 
    {
        $id = http_getIntParameter(FIELD_STATUS_ID);
        $s = Pt_StatusManager::getById($id);
        if ($s == null) {
            throw new Pt_I18nException('PAAS-0412', "Status with id $id not found", [ 'ID' => $id ]);
        }

        $customLabels = Pt_I18nLabelsController::getIdAndLabels(Pt_Status::STATUS_LABELS_INDEXES);
        $statusName = http_getParameter(FIELD_NAME);
        $systemCode = http_getParameter(FIELD_SYS_CODE);

        $processes = Pt_ProcessManager::getByObjectDef($s->getObjectDefId());
        $props = self::getProperties($customLabels);

        XACT_BEGIN('PLATFORM');
        try {
            Pt_StatusManager::update($s, $customLabels, $statusName, $systemCode, $props);

            foreach ($processes as $p) {
                $actionIds = http_getIntArray(FIELD_ASSIGNED.$p->getId());
                Pt_WorkflowManager::setActions($p, $s, $actionIds);
            }
            Pt_Cache::loadWorkflow();

            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.STATUS_HAS_BEEN_UPDATED",
                'placeHolders' => [
                    [ 'name' => 'LINK_TEXT', 'value' => util_encode($s->__toString())],
                    [ 'name' => 'LINK_HREF', 'value' => Pt_WebUtil::url("pt_statusView.phtml?statusId=".$s->getId())]
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.STATUS_HAS_BEEN_UPDATED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Delete existing workflow status.
     */
    public static function delete() 
    {
        $id = http_getIntParameter(FIELD_STATUS_ID);
        $s = Pt_StatusManager::getById($id);
        if ($s == null) {
            throw new Pt_I18nException('PAAS-0413', "Status with id $id not found", [ 'ID' => $id ]);
        }

        XACT_BEGIN('PLATFORM');
        try {
            Pt_StatusManager::delete($s);
            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N: TODO
            db_rollback($ex);
        }
        $tokens = [];
        $placeholderTokens = [
            [
                'id'           => "IA.OBJ_STATUS_HAS_BEEN_DELETED",
                'placeHolders' => [
                    [ 'name' => 'OBJ', 'value' => util_encode($s) ],
                ],
            ],
        ];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.OBJ_STATUS_HAS_BEEN_DELETED");
        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Reorder statuses for given object definition.
     */
    public static function reorder() 
    {
        $objDefId = http_getIntParameter(FIELD_OBJ_DEF_ID);
        $objDef = Pt_DataObjectDefManager::getById($objDefId);
        if ($objDef == null) {
            throw new Pt_I18nException('PAAS-0414', "Object Definition with id $objDefId not found",
                                       [ 'OBJDEFID' => $objDefId ]);
        }

        $statusIds = http_getIntArray(FIELD_ASSIGNED);
        $len = count($statusIds);
        $tokens = ["IA.DISPLAY_ORDER_OF_WORKFLOW_STATUSES_HAS_BEEN_UPD"];
        $placeholderTokens = [];
        $textMap = getIntlTextMap($tokens, $placeholderTokens);
        $infoMessage = GT($textMap, "IA.DISPLAY_ORDER_OF_WORKFLOW_STATUSES_HAS_BEEN_UPD");

        XACT_BEGIN('PLATFORM');
        try {
            for ($k=0; $k<$len; $k++) {
                $s = Pt_StatusManager::getById($statusIds[$k]);
                if ($s == null || $s->getObjectDefId() != $objDefId) {
                    continue; 
                }
                Pt_StatusManager::setOrderNo($s, $k+1);
            }
            Pt_ActivityTrailManager::createI18N($objDef, ["id" => "IA.DISPLAY_ORDER_OF_WORKFLOW_STATUSES_HAS_BEEN_UPD"]); // Bug 38864

            XACT_COMMIT('PLATFORM');
        } catch (Exception $ex) {
            //I18N : TODO
            db_rollback($ex);
        }

        Session::setProperty(ATTR_INFO_MESSAGE, $infoMessage);
    }

    /**
     * Read Properties from request
     *
     * @param array $customLabels
     *
     * @return array
     */
    private static function getProperties($customLabels)
    {
        $props = [];

        foreach ( Pt_Status::STATUS_LABELS_INDEXES as $index ) {
            if ( $customLabels[$index] ?? false ) {
                $props[Pt_I18nLabels::getParamName($index)] = $customLabels[$index]->getCustomLabelId();
            }
        }

        return $props;
    }
    
    /**
     * Generate labels for component
     *
     * @param Pt_Status $status
     *
     */
    private static function generateLabelForComponent(Pt_Status $status)
    {
        $labels = $status->createMissingObjectLabels(Pt_Status::STATUS_LABELS_INDEXES,
                                                     $status->getAllLabels(),
                                                     [ $status->__toString() ]);
        $status->setAllLabels($labels);
        Pt_StatusManager::update($status, $labels, $status->getUnitName(), $status->getSystemName(),
                                 self::getProperties($labels));
    }
    
    /**
     * Get status's labels
     *
     * @param Pt_Status $status
     * @param string    $baseLanguage
     * @param string    $desiredLanguage
     *
     * @return array
     */
    public static function exportLabelForComponent(Pt_Status $status, string $baseLanguage, string $desiredLanguage)
    {
        $customLabels = [];
        self::generateLabelForComponent($status);
        $statusLabelIdx = (int) Pt_Status::STATUS_LABEL_IDX;
        $baseLang = $status->getSpecificLabelForComponent("", $baseLanguage, $statusLabelIdx);
        $desiredLang = $status->getSpecificLabelForComponent("", $desiredLanguage, $statusLabelIdx);
        if ( $labelId = $status->getLabels($statusLabelIdx)->getCustomLabelId() ?? null ) {
            $customLabels[$labelId] = [ $baseLang, $desiredLang ];
        }
        
        return $customLabels;
    }

}
