----------------------------------------------------------------------
-------IA-30649 : Add tax to create TXN rules
-- ------Drop indexes, constraints  and  columns added in RECURGLBATCH and RECURPRRECORD
----------------------------------------------------------------------

--RECURGLBATCH
DROP INDEX IX_RECURGLBATCH_TAXSCHEDULEKEY
/
DROP INDEX IX_RECURGLBATCH_TAXSOLUTIONKEY
/
DROP INDEX IX_RECURGLBATCH_VENDORKEY
/
DROP INDEX IX_RECURGLBATCH_CUSTOMERKEY
/
ALTER TABLE RECURGLBATCH DROP CONSTRAINT FK_RECURGLBATCH_TAXSCHEDULEKEY
/
ALTER TABLE RECURGLBATCH DROP CONSTRAINT FK_RECURGLBATCH_TAXSOLUTIONKEY
/
ALTER TABLE RECURGLBATCH DROP CONSTRAINT FK_RECURGLBATCH_VENDORKEY
/
ALTER TABLE RECURGLBATCH DROP CONSTRAINT FK_RECURGLBATCH_CUSTOMERKEY
/
ALTER TABLE RECURGLBATCH DROP COLUMN  TAXSCHEDULEKEY
/
ALTER TABLE RECURGLBATCH DROP COLUMN  TAXSOLUTIONKEY
/
ALTER TABLE RECURGLBATCH DROP COLUMN  VENDORKEY
/
ALTER TABLE RECURGLBATCH DROP COLUMN  CUSTOMERKEY
/
ALTER TABLE RECURGLBATCH DROP COLUMN  TAXIMPLICATION
/

--RECURPRRECORD
DROP INDEX IX_RECURPRRECORD_TAXSCHEDULEKEY
/
ALTER TABLE RECURPRRECORD DROP CONSTRAINT FK_RECURPRRECORD_TAXSCHEDULEKEY
/
ALTER TABLE RECURPRRECORD DROP COLUMN  TAXSCHEDULEKEY
/
ALTER TABLE RECURPRRECORD DROP COLUMN  TAXIMPLICATION
/
