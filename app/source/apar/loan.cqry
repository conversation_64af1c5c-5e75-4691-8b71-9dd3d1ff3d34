<?

$kloanQueries['QRY_LOAN_LOANID_PRE_EXIST'] = array (
    'QUERY' => "SELECT COUNT(1) AS FOUND FROM LOANMST WHERE LOANID =?  AND CNY# =? ",
    'ARGTYPES' => array('text' ,'integer' )
);

$kloanQueries['QRY_LOAN_LOAN_NUMBER_PRE_EXIST'] = array (
    'QUERY' => "SELECT COUNT(1) AS FOUND FROM LOANMST WHERE LOANNUMBER =?  AND CNY# =? ",
    'ARGTYPES' => array('text' ,'integer' )
);

$kloanQueries['QRY_LOAN_GET_RECORDNO'] = array (
    'QUERY' => "SELECT RECORD# AS RECORDNO, LOANID, ENTITYKEY, LOCATIONKEY FROM LOANMST WHERE LOANID =?  AND CNY# =? ",
    'ARGTYPES' => array('text' ,'integer' )
);

$kloanQueries['QRY_LOAN_GET_DEACTIVE_LOANSCHEDULE'] = array (
    'QUERY' => "SELECT 
					SO.RECORD#		SCHOPKEY,
					SO.OPERATION#	OPKEY,
					SO.SCHEDULE#	SCHKEY
				FROM 
					LOANSCHEDULE LS,
					SCHEDULEDOPERATION SO
				WHERE 
					LS.CNY#=?
					AND SO.CNY#=?
					AND LS.LOANRECORD# = ?
					AND SO.RECORD# = LS.SCHOPKEY
				ORDER BY
					SO.RECORD# ",
    'ARGTYPES' => array('integer' ,'integer' ,'integer')
);

$kloanQueries['QRY_LOAN_GET_SCHEDULE_NEXTEXECDATE'] = array (
    'QUERY' => "SELECT  
					NEXTEXECDATE
				FROM
					SCHEDULE 
				WHERE 
					CNY#=? 
					AND RECORD#=? ",
    'ARGTYPES' => array('integer', 'integer')
);

$kloanQueries['QRY_LOAN_UPDATE_SCHEDULE_STATUS'] = array (
    'QUERY' => "UPDATE SCHEDULE 
					SET STATUS=? 
				WHERE 
					CNY#=?
					AND RECORD#=?
					AND STATUS!=? ",
    'ARGTYPES' => array('text' ,'integer', 'integer', 'text')
);

$kloanQueries['QRY_LOAN_UPDATE_OPERATION_STATUS'] = array (
    'QUERY' => "UPDATE OPERATION 
					SET STATUS=? 
				WHERE 
					CNY#=? 
					AND RECORD#=?
					AND STATUS!=? ",
    'ARGTYPES' => array('text' ,'integer', 'integer', 'text' )
);

$kloanQueries['QRY_LOAN_UPDATE_SCHEDULEDOPERATION_STATUS'] = array (
    'QUERY' => "UPDATE SCHEDULEDOPERATION 
					SET STATUS=? 
				WHERE 
					CNY#=? 
					AND RECORD#=?
					AND STATUS!=? ",
    'ARGTYPES' => array('text' ,'integer', 'integer', 'text' )
);

$kloanQueries['QRY_LOAN_GET_ACCOUNTLABELS'] = array (
    'QUERY' => "SELECT 
					A.RECORD# LABELRECORDNO, 
					A.ACCT_NO GLACCOUNTNO, 
					A.LABEL ACCOUNTLABEL,
					B.RECORD# GLRECORDNO
				FROM 
					ACCOUNTLABEL A, 
					GLACCOUNT B
				WHERE 
					A.CNY# =? 
					AND B.CNY# = ?
					AND A.MODULEKEY = '3.AP'
					AND A.ACCT_NO = B.ACCT_NO ",
    'ARGTYPES' => array('integer', 'integer' )
);