<?
/**
 * Data field for relationships support.
 */
require_once 'Pt_DataFieldDef.cls';
require_once 'Pt_Relationship.cls';

class Pt_FieldRelationship extends Pt_DataFieldDef
{
    /**
     * Set UI field definition
     *
     * @param Pt_Relationship $uiField
     */
    public function setUIField($uiField) 
    {
        if (!($uiField instanceof Pt_Relationship)) {
            throw new Pt_I18nException('PAAS-0200', "Not Pt_Relationship UI field");
        }
        parent::setUIField($uiField);
    }

    /**
     * Get other object definition
     *
     * @return int
     */
    public function getObjectDefId2() 
    {
        /* @var Pt_Relationship $uiField */
        $uiField = $this->uiField;
        return $uiField->getObjectDefId2();
    }

    /**
     * Get other object definition
     *
     * @return Pt_DataObjectDef
     */
    public function getObjectDef2() 
    {
        /* @var Pt_Relationship $uiField */
        $uiField = $this->uiField;
        return $uiField->getObjectDef2();
    }

    /**
     * Given the obj def id of one side of the relationship return the obj def id of the
     * other side of the relationship.
     *
     * @param int $objDefId
     *
     * @return int the obj def id of the other side of the relationship
     */
    public function getOtherObjectDefId($objDefId) 
    {
        $ret = $this->getObjectDefId();
        if ( $objDefId == $ret ) {
            $id = $this->getObjectDefId2();
        }

        /** @noinspection PhpUndefinedVariableInspection */
        return $id;
    }

    /**
     * Given the obj def of one side of the relationship return the obj def of the
     * other side of the relationship.
     *
     * @param Pt_DataObjectDef $objDef
     *
     * @return Pt_DataObjectDef the obj def of the other side of the relationship
     */
    public function getOtherObjectDef($objDef) 
    {
        $ret = $this->getObjectDef();
        if ( $objDef->getId() == $ret->getId() ) {
            $ret = $this->getObjectDef2();
        }
        return $ret;
    }

    /**
     * Get relationship definition id
     *
     * @return int
     */
    public function getRelationshipDefId() 
    {
        /* @var Pt_Relationship $uiField */
        $uiField = $this->uiField;
        return ($uiField == null ? 0 : $uiField->getRelationshipDefId());
    }

    /**
     * Get relationship definition
     *
     * @return Pt_RelationshipDef
     */
    public function getRelationshipDef() 
    {
        /* @var Pt_Relationship $uiField */
        $uiField = $this->uiField;
        return ($uiField == null ? null : $uiField->getRelationshipDef());
    }

    /**
     * Get name of table, where this field is stored
     *
     * @return string
     */
    public function getTableName() 
    {
        return 'PT_RELATIONSHIP';
    }

    /**
     * True, if this field's is calculated based on other fields' values
     *
     * @return bool
     */
    public function isDependent() 
    {
        return false;
    }

    /**
     * True, if this field can be deleted separately
     *
     * @return bool
     */
    public function canBeDeleted() 
    {
        // T 8373
        $rel = $this->getRelationshipDef();
        if ($rel==null) {
            return true; 
        }
        $objDef = $this->getObjectDef();
        if ($objDef==null) {
            return true; 
        }
        /* @var Pt_DataObjectDef $objDef */
        $counter = 0;
        $fields = $objDef->getByDataClass("Pt_FieldRelationship");
        foreach ($fields as $field) {
            if ($field->getRelationshipDefId() == $rel->getId()) {
                $counter++; 
            }
        }
        return $counter != 1;
    }

    /**
     * True, list can be sorted by this field
     *
     * @return bool
     */
    public function isSortable() 
    {
        $objDef2 = $this->getObjectDef2();
        if ($objDef2 == null || !$objDef2->isPlatform()) {
            return false;   // Cannot guarantee that standard object supply sortable column - Bug 34560
        }
        /* @var Pt_Relationship $uiField */
        $uiField = $this->uiField;
        return !$uiField->isMultiple() && isset($this->resultsetName);
    }

    /**
     * Get value corresponding to this field from ResultSet.
     *
     * @param string[] $row
     * @param string  $alternateField
     *
     * @return mixed
     */
    public function fromDB($row, $alternateField=null) 
    {
        $value = $row[$alternateField ?: $this->resultsetName];
        if (!isset($value)) {
            return null; 
        }
        return intval($value);  // This is not real value (which is int[]), used for sorting only.
    }

    /**
     * Get value to be used in update SQL statement.
     *
     * @param mixed $value
     *
     * @return int|null
     */
    public function toDB($value)
    {
        if ( $value instanceof Pt_ArrayIds ) {
            $value = $value->getIds();
        }
        $relValue = ( is_array($value) ? intval($value[0] ?? 0) : ( is_numeric($value) ? intval($value) : null ) );
        
        return $relValue ? : null;
    }
    
    /**
     * Get value to be used in select SQL statement.
     *
     * @param mixed $value
     *
     * @return int
     */
    public function toDBSelect($value)
    {
        return $this->toDB($value) ?? 0;
    }

    /**
     * True, if this field needs to save extra info (not in data table)
     *
     * @return bool
     */
    public function needsSaveExtra() 
    {
        return true;
    }

    /**
     * Save extra info (not in data table)
     *
     * @param Pt_DataObject $data
     * @param mixed         $newValue
     * @param mixed         $oldValue
     * @param array         &$fieldData
     * @param bool          $doUpdate
     */
    public function saveExtra($data, $newValue, $oldValue, &$fieldData, $doUpdate) 
    {
        if ( $newValue instanceof Pt_ArrayIds ) {
            $newValue = $newValue->getIds();
        } else if ( !is_array($newValue) ) {
            $newValue = [ $newValue ];
        }
        $newValue = array_filter($newValue);

        if ( $oldValue instanceof Pt_ArrayIds ) {
            $oldValue = $oldValue->getIds();
        } else if ( !is_array($oldValue) ) {
            $oldValue = [ $oldValue ];
        }
        $oldValue = array_filter($oldValue);

        if ( util_equalArrays($newValue, $oldValue) ) { // Bug 36924
            return;
        }

        $rel = $this->getRelationshipDef();
        if ($rel == null) {
            return;
        }
        /* @var Pt_Relationship $uiField */
        $uiField = $this->uiField;
        $isHierarchy = $uiField->isHierarchy();
        $isParent = false;
        if ($isHierarchy) {
            $isParent = $uiField->isParent();
            if ($doUpdate) {
                Pt_RelationshipManagerChoose::updateHierarchy($rel, $data, $newValue, $isParent);
            } else {
                Pt_RelationshipManagerChoose::createHierarchy($rel, $data, $newValue, $isParent);
            }
        } else {
            if ($doUpdate) {
                Pt_RelationshipManagerChoose::updateRelationships($rel, $data, $newValue);
            } else {
                Pt_RelationshipManagerChoose::createRelationships($rel, $data, $newValue);
            }
        }

        // Set value to other object for single rels.
        if (!$rel->isMultiple($this->objDefId)) {
            $objDef2 = $this->getObjectDef2();
            if ($objDef2 == null) {
                return;
            }
            if ( ! $isHierarchy ) {
                $relField = $objDef2->getRelationshipField($rel);
            } else {
                $relField = $objDef2->getHierarchyField($rel, ! $isParent);
            }
            if ($objDef2 != null && $relField != null && $relField->getColumnName() != null && $objDef2->isPlatform()) {
                $update = new Pt_UpdateQuery($objDef2->getTableName(), GetMyCompany());
                $update->addField($relField->getColumnName());
                $update->setWhere($objDef2->getIdColumn()."=:2");
                $query = $update->__toString();

                $lookupName = $relField->getFieldName();

                // Bug 36160
                if ( $oldValue && is_array($oldValue) ) {
                    foreach ( $oldValue as $id ) {
                        if ( in_array($id, $newValue) ) {
                            continue;
                        }
                        db_exec([ $query, null, $id ]);
                        $otherData = Pt_Cache::getDataObject($id);
                        if ( $otherData !== null ) {
                            $otherData->setFieldValue($lookupName, null);
                        }
                    }
                }

                $dataId = $data->getId();
                foreach ($newValue as $id) {
                    db_exec([$query, $dataId, $id]);
                    $otherData = Pt_Cache::getDataObject($id);
                    if ( $otherData !==  null ) {
                        $otherData->setFieldValue($lookupName, $dataId);
                    }
                }
            }
        }
    }

    /**
     * Generate DDL SQL for IDW
     *
     * @return string|null
     */
    public function idwDdlAddColumn() 
    {
        $rel = $this->getRelationshipDef();
        $objDef2 = $this->getObjectDef2();

        // Data corruption scenario
        if ( $rel == null || $objDef2 == null ) {
            return null;
        }

        if ($rel->isMultiple($objDef2->getId()) || $rel->isHierarchy()) {
            // don't include in IDW
            return null;
        } else {
            if (!$this->isStandard()) {
                $fieldName = Pt_StandardUtil::getFieldNameForGetList($this->idwGetFieldName());
            } else {
                $fieldName = $this->idwGetFieldName();
            }
            return $fieldName . " varchar";
        }
    }

    /**
     * @return bool
     */
    public function ddsInclude()
    {
        /**
         * @var Pt_RelationshipDef $rel
         */
        $rel = $this->getRelationshipDef();
        $objDef2 = $this->getObjectDef2();

        // Data corruption scenario
        if ( $rel == null || $objDef2 == null ) {
            return false;
        }

        // do I want the first of the second?
        if ($rel->isMultiple($objDef2->getId()) || $rel->isHierarchy()) {
            return false;
        } else {
            return true;
        }

    }

    /**
     * called after deleting a field to take care of deleting any associated data
     */
    public function finishDelete()
    {
        $rel = $this->getRelationshipDef();
        if ( $rel ) {
            Pt_RelationshipDefManager::delete($rel);
        }
    }
    
    /**
     * Get name of column in data table, where this field is stored.
     *
     * @param bool $usedForWrite needed for writing in the new storage when we are still reading from the old storage
     *
     * @return string
     */
    public function getColumnName($usedForWrite = false)
    {
        if ( ! preg_match("/^rel([1-9][0-9]?|100)$/i", $this->columnName ?? '')
             && ! preg_match("/^uddrel(1[0-9]|[1-9]|20)$/i", $this->columnName ?? '') ) {
            return $this->columnName;
        }

        if ( $rel = $this->getRelationshipDef() ) {
            $relCanBeInNewStorage = $rel->canUsePrr();
        } else {
            $relCanBeInNewStorage = false;
        }
        if ( Pt_RelationshipManagerChoose::newRelationshipRedesignReadNew($rel ?? null)
             || ( $usedForWrite && $relCanBeInNewStorage ) ) {
            return $this->columnName;
        } else {
            return null;
        }
    }

}
