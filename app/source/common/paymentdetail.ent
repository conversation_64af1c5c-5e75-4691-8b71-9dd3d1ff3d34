<?
/**
 *    FILE: paymentdetail.ent
 *    AUTHOR: <PERSON>
 *    DESCRIPTION:
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
*/

$kSchemas['paymentdetail'] = array(

    'children' => array(
    ),

    'object' => array (
        'PAIDREC_ID',
    ),


    'schema' => array(
        'PAIDREC_ID',
        'PAIDREC_WHENCREATED',
        'PAIDREC_DESCRIPTION',
        'PAIDREC_TOTALENTERED',
        'AMOUNT',
        'PAIDREC_TOTALDUE',
        'PAIDREC_KEY',
        'PAIDREC_RTYPE'
    ),


    'db_fieldinfo' => array (
        'PAIDREC_ID'            => array('type' => 'text'),
        'PAIDREC_WHENCREATED'    => array('type' => 'date'),
        'PAIDREC_DESCRIPTION'    => array('type' => 'text'),
        'PAIDREC_TOTALENTERED'    => array('type' => 'decimal'),
        'AMOUNT'                => array('type' => 'decimal'),
        'PAIDREC_TOTALDUE'        => array('type' => 'decimal'),
        'PAIDREC_KEY'            => array('type' => 'integer'),
        'PAIDREC_RTYPE'            => array('type' => 'text')
    ),


    'fieldinfo' => array(
        array(
            'type' => array( 
                'ptype' => 'currency', 
            ),
            'path' => 'PAIDREC_TOTALENTERED'
        ),
        array(
            'type' => array( 
                'ptype' => 'currency', 
            ),
            'path' => 'AMOUNT'
        ),
        array(
            'type' => array( 
                'ptype' => 'currency', 
            ),
            'path' => 'PAIDREC_TOTALDUE'
        )
    ),

    'printas' => 'IA.PAYMENT_DETAIL',
    'pluralprintas' => 'IA.PAYMENT_DETAILS',
    'vid' => 'PAIDREC_ID',
    'nosysview' => true,
    
);
