openapi: 3.0.0
info:
  title: cash-management-bank-fee-line
  description: cash-management.bank-fee-line API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Bank fee lines
    description: Line items in the bank fee or interested earned.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/cash-management/bank-fee-line:
    get:
      summary: List bank fee lines
      description: Returns a collection with a key, ID, and link for each bank fee line.
      tags:
        - Bank fee lines
      operationId: list-cash-management-bank-fee-line
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of bank-fee-line objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of bank fee lines:
                  value:
                    'ia::result':
                      - key: '1'
                        id: '1'
                        href: /objects/cash-management/bank-fee-line/1
                      - key: '3'
                        id: '3'
                        href: /objects/cash-management/bank-fee-line/3
                      - key: '5'
                        id: '5'
                        href: /objects/cash-management/bank-fee-line/5
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
        '400':
          $ref: '#/components/responses/400error'
  /objects/cash-management/bank-fee-line/{key}:
    parameters:
      - name: key
        description: system-assigned unique key for the bank fee line.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a/an bank fee line
      description: Returns detailed information for a particular bank fee line.
      tags:
        - Bank fee lines
      operationId: get-cash-management-bank-fee-line-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the bank-fee-line
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/cash-management-bank-fee-line'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the bank fee line:
                  value:
                    'ia::result':
                      id: '2801'
                      key: '2801'
                      bank-fee:
                        id: '516'
                        key: '516'
                        href: /objects/cash-management/bank-fee/516
                      glAccount:
                        key: '329'
                        id: '6700.07'
                        name: Financial Fees - Bank Service Charges
                        href: /objects/general-ledger/account/329
                      apAccountLabel:
                        key: '9'
                        id: Bank Fees
                        href: /objects/accounts-payable/account-label/9
                      baseAmount: '100.00'
                      txnAmount: '100.00'
                      dimensions:
                        department:
                          key: '9'
                          id: '11'
                          name: Accounting
                          href: /objects/company-config/department/9
                        location:
                          key: '1'
                          id: '1'
                          name: United States of America
                          href: /objects/company-config/location/1
                        project:
                          key: null
                          id: null
                          name: null
                        customer:
                          key: null
                          id: null
                          name: null
                        vendor:
                          key: null
                          id: null
                          name: null
                        employee:
                          key: null
                          id: null
                          name: null
                        item:
                          key: null
                          id: null
                          name: null
                        class:
                          key: null
                          id: null
                          name: null
                      baseLocation:
                        name: United States of America
                        key: '1'
                        href: /objects/company-config/location/1
                      description: Service charge
                      currency:
                        exchangeRate:
                          date: '2024-01-20'
                          typeId: Intacct Daily Rate
                          rate: 1
                        txnCurrency: USD
                        baseCurrency: USD
                      status: active
                      audit:
                        createdDateTime: '2021-01-29T18:52:56Z'
                        modifiedDateTime: '2021-01-29T18:52:56Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      href: /objects/cash-management/bank-fee-line/2801
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    cash-management-bank-fee-line:
      $ref: ../models/cash-management.bank-fee-line.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
