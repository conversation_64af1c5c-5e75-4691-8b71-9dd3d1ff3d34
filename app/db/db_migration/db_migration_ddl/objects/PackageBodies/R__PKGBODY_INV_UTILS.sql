--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__PKGBODY_INV_UTILS.sql
--OWNER_CONTACT: <EMAIL>,shreerajath.he<PERSON><EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

CREATE OR REPLACE PACKAGE BODY INV_UTILS
IS
    --
    -- Update ICITEMTOTALS for a newly inserted ICITEMACTIVITY
    --
    procedure UpdateICItemTotalsInsert(
        a_cny#                  in icitemactivity.cny#%type,
                a_itemkey               in icitemactivity.itemkey%type,
                a_warehousekey          in icitemactivity.warehousekey%type,
                a_totalkey              in icitemactivity.totalkey%type,
                a_quantity              in icitemactivity.quantity%type,
                a_value                 in icitemactivity.value%type,
                a_whencreated           in icitemactivity.whencreated%type,
  a_locationkey           in icitemactivity.locationkey%type,
  a_deptkey               in icitemactivity.deptkey%type
    ) is
        my_time_period  NUMBER;
        v_row icitemtotals.posquantity%type;
    BEGIN
        -- ComputeTimePeriod from the package acct_utils will return the right bucket,
        -- it takes into account non-standard companies.
        my_time_period := acct_utils.ComputeDateToPeriod(a_cny#, a_whencreated);
        -- We don't know whether a record for our time period exists already in icitemtotals,
        -- so we attempt to insert one anyway. If that fails because of a duplicate index key
        -- error then we update the existing record.
        -- It may have been more efficient to try the UPDATE first (because a record is expected
        -- to exist more than 50% of the time), but I am afraid that that may leave potential for error
        -- if another transaction inserts a value in the time between our UPDATE and INSERT.
        BEGIN
            if (a_quantity > 0) then
                BEGIN
                INSERT INTO icitemtotals (cny#, itemkey, warehousekey, totalkey, timeperiod, posquantity, posvalue, negquantity, negvalue, locationkey, deptkey)
                    VALUES (a_cny#, a_itemkey, nvl(a_warehousekey, 0), a_totalkey, my_time_period, a_quantity, a_value, 0, 0, a_locationkey, a_deptkey);
                EXCEPTION
                    WHEN DUP_VAL_ON_INDEX THEN
                        UPDATE icitemtotals
                        SET icitemtotals.posquantity = nvl(icitemtotals.posquantity,0) + nvl(a_quantity,0),
                            icitemtotals.posvalue = nvl(icitemtotals.posvalue,0) + nvl(a_value,0)
                        WHERE   icitemtotals.cny#     = a_cny#          AND
                            icitemtotals.itemkey      = a_itemkey           AND
                            icitemtotals.warehousekey = nvl(a_warehousekey,0)   AND
                            icitemtotals.totalkey     = a_totalkey          AND
                            icitemtotals.timeperiod   = my_time_period AND
          nvl(icitemtotals.locationkey, 0)  = nvl(a_locationkey, 0) AND
          nvl(icitemtotals.deptkey, 0)      = nvl(a_deptkey, 0) ;
                END;
            else
                BEGIN
                INSERT INTO icitemtotals (cny#, itemkey, warehousekey, totalkey, timeperiod, negquantity, negvalue, posquantity, posvalue, locationkey, deptkey)
                    VALUES (a_cny#, a_itemkey, nvl(a_warehousekey, 0), a_totalkey, my_time_period, a_quantity, a_value, 0, 0, a_locationkey, a_deptkey);
                EXCEPTION
                    WHEN DUP_VAL_ON_INDEX THEN
                        UPDATE icitemtotals
                        SET icitemtotals.negquantity = nvl(icitemtotals.negquantity,0) + nvl(a_quantity,0),
                            icitemtotals.negvalue = nvl(icitemtotals.negvalue,0) + nvl(a_value,0)
                        WHERE   icitemtotals.cny#     = a_cny#          AND
                            icitemtotals.itemkey      = a_itemkey           AND
                            icitemtotals.warehousekey = nvl(a_warehousekey,0)   AND
                            icitemtotals.totalkey     = a_totalkey          AND
                            icitemtotals.timeperiod   = my_time_period AND
          nvl(icitemtotals.locationkey, 0)  = nvl(a_locationkey, 0) AND
          nvl(icitemtotals.deptkey, 0)      = nvl(a_deptkey, 0) ;

                END;
            end if;
        END ;
    END UpdateICItemTotalsInsert;

    procedure UpdateICItemTotalsDelete(
        a_cny#                  in icitemactivity.cny#%type,
                a_itemkey               in icitemactivity.itemkey%type,
                a_warehousekey          in icitemactivity.warehousekey%type,
                a_totalkey              in icitemactivity.totalkey%type,
                a_quantity              in icitemactivity.quantity%type,
                a_value                 in icitemactivity.value%type,
                a_whencreated           in icitemactivity.whencreated%type,
    a_locationkey           in icitemactivity.locationkey%type,
    a_deptkey               in icitemactivity.deptkey%type
    ) is
        my_time_period  NUMBER;
    begin
        -- ComputeTimePeriod from the package acct_utils will return the right bucket,
        -- it takes into account non-standard companies.
        my_time_period := acct_utils.ComputeDateToPeriod(a_cny#, a_whencreated);
        -- A record for our time period should exist in icitemtotals.
        if (a_quantity > 0) then
            UPDATE icitemtotals
            SET icitemtotals.posquantity = nvl(icitemtotals.posquantity,0) - nvl(a_quantity, 0),
                icitemtotals.posvalue = nvl(icitemtotals.posvalue,0) - nvl(a_value,0)
            WHERE   icitemtotals.cny#     = a_cny#          AND
                icitemtotals.itemkey      = a_itemkey           AND
                icitemtotals.warehousekey = nvl(a_warehousekey,0)   AND
                icitemtotals.totalkey     = a_totalkey          AND
                icitemtotals.timeperiod   = my_time_period  AND
    nvl(icitemtotals.locationkey, 0)  = nvl(a_locationkey, 0) AND
    nvl(icitemtotals.deptkey, 0)      = nvl(a_deptkey, 0) ;

        else
            UPDATE icitemtotals
            SET icitemtotals.negquantity = nvl(icitemtotals.negquantity,0) - nvl(a_quantity, 0),
                icitemtotals.negvalue = nvl(icitemtotals.negvalue,0) - nvl(a_value,0)
            WHERE   icitemtotals.cny#     = a_cny#          AND
                icitemtotals.itemkey      = a_itemkey           AND
                icitemtotals.warehousekey = nvl(a_warehousekey,0)   AND
                icitemtotals.totalkey     = a_totalkey          AND
                icitemtotals.timeperiod   = my_time_period  AND
    nvl(icitemtotals.locationkey, 0)  = nvl(a_locationkey, 0) AND
    nvl(icitemtotals.deptkey, 0)      = nvl(a_deptkey, 0) ;
        end if;
    end UpdateICItemTotalsDelete;

    procedure inserticitemactivityondocentry(
        a_cny#                  in docentry.cny#%type,
        a_docentrykey           in docentry.record#%type,
        a_dochdrkey             in docentry.dochdrkey%type,
        a_itemkey               in docentry.itemkey%type,
        a_warehousekey          in docentry.warehousekey%type,
        a_cost                  in docentry.cost%type,
        a_quantity              in docentry.quantity%type,
        a_value                 in docentry.value%type,
        a_whencreated           in docentry.whencreated%type,
        a_locationkey           in docentry.locationkey%type,
        a_deptkey               in docentry.deptkey%type,
        a_source_dockey         in docentry.source_dockey%TYPE,
        a_backorder             in dochdr.backorder%TYPE,
        a_autodeduct_desired    IN OUT BOOLEAN,
        a_relateddockey      IN   docentry.relateddockey%TYPE,
        a_relateddoclinekey      IN   docentry.relateddoclinekey%TYPE
        ) is

        v_qty                docentry.quantity%type;
        v_value              docentry.cost%type;

        -- note whether we've seen INTRANSIT/ONHOLD/ONORDER before....
        v_onhold_seen        BOOLEAN := FALSE;
        v_onorder_seen       BOOLEAN := FALSE;
        v_intransit_seen     BOOLEAN := FALSE;
        v_docentrykey   docentry.record#%type;
        v_dochdrkey     docentry.dochdrkey%type;
        v_corecord#   docentry.record#%type;
        v_codochdrkey     docentry.dochdrkey%type;

    BEGIN
        -- This routine writes the item activity records for the current transaction.
        --  As noted below, we skip dups of the auto-deductible totals.

        --  To save an extra query, the caller needs help knowing if it should auto-deduct ONHOLD/ONORDER/INTRANSIT.
        --      it SHOULD if we encounter ONHAND Q or QV, or a second ONORDER/ONHOLD/INTRANSIT.
        --      Use case for the latter: Create a Sales Order (ONHOLD increases), then have a Cancel Sales Order TD
        --      to reduce ONHOLD.  ONHAND is never referred to in that workflow.
        a_autodeduct_desired := FALSE;

        -- there is at most ONE of each of these.  In practice, a non-backorder would have ONE onhold/hand/transit,
        -- where a backorder might also include an ONHAND.
        FOR indx IN 1 .. g_source_details.count
        LOOP
            -- Notice onhold/onorder/intransit that affect quantity.
            -- yes, the quantity could be zero, but for our purposes this is enough
            --    (because increasing the quantity twice if one is zero is not awful)
            IF (g_source_details(indx).source_quantity != 0) THEN
                IF (g_source_details(indx).totalname = 'ONHOLD') THEN
                    v_onhold_seen := TRUE;
                ELSIF (g_source_details(indx).totalname = 'ONORDER') THEN
                    v_onorder_seen := TRUE;
                ELSIF (g_source_details(indx).totalname = 'INTRANSIT') THEN
                    v_intransit_seen := TRUE;
                END IF;
            END IF;
        END LOOP;

        -- the above tells us about PAST transactions, if any, in the conversion chain.....
        -- loop through the CURRENT transaction's totals like ONHAND, ONHOLD, PAINTSHOP, whatever
        IF a_relateddockey IS NOT NULL AND a_relateddoclinekey IS NOT NULL THEN
            v_docentrykey := a_relateddoclinekey;
            v_dochdrkey := a_relateddockey;
            v_corecord# := a_docentrykey;
            v_codochdrkey := a_dochdrkey;
        ELSE
            v_docentrykey := a_docentrykey;
            v_dochdrkey := a_dochdrkey;
        END IF;
        FOR i IN (
                SELECT
                      Q_QV, TOTALKEY, SIGN, ictotal.name as TOTALNAME
                FROM  docpartotals, dochdr, docpar, ictotal
                WHERE dochdr.cny# = a_cny# and
                      dochdr.record# = v_dochdrkey and
                      dochdr.docparkey = docpar.record# and
                      dochdr.cny# = docpar.cny# and
                      docpartotals.docparkey = docpar.record# and
                      docpartotals.cny# = docpar.cny# and
                      ictotal.cny# = dochdr.cny# and
                      ictotal.record# = docpartotals.totalkey
            )
        LOOP
            BEGIN

                -- if we have already seen ONORDER/ONHOLD/INTRANSIT before, then IGNORE THIS ONE
                -- regardless of increase or decrease, sunce double increase or decrease is bad too,
                --   and we autodeduct decreases in processitemondocentry()
                -- ( CONTINUE means ignore the total)
                IF ((i.totalname = 'ONHOLD') AND v_onhold_seen) THEN
                    a_autodeduct_desired := TRUE;
                    IF (a_backorder != 'T') THEN -- allow a backorder to create a new ONHOLD chain
                        CONTINUE;
                    END IF;
                ELSIF ((i.totalname = 'ONORDER') AND v_onorder_seen) THEN
                    a_autodeduct_desired := TRUE;
                    IF (a_backorder != 'T') THEN -- allow a backorder to create a new ONORDER chain
                        CONTINUE;
                    END IF;
                ELSIF ((i.totalname = 'INTRANSIT') AND v_intransit_seen) THEN
                    a_autodeduct_desired := TRUE;
                    CONTINUE;
                ELSIF (i.totalname = 'ONHAND') THEN
                    IF ((i.Q_QV = 'Q') OR (i.Q_QV = 'QV')) THEN
                        a_autodeduct_desired := TRUE;
                    END IF;
                END IF;

                -- initialize v_qty and v_value based on sign.
                v_qty   := 0;
                v_value := 0;
                IF (i.Q_QV = 'Q' OR i.Q_QV = 'QV') THEN
                    v_qty := nvl(a_quantity,0) * i.SIGN;
                END IF;
                IF (i.Q_QV = 'V' OR i.Q_QV = 'QV') THEN
                    IF (nvl(a_cost,0) != 0) THEN
                        v_value := nvl(a_cost,0) * i.SIGN;
                    ELSE
                        v_value := nvl(a_value,0) * i.SIGN;
                    END IF;
                END IF;

                insert into icitemactivity (cny#, docentrykey, parentdochdrkey, itemkey, warehousekey,
                    totalkey, quantity, value, whencreated, locationkey, deptkey, corecord#, codochdrkey)
                values (a_cny#, v_docentrykey, v_dochdrkey, a_itemkey, a_warehousekey,
                    i.totalkey, v_qty, v_value, a_whencreated, a_locationkey, a_deptkey, v_corecord#, v_codochdrkey);

            END;
        END LOOP;
    EXCEPTION
        WHEN DUP_VAL_ON_INDEX THEN
            DBMS_OUTPUT.PUT_LINE('unique constraint violated, likely UQ_ICITEMACTIVITY_ROW');
        -- but ignore, as this occasionally happens
    END inserticitemactivityondocentry;


PROCEDURE processitemondocentry (
 a_cny#               IN   docentry.cny#%TYPE,
 a_docentrykey        IN   docentry.record#%TYPE,
 a_dochdrkey          IN   docentry.dochdrkey%TYPE,
 a_itemkey            IN   docentry.itemkey%TYPE,
 a_warehousekey       IN   docentry.warehousekey%TYPE,
 a_cost               IN   docentry.COST%TYPE,
 a_quantity           IN   docentry.quantity%TYPE,
 a_value              IN   docentry.VALUE%TYPE,
 a_whencreated        IN   docentry.whencreated%TYPE,
 a_locationkey        IN   docentry.locationkey%TYPE,
 a_deptkey            IN   docentry.deptkey%TYPE,
 a_source_dockey      IN   docentry.source_dockey%TYPE,
 a_source_doclinekey  IN   docentry.source_doclinekey%TYPE,
 a_old_cny#           IN   docentry.cny#%TYPE,
 a_old_docentrykey    IN   docentry.record#%TYPE,
 a_old_dochdrkey      IN   docentry.dochdrkey%TYPE,
 a_item_type          IN   icitem.itemtype%TYPE,
 a_backorder          IN   dochdr.backorder%TYPE,
 a_relateddockey      IN   docentry.relateddockey%TYPE,
 a_relateddoclinekey  IN   docentry.relateddoclinekey%TYPE
 ) is

   v_value              NUMBER;
   v_ctype              VARCHAR2 (1);
   v_q_qv               VARCHAR2 (2);
   v_sign               NUMBER;
   v_autodeduct_desired BOOLEAN;
   v_child_has_total    NUMBER;
   v_qty_to_revert      NUMBER;
   v_value_to_revert    NUMBER;
   v_keep               BOOLEAN;

BEGIN

      IF (a_item_type IN ('I', 'SK'))
      THEN
        -- find and get the source document details which affected inventory totals in the workflow
        -- it may be immediate parent or may be some document earlier in the workflow
        -- Note we're getting the entries created by the document itself, not by subsequent auto-deducts
        -- (because childdochdrkey IS NULL).
        inv_utils.g_source_details.delete;
        FOR invTotal IN (
            SELECT
                a_cny# cny#,
                a_docentrykey docentrykey,
                a_dochdrkey dochdrkey,
                ia.itemkey itemkey,
                ia.quantity source_quantity,
                ia.value source_value,
                de.dochdrkey source_dockey,
                de.record# source_doclinekey,
                ict.name totalname,
                ict.record# totalkey
             FROM
                 icitemactivity ia,
                 ictotal ict,
                 (
                     SELECT
                         de.cny#,
                         de.record#,
                         de.dochdrkey
                     FROM
                         docentry de
                     WHERE
                         de.cny# = a_cny#
                     START WITH de.cny# = a_cny#
                                AND de.record# = a_source_doclinekey
                     CONNECT BY de.record# = PRIOR de.source_doclinekey
                                AND de.cny# = a_cny#
                     ORDER BY
                         de.record# DESC
                 ) de
             WHERE
                 de.cny# = a_cny#
                 AND ia.cny# (+) = de.cny#
                 AND ia.docentrykey (+) = de.record#
                 AND ict.cny# (+) = ia.cny#
                 AND ict.record# (+) = ia.totalkey
                 AND ict.name IN (
                     'ONHOLD',
                     'ONORDER',
                     'INTRANSIT',
                     'ONHAND'
                 )
                 AND ia.itemkey = a_itemkey
                 AND ia.childdochdrkey IS NULL
                 AND ia.childdocentrykey IS NULL
                 AND ia.quantity != 0
                 -- AND ROWNUM = 1  note: used to get the first of any of these, now we want the first of EACH
              )
        LOOP
            BEGIN
                v_keep := TRUE;
                -- we want the FIRST ENCOUNTERED onhand/onorder/intransit/onhand
                -- relax; in practice we'll get here at most once unless a backorder, then a small number of times
                FOR indx IN 1 .. g_source_details.count LOOP
                    IF (g_source_details(indx).totalname = invTotal.totalname) THEN
                        v_keep := FALSE; -- got one already, thanks
                        EXIT;
                    END IF;
                END LOOP;

                -- If we have not seen this before in this list, save it
                IF (v_keep) THEN
                    inv_utils.g_source_details.extend;
                    inv_utils.g_source_details(inv_utils.g_source_details.last).cny# := invTotal.cny#;
                    inv_utils.g_source_details(inv_utils.g_source_details.last).docentrykey := invTotal.docentrykey;
                    inv_utils.g_source_details(inv_utils.g_source_details.last).dochdrkey := invTotal.dochdrkey;
                    inv_utils.g_source_details(inv_utils.g_source_details.last).itemkey := invTotal.itemkey;
                    inv_utils.g_source_details(inv_utils.g_source_details.last).source_quantity := invTotal.source_quantity;
                    inv_utils.g_source_details(inv_utils.g_source_details.last).source_value := invTotal.source_value;
                    inv_utils.g_source_details(inv_utils.g_source_details.last).source_dockey := invTotal.source_dockey;
                    inv_utils.g_source_details(inv_utils.g_source_details.last).source_doclinekey := invTotal.source_doclinekey;
                    inv_utils.g_source_details(inv_utils.g_source_details.last).totalname := invTotal.totalname;
                    inv_utils.g_source_details(inv_utils.g_source_details.last).totalkey := invTotal.totalkey;
                END IF;
            END;
        END LOOP;

       -- Record the current document activity, if not dups
       inv_utils.inserticitemactivityondocentry (a_cny#,
                   a_docentrykey,
                   a_dochdrkey,
                   a_itemkey,
                   a_warehousekey,
                   a_cost,
                   a_quantity,
                   a_value,
                   a_whencreated,
                   a_locationkey,
                   a_deptkey,
                   a_source_dockey,
                   a_backorder,
                   v_autodeduct_desired,
                   a_relateddockey,
                   a_relateddoclinekey
                  );

        -- discussion: Looking at earlier transactions in the workflow,
        --  ONHAND: for EACH backorder in the workflow, we back-out the on-hand/etc that was increased/decreased there
        --
        --  ONORDER/ONHOLD/INTRANSIT: If this current txn should reverse these (v_autodeduct_desired is true),
        --      reverse the quantity and value of the earlier transaction's ONHOLD/etc.
        IF (a_source_dockey IS NOT NULL)
        THEN
          v_value := 0;
          IF (NVL (a_cost, 0) != 0)
          THEN
             v_value := NVL (a_cost, 0);
          ELSE
             v_value := NVL (a_value, 0);
          END IF;

          -- Remember that in practice there are 0..2 entries in the loop
           FOR indx IN 1 .. g_source_details.count
            LOOP
            BEGIN
                -- check the configuration settings for conversion type of the parent
                SELECT b.convtype
                  INTO v_ctype
                  FROM dochdr a, docpar b
                 WHERE a.cny# = b.cny#
                   AND a.cny# = a_cny#
                   AND a.docparkey = b.record#
                   AND a.record# = g_source_details(indx).source_dockey;

                  -- get the source document's totals configuration
                SELECT dpt.q_qv, dpt.sign
                  INTO v_q_qv, v_sign
                  FROM dochdr dh, docpar dp, docpartotals dpt
                 WHERE dh.cny# = a_cny#
                   AND dh.record# = g_source_details(indx).source_dockey
                   AND dh.docparkey = dp.record#
                   AND dh.cny# = dp.cny#
                   AND dpt.docparkey = dp.record#
                   AND dpt.cny# = dp.cny#
                   AND dpt.totalkey = g_source_details(indx).totalkey;
                EXCEPTION WHEN NO_DATA_FOUND THEN
                  v_q_qv := 'NA';
                  v_sign := 1;
                END;

                -- did an ancestor set one of these values?
                IF (g_source_details(indx).totalname = 'ONHOLD' OR
                    g_source_details(indx).totalname = 'ONORDER' OR
                    g_source_details(indx).totalname = 'INTRANSIT')
                THEN
                    -- Should we auto-deduct?
                    --     (in practice, if this TD has ONHAND Q or QV, OR this TD also affects the ONHAND/etc)
                    IF (v_autodeduct_desired) THEN
                        -- See what is left, if anything, from the original ONORDER/etc after the workflow.
                        -- Most likely it is untouched unless we did a partial conversion/backorder
                        SELECT
                            nvl(SUM(quantity),0), nvl(SUM(value),0)
                        INTO v_qty_to_revert, v_value_to_revert
                        FROM
                            icitemactivity
                        WHERE
                            cny# = a_cny#
                            -- AND childdochdrkey   IS NOT NULL -- sum the original AND all modifications to it from the workflow
                            -- AND childdocentrykey IS NOT NULL
                            AND parentdochdrkey = g_source_details(indx).source_dockey
                            AND docentrykey = g_source_details(indx).source_doclinekey
                            AND totalkey = g_source_details(indx).totalkey
                            AND itemkey = a_itemkey;

                        -- if the document is not 'Close' (rathr than 'leave open' or Close/Backorder), reduce by the amount of this docentry
                        -- OR the remaining quantity, whichever is lower but more than zero.
                        -- if it is NOT 'leave open', then we reduce by the full amount.
                        IF ((v_ctype != 'C') AND (a_quantity >= 0) AND (v_qty_to_revert >= 0)) THEN
                            v_qty_to_revert   := LEAST(a_quantity,v_qty_to_revert);
                            v_value_to_revert := LEAST(v_value,   v_value_to_revert);
                        END IF;
                        -- if we need to back something out (not zero), do so....
                        IF (v_qty_to_revert != 0) THEN
                            INSERT INTO icitemactivity
                             (cny#,
                             docentrykey,
                             parentdochdrkey,
                             itemkey,
                              warehousekey,
                              totalkey,
                              quantity,
                              VALUE,
                              whencreated,
                              locationkey,
                              deptkey,
                              childdochdrkey,
                              childdocentrykey)
                            SELECT a_cny#,
                               g_source_details(indx).source_doclinekey,
                               g_source_details(indx).source_dockey,
                               icitemactivity.itemkey,
                               icitemactivity.warehousekey,
                               icitemactivity.totalkey,
                               v_qty_to_revert * -1,
                               v_value_to_revert * -1,
                               a_whencreated,
                               icitemactivity.locationkey,
                               icitemactivity.deptkey,
                               a_dochdrkey,
                               a_docentrykey
                                FROM icitemactivity, icitem, ictotal
                               WHERE icitemactivity.cny# = a_cny#
                                 AND icitemactivity.parentdochdrkey = g_source_details(indx).source_dockey
                                 AND icitemactivity.docentrykey = g_source_details(indx).source_doclinekey
                                 AND icitemactivity.itemkey = a_itemkey
                                 AND ictotal.cny# = icitemactivity.cny#
                                 AND ictotal.record# = icitemactivity.totalkey
                                 AND ictotal.NAME IN ('ONHOLD', 'ONORDER', 'INTRANSIT')
                                 AND icitem.cny# = icitemactivity.cny#
                                 AND icitem.itemid = icitemactivity.itemkey
                                 AND icitem.itemtype IN ('I', 'SK')
                                 AND icitemactivity.childdochdrkey is null
                                 AND icitemactivity.childdocentrykey is null
                                 AND ROWNUM = 1;
                        END IF;
                    END IF;
               ELSE
                    -- there was an ONHAND in the past in the workflow, now we have ANOTHER ONHAND.  Likely a backorder.....
                    -- if this is a backorder transaction being saved, and in the past there was an ONHAND, add ONHAND to this transaction.
                   IF (g_source_details(indx).totalname = 'ONHAND' AND (v_q_qv = 'Q' OR v_q_qv = 'QV') and v_ctype = 'B' and a_backorder ='T')
                   THEN
                        INSERT INTO icitemactivity
                        (cny#,
                          docentrykey,
                          parentdochdrkey,
                          itemkey,
                          warehousekey,
                          totalkey,
                          quantity,
                          VALUE,
                          whencreated,
                          locationkey,
                          deptkey,
                          childdochdrkey,
                          childdocentrykey)
                        SELECT  a_cny#,
                                g_source_details(indx).source_doclinekey,
                                g_source_details(indx).source_dockey,
                                icitemactivity.itemkey,
                                icitemactivity.warehousekey,
                                icitemactivity.totalkey,
                                nvl(a_quantity * v_sign, 0) * -1,
                                nvl(v_value * v_sign, 0) * -1,
                                a_whencreated,
                                icitemactivity.locationkey,
                                icitemactivity.deptkey,
                                a_dochdrkey,
                                a_docentrykey
                          FROM icitemactivity, ictotal, icitem
                          WHERE icitemactivity.cny# = a_cny#
                            AND icitemactivity.parentdochdrkey = g_source_details(indx).source_dockey
                            AND icitemactivity.docentrykey = g_source_details(indx).source_doclinekey
                           AND icitemactivity.itemkey = a_itemkey
                           AND icitemactivity.cny# = a_cny#
                           AND ictotal.cny# = icitemactivity.cny#
                           AND ictotal.record# = icitemactivity.totalkey
                           AND ictotal.NAME IN ('ONHAND')
                           AND icitem.cny# = icitemactivity.cny#
                           AND icitem.itemid = icitemactivity.itemkey
                           AND icitem.itemtype IN ('I')
                           AND icitemactivity.childdochdrkey is null
                           AND icitemactivity.childdocentrykey is null;
                   END IF;

               END IF; -- g_totalname condition ends;

           END LOOP; -- g_source_details loop

       END IF; -- a_source_dockey condition ends;

   END IF; -- a_item_type condition ends;
EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
        DBMS_OUTPUT.PUT_LINE('Unique constraint violated, likely UQ_ICITEMACTIVITY_ROW');
        -- but ignore, as this occasionally happens
END processitemondocentry;

procedure InsertICItemActOnDocEntryCost(
        a_cny#                  in docentrycost.cny#%type,
        a_docentrykey           in docentrycost.docentrykey%type,
        a_dochdrkey             in docentrycost.cny#%type,
        a_itemkey               in docentrycost.itemkey%type,
        a_warehousekey          in docentrycost.whsekey%type,
        a_cost                  in docentrycost.cost%type,
        a_quantity              in docentrycost.quantity%type,
        a_value                 in docentrycost.value%type,
  a_whencreated   in docentrycost.datein%type,
  a_locationkey   in docentrycost.cny#%type,
  a_deptkey       in docentrycost.cny#%type

        ) is
    my_time_period  NUMBER;
    v_qty       docentrycost.quantity%type;
    v_value     docentrycost.cost%type;
  Checkicitemactivity number;
    BEGIN

  FOR i IN (
                SELECT
                    Q_QV, TOTALKEY, SIGN
                FROM docpartotals, dochdr, docpar
                WHERE dochdr.cny# = a_cny# and
                      dochdr.record# = a_dochdrkey and
                      dochdr.docparkey = docpar.record# and
                      dochdr.cny# = docpar.cny# and
                      docpartotals.docparkey = docpar.record# and
                      docpartotals.cny# = docpar.cny#
            )

        LOOP
            BEGIN
                -- initialize v_qty and v_value based on sign.
                v_qty := 0;
                v_value := 0;
                IF (i.Q_QV = 'Q' OR i.Q_QV = 'QV') THEN
                    v_qty := nvl(a_quantity,0) * i.SIGN;
                END IF;
                IF (i.Q_QV = 'V' OR i.Q_QV = 'QV') THEN
                    IF (nvl(a_cost,0) != 0) THEN
                        v_value := nvl(a_cost,0) * i.SIGN;
                    ELSE
                        v_value := nvl(a_value,0) * i.SIGN;
                    END IF;
                END IF;

     select count(cny#) into Checkicitemactivity from icitemactivity where cny#= a_cny# and docentrykey =a_docentrykey and parentdochdrkey=a_dochdrkey
     and itemkey= a_itemkey and warehousekey = a_warehousekey and totalkey= i.totalkey;

    IF (Checkicitemactivity>0) then
     update icitemactivity set quantity= quantity + v_qty ,value= value + v_value where cny#= a_cny# and docentrykey =a_docentrykey and parentdochdrkey=a_dochdrkey
      and itemkey= a_itemkey and warehousekey = a_warehousekey and totalkey= i.totalkey;
    ELSE
        insert into icitemactivity (cny#, docentrykey, parentdochdrkey, itemkey, warehousekey, totalkey, quantity, value, whencreated, locationkey, deptkey)
        values (a_cny#, a_docentrykey, a_dochdrkey, a_itemkey, a_warehousekey,i.totalkey, v_qty, v_value, a_whencreated, a_locationkey, a_deptkey);
    END IF;



            END;
        END LOOP;
    END InsertICItemActOnDocEntryCost;

PROCEDURE updatedochdrondochdrsubtotals (
   a_cny#         IN   dochdrsubtotals.cny#%TYPE,
   a_dochdrkey    IN   dochdrsubtotals.dochdrkey%TYPE,
   a_absval       IN   dochdrsubtotals.absval%TYPE,
   a_percentval   IN   dochdrsubtotals.percentval%TYPE,
   a_total        IN   dochdrsubtotals.total%TYPE,
   a_trx_total    IN   dochdrsubtotals.trx_total%TYPE
)
IS
   v_sumsubtotal      NUMBER;
   v_sumtrxsubtotal   NUMBER;
   v_total            NUMBER;
   v_trx_total        NUMBER;
   l_total            NUMBER;
   l_trx_total        NUMBER;
BEGIN
   SELECT total
     INTO v_total
     FROM dochdrmst
    WHERE cny# = a_cny# AND record# = a_dochdrkey;

   SELECT trx_total
     INTO v_trx_total
     FROM dochdrmst
    WHERE cny# = a_cny# AND record# = a_dochdrkey;

   IF INSERTING
   THEN
      IF v_total IS NULL
      THEN
         v_total := 0;
      END IF;

      IF v_trx_total IS NULL
      THEN
         v_trx_total := 0;
      END IF;

      IF a_total IS NULL
      THEN
         l_total := 0;
      ELSE
         l_total := a_total;
      END IF;

      IF a_trx_total IS NULL
      THEN
         l_trx_total := 0;
      ELSE
         l_trx_total := a_trx_total;
      END IF;

      v_sumsubtotal := v_total + l_total;
      v_sumtrxsubtotal := v_trx_total + l_trx_total;

      UPDATE dochdrmst
         SET total = v_sumsubtotal,
             trx_total = v_sumtrxsubtotal
       WHERE cny# = a_cny# AND record# = a_dochdrkey;
   END IF;
END updatedochdrondochdrsubtotals;

PROCEDURE UpsertDETotals
  (
    de_cny IN docentry.cny#%TYPE,
    de_dochdrkey IN docentry.dochdrkey%TYPE,
    de_docclass IN docpar.docclass%TYPE,
    de_spi IN docpar.sale_pur_trans%TYPE,
    de_entity IN dochdr.entity%TYPE
  )
  IS

    CURSOR grp_docentry_values
    IS
  select
    de.cny#,
    de.itemkey,
    de.basecurr,
    de.currency,
    de.whencreated,
    dp.docid,
    nvl(whse.record#, 0) as warehousekey,
    nvl(de.deptkey, 0) as deptkey,
    nvl(de.locationkey, 0) as locationkey,
    nvl(de.customerdimkey, 0) customerdimkey,
    nvl(de.projectdimkey, 0) as projectdimkey,
    nvl(de.vendordimkey, 0) as vendordimkey,
    nvl(de.employeedimkey, 0) as employeedimkey,
    nvl(de.classdimkey, 0) as classdimkey,
    nvl(de.contractdimkey, 0) as contractdimkey,
    nvl(de.costtypedimkey, 0) as costtypedimkey,
    nvl(de.class2dimkey, 0) as class2dimkey,
    nvl(de.assetdimkey, 0) as assetdimkey,
    nvl(de.affiliateentitydimkey, 0) as affiliateentitydimkey,
    nvl(de.workorderdimkey, 0) as workorderdimkey,
--NEWDIMTOOL-    nvl(de.newstddimkey, 0) as newstddimkey,
--*** DO NOT alter/remove the above line.         
    nvl(de.taskkey, 0) as taskdimkey,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 1), '|', ''), 0) as custdim1,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 2), '|', ''), 0) as custdim2,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 3), '|', ''), 0) as custdim3,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 4), '|', ''), 0) as custdim4,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 5), '|', ''), 0) as custdim5,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 6), '|', ''), 0) as custdim6,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 7), '|', ''), 0) as custdim7,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 8), '|', ''), 0) as custdim8,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 9), '|', ''), 0) as custdim9,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 10), '|', ''), 0) as custdim10,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 11), '|', ''), 0) as custdim11,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 12), '|', ''), 0) as custdim12,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 13), '|', ''), 0) as custdim13,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 14), '|', ''), 0) as custdim14,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 15), '|', ''), 0) as custdim15,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 16), '|', ''), 0) as custdim16,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 17), '|', ''), 0) as custdim17,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 18), '|', ''), 0) as custdim18,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 19), '|', ''), 0) as custdim19,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*$'), '|', ''), 0) as custdim20,
    sum(de.value) as tot_val,
    sum(de.quantity) as tot_qty,
    sum(nvl(de.trx_value, de.value)) as tot_trx_val,
    sum(dec.cost) as total_cost
        from  docentry de, docentrycost dec, icwarehouse whse, dochdr dh, docpar dp
        where de.cny# = de_cny
    and de.dochdrkey = de_dochdrkey
    and dec.cny# = de.cny#
    and dec.docentrykey = de.record#
    and whse.cny# (+) = de.cny#
    and whse.location_no (+) = de.warehousekey
    and dh.cny# = de_cny
    and dh.record# = de_dochdrkey
    and dp.cny# = dh.cny#
    and dp.record# = dh.docparkey
        group by
    de.cny#,
    de.itemkey,
    de.basecurr,
    de.currency,
    de.whencreated,
    dp.docid,
    nvl(whse.record#, 0),
    nvl(de.deptkey, 0),
    nvl(de.locationkey, 0),
    nvl(de.customerdimkey, 0),
    nvl(de.projectdimkey, 0),
    nvl(de.vendordimkey, 0),
    nvl(de.employeedimkey, 0),
    nvl(de.classdimkey, 0),
    nvl(de.contractdimkey, 0),
    nvl(de.costtypedimkey, 0),
    nvl(de.class2dimkey, 0),
    nvl(de.assetdimkey, 0),
    nvl(de.affiliateentitydimkey, 0),
    nvl(de.workorderdimkey, 0),
--NEWDIMTOOL-    nvl(de.newstddimkey, 0),
--*** DO NOT alter/remove the above line.           
    nvl(de.taskkey, 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 1), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 2), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 3), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 4), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 5), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 6), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 7), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 8), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 9), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 10), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 11), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 12), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 13), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 14), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 15), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 16), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 17), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 18), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 19), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*$'), '|', ''), 0)
  ;

    BEGIN
      FOR eachrec in grp_docentry_values
      LOOP
         BEGIN
      INSERT INTO detotals
       (cny#, value, quantity, trx_value, totalcost, itemkey, warehousekey, deptkey,
          locationkey, timeperiod, docparid, entity,
          customerdimkey, projectdimkey, vendordimkey, employeedimkey, classdimkey, contractdimkey, costtypedimkey, 
        class2dimkey, 
        assetdimkey,
        affiliateentitydimkey,
        workorderdimkey,
--NEWDIMTOOL-        newstddimkey,
--*** DO NOT alter/remove the above line.        
        taskdimkey,
        custdim1, custdim2, custdim3, custdim4, custdim5,
        custdim6, custdim7, custdim8, custdim9, custdim10,
        custdim11, custdim12, custdim13, custdim14, custdim15,
        custdim16, custdim17, custdim18, custdim19, custdim20,
          currency, basecurr, docclass, sale_pur_trans)
      values
        (eachrec.cny#,
         eachrec.tot_val,
         eachrec.tot_qty,
         eachrec.tot_trx_val,
         eachrec.total_cost,
         eachrec.itemkey,
         eachrec.warehousekey,
         eachrec.deptkey,
         eachrec.locationkey,
         acct_utils.ComputeDateToPeriod(eachrec.cny#, eachrec.whencreated),
         eachrec.docid,
         de_entity,
         eachrec.customerdimkey,
         eachrec.projectdimkey,
         eachrec.vendordimkey,
         eachrec.employeedimkey,
         eachrec.classdimkey,
         eachrec.contractdimkey,
         eachrec.costtypedimkey,
         eachrec.class2dimkey,
         eachrec.assetdimkey,
         eachrec.affiliateentitydimkey,
         eachrec.workorderdimkey,
--NEWDIMTOOL-         eachrec.newstddimkey,
--*** DO NOT alter/remove the above line.          
         eachrec.taskdimkey,
        eachrec.custdim1,
        eachrec.custdim2,
        eachrec.custdim3,
        eachrec.custdim4,
        eachrec.custdim5,
        eachrec.custdim6,
        eachrec.custdim7,
        eachrec.custdim8,
        eachrec.custdim9,
        eachrec.custdim10,
        eachrec.custdim11,
        eachrec.custdim12,
        eachrec.custdim13,
        eachrec.custdim14,
        eachrec.custdim15,
        eachrec.custdim16,
        eachrec.custdim17,
        eachrec.custdim18,
        eachrec.custdim19,
        eachrec.custdim20,
         eachrec.currency,
         eachrec.basecurr,
         de_docclass,
         de_spi);

      EXCEPTION WHEN DUP_VAL_ON_INDEX

      THEN
          update detotals det set
            det.value = det.value + eachrec.tot_val,
            det.trx_value = det.trx_value + eachrec.tot_trx_val,
            det.quantity = det.quantity + eachrec.tot_qty,
            det.totalcost = det.totalcost + eachrec.total_cost
          where
            det.cny# = eachrec.cny#
            AND det.itemkey = eachrec.itemkey
            AND det.warehousekey = eachrec.warehousekey
            AND det.deptkey = eachrec.deptkey
            AND det.locationkey = eachrec.locationkey
            AND det.docparid = eachrec.docid
            AND det.entity = de_entity
            and det.customerdimkey = eachrec.customerdimkey
            and det.projectdimkey = eachrec.projectdimkey
            and det.vendordimkey = eachrec.vendordimkey
            and det.employeedimkey = eachrec.employeedimkey
            and det.classdimkey = eachrec.classdimkey
            and det.contractdimkey = eachrec.contractdimkey
            and det.costtypedimkey = eachrec.costtypedimkey
            and det.class2dimkey = eachrec.class2dimkey
            and det.assetdimkey = eachrec.assetdimkey
            and det.affiliateentitydimkey = eachrec.affiliateentitydimkey
            and det.workorderdimkey = eachrec.workorderdimkey
--NEWDIMTOOL-            and det.newstddimkey = eachrec.newstddimkey
--*** DO NOT alter/remove the above line.             
            and det.taskdimkey = eachrec.taskdimkey
            and det.timeperiod = acct_utils.ComputeDateToPeriod(eachrec.cny#, eachrec.whencreated)
            and nvl(det.custdim1, 0) = eachrec.custdim1
            and nvl(det.custdim2, 0) = eachrec.custdim2
            and nvl(det.custdim3, 0) = eachrec.custdim3
            and nvl(det.custdim4, 0) = eachrec.custdim4
            and nvl(det.custdim5, 0) = eachrec.custdim5
            and nvl(det.custdim6, 0) = eachrec.custdim6
            and nvl(det.custdim7, 0) = eachrec.custdim7
            and nvl(det.custdim8, 0) = eachrec.custdim8
            and nvl(det.custdim9, 0) = eachrec.custdim9
            and nvl(det.custdim10, 0) = eachrec.custdim10
            and nvl(det.custdim11, 0) = eachrec.custdim11
            and nvl(det.custdim12, 0) = eachrec.custdim12
            and nvl(det.custdim13, 0) = eachrec.custdim13
            and nvl(det.custdim14, 0) = eachrec.custdim14
            and nvl(det.custdim15, 0) = eachrec.custdim15
            and nvl(det.custdim16, 0) = eachrec.custdim16
            and nvl(det.custdim17, 0) = eachrec.custdim17
            and nvl(det.custdim18, 0) = eachrec.custdim18
            and nvl(det.custdim19, 0) = eachrec.custdim19
            and nvl(det.custdim20, 0) = eachrec.custdim20
            AND det.currency = eachrec.currency
            AND det.basecurr = eachrec.basecurr
            AND det.docclass = de_docclass
            AND det.sale_pur_trans = de_spi;
      WHEN OTHERS
      THEN
          raise_application_error(-20001,'An error was encountered - '||SQLCODE||' -ERROR- '||SQLERRM);
    END;
      END LOOP;
  END;

PROCEDURE DeleteDETotals
  (
    de_cny IN docentry.cny#%TYPE,
    de_dochdrkey IN docentry.dochdrkey%TYPE,
    de_docclass IN docpar.docclass%TYPE,
    de_spi IN docpar.sale_pur_trans%TYPE,
    de_entity IN dochdr.entity%TYPE
  )
  IS

    CURSOR grp_docentry_values
    IS
  select
    de.cny#,
    de.itemkey,
    de.basecurr,
    de.currency,
    de.whencreated,
    dp.docid,
    nvl(whse.record#, 0) as warehousekey,
    nvl(de.deptkey, 0) as deptkey,
    nvl(de.locationkey, 0) as locationkey,
    nvl(de.customerdimkey, 0) customerdimkey,
    nvl(de.projectdimkey, 0) as projectdimkey,
    nvl(de.vendordimkey, 0) as vendordimkey,
    nvl(de.employeedimkey, 0) as employeedimkey,
    nvl(de.classdimkey, 0) as classdimkey,
    nvl(de.contractdimkey, 0) as contractdimkey,
    nvl(de.costtypedimkey, 0) as costtypedimkey,
    nvl(de.class2dimkey, 0) as class2dimkey,
    nvl(de.assetdimkey, 0) as assetdimkey,
    nvl(de.affiliateentitydimkey, 0) as affiliateentitydimkey,
    nvl(de.workorderdimkey, 0) as workorderdimkey,
--NEWDIMTOOL-    nvl(de.newstddimkey, 0) as newstddimkey,
--*** DO NOT alter/remove the above line.           
    nvl(de.taskkey, 0) as taskdimkey,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 1), '|', ''), 0) as custdim1,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 2), '|', ''), 0) as custdim2,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 3), '|', ''), 0) as custdim3,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 4), '|', ''), 0) as custdim4,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 5), '|', ''), 0) as custdim5,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 6), '|', ''), 0) as custdim6,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 7), '|', ''), 0) as custdim7,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 8), '|', ''), 0) as custdim8,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 9), '|', ''), 0) as custdim9,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 10), '|', ''), 0) as custdim10,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 11), '|', ''), 0) as custdim11,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 12), '|', ''), 0) as custdim12,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 13), '|', ''), 0) as custdim13,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 14), '|', ''), 0) as custdim14,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 15), '|', ''), 0) as custdim15,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 16), '|', ''), 0) as custdim16,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 17), '|', ''), 0) as custdim17,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 18), '|', ''), 0) as custdim18,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 19), '|', ''), 0) as custdim19,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*$'), '|', ''), 0) as custdim20,
    sum(de.value) as tot_val,
    sum(de.quantity) as tot_qty,
    sum(nvl(de.trx_value, de.value)) as tot_trx_val,
    sum(dec.cost) as total_cost
        from  docentry de, docentrycost dec, icwarehouse whse, dochdr dh, docpar dp
        where de.cny# = de_cny
    and de.dochdrkey = de_dochdrkey
    and dec.cny# = de.cny#
    and dec.docentrykey = de.record#
    and whse.cny# (+) = de.cny#
    and whse.location_no (+) = de.warehousekey
    and dh.cny# = de_cny
    and dh.record# = de_dochdrkey
    and dp.cny# = dh.cny#
    and dp.record# = dh.docparkey
        group by
    de.cny#,
    de.itemkey,
    de.basecurr,
    de.currency,
    de.whencreated,
    dp.docid,
    nvl(whse.record#, 0),
    nvl(de.deptkey, 0),
    nvl(de.locationkey, 0),
    nvl(de.customerdimkey, 0),
    nvl(de.projectdimkey, 0),
    nvl(de.vendordimkey, 0),
    nvl(de.employeedimkey, 0),
    nvl(de.classdimkey, 0),
    nvl(de.contractdimkey, 0),
    nvl(de.costtypedimkey, 0),
    nvl(de.class2dimkey, 0),
    nvl(de.assetdimkey, 0), 
    nvl(de.affiliateentitydimkey, 0),
    nvl(de.workorderdimkey, 0),
--NEWDIMTOOL-    nvl(de.newstddimkey, 0),
--*** DO NOT alter/remove the above line.           
    nvl(de.taskkey, 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 1), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 2), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 3), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 4), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 5), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 6), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 7), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 8), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 9), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 10), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 11), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 12), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 13), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 14), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 15), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 16), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 17), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 18), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 19), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*$'), '|', ''), 0)
  ;

    BEGIN
      FOR eachrec in grp_docentry_values
      LOOP
         BEGIN
    update detotals det set
      det.value = det.value - eachrec.tot_val,
      det.trx_value = det.trx_value - eachrec.tot_trx_val,
      det.quantity = det.quantity - eachrec.tot_qty,
      det.totalcost = det.totalcost - eachrec.total_cost
    where
      det.cny# = eachrec.cny#
      AND det.itemkey = eachrec.itemkey
      AND det.warehousekey = eachrec.warehousekey
      AND det.deptkey = eachrec.deptkey
      AND det.locationkey = eachrec.locationkey
      AND det.docparid = eachrec.docid
      AND det.entity = de_entity
      and det.customerdimkey = eachrec.customerdimkey
      and det.projectdimkey = eachrec.projectdimkey
      and det.vendordimkey = eachrec.vendordimkey
      and det.employeedimkey = eachrec.employeedimkey
      and det.timeperiod = acct_utils.ComputeDateToPeriod(eachrec.cny#, eachrec.whencreated)
      and det.classdimkey = eachrec.classdimkey
      and det.contractdimkey = eachrec.contractdimkey
      and det.costtypedimkey = eachrec.costtypedimkey
      and det.class2dimkey = eachrec.class2dimkey
      and det.assetdimkey = eachrec.assetdimkey
      and det.affiliateentitydimkey = eachrec.affiliateentitydimkey
      and det.workorderdimkey = eachrec.workorderdimkey
--NEWDIMTOOL-      and det.newstddimkey = eachrec.newstddimkey
--*** DO NOT alter/remove the above line.       
      and det.taskdimkey = eachrec.taskdimkey
      and nvl(det.custdim1, 0) = eachrec.custdim1
      and nvl(det.custdim2, 0) = eachrec.custdim2
      and nvl(det.custdim3, 0) = eachrec.custdim3
      and nvl(det.custdim4, 0) = eachrec.custdim4
      and nvl(det.custdim5, 0) = eachrec.custdim5
      and nvl(det.custdim6, 0) = eachrec.custdim6
      and nvl(det.custdim7, 0) = eachrec.custdim7
      and nvl(det.custdim8, 0) = eachrec.custdim8
      and nvl(det.custdim9, 0) = eachrec.custdim9
      and nvl(det.custdim10, 0) = eachrec.custdim10
      and nvl(det.custdim11, 0) = eachrec.custdim11
      and nvl(det.custdim12, 0) = eachrec.custdim12
      and nvl(det.custdim13, 0) = eachrec.custdim13
      and nvl(det.custdim14, 0) = eachrec.custdim14
      and nvl(det.custdim15, 0) = eachrec.custdim15
      and nvl(det.custdim16, 0) = eachrec.custdim16
      and nvl(det.custdim17, 0) = eachrec.custdim17
      and nvl(det.custdim18, 0) = eachrec.custdim18
      and nvl(det.custdim19, 0) = eachrec.custdim19
      and nvl(det.custdim20, 0) = eachrec.custdim20
      AND det.currency = eachrec.currency
      AND det.basecurr = eachrec.basecurr
      AND det.docclass = de_docclass
      AND det.sale_pur_trans = de_spi;
        END;
      END LOOP;
  END;

  --
  -- Rebuilds the contents of DETOTALS from the DOCENTRY and DOCHDR base tables (for one company)
  --
  PROCEDURE builddetotals (cny IN NUMBER)
  IS
  BEGIN

  DELETE FROM detotals WHERE detotals.cny# = cny;

  INSERT INTO detotals
   (cny#, itemkey, warehousekey, deptkey,
      locationkey, timeperiod, docparid, entity,
      customerdimkey, projectdimkey, vendordimkey, employeedimkey,
      classdimkey, contractdimkey, costtypedimkey, class2dimkey, 
      assetdimkey,
      affiliateentitydimkey,
      workorderdimkey,
--NEWDIMTOOL-      newstddimkey,
--*** DO NOT alter/remove the above line.    
      taskdimkey,
    custdim1, custdim2, custdim3, custdim4, custdim5,
    custdim6, custdim7, custdim8, custdim9, custdim10,
    custdim11, custdim12, custdim13, custdim14, custdim15,
    custdim16, custdim17, custdim18, custdim19, custdim20,
      value, quantity, trx_value,
      totalcost, currency, basecurr, docclass, sale_pur_trans)
  select
    de.cny#,
    de.itemkey,
    nvl(whse.record#, 0) as warehousekey,
    nvl(de.deptkey, 0) as deptkey,
    nvl(de.locationkey, 0) as locationkey,
    acct_utils.ComputeDateToPeriod(de.cny#, de.whencreated),
    dp.docid,
    dh.entity,
    nvl(de.customerdimkey, 0) customerdimkey,
    nvl(de.projectdimkey, 0) as projectdimkey,
    nvl(de.vendordimkey, 0) as vendordimkey,
    nvl(de.employeedimkey, 0) as employeedimkey,
    nvl(de.classdimkey, 0) as classdimkey,
    nvl(de.contractdimkey, 0) as contractdimkey,
    nvl(de.costtypedimkey, 0) as costtypedimkey,
    nvl(de.class2dimkey, 0) as class2dimkey,
    nvl(de.assetdimkey, 0) as assetdimkey,
    nvl(de.affiliateentitydimkey, 0) as affiliateentitydimkey,
    nvl(de.workorderdimkey, 0) as workorderdimkey,
--NEWDIMTOOL-    nvl(de.newstddimkey, 0) as newstddimkey,
--*** DO NOT alter/remove the above line.          
    nvl(de.taskkey, 0) as taskdimkey,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 1), '|', ''), 0) as custdim1,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 2), '|', ''), 0) as custdim2,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 3), '|', ''), 0) as custdim3,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 4), '|', ''), 0) as custdim4,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 5), '|', ''), 0) as custdim5,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 6), '|', ''), 0) as custdim6,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 7), '|', ''), 0) as custdim7,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 8), '|', ''), 0) as custdim8,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 9), '|', ''), 0) as custdim9,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 10), '|', ''), 0) as custdim10,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 11), '|', ''), 0) as custdim11,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 12), '|', ''), 0) as custdim12,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 13), '|', ''), 0) as custdim13,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 14), '|', ''), 0) as custdim14,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 15), '|', ''), 0) as custdim15,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 16), '|', ''), 0) as custdim16,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 17), '|', ''), 0) as custdim17,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 18), '|', ''), 0) as custdim18,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 19), '|', ''), 0) as custdim19,
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*$'), '|', ''), 0) as custdim20,
    sum(de.value) as tot_val,
    sum(de.quantity) as tot_qty,
    sum(nvl(de.trx_value, de.value)) as tot_trx_val,
    sum(dec.cost) as total_cost,
    de.currency,
    de.basecurr,
    dp.docclass,
    dp.sale_pur_trans
  from  docentry de, dochdr dh, docpar dp, docentrycost dec, icwarehouse whse
  where de.cny# = cny
    and whse.cny# = de.cny#
    and whse.location_no (+) = de.warehousekey
    and dh.cny# = de.cny#
    and dh.record# = de.dochdrkey
    and dp.cny# = dh.cny#
    and dp.record# = dh.docparkey
    and dec.cny# = de.cny#
    and dec.docentrykey = de.record#
  group by
    de.cny#,
    de.itemkey,
    nvl(whse.record#, 0),
    nvl(de.deptkey, 0),
    nvl(de.locationkey, 0),
    acct_utils.ComputeDateToPeriod(de.cny#, de.whencreated),
    dp.docid,
    dh.entity,
    nvl(de.customerdimkey, 0),
    nvl(de.projectdimkey, 0),
    nvl(de.vendordimkey, 0),
    nvl(de.employeedimkey, 0),
    nvl(de.classdimkey, 0),
    nvl(de.contractdimkey, 0),
    nvl(de.costtypedimkey, 0),
    nvl(de.class2dimkey, 0),
    nvl(de.assetdimkey, 0),
    nvl(de.affiliateentitydimkey, 0),
    nvl(de.workorderdimkey, 0),
--NEWDIMTOOL-    nvl(de.newstddimkey, 0),
--*** DO NOT alter/remove the above line.      
    nvl(de.taskkey, 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 1), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 2), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 3), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 4), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 5), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 6), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 7), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 8), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 9), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 10), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 11), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 12), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 13), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 14), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 15), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 16), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 17), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 18), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*[|$]', 1, 19), '|', ''), 0),
    nvl(replace(regexp_substr(de.customdimensions, '[^|]*$'), '|', ''), 0),
    de.basecurr,
    de.currency,
    dp.docclass,
    dp.sale_pur_trans;

  END builddetotals;

  FUNCTION eval(val in VARCHAR2) return NUMBER
  IS
      result NUMBER;
  BEGIN
        execute immediate 'select ' || val || ' from dual ' into result;
        return result;
  EXCEPTION
        when others then
        return null;
  END eval;

    procedure dochdrentitytotalupdate (
        p_cnykey	IN DOCHDRENTITYTOTAL.CNY#%TYPE,
        p_dochdrkey	IN DOCHDR.RECORD#%TYPE,
        p_locationkey	IN DOCHDRENTITYTOTAL.locationkey%TYPE,
        p_trx_totalentered	IN DOCHDRENTITYTOTAL.trx_totalentered%TYPE,
        p_totalentered	IN DOCHDRENTITYTOTAL.totalentered%TYPE,
        p_trx_subtotal	IN DOCHDRENTITYTOTAL.trx_subtotal%TYPE,
        p_subtotal	IN DOCHDRENTITYTOTAL.subtotal%TYPE
    ) IS
        v_entitykey number;  
    BEGIN
        inv_utils.v_dochdrkey := 0;
        inv_utils.v_cny := p_cnykey;
        v_entitykey := SYS_CONTEXT('TMCtx', 'LOCATIONKEY');
        IF ( SYS_CONTEXT('TMCtx', 'LOCATIONKEY') IS NULL AND p_locationkey IS NOT NULL) THEN
            SELECT entitykey INTO v_entitykey FROM locationmst WHERE cny# = p_cnykey AND record# = p_locationkey;
        END IF;
    
        IF (v_entitykey IS NOT NULL) THEN
        BEGIN
            inv_utils.v_dochdrkey := p_dochdrkey;
            INSERT INTO dochdrentitytotal (
                cny#,
                dochdrkey,
                locationkey,
                trx_totalentered,
                trx_subtotal,
                totalentered,
                subtotal
            )
            VALUES (
                p_cnykey,
                p_dochdrkey,
                v_entitykey,
                p_trx_totalentered,
                p_trx_subtotal,
                p_totalentered,
                p_subtotal
            );
        
            EXCEPTION
                WHEN DUP_VAL_ON_INDEX THEN
                    UPDATE 	dochdrentitytotal
                    SET 
                        trx_totalentered = trx_totalentered + p_trx_totalentered,
                        trx_subtotal = trx_subtotal + p_trx_subtotal,
                        totalentered = totalentered + p_totalentered,
                        subtotal = subtotal + p_subtotal
                    WHERE 	cny# = p_cnykey
                        AND dochdrkey = p_dochdrkey
                        AND locationkey = v_entitykey;
            END;
        END IF;
    END dochdrentitytotalupdate;
    
    procedure dochdrentitytotaldelete (
        p_cnykey	IN DOCHDRENTITYTOTAL.CNY#%TYPE,
        p_dochdrkey	IN DOCHDR.RECORD#%TYPE,
        p_locationkey	IN DOCHDRENTITYTOTAL.locationkey%TYPE,
        p_trx_totalentered	IN DOCHDRENTITYTOTAL.trx_totalentered%TYPE,
        p_totalentered	IN DOCHDRENTITYTOTAL.totalentered%TYPE,
        p_trx_subtotal	IN DOCHDRENTITYTOTAL.trx_subtotal%TYPE,
        p_subtotal	IN DOCHDRENTITYTOTAL.subtotal%TYPE
    ) IS
        v_trx_totalentered number;
        v_entitykey number;
    BEGIN
        inv_utils.v_dochdrkey := 0;
        inv_utils.v_cny := p_cnykey;
        v_entitykey := SYS_CONTEXT('TMCtx', 'LOCATIONKEY');
        IF ( SYS_CONTEXT('TMCtx', 'LOCATIONKEY') IS NULL AND p_locationkey IS NOT NULL) THEN
            SELECT entitykey INTO v_entitykey FROM locationmst WHERE cny# = p_cnykey AND record# = p_locationkey;
        END IF;
        
        IF (v_entitykey IS NOT NULL) THEN
            inv_utils.v_dochdrkey := p_dochdrkey;
    
            UPDATE 	dochdrentitytotal
                SET 
                    trx_totalentered = trx_totalentered - p_trx_totalentered,
                    trx_subtotal = trx_subtotal - p_trx_subtotal,
                    totalentered = totalentered - p_totalentered,
                    subtotal = subtotal - p_subtotal
                WHERE 	cny# = p_cnykey
                    AND dochdrkey = p_dochdrkey
                    AND locationkey = v_entitykey;
        END IF;
    END dochdrentitytotaldelete;
    
    PROCEDURE builddochdrentitytotal(p_cnykey	IN DOCHDRENTITYTOTAL.CNY#%TYPE)
    IS
    BEGIN
        DELETE FROM dochdrentitytotal WHERE cny# = p_cnykey;
        INSERT INTO dochdrentitytotal (
            cny#,
            dochdrkey,
                locationkey,
                trx_totalentered,
                totalentered,
                trx_subtotal,                       
                subtotal
        )
        (  SELECT
                a.cny#,
                a.dochdrkey,
                a.locationkey,
                SUM(a.trx_totalentered)       trx_totalentered,
                SUM(a.totalentered)           totalentered,
                SUM(a.trx_subtotal)           trx_subtotal,
                SUM(a.subtotal)               subtotal
            FROM
                (
                    SELECT
                        a.cny#,
                        a.dochdrkey,
                        c.entitykey                                                      locationkey,
                        nvl(nvl(SUM(a.trx_value), SUM(a.uivalue)), 0)                    trx_totalentered,
                        SUM(a.uivalue)                                                   totalentered,
                        0                                                                trx_subtotal,
                        0                                                                subtotal
                    FROM
                        docentry  a,
                        location  c
                    WHERE
                            a.cny# = p_cnykey
                        AND c.cny# = a.cny#
                        AND c.record# = a.locationkey
                    GROUP BY
                        a.cny#,
                        a.dochdrkey,
                        c.entitykey
                    UNION ALL
                    SELECT
                        b.cny#,
                        b.dochdrkey,
                        c.entitykey                                                    locationkey,
                        0                                                              trx_totalentered,
                        0                                                              totalentered,
                        nvl(nvl(SUM(b.trx_total), SUM(b.total)), 0)                    trx_subtotal,
                        nvl(SUM(b.total), 0)                                           subtotal
                    FROM
                        dochdrsubtotals  b,
                        location         c
                    WHERE
                            b.cny# = p_cnykey
                        AND c.cny# = b.cny#
                        AND c.record# = b.locationkey
                    GROUP BY
                        b.cny#,
                        b.dochdrkey,
                        c.entitykey
                ) a
            GROUP BY
                a.cny#,
                a.dochdrkey,
                a.locationkey
        );
    
    END builddochdrentitytotal;

END inv_utils;
/
