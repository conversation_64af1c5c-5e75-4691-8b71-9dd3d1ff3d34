// +BI+ Force include of common code
function IncludeJavaScript(jsFile) {
    try {
        document.write('<script type="text/javascript" src="'
            + jsFile + '"></script>');
    } catch (e) {
        alert("Include error: ");
    }
}

// Because many scripts include lister.js directly (instead of using xsl), we need to protect these cases
if (window.gReplacementBaseURL === undefined) {
    window.gReplacementBaseURL = '';
}

IncludeJavaScript(gReplacementBaseURL + '../resources/js/base_lib.js');

var ctrlUpDown = '';
var optionMenus = new Array(); // List of milonic options menus in the lister

function setStateKeysDown() {
    if (event.keyCode == 17) {
        ctrlUpDown = 'DOWN';
    }
}

function setStateKeysUp() {
    if (event.keyCode == 17) {
        ctrlUpDown = 'UP';
    }
}

function setStateKeysUnknown() {
    ctrlUpDown = '';
}

function setStateKeysInit() {
    // The following is needed when the user alt-tabs over to our window.
    // Without this, our window is not accepting key events.
    window.focus();
}

function ctrlIsDown() {
    return (ctrlUpDown == 'DOWN');
}

function attachKeyEvents(obj) {
    with (document) {
        attachEvent("onkeyup", setStateKeysUp);
        attachEvent("onkeydown", setStateKeysDown);
        attachEvent("onmouseenter", setStateKeysInit);
        attachEvent("onmouseleave", setStateKeysUnknown);

        // The following is needed when our location href is set,
        // for example, by a filter screen that preceeded us.
        // Without this, our window is not accepting key events.
        window.focus();
    }
}

// This is required for Picker. Init first.
var is = new newIs();

function getCurrentField(subdoc, currentform, currentfield) {
    var field = subdoc.forms[currentform].elements[currentfield];
    var visibleFields = [];
    if (field.length > 1) {
        visibleFields = Array.prototype.filter.call(field, function(element){
            return element.type !== "hidden";
        });
    }
    return visibleFields.length ? visibleFields[0] : field;
}

//
// Legacy.
//
// This is how Picker collaborates with the Parent Window.
//

function SetField(_value, _leaveopened, _refresh, _area) {

    // The new editor will use yui dialog so the context it going to be different
    var ydialog = false;
    var context = window.opener;
    var subdoc = null;
    var fcsset = false;
    var el = null;
    var i = 0;
    var j = 0;
    var afterfunction = null;
    var fv = null;
    if (!window.opener) {
        ydialog = true;
        context = parent;
        if (typeof PAGE_LAYOUT_TYPE != 'undefined' && PAGE_LAYOUT_TYPE === 'Q') {
            if (typeof getWinHandleContext === 'function') {
                context = getWinHandleContext(context);
            }
        }
    }

    if (_refresh) {
        noCheckRequired = true;
    }
    var theForm = context.document.forms[0];
    var currentfield = theForm._currentfocus ? (theForm._currentfocus.value).toString() : '';
    var currentform  = theForm._currentform  ? (theForm._currentform.value).toString()  : '';
    var currentlayer = theForm._currentlayer ? (theForm._currentlayer.value).toString() : '';

    if (theForm._afterfunction && context.document.forms[0]._afterfunction != 'undefined') {
       afterfunction = (theForm._afterfunction.value).toString();
    }

    _value = baseHtmlToUnicode(_value);

    if (currentlayer) {
        subdoc = context.document.getElementById(currentlayer).ownerDocument;
    } else {
        subdoc = context.document;
    }

    if (currentfield) {
        fv = getCurrentField(subdoc, currentform, currentfield);

        if (fv.value != _value) {
            if (context.document.getElementById('_changed')) {
                context.document.getElementById('_changed').value = true;
            } else if (context.document.forms[0]._changed) {
                context.document.forms[0]._changed.value = true;
            }
        }

        fv.value = _value;
        if (fv.fromComboBox && fv.fromComboBox.findOptionByCurrentInput) {
            //94145/DE13655
            var matchingValue = fv.fromComboBox.findOptionByCurrentInput();
            if (matchingValue && matchingValue[0]) {
                fv.value = matchingValue[0];
            }
        }
        if(fv.meta && fv.meta.parentComponent && fv.meta.parentComponent.useUIComponent) {
            fv.fromComboBox.isComboInGridHeading = true;
        }
        
        context.baseSendUIEvent(fv, 'change');

        // begin focus set code
        // following to automatically move cursor
        // to next field in navigation sequence
        // when user picks from picker
        fcsset = false;
        el = subdoc.forms[currentform].elements;
        var checkUseUIComponent;
        for (i = 0; i < el.length; i++) {
            checkUseUIComponent = el[i].meta && el[i].meta.parentComponent && el[i].meta.parentComponent.useUIComponent;
            if (el[i].name == fv.name && !checkUseUIComponent) {
                for (j = i + 1; j < el.length; j++) {
                    if (
                        el[j].type != 'button' &&
                        el[j].type != 'hidden' &&
                        !el[j].disabled &&
                        el[j].tabIndex != -1
                    ) {
                        fcsset = true;
                        try {
                            el[j].focus();
                        }
                        catch (e) {
                        }

                        break;
                    }
                }
            }
            if (fcsset) {
                break;
            }
        }
        if (!fcsset) {
            if (fv.length) {
                fv[0].focus();
            } else {
                fv.focus();
            }
        }
        // end focus set code
    }

    if (currentfield.indexOf('ENTRIES') != -1 && context.AutoFill)
        context.AutoFill(currentlayer, currentform, '', '', currentfield);

    if (afterfunction && afterfunction != 'undefined' && afterfunction != '') {
        var _func = 'context.' + afterfunction;
        if (currentlayer) {
            _func += "(currentlayer, currentform,'' ,'' ,currentfield);";
        } else if (currentfield) {
            _func += "(currentfield);";
        } else {
            _func += "(_value);";
        }
        eval(_func);
    }

    if (_refresh) {
        if (context.document.getElementById('_changed')) {
            context.SetNoCheckRequired();
        }
        if (_area != null && _area == 'ReportingActEditor') {
            context.ReloadReportingAccountSet(fv, 'picker');
        } else {
            context.document.forms[0].submit();
        }
    }

    if (!_leaveopened && ydialog) {
        //ACTION UI
        if (typeof PAGE_LAYOUT_TYPE != 'undefined' && PAGE_LAYOUT_TYPE === 'Q') {
            //Check if closeQxDialog exist on the parent and execute it if it does
            if (window.parent && typeof window.parent.closeQxDialog === 'function' && window.frameElement) {
                window.parent.closeQxDialog(window.frameElement);
                //Check if closeQxDialog exist on the window and execute it if it does
            } else if (typeof window.closeQxDialog === 'function' && window.frameElement) {
                window.closeQxDialog(window.frameElement);
            } else if ( context.YAHOO ) {
                context.YAHOO.picker.dialog.hide();
            } else {
                console.log("Error: closeQxDialog/context.YAHOO does not exists");
                window.close();
            }
        } else {
            //OLD UI
            if (window.parent && typeof window.parent.closeQxDialog === 'function' && window.frameElement) {
                window.parent.closeQxDialog(window.frameElement);
            } else {
                context.YAHOO.picker.dialog.hide();
            }
        }
    }

    if (!_leaveopened && !ydialog) {
        if (typeof PAGE_LAYOUT_TYPE != 'undefined' && PAGE_LAYOUT_TYPE === 'Q') {
            // Check for closeQxDialog function definition and if page is loaded in iframe
            // then try to close qxDialog, otherwise it's a new window then close it
            // this condition is neccesary for pages loaded in 2012 UI which open new window with quixote page
            if (typeof window.parent.closeQxDialog === 'function' && window.frameElement) {
                window.parent.closeQxDialog(window.frameElement);
            } else {
                window.close();
            }
        } else {
            window.close();
        }
    }

}

//
// Yes, Virginia. This is a replacement for the old Java Script
//
// A 'shortcut' to set ( or reset ) some value and then submit the form.
//
//

function c(n, v, r, e) {
    var showLoader = true;
    if ('.export' in document.ff.elements) {
        if (e != null && e != '') {
            showLoader = false; //dont show loader on export, because there is no stop mechanims
            document.ff.elements['.export'].value = e;
        } else {
            document.ff.elements['.export'].value = '';
        }
    }
    if (r != null && r != '') {
        document.ff.elements[r].value = 0;
    }

    if (n != null && n != '') {
        document.ff.elements[n].value = v;
    }
    TrimURL();
    // Don't submit multidelete checkboxes and all buttons from page header
    jq('input[name=".multideletechkboxheader"]').attr('disabled', true);
    jq('input[name=".multidelete[]"]').attr('disabled', true);
    jq('.qx-page-header .btn-toolbar button').attr('disabled', true);
    document.ff.submit();
    window.setTimeout(function () {
        jq('input[name=".multideletechkboxheader"]').attr('disabled', false);
        jq('input[name=".multidelete[]"]').attr('disabled', false);
        jq('.qx-page-header .btn-toolbar button').attr('disabled', false);
    }, 0);
    if (showLoader) showQXLoader();
}


// Create an element on the ff form, input div and set its value
// If it already exists, just update the existing value
function addElement(n, v) {
    var form = document.ff;

    // Check for existence first
    if (!form.elements[n]) {
        var inputField = document.createElement("input");
        inputField.setAttribute("type", "hidden");
        inputField.setAttribute("name", n);
        inputField.setAttribute("value", v);
        form.appendChild(inputField);
    } else {
        form.elements[n].value = v;
    }
}

//
//	just set some value ( no submit )
//

function set(n, v) {
    document.ff.elements[n].value = v;
}


//
//	set the action and submit
//

function setact(notused, a) {
    document.ff.action = a;
    document.ff.submit();
}

//
//	set the action and submit with method
//

function setactMethod(method, actionURL) {
    document.ff.action = actionURL;
    document.ff.method = method;
    document.ff.submit();
}


//
//	execute parent function
//

function execparent(notused, f) {
    var func = eval("parent." + f);
    if (func && typeof(func) == 'function') {
        func();
    }
}

//
//	set the action and execute parent function
//

function setactparent(f, a) {
    document.ff.action = a;
    execparent(null, f);
}


//
//       select / deselect all the checkboxes
//

function SetChecked(val) {
    var f = document.ff;
    var len = f.elements.length;
    var i = 0;
    for (i = 0; i < len; i++) {
        if (f.elements[i].name == '.checks[]') {
            f.elements[i].checked = val;
            if (f.elements[i].onclick) {
                f.elements[i].onclick();
            }
        }
    }
}

function SetMultiDeleteChecked(val) {
    var f = document.ff;
    var len = f.elements.length;
    var i = 0;
    for (i = 0; i < len; i++) {
        if (f.elements[i].name == '.multidelete[]') {
            f.elements[i].checked = val;
        }
    }
}

function setDisabled(n, val) {
    var anch = document.getElementById(n);

    //  Firefox specific code below.
    //  Create label to take place of anchor when it is disabled.
    var hiddenLabel = document.getElementById(n + 'disAnchorText');
    if (!hiddenLabel) {
        var hiddenLabel = document.createElement('span');
        hiddenLabel.setAttribute('id', n + 'disAnchorText');
        hiddenLabel.className = 'HiddenAnchorText';
        baseSetText(hiddenLabel, anch.firstChild.nodeValue);

        anch.parentNode.appendChild(hiddenLabel);
    }

    if (val == 'true') {
        anch.style.visibility = 'hidden';
        hiddenLabel.style.display = '';
    } else {
        anch.style.visibility = 'visible';
        hiddenLabel.style.display = 'none';
    }
}


//
// Confirm deletion
//

function confdel(str, showPanel, ypanelstr) {
    var answer = confirm(str);
    if (answer && showPanel) {
        LaunchLoadingPanel(ypanelstr); // Show the loading panel
    }
    return answer;
}

function confirmMultiDelete(str, showPanel, ypanelstr, url, keyname, postArgs) {
    var keys = [];
    var count = 0;
    var f = document.ff;
    var len = f.elements.length;
    for (var i = 0; i < len; i++) {
        if (f.elements[i].name == '.multidelete[]' && f.elements[i].checked) {
            keys['_obj__' + keyname + "--" + i] = f.elements[i].value;
            count++;
        }
    }

    if (1 > count) {
        alert(GT('IA.SELECT_AT_LEAST_ONE_RECORD_FOR_DELETE'));
        return false;
    }

    if (str) {
        token = {
            'id': str,
            'placeHolders': [
                {'name': 'COUNT', 'value': count}
            ]
        }
        str = GT(token);
    }

    var cnfrm = count > 1 || ((1 == count) &&
        typeof(warnOnDeletePref) != 'undefined' && (warnOnDeletePref === 'true' ? true : false));
    var answer = !cnfrm || confirm(str);
    if (answer) {
        LaunchLoadingPanel(ypanelstr); // Show the loading panel
        var form = document.createElement("form");
        form.setAttribute("method", 'post');
        form.setAttribute("action", url);

        // Additional post args (CSRF token)
        for (var key in postArgs) {
            var hiddenField = document.createElement("input");
            hiddenField.setAttribute("type", "hidden");
            hiddenField.setAttribute("name", key);
            hiddenField.setAttribute("value", postArgs[key]);
            form.appendChild(hiddenField);
        }

        for (var key in keys) {
            var hiddenField = document.createElement("input");
            hiddenField.setAttribute("type", "hidden");
            hiddenField.setAttribute("name", key);
            hiddenField.setAttribute("value", keys[key]);

            form.appendChild(hiddenField);
        }

        document.body.appendChild(form);
        form.submit();
    }
    return answer;
}

// Hide milonic menus and show the loading panel
function LaunchLoadingPanel(msg) {
    // We need to hide the menus because they stay on top on the yahoo dialog
    for (var i = 0; i < optionMenus.length; i++) {
        hide_menu(optionMenus[i]);
    }
    if (!msg) {
        msg = GT('IA.PLEASE_WAIT');
    }
    showQXLoader(msg);
}

// Hide a milonic menu based on its name
function hide_menu(menuName) {
    var menuRef = getMenuByName(menuName); // Milonic API (Get menu)
    menuDisplay(menuRef, 0); // Milonic API (Hide menu)
}

//
// Tricks with filters.
//

function clear_filters(noClear) {
    for (i = 0; i < document.ff.elements.length; i++) {
        // TODO - use string operation here
        if (document.ff.elements[i].name.charAt(0) == 'F' && document.ff.elements[i].name.charAt(1) == '_') {
            document.ff.elements[i].value = '';
        }
    }

    document.ff.elements[".FPaths"].value = '';
    document.ff.elements[".FOprs"].value = '';
    document.ff.elements[".FVals"].value = '';

    if (!noClear) {
        c();
    }

}

//
// This is a common function used to extend any url with its arguments.
//

function ExtendUrl(url, params) {
    return url + (url.indexOf('?') > 0 ? '&' : '?') + params;
}

//
// These are the common function used to launch new windows based on WinPop
//

//
// Generic Launch function
//
function Launch(aUrl, aName, aWidth, aHeight, helpwinparam, checkDuplicateWindow) {
    var innerSess = document.ff.elements['.sess'];

    if (innerSess.length > 1) {
        sess = innerSess[0].value;
    } else {
        sess = innerSess.value;
    }
    aUrl = ExtendUrl(aUrl, '.sess=' + sess);


    if (checkDuplicateWindow != 'undefined' && checkDuplicateWindow === true) {
        var duplicateWindow = getDuplicateWindow(aUrl);
        if (duplicateWindow !== null && !duplicateWindow.closed) {
            duplicateWindow.focus();
            return;
        }
    }

    if (typeof PAGE_LAYOUT_TYPE != 'undefined' && PAGE_LAYOUT_TYPE === 'Q' && typeof showQxDialog === 'function') {
        showQxDialog('', aUrl, '', 'form', 'url');
    } else {
        //when adding custom objects to dashboard. The platform code adds another
        //hidden variable to .sess which needs to be taken care of otherwise
        //the popup will result in login screen

        if (!helpwinparam) {
            helpwinparam = 'width=' + aWidth + ',height=' + aHeight + ',toolbar=no,location=no,directories=no,status=yes,menubar=no,scrollbars=yes,resizable=yes,dependent,left=100,top=100';
        }
        WinPop(aUrl, aName, helpwinparam);
    }
}

//
// This is the slide specific version of Launch used for entity slide, consolidation, templates, tax export
//
function SlideLaunch(type, option, destCny, destMod, destURL, destLocation, popup, helpwinparam, payLoad, isTopLevel) {
    SlideLaunchEx(type, option, destCny, destMod, destURL, destLocation, popup, helpwinparam, payLoad, '');
    if (typeof PAGE_LAYOUT_TYPE !== 'undefined' && PAGE_LAYOUT_TYPE === 'Q' && type === 'multientity') {
        QXSaveMRUEntity(destLocation, isTopLevel); // send also top level if we are on Top level
    }
}

function SlideLaunchEx(type, option, destCny, destMod, destURL, destLocation, popup, helpwinparam, payLoad, invokingUser) {
    // This function handles window launch for various Slides
    var sess = document.forms[0].elements['.sess'].value;
    var srcop = 0;
    if (!popup) popup = 1;
    if (type != 'intacct') {
        srcop = document.forms[0].elements['.op'].value;
    }

    //Firefox doesnot escape '&amp;' as '%26' as IE does. So we need to be extra extra careful to replace the '&amp;' before passing it to
    //escape function below. Firefox encodes '&amp;' to '%26amp%3B'
    destURL = destURL.replace(/\&amp;/g, '&');

    var pfix = (type == 'intacct') ? '../acct/' : '';
    var url = pfix + 'slide.phtml?.sess=' + sess + '&.desttype=' + type + '&.dest=' + destCny + '&.destMod=' + destMod + '&.destlocation=' + destLocation + '&.destURL=' + encodeURIComponent(destURL.obfuscate()) + '&.srcop=' + srcop;
    var name = (option == 1) ? type + destCny + destLocation : 'single';

    if (invokingUser) {
        url = url + "&.invokedby=" + encodeURIComponent(invokingUser);
    }

    if (type == 'taxexport') {
        helpwinparam = 'width=540,height=135,toolbar=no,location=no,directories=no,status=yes,menubar=no,scrollbars=yes,resizable=yes,dependent,left=100,top=100';
    }
    else if (type == 'subsidiary') {
        var mywidth = screen.width;
        var myheigth = screen.height;
        helpwinparam =
            'width=' + mywidth + ',height=' + myheigth + ',toolbar=yes,location=yes,directories=yes,status=yes,menubar=yes,scrollbars=yes,resizable=yes,left=0,top=0,screenX=0,screenY=0';
    }

    // Payload indicates this should be done through a post to conceal the contents
    if (payLoad) {
        var postArgs = [];
        postArgs['.payLoad'] = JSON.stringify(payLoad);
        if (popup == 1) {
            indirectPostLaunch(name, helpwinparam, url, pfix, postArgs);
        } else {
            doPost(url, postArgs);
        }
    } else {
        if (popup == 1) {
            if (typeof PAGE_LAYOUT_TYPE != 'undefined' && PAGE_LAYOUT_TYPE === 'Q' && typeof LaunchInWindow === 'function') {
                LaunchInWindow(url, name);
            } else {
                WinPop(url, name, helpwinparam);
            }
        } else {
            self.location = url;
        }
    }
}

//
// This is the base window opener function - do not add specific options to this function
//
function WinPop(launchURL, launchName, launchParams) {
    if (typeof PAGE_LAYOUT_TYPE != 'undefined' && PAGE_LAYOUT_TYPE === 'Q' && typeof showQxDialog === 'function') {
        showQxDialog('', launchURL, '', 'form', 'url');
    } else {
        var popup;
        if (launchParams) {
            popup = window.open(launchURL, launchName, launchParams);
        }
        else {
            popup = window.open(launchURL, launchName);
        }
        if (popup == null) {
            return;
        }
        if (popup.opener == null) {
            popup.opener = self;
        }
        popup.focus();
        windowObjectReference = popup;
    }
}

function toggleFilter() {
    topArrow = document.getElementById('toggleTopFilter');
    bottomArrow = document.getElementById('toggleBottomFilter');
    if (topArrow && bottomArrow) {
        baseIconToggle(topArrow, baseIconArrowUp, baseIconArrowDown);
        baseIconToggle(bottomArrow, baseIconArrowUp, baseIconArrowDown);
        topArrow.title = ((topArrow.title == "Move Filter Up") ? "Move Filter Down" : "Move Filter Up");
        bottomArrow.title = ((bottomArrow.title == "Move Filter Up") ? "Move Filter Down" : "Move Filter Up");
    }


    topFilter = document.getElementById('topFilterRow');
    bottomFilter = document.getElementById('bottomFilterRow');

    if (bottomFilter) {
        bottomFilter.style.display = (bottomFilter.style.display == '' ? 'none' : '');
        bottomFilter.style.visibility = (bottomFilter.style.visibility == 'hidden' ? 'visible' : 'hidden');

        if (bottomFilter.style.display == '') {
            MoveFilters(topFilter, bottomFilter);
            document.ff.elements['.filterPosition'].value = 'bottom';
        } else {
            MoveFilters(bottomFilter, topFilter);
            document.ff.elements['.filterPosition'].value = 'top';
            bottomFilter.style.display = 'none';
            bottomFilter.style.visibility = 'hidden';
        }
    }
    return true;
}

function MoveFilters(fromObj, toObj) {
    var ObjArr = Array();
    var count = 0;
    var kids = fromObj.childNodes;
    var numkids = kids.length;
    for (var i = numkids - 1; i >= 0; i--) {
        var c = fromObj.removeChild(kids[i]);
        ObjArr[count] = c;
        count++;
    }

    var numkids = ObjArr.length;
    for (var i = numkids - 1; i >= 0; i--) {
        toObj.appendChild(ObjArr[i]);
    }
    return true;
}

function OnListerLoad() {

    /** Disabling the focus on filter field functionality**/
    /*if (((document.URL).indexOf('lister') > 0 || (document.URL).indexOf('prrecord') > 0)
        && document.getElementById('filter')) {
        //document.getElementById('filter').focus();
    }*/
    if ((document.URL).indexOf('picker') > 0 && document.getElementById('select')) {
        try {
            // For YUI dialog this will not work because the frame is hidden when we try to focus on the field
            // We will focus on the field inside from dialog.js
            document.getElementById('select').focus();
        } catch (e) {
            ;
        }
    }

    if (document.ff.elements['.filterPosition'].value == 'bottom') {
        bottomFilter = document.getElementById('bottomFilterRow');
        if (bottomFilter.style.display == 'none') {
            topArrow = document.getElementById('toggleTopFilter');
            if (topArrow) {
                topArrow.onclick();
            }
        }

    } else if (document.ff.elements['.filterPosition'].value == 'top') {
        topFilter = document.getElementById('topFilterRow');
        if (topFilter && topFilter.style.display == 'none') {
            topArrow = document.getElementById('toggleTopFilter');
            if (topArrow) {
                topArrow.onclick();
            }
        }
    }

    otherInits();

    ClearControlCache();
    return true;
}

//  Allow overriding of onload inits (e.g. quixote)
function otherInits() {
}

function ClearControlCache() {
    document.ff.elements['.suv'].value = false;
    document.ff.elements['.newuvid'].value = '';
    //clear querystring also for this values
    removeQueryStringParam('.suv');
    removeQueryStringParam('.newuvid');
}

/**
 * Deletes the given parameter and its associated value, from the list of all url parameters.
 *
 * @param param
 */
function removeQueryStringParam(param) {
    if (typeof URLSearchParams !== 'undefined' && typeof (history.replaceState) !== "undefined") {
        var params = new URLSearchParams(location.search);
        // Because delete is considered as a reserved property in yuicompressor we enclose in quotes
        params['delete'](param);
        history.replaceState(null, '', '?' + params + location.hash);
    } else {
        // Solution for browsers which dont support URLSearchParams or replaceState; ex: IE
    }

}

function PromptUserViewName(clear) {
    var sv = null;
    if (typeof PAGE_LAYOUT_TYPE !== 'undefined' && PAGE_LAYOUT_TYPE === 'Q') {
        sv = document.getElementById("qx-saveview-dialog");
        if (!sv) {
            return false;
        }
        jq('#' + sv.id).modal();
    } else {
        sv = document.getElementById('saveoption');
        if (!sv) {
            return false;
        }
        var svs = sv.style;
        this.displayControl(svs);
    }

    var userviewNameObj = document.getElementById('.userviewname');
    userviewNameObj.value = '';
    if (!clear) {
        userviewNameObj.value = document.ff.elements['.userviewid'].value;
    }
    if (userviewNameObj) {
        userviewNameObj.focus();
    }

    return true;
}

function SaveUserViewData() {
    userviewNameObj = document.getElementById('.userviewname');
    //userviewObj = document.getElementById('userview');
    if (!userviewNameObj.value || userviewNameObj.value.trim() == '') {
        alert('Please enter a view name');
        userviewNameObj.value = '';
        userviewNameObj.focus();
        return false;
    }
    BlackoutBackground(false);
    saveviewObj = document.getElementById('saveoption');
    if (saveviewObj) {
        saveviewObj.style.visibility = 'hidden';
        saveviewObj.style.display = 'none';
    }

    if (document.ff.elements['.uvc'].value == 'saveas') {
        document.ff.elements['.isnewuv'].value = true;
        //document.ff.elements['.userviewid'].value = baseGetText(userviewObj.options[userviewObj.selectedIndex]);
        document.ff.elements['.newuvid'].value = userviewNameObj.value;
    } else {
        document.ff.elements['.userviewid'].value = userviewNameObj.value.encrypt();
    }
    document.ff.elements['.suv'].value = true;
    //alert(document.ff.elements['.userviewid'].value);return true;
    c('.rb', 0);
    //alert();
    return true;
}


function ChangeUserView(selectedControl) {
    document.ff.elements['.userviewid'].value = selectedControl.encrypt();
    clear_filters(true);
    document.ff.elements['.s'].value = '';
    c('.rb', 0);
    return true;
}

function GetCurrentUserView() {
    if (typeof document.ff.elements['.userviewid'] === 'undefined') {
        return null;
    }
    return document.ff.elements['.userviewid'].value.decrypt();
}

function ClearUserViewName() {
    userviewNameObj = document.getElementById('.userviewname');
    userviewNameObj.value = '';
    userviewNameObj.focus();
    return true;
}

function CloseUserViewDiv() {
    var sv = document.getElementById('saveoption');
    if (sv) {
        BlackoutBackground(false);
        HideScrollBar(false, sv);
        var svs = sv.style;
        return this.closeControl(svs);
    } else {
        return false;
    }
}

function SelectListControl(optType, selectedControl) {
    if ('.export' in document.ff.elements) {
        document.ff.elements['.export'].value = "";
    }
    strUVCtrl = removeSpaces(selectedControl);
    if (strUVCtrl && strUVCtrl != '') {
        document.ff.elements['.uvc'].value = strUVCtrl;

        // If selected menu is an userview then change the record lister.
        if (optType == 'Go') {
            ChangeUserView(selectedControl);
        }

        // For all other cases resolve the action on the current view.
        else if (strUVCtrl == 'saveas') {
            BlackoutBackground(true);

            var obj = document.getElementById('saveoption');
            HideScrollBar(true, obj);
            return PromptUserViewName(true);
        }
        else if (strUVCtrl == 'edit') {
            SetAdvancedFilters();
        }
        else if (strUVCtrl == 'new') {
            SetAdvancedFilters("create");
        }
        else if (strUVCtrl == 'clear') {
            return clear_filters();
        }
        else if (strUVCtrl == 'apply') {
            return PromptUserFilters();
        }
    }
    //DisableUVControls();
    return true;
}

function SelectGroups(selectedControl) {
    document.ff.elements['.groupid'].value = selectedControl;
    clear_filters(true);
    document.ff.elements['.s'].value = '';
    c('.rb', 0);
    return true;

}

function GetCurrentGroupView() {
    if (typeof document.ff.elements['.groupid'] === 'undefined') {
        return null;
    }
    return document.ff.elements['.groupid'].value;
}

function removeSpaces(string) {
    var tstring = "";
    string = '' + string;
    splitstring = string.split(" ");
    for (i = 0; i < splitstring.length; i++)
        tstring += splitstring[i];
    return tstring;
}

var filters = new Array();


function SetAdvancedFilters(opt) {
    removeQueryStringParam('.suv');
    removeQueryStringParam('.newuvid');
    if (opt == "create") {
        location.href =
            document.ff.ownerDocument.getElementById('.uvCURL').getAttribute('value') + "&.done=" + document.ff.ownerDocument.getElementById('.uvURL').getAttribute('value');
    } else {
        location.href =
            document.ff.ownerDocument.getElementById('.uvEURL').getAttribute('value') + "&.done=" + document.ff.ownerDocument.getElementById('.uvURL').getAttribute('value');
    }
    return true;
}


function EnableUVControls() {
    uvContObj = document.getElementById('uvcontrol');

    if (uvContObj && uvContObj.style.visibility == 'visible') {
        uvContObj.style.visibility = 'hidden';
        return true;
    }

    uvopt = document.getElementById('uvopt');

    uvContObj.style.top = '' + (this.findTBTopOffset(uvopt) + uvopt.clientHeight) + 'px';
    uvContObj.style.left = '' + this.findTBLeftOffset(uvopt) + 'px';

    uvContObj.style.visibility = 'visible';
    return true;
}

function HighlightMenu(obj) {
    obj.style.backgroundColor = '#FFFFFF';
    obj.style.borderTop = 'solid 1px #D2C4CB';
    obj.style.borderBottom = 'solid 1px #D2C4CB';
    obj.style.paddingBottom = '4px';
    obj.style.paddingTop = '4px';
    return true;
}

function DeselectMenu(obj) {
    obj.style.backgroundColor = '#F2EEF0';
    obj.style.borderTop = 'solid 0px #A0A0FF';
    obj.style.borderBottom = 'solid 0px #A0A0FF';
    obj.style.paddingBottom = '5px';
    obj.style.paddingTop = '5px';
    return true;
}

function displayControl(c) {
    c.top = '' + this.displayTopOffset() + 'px';
    c.left = '' + this.displayLeftOffset() + 'px';
    c.visibility = 'visible';
    c.display = 'block';
    return true;
}

function closeControl(c) {
    c.visibility = 'hidden';
    c.display = 'none';
    return true;
}

function displayLeftOffset() {
    uiwidget_offset = document.getElementById('uvopt');
    if (!uiwidget_offset) {
        uiwidget_offset = document.getElementById('clearAllFilters');
    }
    if (uiwidget_offset) {
        return this.findTBLeftOffset(uiwidget_offset);
    }
    return 0;
}

function findTBLeftOffset(elem) {
    var left = 0;
    while (elem.tagName != "BODY") {
        left += elem.offsetLeft;
        elem = elem.offsetParent;
    }
    return left;
}

function displayTopOffset() {
    uiwidget_offset = document.getElementById('uvopt');
    if (!uiwidget_offset) {
        uiwidget_offset = document.getElementById('clearAllFilters');
    }
    if (uiwidget_offset) {
        return this.findTBTopOffset(uiwidget_offset) + uiwidget_offset.clientHeight;
    }
    return 0;
}

function findTBTopOffset(elem) {
    var top = 0;
    while (elem.tagName != "BODY") {
        top += elem.offsetTop;
        elem = elem.offsetParent;
    }
    return top;
}

function DisableUVControls() {
    document.getElementById('uvcontrol').style.visibility = 'hidden';
    return true;
}

function validateThis(obj) {
    //alert(MayHavePoint(obj.value));
    if (!obj.value || obj.value == '') {
        return false;
    }
    if (obj.ctype == 'currency' || obj.ctype == 'decimal') {
        obj.value = MayHavePoint(obj.value);
        if (!obj.value || obj.value == false || obj.value == 'false') {
            obj.value = '';
            alert('Enter a valid currency');
            obj.focus();
            return false;
        }
    } else if (obj.ctype == 'number') {
        obj.value = IsN(obj.value);
        if (!obj.value || obj.value == false || obj.value == 'false') {
            obj.value = '';
            alert('Enter a valid number');
            obj.focus();
            return false;
        }
    } else if (obj.ctype == 'date') {
        //alert('validation for date');
    }
    return true;
}

function IsN(St) {
    if (!/\D/.test(St)) {
        return St;
    }
    return false;
}

function MayHavePoint(S) {
    finalCurr = S.replace('$', '');
    finalCurr = finalCurr.replace(/,/g, '');
    if (/^\d+(\.\d+)?$/.test(finalCurr)) {
        return finalCurr;
    }
    return false;
}

function Cash(Curr) {
    return /^\d{1,3}(,\d{3})*\.\d\d$|^\d+\.\d\d$/.test(finalCurr)
}

/** This functionality is disabled as of now. Can be enabled by uncommenting the toHTML.xsl file code
 along with this code.
 function GoToPage(pagenoID) {
	pagenoObj = document.getElementById(pagenoID);
	pageno = pagenoObj.value;
	totalpages = parseInt(document.getElementById('.totalpages').value);

	pageno = IsN(pageno);
	pageno = parseInt(pageno);
	//alert(pageno+".."+totalpages);
	if (!pageno || pageno == false || pageno == 'false' || pageno > totalpages || pageno < 1) {
		alert('Invalid page number entered');
		pagenoObj.value = '';
		pagenoObj.focus();
		return false;
	}

	rangesize=document.getElementById('.rs').value;

	pageVal = (parseInt(pageno)-1) * parseInt(rangesize);
	c('.rb',pageVal);
}
 **/

//document.onmousedown = DisableUVControls;

function constructStatFilters() {
    var sfObj = new Object();
    var sfFields = statFilterFlds.split("~~");
    var sfLabels = statFilterLabels.split("~~");
    for (var i = 0; i < sfFields.length; i++) {
        sfObj = new Object();
        sfObj.fldName = sfFields[i];
        sfObj.fldLabel = sfLabels[i];
        statFilters[i] = sfObj;
    }
    return true;
}

function PromptUserFilters() {
    var uf = document.getElementById('uf');
    if (uf) {
        var ufs = uf.style;
        this.displayControl(ufs);
        uf.focus();
        return true;
    } else {
        return false;
    }
}

function CloseUserFilters() {
    var uf = document.getElementById('uf');
    if (uf) {
        var ufs = uf.style;
        return this.closeControl(ufs);
    } else {
        return false;
    }
}

function ShowOptionsMenu(txt, menu, menuStyle) {
    txt = GT(txt);
    optionMenus.push(txt); // Add the menu name in the list of option menus
    with (milonic = new menuname(txt)) {
        style = menuStyle;
        alwaysvisible = 1;
        orientation = "horizontal";
        position = "relative";
        aI("text=" + txt + ";url=#Skip;showmenu=" + menu + ";openonclick=1");
    }
    drawMenus();
}

function ShowOptionsMenuButton(txt, menu, menuStyle) {
    txt = GT(txt);
    optionMenus.push(txt); // Add the menu name in the list of option menus
    with (milonic = new menuname(txt)) {
        style = menuStyle;
        alwaysvisible = 1;
        orientation = "horizontal";
        position = "relative";
        aI("text=" + txt + ";url=#Skip;showmenu=" + menu + ";openonclick=1");
    }
    drawMenus();
}

String.prototype.trim = function () {
    return this.replace(/^\s*/, "").replace(/\s*$/, "");
}

function ApplyFilters() {
    var fps = document.getElementsByName("FP");  // fieldpath
    var ops = document.getElementsByName("OP");  // operator
    var vals = document.getElementsByName("VA"); // value
    var fc = 0;
    CloseUserFilters();
    if (fps && fps.length) {
        var fields = new Array();
        var operators = new Array();
        var values = new Array();
        for (var i = 0; i < fps.length; i++) {
            if (fps[i].value != 'none' && ops[i].value != 'none') {
                if (ops[i].value == 'nu' || ops[i].value == 'nn') { // eq null || not eq null
                    fields[fc] = fps[i].value;
                    operators[fc] = ops[i].value;
                    fc++;
                } else if (vals[i].value && vals[i].value.trim() != '') {
                    fields[fc] = fps[i].value;
                    operators[fc] = ops[i].value;
                    values[fc] = vals[i].value;
                    values[fc] = values[fc].trim();
                    fc++;
                }
            }
        }
        document.getElementById(".FPaths").value = fields.join("~~");
        document.getElementById(".FOprs").value = operators.join("~~");
        document.getElementById(".FVals").value = values.join("~~");
    }

    /*	if (!document.getElementById(".FPaths").value || document.getElementById(".FPaths").value == '') {
            return false;
        }*/
    c('.rb', 0);
    return true;
}

function AddFilters() {

    var doc = document;

    var tbl = doc.getElementById("ft");
    var tbdy = tbl.tBodies[0];

    // New Row
    var new_tr = doc.createElement('tr');

    // Fields
    var new_td_fields = doc.createElement('td');
    var columns = tbdy.rows[2].cells[0].childNodes[0];
    var new_columns = '<select id="FP">';
    var new_colopts = "";
    for (i = 0; i < columns.length; i++) {
        new_colopts += "<option value='" + columns[i].value + "'>" + baseGetText(columns[i]) + "</option>";
    }
    new_td_fields.innerHTML = new_columns + new_colopts + "</select>";

    // Operators
    var new_td_operators = doc.createElement('td');
    var operators = tbdy.rows[2].cells[1].childNodes[0];
    var new_operators = '<select id="OP">';
    var new_opropts = "";
    var selected = "";
    for (i = 0; i < operators.length; i++) {
        selected = "";
        if (operators[i].value == "sw") {
            selected = "selected";
        }
        new_opropts += "<option value='" + operators[i].value + "' " + selected + " >" + baseGetText(operators[i]) + "</option>";
    }
    new_td_operators.innerHTML = new_operators + new_opropts + "</select>";

    // Text field
    var new_td_text = doc.createElement('td');
    var textvalue = tbdy.rows[2].cells[2].childNodes[0];
    new_td_text.innerHTML = "<input type='text' id='VA' value=''></input>";

    new_tr.appendChild(new_td_fields);
    new_tr.appendChild(new_td_operators);
    new_tr.appendChild(new_td_text);

    //Move the filters down the line.
    var linkRow = tbdy.rows[tbdy.rows.length - 1];
    tbdy.appendChild(new_tr);
    tbdy.appendChild(linkRow);

}

function removeFilter() {
    var tbl = document.getElementById("ft");
    var tbdy = tbl.tBodies[0];
    if (tbdy.rows.length > 4) {
        tbdy.deleteRow(tbdy.rows.length - 2);
    }
    return true;
}

function ExportData(exportval) {
    c('.rb', 0, '', exportval);
    hideQXLoader();
    return true;
}

function TrimURL() {
    var filters = document.getElementsByName('filter');
    for (var i = 0; i < filters.length; i++) {
        if (filters[i] && (!filters[i].value || filters[i].value.length == 0)) {
            filters[i].removeAttribute("name");
        }
    }
    return true;
}

function displayControlForPayments(c, rec, appType) {
    c.top = '' + this.displayOffsetForPayments(rec, appType, "top") + 'px';
    c.left = '' + this.displayOffsetForPayments(rec, appType, "left") + 'px';
    c.visibility = 'visible';
    c.display = 'block';
    return true;
}

function displayOffsetForPayments(rec, appType, position) {
    uiwidget_offset = document.getElementById(appType + "_" + rec);
    if (uiwidget_offset) {
        var offestValue = this.findTBLeftOffset(uiwidget_offset);
        if (position == "top") {
            offestValue = this.findTBTopOffset(uiwidget_offset) + uiwidget_offset.clientHeight;
        }

        return offestValue;
    }
    return 0;
}

jq(document).ready(function () {
    // Post new event for chrome.
    window.setTimeout(function () {
        jq('#groups .mmenucontainer').css('z-index', 0);
        jq('.exportMenuButton .mmenucontainer td a').attr('style', '').addClass('Task');
        var xpdiv = jq('.exportMenuButton .mmenucontainer').get(0);
        if (xpdiv) {
            xpdiv.onmouseover = function () {
            };
        }
        var xptd = jq('.exportMenuButton .mmenucontainer .milonictable td').get(0);
        if (xptd) {
            xptd.onmouseover = function () {
            };
        }

        jq('td.select-options .mmenucontainer td a').attr('style', '').addClass('select-options');
        jq('td.select-view .mmenucontainer td a').attr('style', '').addClass('select-view');
        jq('td.manage-view .mmenucontainer td a').attr('style', '').addClass('manage-view');
    }, 0);
});

function BlackoutBackground(dis) {
    var delObj = document.getElementById('saveoption');
    var blackoutdivObj = document.getElementById('page_screen0');
    if (!blackoutdivObj) {
        return false;
    }
    if (dis) {
        blackoutdivObj.style.height = screen.height + 'px';
        blackoutdivObj.style.width = screen.width + 'px';
        blackoutdivObj.style.display = 'block';
    } else {
        blackoutdivObj.style.display = 'none';
    }
}

function HideScrollBar(show, obj) {
    if (show) {
        var myWidth;
        var myHeight;
        var scrollTop = 0;
        var scrollLeft = 0;

        myWidth = jq(window).width();
        myHeight = jq(window).height();

        if (document.documentElement) {
            scrollTop = parseInt(Math.max(parseInt(document.documentElement.scrollTop), parseInt(document.body.scrollTop)));
            scrollLeft = parseInt(Math.max(parseInt(document.documentElement.scrollLeft), parseInt(document.body.scrollLeft)));
        } else {
            scrollTop = parseInt(document.body.scrollTop);
            scrollLeft = parseInt(document.body.scrollLeft);
        }

        obj.style.top = myHeight / 2 - ((myHeight / 2 > 150) ? 150 : 0) + scrollTop + 'px';
        obj.style.left = myWidth / 2 - ((myWidth / 2 > 100) ? 100 : 0) + scrollLeft + 'px';
        obj.style.display = 'block';
        document.getElementsByTagName('BODY')[0].style.overflow = 'hidden';

    } else {
        obj.style.display = 'none';
        var overflow;
        if (globalIs.ie) {
            overflow = 'auto';
        } else {
            overflow = 'visible';
        }
        document.getElementsByTagName('BODY')[0].style.overflow = overflow;
    }
}

function showQXLoader(msg) {
    if (typeof PAGE_LAYOUT_TYPE != 'undefined' && PAGE_LAYOUT_TYPE === 'Q') {
        QXUtil.showLoading(1, msg);
    } else {
        DisplayLoadingPanel(msg);
    }
}

function hideQXLoader() {
    if (typeof PAGE_LAYOUT_TYPE != 'undefined' && PAGE_LAYOUT_TYPE === 'Q') {
        QXUtil.hideLoading();
    } else {
        if (YAHOO.loadingPanel && YAHOO.loadingPanel.panel) {
            YAHOO.loadingPanel.panel.hide();
        }
    }
}

var windowObjectReference = null; // global variable
var previousUrl = null; // global variable which will store the curently url in the secondary window

function getDuplicateWindow(url) {
    var retVal = null;
    if (previousUrl === url && windowObjectReference !== null) {
        retVal = windowObjectReference;
    }
    previousUrl = url;

    return retVal;
}

/**
 * Use a QXDialog or popup window as the target for a form submit.
 *
 * @param form
 */
function submitFormToIFrame(form, width, height)
{
    var target = '';
    if (typeof PAGE_LAYOUT_TYPE != 'undefined' && PAGE_LAYOUT_TYPE === 'Q' && typeof showQxDialog === 'function') {
        var iFrame = getIFrameForQXDialog();
        target = iFrame.name;
        showQxDialog('', iFrame, '', 'form', 'iframe');
    } else {
        if (!width) {
            width = 800;
        }
        if (!height) {
            height = 600;
        }
        var winParam = 'width=' + width + ',height=' + height + ',toolbar=no,location=no,directories=no,status=yes,menubar=no,scrollbars=auto,resizable=yes,dependent,left=100,top=100';
        var popup = window.open('', 'iabulkdialog' , winParam);
        if (popup == null) {
            return;
        }
        if (popup.opener == null) {
            popup.opener = self;
        }
        var iFrame = document.createElement('iframe');
        target = iFrame.name = iFrame.id = 'iabulkdialog'
        iFrame.style.border = 'none';
        iFrame.style.width = iFrame.style.height = '100%';
        popup.document.body.appendChild(iFrame);
        popup.focus();
    }
    form.setAttribute('target', target);
    document.body.appendChild(form);
    form.submit();
}

/**
 * Default Bulk action action handler. Shows an error if no rows have been selected. winParam are used
 * for 2012 UI popup window.
 *
 * @param url
 * @param keyname
 * @param postArgs
 * @param msg
 * @param winParam
 * @returns {boolean}
 */
function postBulkAction(url, keyname, msg, postArgs, width, height)
{
    var keys = [];
    var count = 0;
    var f = document.ff;
    var len = f.elements.length;
    for (var i = 0; i < len; i++) {
      if (f.elements[i].checked && f.elements[i].name == '.checks[]') {
        keys.push(f.elements[i].value);
        count++;
      }
    }

    if (1 > count) {
        alert(GT('IA.SELECT_AT_LEAST_ONE_RECORD'));
        return false;
    }
    if (msg) {
        token = {
            'id': msg,
            'placeHolders': [
                {'name': 'COUNT', 'value': count}
            ]
        }
        msg = GT(token);
    }
    var answer = !msg || confirm(msg);
    if (answer) {
        LaunchLoadingPanel(GT("IA.PLEASE_WAIT"));

        var form = document.createElement('form');
        form.setAttribute('method', 'post');
        form.setAttribute('action', url);

        // Additional post args (CSRF token)
        for (var key in postArgs) {
            var hiddenField = document.createElement("input");
            hiddenField.setAttribute("type", "hidden");
            hiddenField.setAttribute("name", key);
            hiddenField.setAttribute("value", postArgs[key]);
            form.appendChild(hiddenField);
        }

        if (keys.length > 0) {
            var hiddenField = document.createElement("input");
            hiddenField.setAttribute("type", "hidden");
            hiddenField.setAttribute("name", "selectedRecBulk");
            hiddenField.setAttribute("value", JSON.stringify(keys));
            form.appendChild(hiddenField);
        }
        submitFormToIFrame(form, width, height);
    }
    return answer;
}

/**
 * Save most recently used entity when slide into entity
 *
 * @param {string} id - entity location id
 */
function QXSaveMRUEntity(id, isTopLevel) {
    var successCallback = function(response) {
        if(response && response.status === 'error' && response.errors && response.errors[0] && response.errors[0].errno === 'XL03000011') {
            //Force the display of the maintenance page
            if(window.top) window.top.location.reload();
        } else {
            if(window.top && window.top.qxentitypicker) window.top.qxentitypicker.refresh();
        }
    };
    if (window.top && window.top.QXUtil) {
        var qxData = {
            'id': id,
            'topLevel': isTopLevel ? '0' : null,  // DE18732: if we are on top level send 0, the id of top level, otherwise null to not have unwanted entries in the db
        };
        
        window.top.QXUtil.post('save_mru_entity', {'.qxdata': {'data': qxData }}, successCallback);
    }
}

function addSelected() {
    var form = document.ff;
    var len = form.elements.length;
    var allchecks = form.elements['.allchecks'] ? form.elements['.allchecks'].value : "";
    var keys = allchecks ? allchecks.split("#~#") : [];
    for (var i = 0; i < len; i++) {
        var element = form.elements[i];
        if (element.checked && element.name === '.checks[]') {
            var value = element.value;
            if (keys.indexOf(value) === -1) {
                keys.push(value);
            }
        }
    }

    if (1 > keys.length) {
        alert(GT('IA.SELECT_AT_LEAST_ONE_RECORD'));
        return false;
    }

    var selectedItems = keys.join('#~#');
    window.parent.qxMultiPickList.selectedItems = selectedItems;
    window.parent.qxMultiPickList.refresh();

    closeQxWindow();
}

    /**
     * Default Bulk action action handler. Shows an error if no rows have been selected. winParam are used
     * for 2012 UI popup window.
     *
     * @param url
     * @param keyname
     * @param postArgs
     * @param msg
     * @param action
     * @returns {boolean}
     */
    function postBulkActionFulFillment(url, keyname, msg, postArgs, action)
    {
        var keys = [];
        var count = 0;
        var f = document.ff;
        var len = f.elements.length;
        for (var i = 0; i < len; i++) {
            if (f.elements[i].checked && f.elements[i].name == '.checks[]') {
                keys.push(f.elements[i].value);
                count++;
            }
        }

        if (1 > count) {
            alert(GT('IA.SELECT_AT_LEAST_ONE_RECORD'));
            return false;
        }
        if (msg) {
            token = {
                'id': msg,
                'placeHolders': [
                    {'name': 'COUNT', 'value': count}
                ]
            }
            msg = GT(token);
        }
        var answer = !msg || confirm(msg);
        if (answer) {
            LaunchLoadingPanel('Please wait');

            var form = document.createElement('form');
            form.setAttribute('method', 'post');
            form.setAttribute('action', url);

            // Additional post args (CSRF token)
            for (var key in postArgs) {
                var hiddenField = document.createElement("input");
                hiddenField.setAttribute("type", "hidden");
                hiddenField.setAttribute("name", key);
                hiddenField.setAttribute("value", postArgs[key]);
                form.appendChild(hiddenField);
            }

            if (keys.length > 0) {
                var hiddenField = document.createElement("input");
                hiddenField.setAttribute("type", "hidden");
                hiddenField.setAttribute("name", "selectedRecBulk");
                hiddenField.setAttribute("value", JSON.stringify(keys));
                form.appendChild(hiddenField);
            }

            document.body.appendChild(form);
            form.submit();
        }
        return answer;
    }

/**
 * Common function that will handle all the custom flag actions for bulk operations.
 * @param string url
 * @param string keyname
 * @param array postArgs
 * @param string confMsg
 * @param string waitMsg
 * @param string alertStr
 * @param boolean submitToIFrame
 * @returns {boolean}
 */
function submitBulkAction(url, keyname, postArgs, confMsg, waitMsg, alertStr, submitToIFrame) {

    var keys = [];
    var count = 0;
    var f = document.ff;
    var len = f.elements.length;
    for (var i = 0; i < len; i++) {
        if (f.elements[i].checked && f.elements[i].name == '.checks[]') {
            keys['_obj__' + keyname + "--" + i] = f.elements[i].value;
            count++;
        }
    }

    if (count < 1) {
        if (!alertStr) {
            alertStr = 'IA.SELECT_AT_LEAST_ONE_RECORD';
        }
        alert(GT(alertStr));
        return false;
    }

    if (confMsg) {
        var token = {
            'id': confMsg,
            'placeHolders': [
                {'name': 'COUNT', 'value': count}
            ]
        }
        confMsg = GT(token);
    }

    var answer = !confMsg || confirm(confMsg);
    if (answer) {
        if(!waitMsg) {
            waitMsg = GT("IA.PLEASE_WAIT");
        }
        LaunchLoadingPanel(waitMsg); // Show the loading panel
        var form = document.createElement("form");
        form.setAttribute("method", 'post');
        form.setAttribute("action", url);

        // Additional post args (CSRF token)
        for (var key in postArgs) {
            var hiddenField = document.createElement("input");
            hiddenField.setAttribute("type", "hidden");
            hiddenField.setAttribute("name", key);
            hiddenField.setAttribute("value", postArgs[key]);
            form.appendChild(hiddenField);
        }

        for (var key in keys) {
            var hiddenField = document.createElement("input");
            hiddenField.setAttribute("type", "hidden");
            hiddenField.setAttribute("name", key);
            hiddenField.setAttribute("value", keys[key]);

            form.appendChild(hiddenField);
        }

        document.body.appendChild(form);

        if (submitToIFrame) {
            // open the Iframe and then submit the form
            submitFormToIFrame(form);
        } else {
            // directly submit the form
            form.submit();
        }
    }
    return answer;
}
