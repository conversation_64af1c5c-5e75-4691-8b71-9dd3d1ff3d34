
function setLink() {
    let linkObj = window.editor.view.findComponents('EXTERNALSUBMISSIONIDLINK', 'Field');
    if(linkObj && linkObj[0] && linkObj[0].getValue()) {
        linkObj[0].updateProperty('hreftxt', linkObj[0].getValue());
    }
}

Grid.prototype.clearGrid = function () {
    this.gatherData();
    this.value = [];
    this.currentChildren = [];
    this.redraw();
};


function resubmit(grid, rowData) {
    window.view.clearMessages();
    if(rowData && rowData['RECORDNO']) {
        window.editor.showLoadingBar();
        var args = {
            'recordNo': rowData['RECORDNO'],
        };
        window.editor.ajax(false, 'resubmit', args, c_resubmit);
    }
}

function c_resubmit(values) {
    window.view.clearMessages();
    window.editor.hideLoadingBar();
    if(values && values['NAVIGATE_TO_URL']) {
        c_loadPartnerPageURL(values, 'resubmit');
    }
    else if(values && values['SUCCESS']) {
        window.view.addMessage(MESSAGE_INFO, GT('IA.RESUBMISSION_INITIATED'));
        submissionlogGridHandler.refreshSubmissionLog();
    } else {
        window.view.addMessage(MESSAGE_ERROR, GT('IA.WAIT_AND_SUBMIT_BATCH'));
    }
}

jq(document).ready(function () {
    setLink();
});
