<?php
global $gRecordNoFormat, $gBooleanType, $gStatusFieldInfo;
$kSchemas['customfield'] = [
    'children'             => [
        'customcomponent' => [
            'fkey'     => 'record#', 'invfkey' => 'customfieldkey',
            'table'    => 'customcomponent', 'join' => 'outer',
            'children' => [
                'customlayoutcomponent' => [
                    'fkey'  => 'record#',
                    'table' => 'customlayoutcomponent',
                    'join'  => 'outer'
                ],
            ],
        ],
        'package'         => [ 'fkey'  => 'packagekey', 'invfkey' => 'record#', 'join' => 'outer',
                               'table' => 'package',
        ],
    ],
    'ownedobjects'         => [
        // ww: Fake owned object. Gives me CUSTOMLABELS linked to CUSTOMLABELID
        // Since customfield turns 'set' into 'add' for customcomponent, it's easier
        // to set owned objects from customfield directly. See CustomFieldManager::regularSet.
        CustomLabelManager::getOwnedObjectInfo(),
    ],
    'object'               => [
        'RECORDNO',
        'CUSTOMFIELDID',
        'TYPE',
        'OWNEROBJECT',
        'DOCTYPE',
        'OBJECTFIELDID',
        'CUSTOMCOMPONENT.CUSTOMLAYOUTCOMPONENT.PAGE',
        'CUSTOMCOMPONENT.CUSTOMLAYOUTCOMPONENT.FIELDSET',
        'CUSTOMCOMPONENT.LABEL',
        'DATADESCRIPTION',
        'REQUIRED',
        'CUSTOMCOMPONENT.HIDDEN',
        'DESCRIPTION',
        'PACKAGEKEY',
        'PACKAGE.NAME',
        'CUSTOMLABELID',
        'STATUS',
    ],
    'schema'               => [
        'RECORDNO'        => 'record#',
        'CUSTOMFIELDID'   => 'customfieldid',
        'TYPE'            => 'type',
        'OWNEROBJECT'     => 'ownerobject',
        'DOCTYPE'         => 'doctype',
        'OBJECTFIELDID'   => 'objectfieldid',
        'DATADESCRIPTION' => 'data',
        'REQUIRED'        => 'required',
        'DESCRIPTION'     => 'description',
        'PACKAGEKEY'      => 'packagekey',
        'PACKAGENAME'     => 'package.name',
        'STATUS'          => 'active',
        'CUSTOMCOMPONENT' => [
            'customcomponent.*'     => 'customcomponent.*',
            'CUSTOMLAYOUTCOMPONENT' => [
                'customlayoutcomponent.*' => 'customlayoutcomponent.*',
            ],
        ],
        'NAMESPACE'       => 'namespace',
        'NAMESPACELABEL'  => 'namespacelabel',
        'CUSTOMLABELID'   => 'customcomponent.customlabelid',
        'WHENCREATED'     => 'whencreated',
        'WHENMODIFIED'    => 'whenmodified',
        'CREATEDBY'       => 'createdby',
        'MODIFIEDBY'      => 'modifiedby',
    ],
    'fieldinfo'            => [
        [
            'path'     => 'RECORDNO',
            'desc'     => 'IA.RECORD_NUMBER',
            'fullname' => 'IA.RECORD_NUMBER',
            'hidden'   => true,
            'type'     => [
                'ptype'     => 'sequence',
                'type'      => 'integer',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
        ],
        [
            'path'     => 'PACKAGEKEY',
            'desc'     => 'IA.PACKAGE_RECORD_NUMBER',
            'fullname' => 'IA.PACKAGE_RECORD_NUMBER',
            'hidden'   => true,
            'type'     => [
                'ptype'     => 'sequence',
                'type'      => 'integer',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
        ],
        [
            'path'     => 'PACKAGENAME',
            'desc'     => 'IA.PACKAGE_NAME',
            'fullname' => 'IA.PACKAGE_NAME',
            'hidden'   => true,
            'type'     => [
                'type'      => 'text',
                'maxlength' => 40,
            ],
        ],
        [
            'fullname' => 'IA.HIDDEN',
            'type'     => $gBooleanType,
            'default'  => 'false',
            'desc'     => 'IA.HIDDEN',
            'path'     => 'CUSTOMCOMPONENT.HIDDEN',
        ],
        [
            'fullname' => 'IA.REQUIRED',
            'type'     => $gBooleanType,
            'default'  => 'false',
            'desc'     => 'IA.REQUIRED',
            'path'     => 'REQUIRED',
        ],
        [
            'fullname' => 'IA.PAGE',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.PAGE',
            'path'     => 'CUSTOMCOMPONENT.CUSTOMLAYOUTCOMPONENT.PAGE',
        ],
        [
            'fullname' => 'IA.FIELD_SET',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.FIELD_SET',
            'path'     => 'CUSTOMCOMPONENT.CUSTOMLAYOUTCOMPONENT.FIELDSET',
        ],
        [
            'fullname' => 'IA.FIELD_SET',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.FIELD_SET',
            'path'     => 'FIELDSET',
        ],
        [
            'fullname' => 'IA.FIELD_ID',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^[a-zA-Z][_a-zA-Z0-9:]{0,79}$/',
            ],
            'required' => true,
            'desc'     => 'IA.FIELD_ID',
            'path'     => 'CUSTOMFIELDID',
        ],
        [
            'fullname' => 'IA.LABEL',
            'type'     => [
                'ptype'     => 'customlabel',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'required' => true,
            'desc'     => 'IA.LABEL',
            'path'     => 'CUSTOMCOMPONENT.LABEL',
        ],
        [
            'fullname'        => 'IA.DATA_TYPE',
            'type'            => [
                'ptype'       => 'enum',
                'type'        => 'enum',
                'validlabels' => [ 'IA.CHECK_BOX', 'IA.CURRENCY', 'IA.DATE', 'IA.EMAIL', 'IA.NUMBER', 'IA.PERCENT',
                                   'IA.PICKLIST', 'IA.PICKLIST_MULTI_SELECT', 'IA.SEQUENCE', 'IA.TEXT', 'IA.TEXT_AREA',
                                   'IA.URL', 'IA.PASSWORD', ],
                'validvalues' => [
                    'CHECKBOX',
                    'CURRENCY',
                    'DATE',
                    'EMAIL',
                    'NUMBER',
                    'PERCENT',
                    'PICKLIST',
                    'PICKLISTMULTI',
                    'SEQUENCE',
                    'TEXT',
                    'TEXTAREA',
                    'URL',
                    'PASSWORD',
                ],
            ],
            'required'        => true,
            'desc'            => 'IA.TYPE',
            'path'            => 'TYPE',
            'showlabelalways' => true,
        ],

        [
            'fullname'        => 'IA.OBJECT',
            'type'            => [
                'ptype'       => 'enum',
                'type'        => 'enum',
                'validlabels' => [
                    //                         'Build/Disassemble Kits Transaction Detail',
                    // 						'Build/Disassemble Kits Transaction',
                    // 						'Company',
                ],
                'validvalues' => [
                    // 						'stkitdocumententry',
                    // 		                'stkitdocument',
                    // 						'companypref',
                ],
            ],
            'required'        => true,
            'desc'            => 'IA.OWNER_OBJECT',
            'path'            => 'OWNEROBJECT',
            'onchange'        => 'handleOwnerObject(this);',
            'showlabelalways' => true,
        ],

        [
            'fullname' => 'IA.DOCUMENT_TYPE',
            'type'     => [
                'ptype'       => 'multipick',
                'type'        => 'multipick',
                'validlabels' => [],
                'validvalues' => [],
                'delimiter'   => '#~#',
            ],

            'desc' => 'IA.DOCUMENT_TYPE',
            'path' => 'DOCTYPE',
        ],

        [
            'fullname'        => 'IA.REPLICATE_CHANGES_ON_ALL_RELATED_OBJECTS',
            'type'            => [
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => [ 'true', 'false' ],
                'validlabels' => [ 'IA.TRUE', 'IA.FALSE' ],
            ],
            'value'           => 'false',
            'desc'            => 'IA.REPLICATE_CHANGES_ON_ALL_RELATED_OBJECTS',
            'path'            => 'FIELDREPLICATE',
            'showlabelalways' => true,
        ],

        [
            'fullname'        => 'IA.OBJECT_FIELD_ID',
            'required'        => true,
            'type'            => [
                'ptype'       => 'enum',
                'type'        => 'enum',
                'validlabels' => [ 'IA.CUSTOM_FIELD_1', 'IA.CUSTOM_FIELD_2', 'IA.CUSTOM_FIELD_3', 'IA.CUSTOM_FIELD_4',
                                   'IA.CUSTOM_FIELD_5', 'IA.CUSTOM_FIELD_6', 'IA.CUSTOM_FIELD_7', 'IA.CUSTOM_FIELD_8',
                                   'IA.CUSTOM_FIELD_9', 'IA.CUSTOM_FIELD_10', 'IA.CUSTOM_FIELD_11',
                                   'IA.CUSTOM_FIELD_12', 'IA.CUSTOM_FIELD_13', 'IA.CUSTOM_FIELD_14',
                                   'IA.CUSTOM_FIELD_15', 'IA.CUSTOM_FIELD_16', 'IA.CUSTOM_FIELD_17',
                                   'IA.CUSTOM_FIELD_18', 'IA.CUSTOM_FIELD_19', 'IA.CUSTOM_FIELD_20',
                                   'IA.CUSTOM_FIELD_21', 'IA.CUSTOM_FIELD_22', 'IA.CUSTOM_FIELD_23',
                                   'IA.CUSTOM_FIELD_24', 'IA.CUSTOM_FIELD_25', 'IA.CUSTOM_FIELD_26',
                                   'IA.CUSTOM_FIELD_27', 'IA.CUSTOM_FIELD_28', 'IA.CUSTOM_FIELD_29',
                                   'IA.CUSTOM_FIELD_30', 'IA.CUSTOM_FIELD_31', 'IA.CUSTOM_FIELD_32',
                                   'IA.CUSTOM_FIELD_33', 'IA.CUSTOM_FIELD_34', 'IA.CUSTOM_FIELD_35',
                                   'IA.CUSTOM_FIELD_36', 'IA.CUSTOM_FIELD_37', 'IA.CUSTOM_FIELD_38',
                                   'IA.CUSTOM_FIELD_39', 'IA.CUSTOM_FIELD_40', 'IA.CUSTOM_FIELD_41',
                                   'IA.CUSTOM_FIELD_42', 'IA.CUSTOM_FIELD_43', 'IA.CUSTOM_FIELD_44',
                                   'IA.CUSTOM_FIELD_45', 'IA.CUSTOM_FIELD_46', 'IA.CUSTOM_FIELD_47',
                                   'IA.CUSTOM_FIELD_48', 'IA.CUSTOM_FIELD_49', 'IA.CUSTOM_FIELD_50',
                                   'IA.CUSTOM_FIELD_51', 'IA.CUSTOM_FIELD_52', 'IA.CUSTOM_FIELD_53',
                                   'IA.CUSTOM_FIELD_54', 'IA.CUSTOM_FIELD_55', 'IA.CUSTOM_FIELD_56',
                                   'IA.CUSTOM_FIELD_57', 'IA.CUSTOM_FIELD_58', 'IA.CUSTOM_FIELD_59',
                                   'IA.CUSTOM_FIELD_60', 'IA.CUSTOM_FIELD_61', 'IA.CUSTOM_FIELD_62',
                                   'IA.CUSTOM_FIELD_63', 'IA.CUSTOM_FIELD_64', 'IA.CUSTOM_FIELD_65',
                                   'IA.CUSTOM_FIELD_66', 'IA.CUSTOM_FIELD_67', 'IA.CUSTOM_FIELD_68',
                                   'IA.CUSTOM_FIELD_69', 'IA.CUSTOM_FIELD_70', 'IA.CUSTOM_FIELD_71',
                                   'IA.CUSTOM_FIELD_72', 'IA.CUSTOM_FIELD_73', 'IA.CUSTOM_FIELD_74',
                                   'IA.CUSTOM_FIELD_75', 'IA.CUSTOM_FIELD_76', 'IA.CUSTOM_FIELD_77',
                                   'IA.CUSTOM_FIELD_78', 'IA.CUSTOM_FIELD_79', 'IA.CUSTOM_FIELD_80',
                                   'IA.CUSTOM_FIELD_81', 'IA.CUSTOM_FIELD_82', 'IA.CUSTOM_FIELD_83',
                                   'IA.CUSTOM_FIELD_84', 'IA.CUSTOM_FIELD_85', 'IA.CUSTOM_FIELD_86',
                                   'IA.CUSTOM_FIELD_87', 'IA.CUSTOM_FIELD_88', 'IA.CUSTOM_FIELD_89',
                                   'IA.CUSTOM_FIELD_90', 'IA.CUSTOM_FIELD_91', 'IA.CUSTOM_FIELD_92',
                                   'IA.CUSTOM_FIELD_93', 'IA.CUSTOM_FIELD_94', 'IA.CUSTOM_FIELD_95',
                                   'IA.CUSTOM_FIELD_96', 'IA.CUSTOM_FIELD_97', 'IA.CUSTOM_FIELD_98',
                                   'IA.CUSTOM_FIELD_99', 'IA.CUSTOM_FIELD_100', ],
                'validvalues' => [
                    'custfield1',
                    'custfield2',
                    'custfield3',
                    'custfield4',
                    'custfield5',
                    'custfield6',
                    'custfield7',
                    'custfield8',
                    'custfield9',
                    'custfield10',
                    'custfield11',
                    'custfield12',
                    'custfield13',
                    'custfield14',
                    'custfield15',
                    'custfield16',
                    'custfield17',
                    'custfield18',
                    'custfield19',
                    'custfield20',
                    'custfield21',
                    'custfield22',
                    'custfield23',
                    'custfield24',
                    'custfield25',
                    'custfield26',
                    'custfield27',
                    'custfield28',
                    'custfield29',
                    'custfield30',
                    'custfield31',
                    'custfield32',
                    'custfield33',
                    'custfield34',
                    'custfield35',
                    'custfield36',
                    'custfield37',
                    'custfield38',
                    'custfield39',
                    'custfield40',
                    'custfield41',
                    'custfield42',
                    'custfield43',
                    'custfield44',
                    'custfield45',
                    'custfield46',
                    'custfield47',
                    'custfield48',
                    'custfield49',
                    'custfield50',
                    'custfield51',
                    'custfield52',
                    'custfield53',
                    'custfield54',
                    'custfield55',
                    'custfield56',
                    'custfield57',
                    'custfield58',
                    'custfield59',
                    'custfield60',
                    'custfield61',
                    'custfield62',
                    'custfield63',
                    'custfield64',
                    'custfield65',
                    'custfield66',
                    'custfield67',
                    'custfield68',
                    'custfield69',
                    'custfield70',
                    'custfield71',
                    'custfield72',
                    'custfield73',
                    'custfield74',
                    'custfield75',
                    'custfield76',
                    'custfield77',
                    'custfield78',
                    'custfield79',
                    'custfield80',
                    'custfield81',
                    'custfield82',
                    'custfield83',
                    'custfield84',
                    'custfield85',
                    'custfield86',
                    'custfield87',
                    'custfield88',
                    'custfield89',
                    'custfield90',
                    'custfield91',
                    'custfield92',
                    'custfield93',
                    'custfield94',
                    'custfield95',
                    'custfield96',
                    'custfield97',
                    'custfield98',
                    'custfield99',
                    'custfield100',
                ],
            ],
            'desc'            => 'IA.OBJECT_FIELD_ID',
            'path'            => 'OBJECTFIELDID',
            'showlabelalways' => true,
        ],
        [
            'fullname' => 'IA.FIELD_ID',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.CUSTOM_FIELD_ID',
            'path'     => 'CHECKBOXCUSTOMFIELDID',
        ],
        [
            'fullname' => 'IA.FIELD_ID',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.CUSTOM_FIELD_ID',
            'path'     => 'CURRENCYCUSTOMFIELDID',
        ],
        [
            'fullname' => 'IA.FIELD_ID',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.CUSTOM_FIELD_ID',
            'path'     => 'DATECUSTOMFIELDID',
        ],
        [
            'fullname' => 'IA.FIELD_ID',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.CUSTOM_FIELD_ID',
            'path'     => 'EMAILCUSTOMFIELDID',
        ],
        [
            'fullname' => 'IA.FIELD_ID',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.CUSTOM_FIELD_ID',
            'path'     => 'NUMBERCUSTOMFIELDID',
        ],
        [
            'fullname' => 'IA.FIELD_ID',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.CUSTOM_FIELD_ID',
            'path'     => 'PERCENTCUSTOMFIELDID',
        ],
        [
            'fullname' => 'IA.FIELD_ID',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.CUSTOM_FIELD_ID',
            'path'     => 'PICKLISTCUSTOMFIELDID',
        ],
        [
            'fullname' => 'IA.FIELD_ID',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.CUSTOM_FIELD_ID',
            'path'     => 'PICKLISTMULTICUSTOMFIELDID',
        ],
        [
            'fullname' => 'IA.FIELD_ID',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.CUSTOM_FIELD_ID',
            'path'     => 'SEQUENCECUSTOMFIELDID',
        ],
        [
            'fullname' => 'IA.FIELD_ID',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.CUSTOM_FIELD_ID',
            'path'     => 'TEXTCUSTOMFIELDID',
        ],
        [
            'fullname' => 'IA.FIELD_ID',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.CUSTOM_FIELD_ID',
            'path'     => 'TEXTAREACUSTOMFIELDID',
        ],
        [
            'fullname' => 'IA.FIELD_ID',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.CUSTOM_FIELD_ID',
            'path'     => 'PASSWORDCUSTOMFIELDID',
        ],
        [
            'fullname' => 'IA.FIELD_ID',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.CUSTOM_FIELD_ID',
            'path'     => 'URLCUSTOMFIELDID',
        ],

        [
            'fullname' => 'IA.LABEL',
            'type'     => [
                'ptype'     => 'customlabel',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.LABEL',
            'onblur'   => "setFieldID(this, '_obj__CHECKBOXCUSTOMFIELDID');updateCustomLabel(this);",
            'path'     => 'CHECKBOXLABEL',
            'assist' => 'edit',
            'context' => CustomFieldEditor::getEditCustomLabelDialogFunction(),
            'script' => 'editor.phtml',
            'function' => 'editCustomLabels',
        ],
        [
            'fullname' => 'IA.LABEL',
            'type'     => [
                'ptype'     => 'customlabel',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.LABEL',
            'onblur'   => "setFieldID(this, '_obj__CURRENCYCUSTOMFIELDID');updateCustomLabel(this);",
            'path'     => 'CURRENCYLABEL',
            'assist' => 'edit',
            'context' => CustomFieldEditor::getEditCustomLabelDialogFunction(),
            'script' => 'editor.phtml',
            'function' => 'editCustomLabels',
        ],
        [
            'fullname' => 'IA.LABEL',
            'type'     => [
                'ptype'     => 'customlabel',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.LABEL',
            'onblur'   => "setFieldID(this, '_obj__DATECUSTOMFIELDID');updateCustomLabel(this);",
            'path'     => 'DATELABEL',
            'assist' => 'edit',
            'context' => CustomFieldEditor::getEditCustomLabelDialogFunction(),
            'script' => 'editor.phtml',
            'function' => 'editCustomLabels',
        ],
        [
            'fullname' => 'IA.LABEL',
            'type'     => [
                'ptype'     => 'customlabel',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.LABEL',
            'onblur'   => "setFieldID(this, '_obj__EMAILCUSTOMFIELDID');updateCustomLabel(this);",
            'path'     => 'EMAILLABEL',
            'assist' => 'edit',
            'context' => CustomFieldEditor::getEditCustomLabelDialogFunction(),
            'script' => 'editor.phtml',
            'function' => 'editCustomLabels',
        ],
        [
            'fullname' => 'IA.LABEL',
            'type'     => [
                'ptype'     => 'customlabel',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.LABEL',
            'onblur'   => "setFieldID(this, '_obj__NUMBERCUSTOMFIELDID');updateCustomLabel(this);",
            'path'     => 'NUMBERLABEL',
            'assist' => 'edit',
            'context' => CustomFieldEditor::getEditCustomLabelDialogFunction(),
            'script' => 'editor.phtml',
            'function' => 'editCustomLabels',
        ],
        [
            'fullname' => 'IA.LABEL',
            'type'     => [
                'ptype'     => 'customlabel',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.LABEL',
            'onblur'   => "setFieldID(this, '_obj__PERCENTCUSTOMFIELDID');updateCustomLabel(this);",
            'path'     => 'PERCENTLABEL',
            'assist' => 'edit',
            'context' => CustomFieldEditor::getEditCustomLabelDialogFunction(),
            'script' => 'editor.phtml',
            'function' => 'editCustomLabels',
        ],
        [
            'fullname' => 'IA.LABEL',
            'type'     => [
                'ptype'     => 'customlabel',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.LABEL',
            'onblur'   => "setFieldID(this, '_obj__PICKLISTCUSTOMFIELDID');updateCustomLabel(this);",
            'path'     => 'PICKLISTLABEL',
            'assist' => 'edit',
            'context' => CustomFieldEditor::getEditCustomLabelDialogFunction(),
            'script' => 'editor.phtml',
            'function' => 'editCustomLabels',
        ],
        [
            'fullname' => 'IA.LABEL',
            'type'     => [
                'ptype'     => 'customlabel',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.LABEL',
            'onblur'   => "setFieldID(this, '_obj__PICKLISTMULTICUSTOMFIELDID');updateCustomLabel(this);",
            'path'     => 'PICKLISTMULTILABEL',
            'assist' => 'edit',
            'context' => CustomFieldEditor::getEditCustomLabelDialogFunction(),
            'script' => 'editor.phtml',
            'function' => 'editCustomLabels',
        ],
        [
            'fullname' => 'IA.LABEL',
            'type'     => [
                'ptype'     => 'customlabel',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.LABEL',
            'onblur'   => "setFieldID(this, '_obj__SEQUENCECUSTOMFIELDID');updateCustomLabel(this);",
            'path'     => 'SEQUENCELABEL',
            'assist' => 'edit',
            'context' => CustomFieldEditor::getEditCustomLabelDialogFunction(),
            'script' => 'editor.phtml',
            'function' => 'editCustomLabels',
        ],
        [
            'fullname' => 'IA.LABEL',
            'type'     => [
                'ptype'     => 'customlabel',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.LABEL',
            'onblur'   => "setFieldID(this, '_obj__TEXTCUSTOMFIELDID');updateCustomLabel(this);",
            'path'     => 'TEXTLABEL',
            'assist' => 'edit',
            'context' => CustomFieldEditor::getEditCustomLabelDialogFunction(),
            'script' => 'editor.phtml',
            'function' => 'editCustomLabels',
        ],
        [
            'fullname' => 'IA.LABEL',
            'type'     => [
                'ptype'     => 'customlabel',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.LABEL',
            'onblur'   => "setFieldID(this, '_obj__TEXTAREACUSTOMFIELDID');updateCustomLabel(this);",
            'path'     => 'TEXTAREALABEL',
            'assist' => 'edit',
            'context' => CustomFieldEditor::getEditCustomLabelDialogFunction(),
            'script' => 'editor.phtml',
            'function' => 'editCustomLabels',
        ],
        [
            'fullname' => 'IA.LABEL',
            'type'     => [
                'ptype'     => 'customlabel',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.LABEL',
            'onblur'   => "setFieldID(this, '_obj__URLCUSTOMFIELDID');updateCustomLabel(this);",
            'path'     => 'URLLABEL',
            'assist' => 'edit',
            'context' => CustomFieldEditor::getEditCustomLabelDialogFunction(),
            'script' => 'editor.phtml',
            'function' => 'editCustomLabels',
        ],
        [
            'fullname' => 'IA.LABEL',
            'type'     => [
                'ptype'     => 'customlabel',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.LABEL',
            'onblur'   => "setFieldID(this, '_obj__PASSWORDCUSTOMFIELDID');updateCustomLabel(this);",
            'path'     => 'PASSWORDLABEL',
            'assist' => 'edit',
            'context' => CustomFieldEditor::getEditCustomLabelDialogFunction(),
            'script' => 'editor.phtml',
            'function' => 'editCustomLabels',
        ],
        [
            'fullname'        => 'IA.HIDDEN',
            'type'            => $gBooleanType,
            'default'         => 'false',
            'desc'            => 'IA.HIDDEN',
            'path'            => 'HIDDEN',
            'showlabelalways' => true,
        ],
        [
            'fullname'        => 'IA.REQUIRED',
            'type'            => $gBooleanType,
            'default'         => 'false',
            'desc'            => 'IA.REQUIRED',
            'path'            => 'REQUIRED',
            'showlabelalways' => true,
        ],
        [
            'fullname'  => 'IA.DATA_DESCRIPTION',
            'type'      => [
                'ptype'     => 'multitext',
                'type'      => 'multitext',
                'numofrows' => 10,
                'numofcols' => 100,
                'maxlength' => 400000,
            ],
            'desc'      => 'IA.DATA_DESCRIPTION',
            'path'      => 'DATADESCRIPTION',
            'showaudit' => false,
        ],
        [
            'fullname' => 'IA.DESCRIPTION',
            'type'     => [
                'ptype'     => 'textarea',
                'type'      => 'textarea',
                'maxlength' => 500,
            ],
            'desc'     => 'IA.DESCRIPTION',
            'path'     => 'CHECKBOXDESCRIPTION',
        ],
        [
            'fullname' => 'IA.DESCRIPTION',
            'type'     => [
                'ptype'     => 'textarea',
                'type'      => 'textarea',
                'maxlength' => 500,
            ],
            'desc'     => 'IA.DESCRIPTION',
            'path'     => 'CURRENCYDESCRIPTION',
        ],
        [
            'fullname' => 'IA.DESCRIPTION',
            'type'     => [
                'ptype'     => 'textarea',
                'type'      => 'textarea',
                'maxlength' => 500,
            ],
            'desc'     => 'IA.DESCRIPTION',
            'path'     => 'DATEDESCRIPTION',
        ],
        [
            'fullname' => 'IA.DESCRIPTION',
            'type'     => [
                'ptype'     => 'textarea',
                'type'      => 'textarea',
                'maxlength' => 500,
            ],
            'desc'     => 'IA.DESCRIPTION',
            'path'     => 'EMAILDESCRIPTION',
        ],
        [
            'fullname' => 'IA.DESCRIPTION',
            'type'     => [
                'ptype'     => 'textarea',
                'type'      => 'textarea',
                'maxlength' => 500,
            ],
            'desc'     => 'IA.DESCRIPTION',
            'path'     => 'NUMBERDESCRIPTION',
        ],
        [
            'fullname' => 'IA.DESCRIPTION',
            'type'     => [
                'ptype'     => 'textarea',
                'type'      => 'textarea',
                'maxlength' => 500,
            ],
            'desc'     => 'IA.DESCRIPTION',
            'path'     => 'PERCENTDESCRIPTION',
        ],
        [
            'fullname' => 'IA.DESCRIPTION',
            'type'     => [
                'ptype'     => 'textarea',
                'type'      => 'textarea',
                'maxlength' => 500,
            ],
            'desc'     => 'IA.DESCRIPTION',
            'path'     => 'PICKLISTDESCRIPTION',
        ],
        [
            'fullname' => 'IA.DESCRIPTION',
            'type'     => [
                'ptype'     => 'textarea',
                'type'      => 'textarea',
                'maxlength' => 500,
            ],
            'desc'     => 'IA.DESCRIPTION',
            'path'     => 'PICKLISTMULTIDESCRIPTION',
        ],
        [
            'fullname' => 'IA.DESCRIPTION',
            'type'     => [
                'ptype'     => 'textarea',
                'type'      => 'textarea',
                'maxlength' => 500,
            ],
            'desc'     => 'IA.DESCRIPTION',
            'path'     => 'SEQUENCEDESCRIPTION',
        ],
        [
            'fullname' => 'IA.DESCRIPTION',
            'type'     => [
                'ptype'     => 'textarea',
                'type'      => 'textarea',
                'maxlength' => 500,
            ],
            'desc'     => 'IA.DESCRIPTION',
            'path'     => 'TEXTDESCRIPTION',
        ],
        [
            'fullname' => 'IA.DESCRIPTION',
            'type'     => [
                'ptype'     => 'textarea',
                'type'      => 'textarea',
                'maxlength' => 500,
            ],
            'desc'     => 'IA.DESCRIPTION',
            'path'     => 'TEXTAREADESCRIPTION',
        ],
        [
            'fullname' => 'IA.DESCRIPTION',
            'type'     => [
                'ptype'     => 'textarea',
                'type'      => 'textarea',
                'maxlength' => 500,
            ],
            'desc'     => 'IA.DESCRIPTION',
            'path'     => 'URLDESCRIPTION',
        ],
        [
            'fullname' => 'IA.DESCRIPTION',
            'type'     => [
                'ptype'     => 'textarea',
                'type'      => 'textarea',
                'maxlength' => 500,
            ],
            'desc'     => 'IA.DESCRIPTION',
            'path'     => 'PASSWORDDESCRIPTION',
        ],
        [
            'fullname'        => 'IA.DEFAULT_VALUE',
            'type'            => [
                'ptype'       => 'radio',
                'type'        => 'radio',
                'validlabels' => [ 'IA.CHECKED', 'IA.UNCHECKED' ],
                'validvalues' => [ 'true', 'false' ],
            ],
            'default'         => 'false',
            'desc'            => 'IA.DEFAULT_VALUE',
            'path'            => 'CHECKBOXDEFAULT',
            'showlabelalways' => true,
        ],
        [
            'path'     => 'CURRENCYLENGTH',
            'desc'     => 'IA.LENGTH',
            'fullname' => 'IA.LENGTH',
            'type'     => [
                'ptype'     => 'integer',
                'type'      => 'integer',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
        ],
        [
            'path'     => 'NUMBERLENGTH',
            'desc'     => 'IA.LENGTH',
            'fullname' => 'IA.LENGTH',
            'type'     => [
                'ptype'     => 'integer',
                'type'      => 'integer',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
        ],
        [
            'path'     => 'PERCENTLENGTH',
            'desc'     => 'IA.LENGTH',
            'fullname' => 'IA.LENGTH',
            'type'     => [
                'ptype'     => 'integer',
                'type'      => 'integer',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
        ],
        [
            'path'     => 'TEXTLENGTH',
            'desc'     => 'IA.LENGTH',
            'fullname' => 'IA.LENGTH',
            'type'     => [
                'ptype'     => 'integer',
                'type'      => 'integer',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
        ],

        [
            'path'     => 'CURRENCYDECIMALPLACES',
            'desc'     => 'IA.DECIMAL_PLACES',
            'fullname' => 'IA.DECIMAL_PLACES',
            'type'     => [
                'ptype'     => 'integer',
                'type'      => 'integer',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
        ],
        [
            'path'     => 'NUMBERDECIMALPLACES',
            'desc'     => 'IA.DECIMAL_PLACES',
            'fullname' => 'IA.DECIMAL_PLACES',
            'type'     => [
                'ptype'     => 'integer',
                'type'      => 'integer',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
        ],
        [
            'path'     => 'PERCENTDECIMALPLACES',
            'desc'     => 'IA.DECIMAL_PLACES',
            'fullname' => 'IA.DECIMAL_PLACES',
            'type'     => [
                'ptype'     => 'integer',
                'type'      => 'integer',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
        ],

        [
            'fullname'        => 'IA.DEFAULT_TO_TODAYS_DATE',
            'type'            => [
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => [ 'true', 'false' ],
                'validlabels' => [ 'IA.TRUE', 'IA.FALSE' ],
            ],
            'value'           => 'false',
            'desc'            => "IA.DEFAULT_TO_TODAYS_DATE",
            'path'            => 'DEFAULTTOTODAY',
            'showlabelalways' => true,
        ],
        [
            'fullname'        => 'IA.USE_FIRST_VALUE_AS_DEFAULT',
            'type'            => [
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => [ 'true', 'false' ],
                'validlabels' => [ 'IA.TRUE', 'IA.FALSE' ],
            ],
            'value'           => 'false',
            'desc'            => "IA.USE_FIRST_VALUE_AS_DEFAULT",
            'path'            => 'PICKLISTUSEFIRSTVALUEASDEFAULT',
            'showlabelalways' => true,
        ],
        [
            'fullname'        => 'IA.USE_FIRST_VALUE_AS_DEFAULT',
            'type'            => [
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => [ 'true', 'false' ],
                'validlabels' => [ 'IA.TRUE', 'IA.FALSE' ],
            ],
            'value'           => 'false',
            'desc'            => "IA.USE_FIRST_VALUE_AS_DEFAULT",
            'path'            => 'PICKLISTMULTIUSEFIRSTVALUEASDEFAULT',
            'showlabelalways' => true,
        ],
        [
            'fullname'        => 'IA.SORT_LIST_ALPHABETICALLY',
            'type'            => [
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => [ 'true', 'false' ],
                'validlabels' => [ 'IA.TRUE', 'IA.FALSE' ],
            ],
            'value'           => 'false',
            'desc'            => "IA.SORT_LIST_ALPHABETICALLY",
            'path'            => 'PICKLISTSORTLISTALPHABETICALLY',
            'showlabelalways' => true,
        ],
        [
            'fullname'        => 'IA.SORT_LIST_ALPHABETICALLY',
            'type'            => [
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => [ 'true', 'false' ],
                'validlabels' => [ 'IA.TRUE', 'IA.FALSE' ],
            ],
            'value'           => 'false',
            'desc'            => "IA.SORT_LIST_ALPHABETICALLY",
            'path'            => 'PICKLISTMULTISORTLISTALPHABETICALLY',
            'showlabelalways' => true,
        ],
        [
            'fullname'  => 'IA.PICK_VALUES',
            'type'      => [
                'ptype'     => 'textarea',
                'type'      => 'textarea',
                'maxlength' => 60000,
            ],
            'numofrows' => 10,
            'numofcols' => 50,
            'desc'      => 'IA.PICK_VALUES',
            'path'      => 'PICKLISTPICKVALUES',
        ],
        [
            'fullname'  => 'IA.PICK_VALUES',
            'type'      => [
                'ptype'     => 'textarea',
                'type'      => 'textarea',
                'maxlength' => 60000,
            ],
            'numofrows' => 10,
            'numofcols' => 50,
            'desc'      => 'IA.PICK_VALUES',
            'path'      => 'PICKLISTMULTIPICKVALUES',
        ],
        [
            'path'     => 'PICKLISTNUMBEROFROWSTODISPLAY',
            'desc'     => 'IA.NUMBER_OF_ROWS_TO_DISPLAY',
            'fullname' => 'IA.NUMBER_OF_ROWS_TO_DISPLAY',
            'type'     => [
                'ptype'     => 'integer',
                'type'      => 'integer',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
        ],
        [
            'path'     => 'PICKLISTMULTINUMBEROFROWSTODISPLAY',
            'desc'     => 'IA.NUMBER_OF_ROWS_TO_DISPLAY',
            'fullname' => 'IA.NUMBER_OF_ROWS_TO_DISPLAY',
            'type'     => [
                'ptype'     => 'integer',
                'type'      => 'integer',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
        ],
        [
            'path'     => 'TEXTAREANUMBEROFROWSTODISPLAY',
            'desc'     => 'IA.NUMBER_OF_ROWS_TO_DISPLAY',
            'fullname' => 'IA.NUMBER_OF_ROWS_TO_DISPLAY',
            'type'     => [
                'ptype'     => 'integer',
                'type'      => 'integer',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
        ],
        [
            'path'     => 'SEQUENCE',
            'fullname' => 'IA.DOCUMENT_NUMBERING_SCHEME',
            'desc'     => 'IA.DOCUMENT_SEQUENCE',
            'type'     => [
                'ptype'     => 'ptr',
                'type'      => 'ptr',
                'entity'    => 'seqnum',
                'size'      => 20,
                'maxlength' => 100,
            ],
        ],
        [
            'fullname'        => 'IA.FIELD_IS_REQUIRED',
            'type'            => [
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => [ 'true', 'false' ],
                'validlabels' => [ 'IA.TRUE', 'IA.FALSE' ],
            ],
            'value'           => 'false',
            'desc'            => "IA.FIELD_IS_REQUIRED",
            'path'            => 'FIELDISREQUIRED',
            'showlabelalways' => true,
        ],
        [
            'fullname'        => 'IA.FIELD_IS_HIDDEN',
            'type'            => [
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => [ 'true', 'false' ],
                'validlabels' => [ 'IA.TRUE', 'IA.FALSE' ],
            ],
            'value'           => 'false',
            'desc'            => "IA.FIELD_IS_HIDDEN",
            'path'            => 'FIELDISHIDDEN',
            'showlabelalways' => true,
        ],
        [
            'fullname'        => 'IA.FIELD_IS_INACTIVE',
            'type'            => [
                'ptype'       => 'boolean',
                'type'        => 'boolean',
                'validvalues' => [ 'true', 'false' ],
                'validlabels' => [ 'IA.TRUE', 'IA.FALSE' ],
            ],
            'value'           => 'false',
            'desc'            => "IA.FIELD_IS_INACTIVE",
            'path'            => 'FIELDISINACTIVE',
            'showlabelalways' => true,
        ],
        [
            'fullname' => '',
            'type'     => [
                'ptype'       => 'enum',
                'type'        => 'enum',
                'validlabels' => [],
                'validvalues' => [
                ],
            ],
            'desc'     => 'IA.PAGE',
            'path'     => 'EXISTINGPAGE',
        ],
        [
            'fullname' => '',
            'type'     => [
                'ptype' => 'textlabel',
                'type'  => 'textlabel',
            ],
            'desc'     => 'IA.PAGE',
            'default'  => 'IA.OR_NEW',
            'path'     => 'LABELORNEW',
        ],
        [
            'fullname' => '',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'desc'     => 'IA.PAGE',
            'path'     => 'NEWPAGE',
        ],
        [
            'fullname' => 'IA.NAMESPACE',
            'type'     => [
                'ptype'       => 'enum',
                'type'        => 'enum',
                'validlabels' => [],
                'validvalues' => [],
            ],
            'desc'     => 'IA.NAMESPACE',
            'onchange' => '',
            'path'     => 'NAMESPACE',
        ],
        [
            'fullname' => 'IA.NAMESPACE_LABEL',
            'type'     => [
                'ptype' => 'text',
                'type'  => 'text',
            ],
            'readonly' => true,
            'desc'     => 'IA.NAMESPACE_LABEL',
            'path'     => 'NAMESPACELABEL',
        ],
        $gStatusFieldInfo,
        [
            'fullname' => 'IA.CUSTOMLABEL_ID',
            'type'     => [
                'ptype'     => 'text',
                'type'      => 'text',
                'maxlength' => 80,
                'size'      => 20,
                'format'    => '/^.{1,80}$/',
            ],
            'hidden' => true,
            'readonly' => true,
            'desc'     => 'IA.CUSTOMLABEL_ID_DESCRIPTION',
            'path'     => 'CUSTOMLABELID',
        ],
        $gCreatedByFieldInfo,
        $gWhenCreatedFieldInfo,
        $gModifiedByFieldInfo,
        $gWhenModifiedFieldInfo,
    ],
    'requiredfields'       => [
        'CHECKBOXPAGE'      => [
            'CHECKBOXLABEL',
            'CHECKBOXCUSTOMFIELDID',
        ],
        'CURRENCYPAGE'      => [
            'CURRENCYLABEL',
            'CURRENCYLENGTH',
            'CURRENCYDECIMALPLACES',
            'CURRENCYCUSTOMFIELDID',
        ],
        'DATEPAGE'          => [
            'DATELABEL',
            'DATECUSTOMFIELDID',
        ],
        'EMAILPAGE'         => [
            'EMAILLABEL',
            'EMAILCUSTOMFIELDID',
        ],
        'NUMBERPAGE'        => [
            'NUMBERLABEL',
            'NUMBERLENGTH',
            'NUMBERDECIMALPLACES',
            'NUMBERCUSTOMFIELDID',
        ],
        'PERCENTPAGE'       => [
            'PERCENTLABEL',
            'PERCENTLENGTH',
            'PERCENTDECIMALPLACES',
            'PERCENTCUSTOMFIELDID',
        ],
        'PICKLISTPAGE'      => [
            'PICKLISTLABEL',
            'PICKLISTPICKVALUES',
            'PICKLISTCUSTOMFIELDID',
        ],
        'PICKLISTMULTIPAGE' => [
            'PICKLISTMULTILABEL',
            'PICKLISTMULTIPICKVALUES',
            'PICKLISTMULTICUSTOMFIELDID',
        ],
        'SEQUENCEPAGE'      => [
            'SEQUENCELABEL',
            'SEQUENCE',
            'SEQUENCECUSTOMFIELDID',
        ],
        'TEXTPAGE'          => [
            'TEXTLABEL',
            'TEXTLENGTH',
            'TEXTCUSTOMFIELDID',
        ],
        'TEXTAREAPAGE'      => [
            'TEXTAREALABEL',
            'TEXTAREANUMBEROFROWSTODISPLAY',
            'TEXTAREACUSTOMFIELDID',
        ],
        'PASSWORDPAGE'      => [
            'PASSWORDLABEL',
            'PASSWORDCUSTOMFIELDID',
        ],
        'URLPAGE'           => [
            'URLLABEL',
            'URLCUSTOMFIELDID',
        ],
    ],
    'table'                => 'customfield',
    'printas'              => 'IA.FIELD',
    'pluralprintas'        => 'IA.FIELDS',
    'vid'                  => 'RECORDNO',
    'autoincrement'        => 'RECORDNO',
    'module'               => 'co',
    'module_list'          => [ 'co', 'cerp' ],
    'nosysview'            => true,
    'audittrail_cache_add' => true,
    'upsertEntries'        => true,
];
