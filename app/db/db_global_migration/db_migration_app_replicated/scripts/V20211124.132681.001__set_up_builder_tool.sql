--liquibase formatted sql
--changeset alexandru.ardelean:20211124.132681.001 runOnChange:false logicalFilePath:V20211124.132681.001__set_up_builder_tool.sql

-- add tool
INSERT INTO cstoolnames (record#, id, name, href, menugroupkey)
VALUES(CSTOOLNAMES_SEQ.NEXTVAL, 'CSTOOLS_TOOLNAME_SET_UP_BUILDER', 'Set-up Builder', 'set_up_builder.phtml', (SELECT record# FROM csmenugroup WHERE name = 'Engineering Tools'))
/

-- add roles for tool
INSERT INTO csroletool (toolkey, rolekey)
(SELECT record#, (SELECT record# FROM csroles WHERE name = 'Engineering CRT') FROM cstoolnames WHERE ID = 'CSTOOLS_TOOLNAME_SET_UP_BUILDER')
/

INSERT INTO csroletool (toolkey, rolekey)
(SELECT record#, (SELECT record# FROM csroles WHERE name = 'Engineering Management') FROM cstoolnames WHERE ID = 'CSTOOLS_TOOLNAME_SET_UP_BUILDER')
/