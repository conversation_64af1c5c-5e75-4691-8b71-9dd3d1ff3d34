openapi: 3.0.0
info:
  title: company-config-email-delivery-record
  description: company-config.email-delivery-record API
  version: '1.0'
  contact:
    name: vira<PERSON> karekar
    email: <EMAIL>
tags:
  - name: email delivery record
    description: The email delivery record for the enhanced email delivery service using sendGrid lets you check if any sent email encountered errors.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/company-config/email-delivery-record:
    get:
      summary: List email delivery record
      description: Returns a collection with a key, ID, and link for each  email delivery record.
      tags:
        - email delivery record
      operationId: get-objects-email-delivery-record
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of email-delivery-record objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of email delivery record:
                  value:
                    'ia::result':
                      - key: '1'
                        id: '1'
                        href: /objects/company-config/email-delivery-record/1
                      - key: '2'
                        id: '2'
                        href: /objects/company-config/email-delivery-record/2
                      - key: '3'
                        id: '3'
                        href: /objects/company-config/email-delivery-record/3
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  '/objects/company-config/email-delivery-record/{key}':
    parameters:
      - name: key
        description: system-assigned unique key for the  email delivery record.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a  email delivery record
      description: Returns detailed information for a particular  email delivery record.
      tags:
        - email delivery record
      operationId: get-objects-email-delivery-record-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the email-delivery-record
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/company-config-email-delivery-record'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the email delivery record:
                  value:
                    'ia::result':
                      id: '1'
                      key: '1'
                      status: queued
                      tenantContext: WDIjad73gE8Q8aNLYEkv2zJn/CO3rAUxt3ia1wLRKFGeqC+4lSp48iFoYJ5LJLf36toGA7faZAVS/J5H1xHDfB8NQwyCi2HUtQNrWsE176jr7vRb
                      from: Support <<EMAIL>>
                      to: <EMAIL>
                      subject: 'dev01: Document due on 09/30/2023 for Customer - Associated Research Inc'
                      failureStatus:
                      failureReason:
                      sentDateTime: '2024-01-15T11:29:15Z'
                      lastUpdatedDateTime:
                      emailMessage:
                        FROM: Support <<EMAIL>>
                        SENDERNAME: Support
                        SENDEREMAIL: <EMAIL>
                        REPLY_TO: Support <<EMAIL>>
                        TO:
                          - <EMAIL>
                        CC: [ ]
                        BCC: [ ]
                        SUBJECT: 'dev01: Document due on 09/30/2023 for Customer - Associated Research
                          Inc'
                        BODY: 'Dear Customer,<br><br>A document is attached to this email as a PDF file.<br>    Document
                          No. Sales Invoice-Sal#0175#inv<br>    Due Date: 09/30/2023<br><br>If you have
                          any questions, please reply to this email.<br><br>To view this, click on the
                          attachment. Adobe Acrobat should launch. If you do not have Acrobat installed
                          on your computer, you can download it from <a href=''http://www.adobe.com/products/acrobat/readstep2.html''>here</a><br><br>Sincerely,<br>Punji
                          Chandrashekhar<br>AL_Prod_GL-main (<EMAIL>)<br/><br/><br/><br/><br/><hr/><img
                          width="120" src="https://intacct-ops-prod-public-assets.s3.us-west-2.amazonaws.com/logo.png"/>'
                      emailProviderId: dt2FGrVFSNO0JQXAGSxTag
                      deliveryHistory:
                        - RECORDNO: '2'
                          EMAILRECORDNO: '1'
                          SENDTO: <EMAIL>
                          STATUS_V: processed
                          TIMESTAMP: 01/15/2024 11:29:16.0
                          ADDITIONALINFO: ''
                        - RECORDNO: '1'
                          EMAILRECORDNO: '1'
                          SENDTO: <EMAIL>
                          STATUS_V: delivered
                          TIMESTAMP: 01/15/2024 11:29:19.0
                          ADDITIONALINFO: 'TSL: 1'
                        - RECORDNO: '3'
                          EMAILRECORDNO: '1'
                          SENDTO: <EMAIL>
                          STATUS_V: open
                          TIMESTAMP: 01/15/2024 11:29:43.0
                          ADDITIONALINFO: ''
                      replyTo: Support <<EMAIL>>
                      cc: ''
                      bcc: ''
                      body: 'Dear Customer,<br><br>A document is attached to this email as a PDF file.<br>    Document
                        No. Sales Invoice-Sal#0175#inv<br>    Due Date: 09/30/2023<br><br>If you have
                        any questions, please reply to this email.<br><br>To view this, click on the attachment.
                        Adobe Acrobat should launch. If you do not have Acrobat installed on your computer,
                        you can download it from here<br><br>Sincerely,<br>Support<br>AL_Prod_GL-main
                        (<EMAIL>)<br/><br/><br/><br/><br/>'
                      href: /objects/company-config/email-delivery-record/1
                      'ia::meta':
                        totalCount: 1
                        totalSuccess: 1
                        totalError: 0

        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    company-config-email-delivery-record:
      $ref: ../models/company-config.email-delivery-record.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
