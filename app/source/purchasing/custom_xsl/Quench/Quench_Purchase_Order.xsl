<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:fox="http://xml.apache.org/fop/extensions">
	<xsl:include href="../../private/xslinc/miscdocument_inc.xsl"/>
	<xsl:template match="/">
		<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:svg="http://www.w3.org/2000/svg" xmlns:fox="http://xml.apache.org/fop/extensions">
			<xsl:choose>
				<xsl:when test="//REC/CUSTVEND/COMMENTS !=''">
					<xsl:call-template name="pagemaster_custom"/>
				</xsl:when>
				<xsl:otherwise>
					<xsl:call-template name="pagemaster"/>
				</xsl:otherwise>
			</xsl:choose>
			<xsl:apply-templates/>
		</fo:root>
	</xsl:template>
	<xsl:template match="DOCUMENT">
		<xsl:apply-templates/>
	</xsl:template>
	<xsl:template match="OLDROOT">
		<fo:page-sequence master-reference="psmA" initial-page-number="1">
			<fo:static-content flow-name="xsl-region-after">
				<xsl:choose>
					<xsl:when test="//REC/CUSTVEND/COMMENTS !=''">
						<xsl:call-template name="message_custom"/>
						<xsl:call-template name="footer_custom">
							<xsl:with-param name="pagenum" select="'footerpage'"/>
						</xsl:call-template>
					</xsl:when>
					<xsl:otherwise>
						<xsl:call-template name="footer">
							<xsl:with-param name="pagenum" select="'footerpage'"/>
						</xsl:call-template>
					</xsl:otherwise>
				</xsl:choose>
			</fo:static-content>
			<fo:flow flow-name="xsl-region-body">
				<xsl:call-template name="pagetop_custom_shipto"/>
				<xsl:call-template name="titleordernumber"/>
				<xsl:call-template name="titleorderdate"/>
				<xsl:call-template name="titleorderduedate_custom"/>
				<xsl:call-template name="titleordershipvia"/>
				<xsl:choose>
					<xsl:when test="(REC/_DOCPAR/SHOWTITLE1 !='' ) and (REC/_DOCPAR/SHOWTITLE2 !='' )">
						<xsl:call-template name="displaycontacts_custom"/>
					</xsl:when>
					<xsl:otherwise>
					<xsl:call-template name="paytoreturntoaddress_custom"/>
					</xsl:otherwise>
				</xsl:choose>
			<xsl:call-template name="topnotes">
				<xsl:with-param name="label0"        select="'Reference #'"/>
				<xsl:with-param name="data0"         select="REC/PONUMBER"/>
				<xsl:with-param name="label1"        select="'Vendor Document #'"/>
				<xsl:with-param name="data1"         select="REC/VENDORDOCNO"/>
				<xsl:with-param name="label2"        select="'Terms'"/>
				<xsl:with-param name="data2"         select="REC/TERM/NAME"/>
			</xsl:call-template>
				<xsl:call-template name="quotebody"/>
			</fo:flow>
		</fo:page-sequence>
	</xsl:template>
	<xsl:template name="pagetop_custom_shipto">
		<xsl:call-template name="companyaddress_shipto"/>
		<xsl:call-template name="documentlabel"/>
	</xsl:template>
	<xsl:template name="companyaddress_shipto">
		<xsl:variable name="companynameindent">0.5in</xsl:variable>
		<xsl:if test="(.//LOGO != '')">
			<fo:block start-indent="{$companynameindent}">
				<fo:external-graphic content-width="auto" width="auto" src="{//COMPANY/LOGO}"/>
			</fo:block>
		</xsl:if>
		<fo:block text-align="start" line-height="18pt" font-family="{$myfont}" font-weight="bold" font-size="14pt" height="4cm" width="12cm" start-indent="{$companynameindent}"/>
		<fo:block text-align="start" line-height="18pt" font-family="{$myfont}" font-weight="bold" font-size="12pt" height="4cm" width="12cm" start-indent="{$companynameindent}">			
				Ship To:
		</fo:block>
		<fo:block text-align="start" line-height="18pt" font-family="{$myfont}" font-weight="bold" font-size="12pt" height="4cm" width="12cm" start-indent="{$companynameindent}">
			<xsl:value-of select=".//COMPANY/TITLE"/>
		</fo:block>
		<fo:block font-size="10pt" start-indent="{$companynameindent}">
			<xsl:value-of select=".//COMPANY/ADDRESS1"/>
		</fo:block>
		<xsl:if test=".//COMPANY/ADDRESS2!=''">
			<fo:block font-size="10pt" start-indent="{$companynameindent}">
				<xsl:value-of select=".//COMPANY/ADDRESS2"/>
			</fo:block>
		</xsl:if>
		<fo:block font-size="10pt" start-indent="{$companynameindent}">
			<xsl:value-of select=".//COMPANY/CITY"/>
			<xsl:if test=".//COMPANY/STATE != '' and .//COMPANY/CITY != ''">
				<xsl:text>,  </xsl:text>
			</xsl:if>
			<xsl:value-of select=".//COMPANY/STATE"/>
			<xsl:text/>
			<xsl:value-of select=".//COMPANY/ZIPCODE"/>
		</fo:block>
		<xsl:if test=".//COMPANY/CONTACTPHONE != ''">
			<fo:block font-size="10pt" start-indent="{$companynameindent}">
				<xsl:text>Ph:  </xsl:text>
				<xsl:value-of select="..//COMPANY/CONTACTPHONE"/>
			</fo:block>
		</xsl:if>
		<xsl:if test=".//COMPANY/FAX != ''">
			<fo:block font-size="10pt" start-indent="{$companynameindent}">
				<xsl:text>Fax: </xsl:text>
				<xsl:value-of select=".//COMPANY/FAX"/>
			</fo:block>
		</xsl:if>
		<xsl:if test=".//COMPANY/CONTACTEMAIL != ''">
			<fo:block font-size="10pt" start-indent="{$companynameindent}">
				<xsl:text>E-Mail: </xsl:text>
				<xsl:value-of select=".//COMPANY/CONTACTEMAIL"/>
			</fo:block>
		</xsl:if>
		<xsl:if test=".//COMPANY/MARKETING_TEXT != ''">
			<fo:block font-size="10pt" start-indent="{$companynameindent}">
				<xsl:value-of select=".//COMPANY/MARKETING_TEXT"/>
			</fo:block>
		</xsl:if>
	</xsl:template>
	<xsl:template name="titleinvoiceduedate_custom">
		<xsl:choose>
			<xsl:when test="//_DOCPAR/CATEGORY='R'"/>
			<xsl:otherwise>
				<xsl:call-template name="generictitle_custom">
					<xsl:with-param name="label" select="'Due Date'"/>
					<xsl:with-param name="data" select="REC/WHENDUE"/>
					<xsl:with-param name="position" select="'2.2cm'"/>
				</xsl:call-template>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>
	<xsl:template name="generictitle_custom">
		<xsl:param name="label"/>
		<xsl:param name="data"/>
		<xsl:param name="position"/>
		<fo:block-container border-color="white" border-style="solid" border-bottom-width="0.000pt" border-top-width="0.000pt" border-left-width="0.000pt" border-right-width="0.000pt" height="0.5cm" width="2.5in" top="{$position}" left="4.3in" padding="2pt" position="absolute">
			<fo:block text-align="start" space-after.optimum="3pt" font-family="{$myfont}" font-weight="bold" font-size="11pt">
				<xsl:value-of select="$label"/>
				<xsl:text> : </xsl:text>
				<xsl:value-of select="$data"/>
			</fo:block>
		</fo:block-container>
	</xsl:template>
	<xsl:template name="displaycontacts_custom">
		<xsl:variable name="showtitle1">
			<xsl:value-of select="REC/_DOCPAR/SHOWTITLE1"/>
		</xsl:variable>
		<xsl:variable name="showtitle2">
			<xsl:value-of select="REC/_DOCPAR/SHOWTITLE2"/>
		</xsl:variable>
		<fo:block space-before="1.5cm" border-color="white">
			<fo:table table-layout="fixed" border-color="white" border-style="solid" border-bottom-width="0.000pt" border-top-width="0.000pt" border-left-width="0.000pt" border-right-width="0.000pt">
				<fo:table-column column-width="9.7cm"/>
				<fo:table-column column-width="9.0cm"/>
				<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="10pt">
					<fo:table-row line-height="15pt">
						<fo:table-cell padding="6pt">
							<!-- title1 is always billto and title2 is always shipto-->
							<xsl:choose>
								<xsl:when test="$showtitle1='true'">
									<xsl:call-template name="genericbilltoaddress_custom">
										<xsl:with-param name="billorship" select="REC/_DOCPAR/CONTACTTITLE1"/>
									</xsl:call-template>
								</xsl:when>
								<xsl:otherwise>
									<xsl:if test="$showtitle2='true'">
										<xsl:call-template name="genericshiptoaddress_custom">
											<xsl:with-param name="billorship" select="REC/_DOCPAR/CONTACTTITLE2"/>
										</xsl:call-template>
									</xsl:if>
								</xsl:otherwise>
							</xsl:choose>
						</fo:table-cell>
						<fo:table-cell padding="6pt">
							<xsl:if test="$showtitle1='true' and $showtitle2='true'">
								<xsl:call-template name="genericshiptoaddress_custom">
									<xsl:with-param name="billorship" select="REC/_DOCPAR/CONTACTTITLE2"/>
								</xsl:call-template>
							</xsl:if>
						</fo:table-cell>
					</fo:table-row>
				</fo:table-body>
			</fo:table>
		</fo:block>
	</xsl:template>
	<xsl:template name="genericbillshipaddress_custom">
		<xsl:param name="billorship"/>
		<xsl:param name="companyname"/>
		<xsl:param name="printas"/>
		<xsl:param name="address1"/>
		<xsl:param name="address2"/>
		<xsl:param name="city"/>
		<xsl:param name="state"/>
		<xsl:param name="zip"/>
		<xsl:param name="country"/>
		<xsl:param name="phone1"/>
		<xsl:param name="fax"/>
		<xsl:param name="email"/>		
		<fo:inline start-indent="0.42in">
			<fo:block text-align="start" font-weight="bold">
				<xsl:value-of select="$billorship"/>
				<xsl:text> : </xsl:text>
			</fo:block>
			<xsl:if test="$companyname != '' and $companyname != $printas">
				<fo:block text-align="start" font-weight="bold">
					<xsl:value-of select="$companyname"/>
				</fo:block>
			</xsl:if>
			<fo:block text-align="start" font-weight="bold">
				<xsl:if test="$companyname != '' and $companyname != $printas and $billorship != 'Remit To'">
					<xsl:text>Attn: </xsl:text>
				</xsl:if>
				<xsl:value-of select="$printas"/>
			</fo:block>
			<fo:block text-align="start">
				<xsl:value-of select="$address1"/>
			</fo:block>
			<fo:block text-align="start">
				<xsl:value-of select="$address2"/>
			</fo:block>
			<fo:block text-align="start">
				<xsl:value-of select="$city"/>
				<xsl:if test="$state != '' and $city != ''">
					<xsl:text>,  </xsl:text>
				</xsl:if>
				<xsl:value-of select="$state"/>
				<xsl:text/>
				<xsl:value-of select="$zip"/>
			</fo:block>
			<fo:block text-align="start">
				<xsl:value-of select="$country"/>
			</fo:block>
			<xsl:if test="$phone1 != ''">
				<fo:block text-align="start">
					<xsl:text>Phone: </xsl:text>
					<xsl:value-of select="$phone1"/>
				</fo:block>
			</xsl:if>
			<xsl:if test="$fax != ''">
				<fo:block text-align="start">
					<xsl:text>Fax #: </xsl:text>
					<xsl:value-of select="$fax"/>
				</fo:block>
			</xsl:if>
			<xsl:if test="$email != ''">
				<fo:block text-align		="start" >
					<xsl:text>E-Mail: </xsl:text><xsl:value-of select="$email"/>
				</fo:block>
			</xsl:if>				
		</fo:inline>
	</xsl:template>
<xsl:template name="paytoreturntoaddress_custom">
	<xsl:call-template name="internalbilltoshiptoaddress_custom">
		<xsl:with-param name="addressa" select="'payto'"/>
		<xsl:with-param name="addressb" select="'returnto'"/>
	</xsl:call-template>
</xsl:template>

	<xsl:template name="internalbilltoshiptoaddress_custom">
		<xsl:param name="addressa"/>
		<xsl:param name="addressb"/>
		<fo:block space-before="1.5cm" border-color="white">
			<fo:table table-layout="fixed" border-color="white" border-style="solid" border-bottom-width="0.000pt" border-top-width="0.000pt" border-left-width="0.000pt" border-right-width="0.000pt">
				<fo:table-column column-width="9.7cm"/>
				<fo:table-column column-width="9.0cm"/>
				<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="10pt">
					<fo:table-row line-height="15pt">
						<xsl:call-template name="internalinsertaddress_custom">
							<xsl:with-param name="whichaddress" select="$addressa"/>
						</xsl:call-template>
						<xsl:call-template name="internalinsertaddress_custom">
							<xsl:with-param name="whichaddress" select="$addressb"/>
						</xsl:call-template>
					</fo:table-row>
				</fo:table-body>
			</fo:table>
		</fo:block>
	</xsl:template>
	<xsl:template name="internalinsertaddress_custom">
		<xsl:param name="whichaddress"/>
		<fo:table-cell padding="6pt">
			<xsl:if test="$whichaddress='billto'">
				<xsl:call-template name="genericbilltoaddress_custom">
					<xsl:with-param name="billorship" select="'Bill To'"/>
				</xsl:call-template>
			</xsl:if>
			<xsl:if test="$whichaddress='shipto'">
				<xsl:call-template name="genericshiptoaddress_custom">
					<xsl:with-param name="billorship" select="'Ship To'"/>
				</xsl:call-template>
			</xsl:if>
			<xsl:if test="$whichaddress='payto'">
				<xsl:call-template name="genericbilltoaddress_custom">
					<xsl:with-param name="billorship" select="'Pay To'"/>
				</xsl:call-template>
			</xsl:if>
			<xsl:if test="$whichaddress='returnto'">
				<xsl:call-template name="genericshiptoaddress_custom">
					<xsl:with-param name="billorship" select="'Return To'"/>
				</xsl:call-template>
			</xsl:if>
			<xsl:if test="$whichaddress='vendor'">
				<xsl:call-template name="genericshiptoaddress_custom">
					<xsl:with-param name="billorship" select="'Vendor'"/>
				</xsl:call-template>
			</xsl:if>
		</fo:table-cell>
	</xsl:template>
	<xsl:template name="genericbilltoaddress_custom">
		<xsl:param name="billorship"/>
		<xsl:call-template name="genericbillshipaddress_custom">
			<xsl:with-param name="billorship" select="$billorship"/>
			<xsl:with-param name="companyname" select=".//BILLTO/COMPANYNAME"/>
			<xsl:with-param name="printas" select=".//BILLTO/PRINTAS"/>
			<xsl:with-param name="address1" select=".//BILLTO/MAILADDRESS/ADDRESS1"/>
			<xsl:with-param name="address2" select=".//BILLTO/MAILADDRESS/ADDRESS2"/>
			<xsl:with-param name="city" select=".//BILLTO/MAILADDRESS/CITY"/>
			<xsl:with-param name="state" select=".//BILLTO/MAILADDRESS/STATE"/>
			<xsl:with-param name="zip" select=".//BILLTO/MAILADDRESS/ZIP"/>
			<xsl:with-param name="phone1" select=".//BILLTO/PHONE1"/>
			<xsl:with-param name="fax" select=".//BILLTO/FAX"/>
			<xsl:with-param name="email" select=".//BILLTO/EMAIL1"/>			
		</xsl:call-template>
	</xsl:template>
	<xsl:template name="genericshiptoaddress_custom">
		<xsl:param name="billorship"/>
		<xsl:call-template name="genericbillshipaddress_custom">
			<xsl:with-param name="billorship" select="$billorship"/>
			<xsl:with-param name="companyname" select=".//SHIPTO/COMPANYNAME"/>
			<xsl:with-param name="printas" select=".//SHIPTO/PRINTAS"/>
			<xsl:with-param name="address1" select=".//SHIPTO/MAILADDRESS/ADDRESS1"/>
			<xsl:with-param name="address2" select=".//SHIPTO/MAILADDRESS/ADDRESS2"/>
			<xsl:with-param name="city" select=".//SHIPTO/MAILADDRESS/CITY"/>
			<xsl:with-param name="state" select=".//SHIPTO/MAILADDRESS/STATE"/>
			<xsl:with-param name="zip" select=".//SHIPTO/MAILADDRESS/ZIP"/>
			<xsl:with-param name="phone1" select=".//SHIPTO/PHONE1"/>
			<xsl:with-param name="fax" select=".//SHIPTO/FAX"/>
			<xsl:with-param name="email" select=".//SHIPTO/EMAIL1"/>			
		</xsl:call-template>
	</xsl:template>
	<xsl:template name="pagemaster_custom">
		<xsl:variable name="pgwidth">8.5in</xsl:variable>
		<xsl:variable name="pgheight">11in</xsl:variable>
		<fo:layout-master-set>
			<fo:simple-page-master master-name="first" margin-right="1.2cm" margin-left="1.2cm" margin-bottom="0.5cm" margin-top="1.0cm" page-width="{$pgwidth}" page-height="{$pgheight}">
				<fo:region-body overflow="auto" margin-top="0.20in" margin-bottom="1.60in"/>
				<fo:region-before extent="1.00in"/>
				<fo:region-after extent="1.60in"/>
			</fo:simple-page-master>
			<fo:simple-page-master master-name="rest" margin-right="1.2cm" margin-left="1.2cm" margin-bottom="0.5cm" margin-top="1.0cm" page-width="{$pgwidth}" page-height="{$pgheight}">
				<fo:region-body overflow="auto" margin-top="0.20in" margin-bottom="1.60in"/>
				<fo:region-before extent="1.00in"/>
				<fo:region-after extent="1.60in"/>
			</fo:simple-page-master>
			<fo:page-sequence-master master-name="psmA">
				<fo:repeatable-page-master-alternatives>
					<fo:conditional-page-master-reference master-reference="first" page-position="first"/>
					<fo:conditional-page-master-reference master-reference="rest" page-position="rest"/>
					<!-- recommended fallback procedure -->
					<fo:conditional-page-master-reference master-reference="rest"/>
				</fo:repeatable-page-master-alternatives>
			</fo:page-sequence-master>
		</fo:layout-master-set>
	</xsl:template>
	<xsl:template match="ENTRIES" mode="withamounts_custom">
		<xsl:if test="child::UIQTY!=0">
			<xsl:variable name="theprice">
				<xsl:call-template name="zeropadwithdollarandprecision">
					<xsl:with-param name="data" select="child::UIPRICE"/>
				</xsl:call-template>
			</xsl:variable>
			<xsl:variable name="thecalculatedvalue">
				<xsl:call-template name="zeropadwithdollar">
					<xsl:with-param name="data" select="child::UIVALUE"/>
				</xsl:call-template>
			</xsl:variable>
			<fo:table-row line-height="10pt">
				<xsl:call-template name="tableentry_item">
					<xsl:with-param name="data" select="child::ITEMID"/>
				</xsl:call-template>
				<xsl:call-template name="tableentry_itemdesc">
					<xsl:with-param name="data" select="child::ITEMDESC"/>
					<xsl:with-param name="data2" select="child::EXTENDED_DESCRIPTION"/>
					<xsl:with-param name="data3" select="child::MEMO"/>
					<xsl:with-param name="data4" select="child::STOCK_NUMBER"/>
				</xsl:call-template>
				<xsl:call-template name="tableentry_unit">
					<xsl:with-param name="data" select="child::UNIT"/>
				</xsl:call-template>
				<xsl:call-template name="tableentry_quantity">
					<xsl:with-param name="data" select="child::UIQTY"/>
				</xsl:call-template>
				<xsl:call-template name="tableentry_price">
					<xsl:with-param name="data" select="$theprice"/>
				</xsl:call-template>
				<xsl:call-template name="tableentry_value">
					<xsl:with-param name="data" select="$thecalculatedvalue"/>
				</xsl:call-template>
			</fo:table-row>
		</xsl:if>
	</xsl:template>
	<xsl:template name="footer_custom">
		<xsl:param name="pagenum"/>
		<xsl:call-template name="footerline_custom"/>
		<fo:block-container height="0.25in" width="7.5in" top="1.25in" left="0.0in" position="absolute">
			<fo:table table-layout="fixed" height="0.25in">
				<fo:table-column column-width="7.50in"/>
				<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="8pt">
					<fo:table-row line-height="11pt">
						<fo:table-cell>
							<fo:block text-align="end">
								<xsl:call-template name="footerpageof"/>
							</fo:block>
						</fo:table-cell>
					</fo:table-row>
				</fo:table-body>
			</fo:table>
		</fo:block-container>
	</xsl:template>
	<xsl:template name="footerline_custom">
		<fo:block-container height="0.20in" width="7.50in" top="1.10in" left="0.00in" position="absolute">
			<fo:block>
				<fo:leader leader-pattern="rule" rule-thickness="0.003in"/>
			</fo:block>
		</fo:block-container>
	</xsl:template>
	<xsl:template name="message_custom">
		<fo:block-container height="1.00in" width="3.50in" top="0.2in" position="absolute">
			<fo:table table-layout="fixed" height="1.00in" top="0.00m" width="3.50in" border-color="black" border-style="solid" border-width="0.05pt">
				<fo:table-column column-width="3.50in"/>
				<fo:table-body font-family="{$myfont}">
					<fo:table-row line-height="11pt" border-color="black" border-style="solid" border-width="0.05pt">
						<fo:table-cell>
							<fo:block space-before="4pt" margin="2pt" text-align="start" font-style="bold" font-size="11pt" white-space-collapse="false">Special Instructions &amp; License Number:</fo:block>
						</fo:table-cell>
					</fo:table-row>
									
					<fo:table-row>
						<fo:table-cell line-height="11pt">
							<fo:block space-before="4pt" margin="5pt" text-align="start" font-size="9pt" white-space-collapse="false">
								<xsl:value-of select="REC/CUSTVEND/COMMENTS"/>
							</fo:block>
						</fo:table-cell>
					</fo:table-row>
				</fo:table-body>
			</fo:table>
		</fo:block-container>
	</xsl:template>
	<xsl:template name="footerpageof">
		<fo:inline font-weight="bold">
			<xsl:text>Page </xsl:text>
			<fo:page-number/>
		</fo:inline>
	</xsl:template>
	<xsl:template name="titleorderduedate_custom">
		<xsl:call-template name="generictitle_custom">
			<xsl:with-param name="label" select="'Due Date'"/>
			<xsl:with-param name="data" select="REC/WHENDUE"/>
			<xsl:with-param name="position" select="'2.2cm'"/>
		</xsl:call-template>
	</xsl:template>
</xsl:stylesheet>
