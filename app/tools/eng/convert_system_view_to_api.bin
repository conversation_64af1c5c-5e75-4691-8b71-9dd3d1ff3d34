#!/usr/local/php/bin/php -q
<?php

$entity = $argv[1];
if ( empty($entity) ) {
    die("ERROR: Entity name is missing\n");
}
define('INTACCTCONTEXT', "true");

chdir(dirname($argv[0]));
$linkParent = wheresLinkLive();
// print "--- $linkParent ---\n";
// Must be one level below link for cfg paths to work
chdir('./' . $linkParent . 'link/www');
// ini_set('include_path', ini_get('include_path') . ":../private/inc:../source/*:../private/lib/log4php:../private/lib/php4:.:../private/lib/misc");

require_once('util.inc');
InitGlobalConstants();

if (Globals::$g->islive) {
    die("ERROR: This tool is unavailable in Production");
}

/**
 * @return string  the relative path of the link directory's parent
 */
function wheresLinkLive() {
    if (file_exists("./link")) {
        $indirection = "./";
    } elseif (file_exists("../../link")) {
        $indirection = "../../";
    } elseif (file_exists("../../../link")) {
        $indirection = "../../../";
    } else if (file_exists("../../../../link")) {
        $indirection = "../../../../";
    } else {
        die("ERROR: can't find common files in ../../ or ../../..");
    }
    ini_set('include_path', ini_get('include_path') . ":"
                            . $indirection . "link/private/inc:"
                            . $indirection . "source/*:"
                            . $indirection . "link/private/lib/log4php:"
                            . $indirection . "link/private/lib/php4:.:"
                            . $indirection . "link/private/lib/misc:"
                            . $indirection . "link/private/lib/nusoap:"
                            . $indirection . "link/private/lib/sforcev2:"
    );
    return $indirection;
}

try {
    $mgr = Globals::$g->gManagerFactory->getManager($entity);
    if ( !($mgr) ) {
        die("ERROR: Entity name $entity is not found\n");
    }
    $schema = $mgr->getWholeSchema();
    $toConvert[APIConstants::USER_VIEW_OBJECT_NAME] = [];
    // use 'filterset' to convert
    $contexts = [];
    foreach ( $schema['filtercontexts']['system'] as $context => $details ) {
        foreach ( $details['views'] as $detail ) {
            // add at the beginning
            $item = [
                GenericViewAdapter::FV_KEY => $detail[UIFilterViewDialect::FILTER_VIEW_ID_KEY],
                GenericViewAdapter::FV_OBJECT_KEY => $entity,
            ];
            foreach ($detail as $key=>$value) {
                if ($key === 'queryParameters') {
                    $key = GenericViewAdapter::FV_QUERY_KEY;
                } else if ($key === UIFilterViewDialect::FILTER_VIEW_ID_KEY|| $key === UIFilterViewDialect::OWNER_KEY) {
                    continue; // skip
                }
                $item[strtoupper($key)] = $value;
            }
            $toConvert[APIConstants::USER_VIEW_OBJECT_NAME][] = $item;
        }
        $contexts[] = $context;
    }
    $payload = [
        APIConstants::API_VERSION_KEY      => '0',
        ConverterServiceOrchestrator::DATA => $toConvert,
    ];
    // print_r($payload);
    $parameters = [
        'sessionId'     => 'xxx',
        'senderId'      => 'yyyy',
        'url'           => 'v0/services/converter/toapi',
        'httpMethod'    => 'POST',
        'body'          => json_encode($payload),
    ];
    $disp = APIDispatcher::getDispatcher($parameters);
    $res = $disp->dispatch();
    $resAsArray = json_decode($res, true);
    if ($resAsArray[APIConstants::IA_META_KEY][APIConstants::API_TOTAL_ERROR_KEY] !== 0) {
        print "\n " . json_encode($resAsArray[APIConstants::IA_RESULT_KEY], 128) . "\n";
        die("ERROR: Converter failed\n");
    }
    // return back as system-filterset
    $fs[APIConstants::SYSTEM_VIEW_OBJ_NAME] =
        $resAsArray[APIConstants::IA_RESULT_KEY][ConverterServiceOrchestrator::DATA][APIConstants::USER_VIEW_OBJECT_NAME];
    foreach ( $fs[APIConstants::SYSTEM_VIEW_OBJ_NAME] as &$item) {
        unset($item[APIConstants::API_HREF_FIELD_NAME]);
        $item[APIConstants::CONTEXTS_KEY] = $contexts;
    }
    if ($argv[2] === "yaml") {
        $linkParent = wheresLinkLive();
        $parseLib = $linkParent . 'link/private/lib/symfony-yaml/';
        foreach (glob($parseLib . '*.php') as $incl) {
            require_once($incl);
        }
        print Symfony\Component\Yaml\Yaml::dump($fs, 256);
    } else {
        print "\n" . json_encode($fs, 128) . "\n";
    }
} catch (Throwable $e) {
    if ($e instanceof APIException && $e->hasAPIError()) {
        die("ERROR: " . $e->getAPIError()->getMessage() . "\n");
    }
    die("ERROR: " . $e->getMessage() . "\n");
}
