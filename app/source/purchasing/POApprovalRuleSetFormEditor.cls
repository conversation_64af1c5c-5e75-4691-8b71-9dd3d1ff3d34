<?php
/**
 * Purchasing POApprovalRuleSet Form Editor
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Intacct Corporation, All Rights Reserved
 */
class POApprovalRuleSetFormEditor extends ApprovalRuleSetFormEditor
{
    /**
     * @param array $_params the initialization parameters of the class
     */
    public function __construct($_params)
    {
        parent::__construct($_params);
    }


    /**
     * getEntityData
     *    helper function to get data from entity manager and format the data for display
     *
     * @param string   $entity  entity or object
     * @param string   $objId   entityid or objectid
     * @param string   $doctype
     * @param string[] $fields
     *
     * @return array|bool  returns the formatted result set
     */
    protected function getEntityData($entity, $objId, $doctype='', $fields=null)
    {
        // Get the data
        $entityData = parent::getEntityData($entity, $objId);

        //From Approval policy rulesetname will be passed
        //So get recordno from name and get ruleset values
        if( !isset($entityData) || empty($entityData)){
            /* @var POApprovalRuleSetManager $mgr*/
            $mgr = $this->getEntityMgr();
            assert($mgr instanceof POApprovalRuleSetManager);
            $entityData = $mgr->getEntityData($objId);
        }

        // If we have no ruleset recordno, no need to continue
        if (empty($entityData['RECORDNO'])) {
            return $entityData;
        }

        $gManagerFactory = Globals::$g->gManagerFactory;
        $poRuleMgr = $gManagerFactory->getManager('poapprovalrule');
        $ruleDetails = $poRuleMgr->getRuleDetails($entityData['RECORDNO']);

        $entityData['RULES_LIST'] = $ruleDetails['DEPT_RULES'];
        $entityData['RULE_DETAILS'] = $ruleDetails['DEFAULT_RULE']['RULE_DETAILS'];

        return $entityData;
    }

    /**
     * This is a hook function to adjust the data before it is beeing passed to the manager to create a new instance
     * of the object
     *
     * @param array $_params the metadata
     * @param array $obj the data
     * @param bool $ok false if error else true
     *
     * @return bool false if error else true
     */
    protected function innerProcessCreateAction(&$_params, &$obj, $ok)
    {
        $ownerid = 'ProjectEditor::innerProcessCreateAction';
        $origValues = $obj;
        $ok = $this->getEntityMgr()->beginTrx($ownerid);
        $ok = $ok && parent::innerProcessCreateAction($_params, $obj, $ok);
        $rule = $this->processRuleData($obj);

        $gManagerFactory = Globals::$g->gManagerFactory;
        $poRuleMgr = $gManagerFactory->getManager('poapprovalrule');
        $ok = $ok && $poRuleMgr->add($rule);
        // If this object has smart rule applied to it, we should probably clear the cache here again (even though it
        // is done above in the parent call of the function), as it might result in a loop
        if ($ok) {
            // Clear the warning cache struncture WHEN operation is successful
            Globals::$g->gErr->warningsCache->clear();
        }

        $ok = $ok && $this->getEntityMgr()->commitTrx($ownerid);
        if ($ok) {
            $this->state = $this->kGoBackState;
        } else {
                $this->state = $this->kErrorState;
                $this->getEntityMgr()->rollbackTrx($ownerid);
        }

        $entityDesc = ($_params['entityDesc']) ?: "record" ;
        $ok = $this->innerFinishCreateAction($_params, $obj, $origValues, $entityDesc, $ok);

        return $ok;
    }

    /**
     * @param array $_params
     * @param array $obj
     * @param bool  $ok
     *
     * @return bool true if success or false if failure
     */
    protected function innerProcessSaveAction(&$_params, &$obj, $ok)
    {
        $ownerid = 'RuleSetFormEditor::innerProcessCreateAction';
        $ok = $this->getEntityMgr()->beginTrx($ownerid);
        $ok = $ok && parent::innerProcessSaveAction($_params, $obj, $ok);
        $gManagerFactory = Globals::$g->gManagerFactory;
        $poRuleMgr = $gManagerFactory->getManager('poapprovalrule');

            if (count($obj['RULE_DETAILS']) > 0) {
                $ruleKey = $obj['RULE_DETAILS'][0]['RULEKEY'];
                if ($ruleKey == null) {
                    // Add default rule to an empty ruleset
                    $rule = $this->processRuleData($obj);
                    $ok = $ok && $poRuleMgr->add($rule);
                } else {
                    // update existing default rule details
                    $defaultRule = $poRuleMgr->get($ruleKey);
                    $defaultRule['RULE_DETAILS'] = $obj['RULE_DETAILS'];
                    $ok = $ok && $poRuleMgr->set($defaultRule);
                }
            }

        $ok = $ok && $this->getEntityMgr()->commitTrx($ownerid);
        if ($ok) {
            $this->state = $this->kCloseState;
        } else {
            $this->getEntityMgr()->rollbackTrx($ownerid);
            $this->state = $this->kErrorState;
        }
        return $ok;
    }

    /**
     * Get the Add URL for the grid
     *
     * @param string $entity the entity
     * @param array  $object the data
     *
     * @return string the URL
     */
    public function getGridAddUrl($entity, $object)
    {
        $url = parent::GetGridAddUrl($entity, $object);
        if ( $url != '' && $entity == 'poapprovalrule' ) {
            $url .= '&.rulesetkey=' . $object['RECORDNO'] . '&.rulesetname=' . urlencode($object['RULESETNAME']);
        }
        return $url;
    }

    /**
     * Returns javascript files used
     *
     * @return array|null
     */
    protected function getJavaScriptFileNames()
    {
        return array('../resources/js/poapprovalrule.js');
    }
    protected function getFormTokens() : array
    {
        $this->textTokens = array_merge($this->textTokens,[ 'IA.USER_LEVEL', 'IA.USER_GROUP_LEVEL' ] );
        return parent::getFormTokens();
    }
}