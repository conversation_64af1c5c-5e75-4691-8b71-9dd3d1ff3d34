<?xml version='1.0' encoding='utf-8'?>
<ROOT>
  <entity>invdocument</entity>
  <title>IA.INVENTORY_DOCUMENT</title>
  <view system="true">
    <pages id="mainPages">
      <page id="mainPage" title="IA.TRANSACTION">
        <child>
          <section id="trxSummary" className="horizontal"
          customFields="no">
            <field path="SUMMARY.WHENCREATED"
            fullname="IA.TRANSACTION_DATE" required="false"
            readonly="true" clazz="TrxSummaryField">
              <type type="date" ptype="date" />
            </field>
            <field path="SUMMARY.ITEMS" fullname="IA.ITEM_TOTALS"
            clazz="TrxSummaryField" readonly="true"
            nullValue="0.00">
              <type type="decimal" ptype="decimal" />
            </field>
            <field path="SUMMARY.SUBTOTAL" fullname="IA.SUBTOTALS"
            clazz="TrxSummaryField" readonly="true"
            nullValue="0.00">
              <type type="decimal" ptype="decimal" />
            </field>
            <field path="SUMMARY.TOTAL"
            fullname="IA.TRANSACTION_TOTAL" clazz="TrxSummaryField"
            readonly="true" nullValue="0.00">
              <type type="decimal" ptype="decimal" />
            </field>
            <field path="SUMMARY.STATE"
            fullname="IA.TRANSACTION_STATUS" readonly="true"
            clazz="TrxSummaryField"></field>
          </section>
        </child>
        <child>
          <section isCollapsible="true" id="mainSection">
            <field path="ADJDOCHDRID" userUIControl="AdjDocPicker">
                <events>
                    <change>RefreshOnSourceTransSelect('Populate', this.value, true);</change>
                </events>
            </field>
            <field path="WHENCREATED">
              <events>
                <change>AutoPopulateDate(this)</change>
              </events>
            </field>
            <field path="DOCNO">
              <events>
                <change>GetDuplicateObjectVal(this)</change>
              </events>
            </field>
            <field>DOCID</field>
            <field>CREATEDFROM</field>
            <field path="CREATEDFROMLINK" readonly="true"
            fullname="" href="javascript:void(0);" hidden="true">
              <events>
                <click>convertFromClicked(); false;</click>
              </events>
            </field>
            <field>WHENPOSTED</field>            
            <field>PONUMBER</field>
            <!--<field>VENDORDOCNO</field>-->
            <field>MESSAGE</field>
            <field fullname="IA.ATTACHMENT">SUPDOCID</field>
            <!--  
                                <field>WAREHOUSE.LOCATIONID</field>
                                                <field>DEPARTMENT</field>
                                                <field>LOCATION</field>
                                                -->
            <field>NOTE</field>
            <field path="STATE">
              <events change="UpdateTrxSummary(this)" />
            </field>
            <field hidden="true">BASECURR</field>
            <field hidden="true">PRCLINENUM</field>
            <field hidden="true">RECURRINGSCHEDULE</field>
            <field hidden="true">DOCPARID</field>
            <field hidden="true">SCHOPKEY</field>
          </section>
        </child>
        <child>
          <grid helpText="" clazz="EntriesGrid"
          className="columns3Grid" allowEditPage="true">
            <path>ENTRIES</path>
            <title>IA.ENTRY_ROWS</title>
            <column>
              <field path="RECORDNO" hidden="true" />
            </column>
            <column>
              <field path="ITEMID" clazz="ItemField">
                <events>
                  <change>AutoFillItem(this)</change>
                </events>
              </field>
            </column>
            <column>
              <field path="WAREHOUSE.LOCATION_NO" ignoreLargeDataPicker="true"
              fullname="IA.WAREHOUSE" clazz="WarehouseField">
                <events>
                  <change>AutoFillWarehouse(this)</change>
                </events>
              </field>
            </column>
            <column>
              <field path="UIQTY">
                <events>
                  <change>AutoFillUIQty(this);</change>
                </events>
              </field>
            </column>
            <column>
              <field path="UNIT" clazz="UnitField">
                <events>
                  <change>AutoFillUnitFactor(this)</change>
                </events>
              </field>
            </column>
            <column>
              <field path="QTY_CONVERTED"></field>
            </column>
            <column>
              <field path="UIPRICE" fullname="IA.COST"
              clazz="CostField">
                <events>
                  <change>AutoFillPrice(this)</change>
                </events>
              </field>
            </column>
            <column>
              <field path="UIVALUE" hasTotal="true"
              fullname="IA.EXTENDED_COST" clazz="CostField">
                <events>
                  <change>AutoFillValue(this)</change>
                </events>
              </field>
            </column>
            <column>
              <field path="ITEMCURRENT" fullname="IA.CURRENT_ITEM"
              hidden="true"></field>
            </column>
            <column>
              <field path="PRODLINE" fullname="IA.PRODUCT_LINE"
              hidden="true"></field>
            </column>
            <column className='center'>
              <field path="ITEM.TAXABLE" fullname="IA.IS_TAXABLE"
              hidden="true"></field>
            </column>
            <column>
              <field path="ITEM.UOMGRPKEY" hidden="true"></field>
            </column>
            <column>
              <field path="ITEM.TAXGROUP.RECORDNO"
              fullname="IA.TAX_GROUP" hidden="true"></field>
            </column>
            <column>
              <field path="ITEMGLGROUP" fullname="IA.ITEM_GL_GROUP"
              hidden="true"></field>
            </column>
            <column>
              <field path="SOURCE_DOCKEY" fullname="IA.SOURCE_DOC_KEY"
              hidden="true"></field>
            </column>
            <column>
              <field path="SOURCE_DOCLINEKEY"
              fullname="IA.SOURCE_DOC_LINE_KEY" hidden="true"></field>
            </column>
            <column>
              <field path="SOURCE_DOCID" fullname="IA.SOURCE_DOC_KEY"
              hidden="true"></field>
            </column>
            <column assoc="T">
              <field path="COST_METHOD" fullname="IA.COST_METHOD"
              hidden="true"></field>
            </column>
            <column>
              <field path="ENABLESNO" fullname="IA.ENABLE_SERIAL_NO"
              hidden="true"></field>
            </column>
            <lineDetails id="itemDetails" className="columns3"
            customFields="ENTRIES">
              <pages>
                <page title="IA.DETAILS">
                  <section id="detailsPage" title="IA.DETAILS"
                  columnCount="3">
                    <child>
                      <field path="ITEMDESC" autofill="true" />
                    </child>
                    <child>
                      <field path="MEMO" autofill="true" />
                    </child>
                  </section>
                  <section id="linksSection" title="IA.LINKS"
                  columnCount="3">
                    <child>
                      <link fullname="IA.AVAILABLE_QUANTITIES"
                      href="javascript:void(0);" id="AVAILLINK"
                      clazz="DELink">
                        <events>
                          <click>AvailableClick(this);</click>
                        </events>
                      </link>
                    </child>
                    <child>
                      <link fullname="IA.COST"
                      href="javascript:void(0);" id="COSTLINK"
                      hidden="true">
                        <events>
                          <click>CostReportClick(this);</click>
                        </events>
                      </link>
                    </child>
                    <child>
                      <link fullname="IA.PICK"
                      href="javascript:void(0);" id="PICKLIST"
                      hidden="true" clazz="DELink">
                        <events>
                          <click>PickListReport(this);</click>
                        </events>
                      </link>
                    </child>
                  </section>
                  <section id="dimPage" title="IA.DIMENSIONS"
                  dimFields="ENTRIES" columnCount="3">
                    <field hidden="true">LOCATION</field>
                    <field hidden="true">DEPARTMENT</field>
                  </section>
                  <section id="TrackingInfoButttonSection">
                    <button id="trackingButton"
                    clazz="SerialButton">
                      <name>IA.SERIAL_LOT_AND_BINS</name>
                      <path>trackingButton</path>
                      <events>
                        <click>showTrackingPage(this);</click>
                      </events>
                    </button>
                  </section>
                  <section id="TrackingInfo" title="IA.TRACKING_INFO"
                  clazz="TrackingInfoSection" hidden="true">
                    <grid clazz="TrackingGrid">
                      <path>TRACKINGENTRIES</path>
                      <caption className="float_left"
                      id="TrackingDetailsCaption"
                      isCollapsible="false"></caption>
                      <column>
                        <field>
                          <path>ITEMID</path>
                        </field>
                      </column>
                      <column>
                        <field>
                          <path>TRACK_QUANTITY</path>
                        </field>
                      </column>
                      <column>
                        <field>
                          <path>SERIALNO</path>
                        </field>
                      </column>
                      <column>
                        <field>
                          <path>LOTNO</path>
                        </field>
                      </column>
                      <column>
                        <field>
                          <path>EXPIRATION</path>
                        </field>
                      </column>
                      <column>
                        <field>
                          <path>AISLEID</path>
                        </field>
                      </column>
                      <column>
                        <field>
                          <path>ROWID</path>
                        </field>
                      </column>
                      <column>
                        <field>
                          <path>BINID</path>
                        </field>
                      </column>
                    </grid>
                  </section>
                </page>
              </pages>
            </lineDetails>
          </grid>
        </child>
        <child>
          <grid helpText="" hasFixedNumOfRows="true"
          noDragDrop="true">
            <path>SUBTOTALS</path>
            <title>IA.SUBTOTALS</title>
            <caption className="float_left" id="subtotalCaption"
            isCollapsible="false">
              <button id="subtotalCalculateButton">
                <name>IA.CALCULATE_SUBTOTALS</name>
                <events>
                  <click>ReCalculateClick();</click>
                </events>
              </button>
            </caption>
            <column>
              <field path="PERCENTEDIT" hidden="true" />
            </column>
            <column>
              <field path="DESCRIPTION" />
            </column>
            <column>
              <field path="PERCENTVAL" clazz="SubtotalField" />
            </column>
            <column>
              <field path="ABSEDIT" hidden="true" />
            </column>
            <column>
              <field path="ABSVAL" clazz="SubtotalField" />
            </column>
            <column>
              <field path="TOTAL" />
            </column>
            <column>
              <field path="TRX_ABSVAL" hidden="true"
              clazz="SubtotalField" />
            </column>
            <column>
              <field path="TRX_TOTAL" hidden="true"
              clazz="SubtotalField" />
            </column>
            <column>
              <field path="DISPLAY_BASECURRENCY" hidden="true"
              clazz="SubtotalField" />
            </column>
            <column>
              <field path="DEPARTMENT" hidden="true"
              clazz="SubtotalDimensionField" />
            </column>
            <column>
              <field path="LOCATION" hidden="true"
              clazz="SubtotalDimensionField" />
            </column>
            <column>
              <field path="BASELINE" hidden="true" />
            </column>
            <column>
              <field path="DISC_CHARGE" hidden="true" />
            </column>
            <column>
              <field path="APPORTIONED" hidden="true" />
            </column>
            <column>
              <field path="GLACCOUNTKEY" hidden="true" />
            </column>
            <column>
              <field path="DEB_CRED" hidden="true" />
            </column>
            <column>
              <field path="ISTAX" hidden="true" />
            </column>
            <column>
              <field path="TAXDETAIL" hidden="true" />
            </column>
            <lineDetails id="subtotalDetails" dimFields="SUBTOTALS"
            className="columns3" customFields="SUBTOTALS"
            clazz="SubtotalsLineDetails">
              <pages>
                <page title="IA.DETAILS">
                  <section id="subtotalDimPage" title="IA.DIMENSIONS"
                  columnCount="3"></section>
                </page>
              </pages>
            </lineDetails>
          </grid>
        </child>
      </page>
      <!-- rpn -->
      <page id="postingDetailsPage" title="IA.POSTING_DETAILS" readonly="true" hidden="true">
            <xi:include href="glposting_grid.xml" xmlns:xi="http://www.w3.org/2003/XInclude"/>
      </page>
      <!-- rpn -->
      <page id="historyPage" title="IA.HISTORY" readonly="true">
        <child>
          <section id="secondSection" columnCount="2">
            <field>VIRTUALWHENCREATED</field>
            <field>CREATEDUSERID</field>
            <field>AUWHENCREATED</field>
            <field>USERID</field>
            <field>WHENMODIFIED</field>
            <field>VIRTUALDOCNO</field>
            <field>PRINTED</field>
            <field>DATEPRINTED</field>
            <field>PRINTEDUSERID</field>
            <field>RECORDNO</field>
          </section>
        </child>
        <child>
          <grid hasFixedNumOfRows="true" readonly="true">
            <path>HISTORY</path>
            <title>IA.HISTORY</title>
            <column>
              <field path="DOCUMENTCREATED" />
            </column>
            <column>
              <field path="DOCUMENTCREATEDBY" />
            </column>
            <column>
              <field path="DOCUMENTMODIFIED" />
            </column>
            <column>
              <field path="DOCUMENTMODIFIEDBY" />
            </column>
            <column>
              <field path="DOCUMENTID" />
            </column>
            <column>
              <field path="SOURCEDOCUMENTID">
                <events>
                  <click>ShowHistory(this, 'HISTORY_TAB')</click>
                </events>
              </field>
            </column>
            <column>
              <field path="DOCCONFIG" />
            </column>
            <column>
              <field path="DOCSTATE" />
            </column>
            <column>
              <field path="DUMMYSOURCEMENU" />
            </column>
          </grid>
        </child>
        <child>
          <grid>
            <norefreshlink>1</norefreshlink>
            <path>RECURHISTORY</path>
            <title>IA.LINE_ITEM_CONVERSION_TEMPLATES</title>
            <hidden>1</hidden>
            <column>
              <field path="DOCTYPE" />
            </column>
            <column>
              <field path="RECURRECNO" fullname="" />
            </column>
            <column>
              <field path="RECURTRANSACTION" fullname="" />
            </column>
            <column>
              <field path="LINENO" />
            </column>
            <column>
              <field path="ITEMKEY" />
            </column>
            <column>
              <field path="MEMO" />
            </column>
            <column>
              <field path="UIVALUE" />
            </column>
            <column>
              <field path="ADJDOCHDRKEY" />
            </column>
            <column>
              <field path="ADJDOCENTRYKEY" />
            </column>
          </grid>
        </child>
        <child>
          <grid hasFixedNumOfRows="true" readonly="true" hidden="true">
            <path>ADJHISTORY</path>
            <title>IA.ADJUSTMENT_HISTORY</title>
            <column>
              <field path="WHENCREATED" />
            </column>
            <column>
              <field path="VIRTUALDOCID" />
            </column>
            <column>
              <field path="CREATEDUSERID" />
            </column>
            <column>
              <field path="WHENMODIFIED"/>
            </column>
            <column>
              <field path="USERID" />
            </column>
            <column>
              <field path="TOTAL" />
            </column>
          </grid>
        </child>
        <child>
          <grid hasFixedNumOfRows="true" readonly="true" hidden="true">
            <path>SUPPLIESHISTORY</path>
            <title>IA.SUPPLIES_HISTORY</title>
            <column>
              <field path="ITEM" fullname="IA.ITEM">
                <type type="href" ptype="href"/>
              </field>
            </column>
            <column>
              <field path="EMPLOYEE" fullname="IA.EMPLOYEE"/>
            </column>
            <column>
              <field path="SUPPLYDOCUMENT" fullname="IA.TRANSACTION">
                <type type="href" ptype="href"/>
              </field>
            </column>
            <column>
              <field path="LINE_NO" fullname="IA.LINE_NO" />
            </column>
            <column>
              <field path="QUANTITY" fullname="IA.QUANTITY"/>
            </column>
            <column>
              <field path="DATE" fullname="IA.DATE"/>
            </column>
            <column>
              <field path="STATUS" fullname="IA.STATUS"/>
            </column>
            <column>
              <field path="ACTIVE" fullname="IA.ACTIVE"/>
            </column>
          </grid>
        </child>
      </page>
    </pages>
    <!-- Begin: Floating page for Tracking info  -->
    <floatingPage move="true" resize="true" close="true"
    fixedcenter="true" modal="true" width="900" height="250">
      <title>IA.SERIAL_LOT_AND_BIN_INFORMATION</title>
      <id>Tracking_Folat_Page</id>
      <path>FLOAT_TRACKINGENTRIES_PAGE</path>
      <pages>
        <page>
          <section id="main2" columnCount="3" className="columns3">
            <field fullname="IA.LINE_NO">LINEITEMNO</field>
            <field fullname="IA.ITEM_ID">LINEITEMID</field>
            <field fullname="IA.QUANTITY">LINEITEMQTY</field>
            <field fullname="IA.WAREHOUSE" hidden="true">
            LINEITEMWAREHOUSE</field>
          </section>
          <grid clazz="TrackingGrid">
            <title>IA.SERIAL_LOT_AND_BIN_INFORMATION</title>
            <path>FLOAT_TRACKINGENTRIES_GRID</path>
            <column>
              <field path="ITEMID" hidden="true"></field>
            </column>
            <column>
              <field fullname="IA.QUANTITY" hasTotal="true">
                <path>TRACK_QUANTITY</path>
              </field>
            </column>
            <column>
              <field fullname="IA.SERIAL_NUMBER">
                <path>SERIALNO</path>
              </field>
            </column>
            <column>
              <field fullname="IA.LOT_NUMBER">
                <path>LOTNO</path>
              </field>
            </column>
            <column>
              <field fullname="IA.EXPIRATION_DATE">
                <path>EXPIRATION</path>
              </field>
            </column>
            <column assoc="T">
              <field fullname="IA.AISLE" userUIControl="ARBControl">
                <path>AISLEID</path>
              </field>
            </column>
            <column assoc="T">
              <field fullname="IA.ROW" userUIControl="ARBControl">
                <path>ROWID</path>
              </field>
            </column>
            <column assoc="T">
              <field fullname="IA.BIN" userUIControl="ARBControl">
                <path>BINID</path>
              </field>
            </column>
          </grid>
        </page>
      </pages>
      <footer>
        <button>
          <name>IA.DONE</name>
          <events>
            <click>applyTrackingDetails();</click>
          </events>
        </button>
        <button>
          <name>IA.CANCEL</name>
          <events>
            <click>hideTrackingPage();</click>
          </events>
        </button>
      </footer>
    </floatingPage>
    <!-- End : Floating page for Tracking info  -->

  <floatingPage modalsize="small" id="Warnforwarehouseqty" close="true" fullscreen="false">
    <pages>
      <page>
        <section>
          <field noLabel="1" isHTML="1" path="QXMESSAGE" component="msgcomp" textSize="medium">
            <type type='msgcomp' ptype='msgcomp' ></type>
          </field>
        </section>
      </page>
    </pages>
    <footer>
      <button>
        <name>IA.OK</name>
        <events>
          <click>closeFloatingPage('Warnforwarehouseqty', false);</click>
        </events>
      </button>
    </footer>
  </floatingPage>

  </view>
  <helpfile>Create_Inventory_Transaction_Redirect</helpfile>
</ROOT>
