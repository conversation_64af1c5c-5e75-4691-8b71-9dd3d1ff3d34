<?php

/**
 * Util for common methods in report execution
 *
 * <AUTHOR>
 * @copyright Copyright (C)2024 Sage Intacct Corporation, All Rights Reserved
 */
class APIReportUtil
{

    public static function getRecordForRequest(array $request, EntityManager $mgr) : array
    {
        if ( ! ( isset($request[APIReportConstants::REPORTID_FIELD_NAME]) ) ) {
            throw ( new APIException() )->setAPIError(APIError::getInstance(
                APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0205,
                [ "PARAMETER" => APIReportConstants::REPORTID_API_FIELD_NAME ], true));
        }

        $reportId = $request[APIReportConstants::REPORTID_FIELD_NAME];
        $record = $mgr->get($reportId);
        if ( empty($record) ) {
            throw ( new APIAdapterRecordKeyException() )->setAPIError(APIError::getInstance(
                APIErrorMessages::NOT_FOUND_KEY_ISSUE_004, [ "REPORT_ID" => $reportId ],
                true));
        }
        return $record;
    }
}