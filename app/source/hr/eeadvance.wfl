<?

/*
 *
 * FILE:        eeadvance.wfl
 * AUTHOR:      <PERSON>
 * DESCRIPTION: EE Advance workflow structure
 *
 *	(C) 2001, Intacct Corporation, All Rights Reserved
 *
 *	This document contains trade secret data that belongs to Intacct
 *	Corporation and is protected by the copyright laws.  Information
 *	herein may not be used, copied or disclosed in whole or in part
 *	without prior written consent from Intacct Corporation.
 */

/** @noinspection PhpUndefinedVariableInspection */
if (!is_array($kWorkflow['eppayment'])) {
    include( 'eppayment.wfl' );
}

$kWorkflow['eeadvance'] = $kWorkflow['eppayment'];

$kWorkflow['eeadvance']['eventmanager'] = 'ER';
$kWorkflow['eeadvance']['nomenclature']['documentname'] = 'advance';
$kWorkflow['eeadvance']['notify'] = false;