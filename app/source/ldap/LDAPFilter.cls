<?php

/**
 * Parse an LDAP filter string into a PHP data structure easier to process.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Intacct Corporation, All Rights Reserved
 */

/**
 * Class LDAPFilter parses an OpenLDAP filter string into a data structure easier to process
 */
class LDAPFilter
{

    /**
     * @var string $filterText
     */
    private $filterText;

    /**
     * Parse the filter string into the data structure
     *
     * @param string $filter  the filter text
     *
     * @return array  the data structure representing the filter
     */
    public function parseFilter($filter)
    {
        $this->filterText = $filter;
        return $this->extractExpression(0, isl_strlen($filter) - 1);
    }

    /**
     * Extract expressions out of a filter text fragment
     *
     * @param int $start  the start index in the filter text
     * @param int $end    the end index in the filter text
     *
     * @return array  the list of expressions extracted
     *
     * @throws Exception - if the filter text is not correct
     */
    private function extractExpression($start, $end)
    {
        if ($start == $end) {
            // the expression is empty - we're done
            return null;
        }
        if ($this->filterText[$start] != '(' || $this->filterText[$end] != ')') {
            throw new Exception("Incorrect filter: unmatched parenthesis at index 0");
        }
        $start++;
        $end--;

        $expressions = [];

        if ($this->filterText[$start] == '&') {
            // we have an AND expression
            // let's parse all the inner expressions
            $newStart = $start + 1;
            do {
                if ($this->filterText[$newStart] != '(') {
                    throw new Exception("Incorrect filter: expected parenthesis at index $newStart");
                }
                $k = $newStart;
                $parenCount = 1;
                do {
                    $k++;
                    if ($this->filterText[$k] == ')') {
                        $parenCount--;
                    } else if ($this->filterText[$k] == '(') {
                        $parenCount++;
                    }
                } while ($parenCount > 0 && $k < $end);

                if ($parenCount > 0) {
                    // we could not find the close parenthesis for the sub expression
                    throw new Exception("Incorrect filter: unmatched open parenthesis at index $newStart");
                }
                $newEnd = $k;
                $expressions = array_append($expressions, $this->extractExpression($newStart, $newEnd));

                $newStart = $k + 1;
            } while ($newStart < $end);
        } else {
            // we assume it is just a simple equality expression attribute = value
            $exprText = isl_substr($this->filterText, $start, $end - $start + 1);
            $eqPos = strpos($exprText, '=');
            if ($eqPos === false) {
                throw new Exception("Incorrect filter: expected to find equality operator after index $start");
            }
            $attribute = isl_substr($exprText, 0, $eqPos);
            if ($attribute[0] == '?') {
                // this is probably the "presence" filter - let's remove the "?" character
                $attribute = isl_substr($attribute, 1);
            }
            $value = isl_substr($exprText, $eqPos + 1);
            $expressions[$attribute] = $value;
        }

        return $expressions;
    }
}