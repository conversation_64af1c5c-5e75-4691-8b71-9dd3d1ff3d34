
title: accounts-receivable-invoice-tax-entry
x-mappedTo: arinvoicetaxentry
x-ownedBy: accounts-receivable/invoice-line
type: object
description: For VAT enabled transactions, invoice line items will have tax entries.
allOf:
  - $ref: ../../common/models/tax-entries.s1.schema.yaml
  - type: object
    properties:
      orderEntryTaxDetail:
        type: object
        description: Order Entry tax details describe a specific type of tax that applies to lines in Accounts Receivable transactions.
        x-object: tax/order-entry-tax-detail
        properties:
          key:
            type: string
            description: System-assigned key for the tax detail.
            x-mappedTo: DETAILKEY
            example: '1'
          id:
            type: string
            description: Unique ID for the tax detail.
            x-mappedTo: DETAILID
            example: Alaska Tax Detail
          href:
            type: string
            description: URL endpoint for the tax detail object.
            readOnly: true
            example: /objects/tax/order-entry-tax-detail/1
      invoiceLine:
        title: invoice-line
        description: Line item that the tax entries are associated with.
        readOnly: true
        type: object
        x-mappedTo: arinvoiceitem
        x-object: accounts-receivable/invoice-line
        properties:
          id:
            type: string
            description: Unique ID for the invoice line object.
            example: '100'
            readOnly: true
            x-mappedTo: PARENTENTRY
          key:
            type: string
            description: Unique key for the invoice line object.
            example: '100'
            readOnly: true
            x-mappedTo: PARENTENTRY
          href:
            type: string
            description: URL endpoint for the invoice line object.
            readOnly: true
            example: /objects/accounts-receivable/invoice-line/100