<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
<xsl:include href="../../private/xslinc/report_helpers.xsl"/> 

<xsl:include href="../../private/xslinc/stylegroup_inc.xsl"/> 
<xsl:include href="../../private/xslinc/inventoryjs_inc.xsl"/>

	<!-- This module supports PHP code intended specifically for invvaluation_stdxml. -->
	<!-- 'usePHPCode' starts as 'N', but the xsl_interface.inc preprocessor may change it to 'Y' -->
	<!-- Sadly, this code must be HERE and not in customreport_inc.xsl, as the XPath code can't reach inside the included file!  Sheesh.  -->
	<xsl:variable name="supportsPHPCode">invvaluation_stdxml_ProcessItems</xsl:variable>
	<xsl:variable name="usePHPCode">N</xsl:variable>

	<xsl:template match="/">
		<xsl:apply-templates/>			
	</xsl:template>
	
	<xsl:template match="reportdata">
		<xsl:apply-templates/>
	</xsl:template>
	

	<xsl:template match="report">
		<report
			showHeader="{@showHeader}"
			department 		= "{@department}"
			location 		= "{@location}"
			orientation 		= "Landscape"
			report_date		= "{@reportdate}"
			report_time		= "{@reporttime}"
			align_currency 		= "left"
			page_number 		= "Y"
			action 			= ""
			sess			= "{@sess}"
			done			= "{@done}"
			footer_allpages	= "Y"
		>


	    <xsl:if test="(@orientation = 'Portrait')">
		    <xsl:attribute name="maxfit">Y</xsl:attribute>
	    </xsl:if>
		<company s="2"><xsl:value-of select="@co"/></company>
		<title s="3" titleNum="1"><xsl:value-of select="@title"/></title>
		<title s="3" titleNum="2"><xsl:value-of select="@title2"/></title>
		<title s="4" otherTitle="As of Date"><xsl:value-of select="@asofdate"/></title>
		<footer s="5" lines="1" footerNum="1"><xsl:value-of select="@titlecomment"/></footer>
        <xsl:for-each select="rtdim">
        	<rtdim s="4" name="@name">
        		<name><xsl:value-of select="@name"/></name>
				<value><xsl:value-of select="@value"/></value>
			</rtdim>
        </xsl:for-each>

        <xsl:call-template name="buildheader"/>

		<body s="body">
            <xsl:if test="number($narrow_format) = 1">
			    <row s="13">
			    	<col  s="19" colspan="{/reportdata/report/@noofcolumns}" ></col>
			    </row>
            </xsl:if>
			<xsl:apply-templates/>
		</body>
		<xsl:call-template name="stylegroups"/>
		<script language="javascript">
			<xsl:apply-templates select="@javascript"/>
			<xsl:call-template name="script"/>		
		</script>
    </report>

	</xsl:template>

    <xsl:template name="buildheader">
		<header >
            <xsl:if test="number($narrow_format) = 1">
			    <hrow s="header">
                <xsl:choose>
			        <xsl:when test="(//report/@summary = 'Item Summary')">
			    	    <hcol width="13"/>
			    	    <hcol width="13" s="txt"/>
			    	    <hcol width="2" s="txt"/>
			    	    <hcol width="2" s="txt"/>
			    	    <hcol width="13" s="txt"/>
			    	    <hcol width="2" s="txt"/>
			    	    <hcol width="2" s="txt"/>
			    	    <hcol width="6" s="txt"/>
			    	    <hcol width="5" s="txt"/>
			    	    <hcol width="5" s="txt"/>
			    	    <hcol width="6" s="txt"/>
			    	    <hcol width="6" s="txt"/>
			    	    <hcol width="5" s="txt"/>
			    	    <hcol width="8" s="txt"/>
			    	    <hcol width="5" s="txt"/>
			    	    <hcol width="7" s="txt"/>
			    	    <hcol width="7" s="txt"/>
                    </xsl:when>
			        <xsl:when test="(//report/@summary = 'Warehouse Summary')">
			    	    <hcol width="7"  />
			    	    <hcol width="13" s="txt"/>
			    	    <hcol width="13" s="txt"/>
			    	    <hcol width="13" s="txt"/>
			    	    <hcol width="2" s="txt"/>
			    	    <hcol width="2" s="txt"/>
			    	    <hcol width="10" s="txt"/>
			    	    <hcol width="2" s="txt"/>
			    	    <hcol width="2" s="txt"/>
			    	    <hcol width="5" s="txt"/>
			    	    <hcol width="4" s="txt"/>
			    	    <hcol width="3" s="txt"/>
			    	    <hcol width="7" s="txt"/>
			    	    <hcol width="7" s="txt"/>
                    </xsl:when>
                    <xsl:otherwise>
			    	    <hcol width="13"/>
			    	    <hcol width="13" s="txt"/>
			    	    <hcol width="6" s="txt"/>
			    	    <hcol width="2" s="txt"/>
			    	    <hcol width="13" s="txt"/>
			    	    <hcol width="6" s="txt"/>
			    	    <hcol width="5" s="txt"/>
			    	    <hcol width="5" s="txt"/>
			    	    <hcol width="6" s="txt"/>
			    	    <hcol width="6" s="txt"/>
			    	    <hcol width="5" s="txt"/>
			    	    <hcol width="8" s="txt"/>
			    	    <hcol width="5" s="txt"/>
			    	    <hcol width="7" s="txt"/>
                    </xsl:otherwise>
                </xsl:choose>
			    </hrow>

            </xsl:if>
			<xsl:choose>
				<xsl:when test="@summary = 'Detail'">
					<hrow s="51"></hrow>
					<hrow s="51">
					<xsl:choose>
						<xsl:when test="(@term_Item = 'IA.UPC')">
							<hcol id="0" s="17">IA.UPC</hcol>
						</xsl:when>
						<xsl:otherwise>
							<hcol id="0" s="17">IA.ITEM_ID</hcol>
						</xsl:otherwise>
					</xsl:choose>
						<!--<hcol id="0" s="17"><xsl:value-of select="@term_Item"/> ID</hcol>-->
						<hcol id="0" s="17">IA.COSTING_METHOD</hcol>
						<hcol id="0" s="17">IA.PRODUCT_LINE</hcol>
						<hcol id="0" s="17" />
						<hcol id="0" s="17" />
						<hcol id="0" s="17" />
						<hcol id="0" s="17" />
						<hcol id="0" s="17" />
						<hcol id="0" s="16" />
						<hcol id="0" s="16" />
						<hcol id="0" s="16" />
						<hcol id="0" s="16" />
						<hcol id="0" s="16" />
						<hcol id="0" s="16" />
					</hrow>
					<hrow s="51">
						<hcol id="0" s="16"></hcol>
						<hcol id="0" s="17">IA.DESCRIPTION</hcol>
						<hcol id="0" s="17">IA.UOM</hcol>
						<hcol id="0" s="17" >IA.WAREHOUSE</hcol>
                        <hcol id="0" s="17" />
						<hcol id="0" s="16" /> 
						<hcol id="0" s="16" /> 
						<hcol id="0" s="16" /> 
						<hcol id="0" s="16" /> 
						<hcol id="0" s="17">IA.DLA</hcol>
						<hcol id="0" s="18">IA.LAST_COST</hcol>
						<hcol id="0" s="18">IA.QOH</hcol>
						<hcol id="0" s="18">IA.UC</hcol>
						<hcol id="0" s="18">IA.VALUE</hcol>
					</hrow>
					<hrow s="51">
						<hcol id="0" s="16" /> 
						<hcol id="0" s="16" /> 
						<hcol id="0" s="16" /> 
						<hcol id="0" s="16" /> 
						<hcol id="0" s="17">IA.TRANSACTION</hcol>
						<hcol id="0" s="17">IA.DATE</hcol>
						<hcol id="0" s="18">IA.QTY</hcol>
						<hcol id="0" s="18">IA.UC</hcol>
						<hcol id="0" s="18">IA.VALUE</hcol>
						<hcol id="0" s="16" /> 
						<hcol id="0" s="16" /> 
						<hcol id="0" s="16" /> 
						<hcol id="0" s="16" /> 
						<hcol id="0" s="16" /> 
					</hrow>
				</xsl:when>
				<xsl:when test="@summary = 'Warehouse Summary'">
					<hrow s="51">
						<xsl:choose>
							<xsl:when test="(@term_Item = 'IA.UPC')">
								<hcol id="0" s="17">IA.UPC</hcol>
							</xsl:when>
							<xsl:otherwise>
								<hcol id="0" s="17">IA.ITEM_ID</hcol>
							</xsl:otherwise>
						</xsl:choose>
						<!--<hcol id="0" s="17"><xsl:value-of select="@term_Item"/> ID</hcol>-->
						<hcol id="0" s="17">IA.COSTING_METHOD</hcol>
						<hcol id="0" s="17">IA.PRODUCT_LINE</hcol>
						<hcol id="0" s="17" />
						<hcol id="0" s="17" />
						<hcol id="0" s="17" />
						<hcol id="0" s="17" />
						<hcol id="0" s="17" />

						<hcol id="0" s="18"></hcol>
						<hcol id="0" s="17">IA.DLA</hcol>
						<hcol id="0" s="74">IA.LAST_COST</hcol>
						<hcol id="0" s="18">IA.QOH</hcol>
						<hcol id="0" s="18">IA.UC</hcol>
						<hcol id="0" s="18">IA.VALUE</hcol>
					</hrow>
					<hrow s="51">
						<hcol id="0" s="16"></hcol>
						<hcol id="0" s="17">IA.DESCRIPTION</hcol>
						<hcol id="0" s="17">IA.UOM</hcol>
						<hcol id="0" s="17">IA.WAREHOUSE</hcol>
						<hcol id="0" s="17"></hcol>
						<hcol id="0" s="17"></hcol>
						<hcol id="0" s="18"></hcol>
						<hcol id="0" s="18"></hcol>
						<hcol id="0" s="18"></hcol>
						<hcol id="0" s="16"></hcol>
						<hcol id="0" s="16"></hcol>
						<hcol id="0" s="16"></hcol>
						<hcol id="0" s="16"></hcol>
						<hcol id="0" s="16"></hcol>
					</hrow>
				</xsl:when>
				<xsl:otherwise>
					<hrow s="51">
						<xsl:choose>
							<xsl:when test="(@term_Item = 'IA.UPC')">
								<hcol id="0" s="17">IA.UPC</hcol>
							</xsl:when>
							<xsl:otherwise>
								<hcol id="0" s="17">IA.ITEM_ID</hcol>
							</xsl:otherwise>
						</xsl:choose>
						<!--<hcol id="0" s="17"><xsl:value-of select="@term_Item"/> ID</hcol>-->
						<hcol id="0" s="17">IA.DESCRIPTION</hcol>
						<hcol id="0" s="17" />
						<hcol id="0" s="17" />
						<hcol id="0" s="17">IA.PROD_LINE</hcol>
						<hcol id="0" s="17" />
						<hcol id="0" s="17" />
						<hcol id="0" s="17" />
						<hcol id="0" s="17">IA.COSTING_METHOD</hcol>
						<hcol id="0" s="74">IA.UOM</hcol>
						<hcol id="0" s="17">IA.DLA</hcol>
						<hcol id="0" s="18">IA.LAST_COST</hcol>
						<hcol id="0" s="18">IA.QOH</hcol>
						<hcol id="0" s="18">IA.UC</hcol>
						<hcol id="0" s="18">IA.VALUE</hcol>
					</hrow>
				</xsl:otherwise>
			</xsl:choose>
            <xsl:if test="number($narrow_format) = 1">
			    <hrow s="14">
			    	<hcol id="0" s="19" colspan="14"></hcol>
			    </hrow>
            </xsl:if>
		</header>
	</xsl:template>

	<xsl:template match="GRANDTOTALTOP">
		<xsl:choose>
			<xsl:when test="(//report/@ISMCMESUBSCR != 'true')">
				<row s="">
					<col id="0" s="59">
                        <xsl:if test="number($narrow_format) = 1">
                            <xsl:attribute name="colspan">5</xsl:attribute>
                        </xsl:if>
						<xsl:call-template name="string-replace">
							<xsl:with-param name="haystack" select="'IA.TOTAL_FOR_ITEMS'" />
							<xsl:with-param name="replace" select="'${ITEMS}'" />
							<xsl:with-param name="by" select="@ITEMCOUNT" />
						</xsl:call-template>
                    </col>

                    <xsl:if test="number($narrow_format) = 0">
                        <col id="0" s="59"/>
                        <col id="0" s="59"/>
                        <col id="0" s="59"/>
                        <col id="0" s="59"/>
                    </xsl:if>

					<col id="0" s="19"></col>
					<col id="0" s="19"></col>
					<col id="0" s="19"></col>
					<col id="0" s="19"></col>
					<col id="0" s="19"></col>
			        <xsl:if test="(//report/@summary = 'Item Summary')">
    					<col id="0" s="19"></col>
                    </xsl:if>

					<col id="0" s="34"></col>
					<col id="0" s="34">
                        <xsl:if test="@COSTEACH != ''">
                            <xsl:attribute name="s">34</xsl:attribute>
                        </xsl:if>
                        <xsl:value-of select="@COSTEACH"/>
                    </col>
					<col id="0" s="34"></col>
					<col id="0" s="63"><xsl:value-of select="@VALUE"/></col>
				</row>
				<row s="">
					<col id="0" s="19" />
					<col id="0" s="19" />
					<col id="0" s="19" />
					<col id="0" s="19" />
					<col id="0" s="19" />
					<col id="0" s="19" />
					<col id="0" s="19" />
					<col id="0" s="19" />
					<col id="0" s="19" />
					<col id="0" s="19" />
				</row>
			</xsl:when>
		</xsl:choose>
	</xsl:template>
	<xsl:template match="GRANDTOTAL">
		<xsl:choose>
			<xsl:when test="(//report/@ISMCMESUBSCR != 'true')">
				<row s="">
					<col id="0" s="59">
                        <xsl:if test="number($narrow_format) = 1">
                            <xsl:attribute name="colspan">5</xsl:attribute>
                        </xsl:if>
						<xsl:call-template name="string-replace">
							<xsl:with-param name="haystack" select="'IA.TOTAL_FOR_ITEMS'" />
							<xsl:with-param name="replace" select="'${ITEMS}'" />
							<xsl:with-param name="by" select="@ITEMCOUNT" />
						</xsl:call-template>
                    </col>
                    <xsl:if test="number($narrow_format) = 0">
                        <col id="0" s="59"/>
                        <col id="0" s="59"/>
                        <col id="0" s="59"/>
                        <col id="0" s="59"/>
                    </xsl:if>
					<col id="0" s="19"></col>
					<col id="0" s="19"></col>
					<col id="0" s="19"></col>
					<col id="0" s="19"></col>
					<col id="0" s="19"></col>
					<col id="0" s="19"></col>
			        <xsl:if test="(//report/@summary = 'Item Summary')">
    					<col id="0" s="19"></col>
                    </xsl:if>
					<col id="0" s="34">
                        <xsl:if test="@COSTEACH != ''">
                            <xsl:attribute name="s">32</xsl:attribute>
                        </xsl:if>
                        <xsl:value-of select="@COSTEACH"/>
                    </col>
					<col id="0" s="34"></col>
					<col id="0" s="32"><xsl:value-of select="@VALUE"/></col>
				</row>
			</xsl:when>
		</xsl:choose>
	</xsl:template>

	<xsl:template match="PHPITEMTRANSFORM">
		<xsl:choose>
			<xsl:when  test="($usePHPCode = 'Y')">
				REPLACE_invvaluation_stdxml_HERE
			</xsl:when>
		</xsl:choose>
	</xsl:template>

	<!-- Do nothing for these. -->
	<xsl:template match="STDPHPITEMS"/>

	<xsl:template match="STDPHPWAREHOUSE"/>

	<xsl:template match="STDPHPTRANSACTION"/>

	<!-- NOTE: PHP transformation supersedes the XSL transformation below for both the 'Details' and         -->
	<!-- 'Warehouse Summary' report formats. Any changes here should be kept in sync with PHP transformation -->
	<!-- effected by InvvaluationReport::invvaluation_stdxml_ProcessItems().                                 -->
	<xsl:template match="ITEMS">
		<xsl:choose>
			<xsl:when test="(//report/@summary != 'Item Summary')">
				<row s="">
					<xsl:choose>
						<xsl:when test="(//report/@term_Item = 'IA.UPC')">
							<col id="0" s="20" href="{@HREF}"><xsl:value-of select="@UPC"/></col>
						</xsl:when>
						<xsl:otherwise>
							<col id="0" s="20" href="{@HREF}"><xsl:value-of select="@ITEMID"/></col>
						</xsl:otherwise>
					</xsl:choose>
					<col id="0" s="24"><xsl:value-of select="@COST_METHOD"/></col>
					<col id="0" s="24">
                        <xsl:if test="number($narrow_format) = 1">
                            <xsl:attribute name="colspan">6</xsl:attribute>
                        </xsl:if>
                        <xsl:value-of select="@PRODUCTLINE"/>
                    </col>
                    <xsl:if test="number($narrow_format) = 0">
					    <col id="0" s="24"/>
					    <col id="0" s="24"/>
					    <col id="0" s="24"/>
					    <col id="0" s="24"/>
					    <col id="0" s="24"/>
                    </xsl:if>

					<col id="0" s="25"></col>
					<col id="0" s="25"></col>
					<col id="0" s="25"></col>
					<col id="0" s="25"></col>
					<col id="0" s="25"></col>
					<col id="0" s="25"></col>
				</row>
				<xsl:apply-templates mode="warehouse"/>
				<xsl:choose>
					<xsl:when test="(//report/@ISMCMESUBSCR != 'true')">
						<row s="">
							<col id="0" s="19"></col>
							<col id="0" s="19"></col>
							<col id="0" s="19"></col>
							<col id="0" s="19"></col>
							<col id="0" s="19"></col>
							<col id="0" s="19"></col>
							<col id="0" s="23" href="{@HREF}">
                                <xsl:if test="number($narrow_format) = 1">
                                    <xsl:attribute name="colspan">3</xsl:attribute>
                                </xsl:if>
							    <xsl:choose>
							    	<xsl:when test="(//report/@term_Item = 'IA.UPC')">
							    		<xsl:value-of select="@UPC"/> IA.SUMMARY
							    	</xsl:when>
							    	<xsl:otherwise>
							    		<xsl:value-of select="@ITEMID"/> IA.SUMMARY
							    	</xsl:otherwise>
							    </xsl:choose>
                            </col>
                            <xsl:if test="number($narrow_format) = 0">
                                <col id="0" s="23"/>
                                <col id="0" s="23"/>
                            </xsl:if>

							<col id="0" s="71"><xsl:value-of select="@DLA"/></col>
							<col id="0" s="62"><xsl:value-of select="@LASTCOST"/></col>
							<col id="0" s="62"><xsl:value-of select="@QUANTITY"/></col>
							<col id="0" s="62"><xsl:value-of select="@COSTEACH"/></col>
							<col id="0" s="30"><xsl:value-of select="@VALUE"/></col>
						</row>
					</xsl:when>
				</xsl:choose>
			</xsl:when>
			<xsl:otherwise>
				<row s="">
					<!--<col id="0" s="20" href="{@HREF}"><xsl:value-of select="@ITEMID"/></col>-->
					<xsl:choose>
						<xsl:when test="(//report/@term_Item = 'IA.UPC')">
							<col id="0" s="20" href="{@HREF}"><xsl:value-of select="@UPC"/></col>
						</xsl:when>
						<xsl:otherwise>
							<col id="0" s="20" href="{@HREF}"><xsl:value-of select="@ITEMID"/></col>
						</xsl:otherwise>
					</xsl:choose>
					<col id="0" s="19"><xsl:value-of select="@DESCRIPTION"/></col>
					<col id="0" s="19" />
					<col id="0" s="19" />
					<col id="0" s="19"><xsl:value-of select="@PRODUCTLINE"/></col>
					<col id="0" s="19" />
					<col id="0" s="19" />
					<col id="0" s="19" />
					<col id="0" s="21"><xsl:value-of select="@COST_METHOD"/></col>
					<col id="0" s="21"><xsl:value-of select="@UOM"/></col>
					<col id="0" s="21"><xsl:value-of select="@DLA"/></col>
					<col id="0" s="22"><xsl:value-of select="@LASTCOST"/></col>
					<col id="0" s="22"><xsl:value-of select="@QUANTITY"/></col>
					<col id="0" s="22"><xsl:value-of select="@COSTEACH"/></col>
					<col id="0" s="34"><xsl:value-of select="@VALUE"/></col>
				</row>
			</xsl:otherwise>
		</xsl:choose>
		<xsl:if test="(//report/@summary != 'Item Summary')">
		    <row s="">
		    	<col id="0" s="19"></col>
		    </row>
        </xsl:if>
	</xsl:template>

	<xsl:template match="WAREHOUSE" mode="warehouse">
		<xsl:choose>
			<xsl:when test="string(*/@DOCNO)">
					<row s="">
						<col id="0" s="24"></col>
						<xsl:choose>
							<xsl:when test="position()='2'">
								<col id="1" s="24">
									<xsl:value-of select="../@DESCRIPTION"/>
								</col>
								<col id="1" s="24">
									<xsl:value-of select="../@UOM"/>
								</col>
							</xsl:when>
							<xsl:otherwise>
								<col id="1" s="24">
								</col>
								<col id="1" s="24">
								</col>
							</xsl:otherwise>
						</xsl:choose>
						<col id="0" s="19">
                            <xsl:if test="number($narrow_format) = 1">
                                <xsl:attribute name="colspan">6</xsl:attribute>
                            </xsl:if>
                            <xsl:value-of select="@NAME"/>
                        </col>
                        <xsl:if test="number($narrow_format) != 1">
						    <col id="0" s="19"></col>
						    <col id="0" s="19"></col>
						    <col id="0" s="19"></col>
						    <col id="0" s="19"></col>
						    <col id="0" s="19"></col>
                        </xsl:if>
						<col id="0" s="19"></col>
						<col id="0" s="19"></col>
						<col id="0" s="19"></col>
						<col id="0" s="19"></col>
						<col id="0" s="19"></col>
					</row>
					<xsl:apply-templates mode="transaction"/>
					<row s="">
						<col id="0" s="24"></col>
						<col id="0" s="24"></col>
						<col id="0" s="24"></col>
						<col id="0" s="19"></col>
						<col id="0" s="19"></col>
						<col id="0" s="19"></col>
						<col id="0" s="19"></col>
						<col id="0" s="19"></col>
						<col id="0" s="19"></col>
						<col id="0" s="21"><xsl:value-of select="@DLA"/></col>
						<col id="0" s="22"><xsl:value-of select="@LASTCOST"/></col>
						<col id="0" s="22"><xsl:value-of select="@QUANTITY"/></col>
						<col id="0" s="22"><xsl:value-of select="@COSTEACH"/></col>
						<col id="0" s="34"><xsl:value-of select="@VALUE"/></col>
					</row>
			</xsl:when>
			<xsl:otherwise>
					<row s="">
						<col id="0" s="24"></col>
						<xsl:choose>
							<xsl:when test="position()='2'">
								<col id="0" s="24">
									<xsl:value-of select="../@DESCRIPTION"/>
								</col>
								<col id="0" s="24">
									<xsl:value-of select="../@UOM"/>
								</col>
							</xsl:when>
							<xsl:otherwise>
								<col id="0" s="24">
								</col>
								<col id="0" s="24">
								</col>
							</xsl:otherwise>
						</xsl:choose>
						<col id="0" s="19">
                            <xsl:if test="number($narrow_format) = 1">
                                <xsl:attribute name="colspan">6</xsl:attribute>
                            </xsl:if>
                            <xsl:value-of select="@NAME"/>
                        </col>
                        <xsl:if test="number($narrow_format) != 1">
						    <col id="0" s="19"></col>
						    <col id="0" s="19"></col>
						    <col id="0" s="19"></col>
						    <col id="0" s="19"></col>
						    <col id="0" s="19"></col>
                        </xsl:if>
						<col id="0" s="21"><xsl:value-of select="@DLA"/></col>
						<col id="0" s="22"><xsl:value-of select="@LASTCOST"/></col>
						<col id="0" s="22"><xsl:value-of select="@QUANTITY"/></col>
						<col id="0" s="22"><xsl:value-of select="@COSTEACH"/></col>
						<col id="0" s="34"><xsl:value-of select="@VALUE"/></col>
					</row>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>


	<xsl:template match="TRANSACTION" mode="transaction">
				<row s="">
					<col id="0" s="24"></col>
					<col id="0" s="24"></col>
					<col id="0" s="24"></col>
					<col id="0" s="22"></col>
					<col id="0" s="19" href="{@HREF}"><xsl:value-of select="@TRANSDESCRIPTION"/></col>
					<col id="0" s="21"><xsl:value-of select="@DATE"/></col>
					<col id="0" s="22"><xsl:value-of select="@QUANTITY"/></col>
					<col id="0" s="22"><xsl:value-of select="@COSTEACH"/></col>
					<col id="0" s="34"><xsl:value-of select="@VALUE"/></col>
					<col id="0" s="24" />
					<col id="0" s="24" />
					<col id="0" s="24" />
					<col id="0" s="24" />
					<col id="0" s="24" />
				</row>
	</xsl:template>
</xsl:stylesheet>
