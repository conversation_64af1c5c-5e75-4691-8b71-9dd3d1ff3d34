<?php

/**
 * Class SforceSyncProcessorContact
 * Class to sync account from Salesforce to Intacct customer
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */


class SforceSyncProcessorContact extends SforceSyncProcessor
{
    /**
     * To generate the contact Id/sequence number
     * LastName,FirstName will be used as auto assigned contact Id
     * if ia_crm_Intacct_Contact_ID__c is not provided in Salesforce by the user.
     *
     * @param array $sfValues
     *
     * @return string
     */
    protected function generatePrimaryFieldValue($sfValues)
    {
        $custSeq = $sfValues['LastName'] . "," . $sfValues['FirstName'];
        return $custSeq;
    }

    /**
     * @param string $action
     *
     * @return bool
     */
    protected function isValidAction(string $action): bool
    {
        $contactSyncOnce = $this->sfpreferences['CONTACTSYNCONCE'] ?? null;
        if ( $contactSyncOnce === 'true' && $action == 'update'){
            $msg = 'Contact is already synced with Intacct. Update sync is disabled, edit Advanced CRM Integration configuration options to update.';
            Globals::$g->gErr->addError(
                'SFDC-1254',
                __FILE__.'.'.__LINE__,
                $msg
            );
            return false;
        }
        return true;
    }
}
