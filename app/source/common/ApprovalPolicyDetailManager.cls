<?php
/**
 * ApprovalPolicyDetailManager manages the one to many relationship between policy and its detail children
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Intacct Corporation, All Rights Reserved
 */

/**
 * Manager class for the ApprovalRuleDetail entity
 */
class ApprovalPolicyDetailManager extends OwnedObjectManager
{
    /**
     * @param array $params the initialization parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }

    /**
     * Validates integrity of entity
     *
     * @param array $values values to validate
     *
     * @return bool true if successful, false otherwise
     */
    protected function validate(/** @noinspection PhpUnusedParameterInspection */ array &$values)
    {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $gErr = Globals::$g->gErr;
        $ok = true;

        // TODO add validations

        return $ok;
    }


    /**
     * Translates id/names to keys for the entity
     *
     * @param array $values values to translate
     *
     * @return bool true if successful, false otherwise
     */
    protected function translate(array &$values)
    {
        $ok = true;

        // save the line order in sequence so that we can guarantee to preserve the order
        $values['SEQNO'] = $values['LINE_NO'] + 1;

        return $ok;
    }

    /**
     * Validate, translate and add a new entity record
     *
     * @param array $values Values of the fields from the form
     *
     * @return bool true if the function did not raise any error
     */
    protected function regularAdd(&$values)
    {
        $source = "ApprovalPolicyDetailManager::add";
        $ok = $this->beginTrx($source);

        $ok = $ok && $this->validate($values);
        $ok = $ok && $this->translate($values);
        $ok = $ok && parent::regularAdd($values);

        $ok = $ok && $this->commitTrx($source);
        if ( !$ok ) {
            if ( !HasWarnings() || HasErrors() ) {
                $gErr = Globals::$g->gErr;
                $msg = 'The system could not create or edit the ApprovalPolicyDetail record.';
                $gErr->addError('', __FILE__ . ':' . __LINE__, $msg);
            }
            $this->rollbackTrx($source);
        } else {
            $this->addApprovalPolicyIntoMetrics($values);
        }

        return $ok;
    }

    /**
     * Modify an existing entity
     *
     * @param array $values the data
     *
     * @return bool false if error else true
     */
    protected function regularSet(&$values)
    {
        $source = "ApprovalPolicyDetailManager::set";
        $ok = $this->beginTrx($source);

        $ok = $ok && $this->validate($values);
        $ok = $ok && $this->translate($values);
        $ok = $ok && parent::regularSet($values);

        $ok = $ok && $this->commitTrx($source);
        if (!$ok) {
            if (!HasWarnings() || HasErrors()) {
                $gErr = Globals::$g->gErr;
                $msg = 'The system could not create the ApprovalPolicyDetails record.';
                $gErr->addError('', __FILE__ . ':' . __LINE__, $msg);
            }
            $this->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * @param array $values
     * @return void
     */
    protected function addApprovalPolicyIntoMetrics(array $values)
    {
    }

}