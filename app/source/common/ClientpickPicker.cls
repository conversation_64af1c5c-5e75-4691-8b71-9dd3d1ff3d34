<?

import('entitymanager');

class ClientpickPicker extends NPicker
{

    /* @var bool $isConsoleTreeEnabled */
    var $isConsoleTreeEnabled = false;

    function __construct()
    {
        $this->isConsoleTreeEnabled = ( IsConsoleTreeEnabled() && IsAnonymousSlideDisabled() ? true : false );

        if ( $this->isConsoleTreeEnabled ) {
            $fields = [ 'TITLE', 'CLIENTENTITYID', 'CLIENTENTITYNAME', 'NAME' ];
        } else {
            $fields = [ 'TITLE', 'NAME', 'DESCRIPTION' ];
        }

        parent::__construct(
            array(
                "titleoverride" => I18N::getSingleToken("IA.SELECT_INSTANCE"),
                "entity" => "clientpick",
                "fields" => $fields,
                "pickfield" => "TITLE",
                "helpfile" => "",
            )
        );
    }

    /**
     * @return array|mixed
     */
    function CalcFiltersLite()
    {
        $nfilters = Lister::CalcFiltersLite();
        $nfilters[] = [ 'USERID', '=', GetMyUserid() ];

        return $nfilters;
    }

    /**
     * @return array
     */
    function BuildQuerySpecAll()
    {
        return $this->BuildQuerySpec();
    }

    /**
     * @param string $i
     * @param string $refr
     *
     * @return string
     */
    function calcSelectUrl($i, /** @noinspection PhpUnusedParameterInspection */ $refr = "")
    {
        $p = &$this->_params; //???
        $t = &$this->table;

        $text = $p['_selectbutton'];
        $refr = '1';

        // while expanding client tree we have to set two values for client.title and client.cliententityid
        if ( $this->isConsoleTreeEnabled && $t[$i]['CLIENTENTITYID'] != '' ) {
            $pickValue = $t[$i][$p['_pickfield']] . MRU_DELIMITOR . $t[$i]['CLIENTENTITYID'];
        } else {
            $pickValue = $t[$i][$p['_pickfield']];
        }

        //  Properly encode to JS-ready (e.g. UTF-8 chars).
        $pick = isl_str_to_js($pickValue);

        $ret = "<a href=\"javascript:SetField('" . $pick . "', '', '$refr')\">" . $text . "</a>";

        return $ret;
    }

}

