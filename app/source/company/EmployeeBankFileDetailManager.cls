<?php

/**
 * EmployeeBankFileDetailManager.cls
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 Intacct Corporation, All Rights Reserved
 */

/**
 * Class EmployeeBankFileDetailManager
 */
class EmployeeBankFileDetailManager extends OwnedObjectManager
{
    /**
     *  API_pruneFieldsOwnedToo
     *   The function API_pruneFields, above, prunes fields and 'LESS_GET_FIELDS' from an item.
     *   This function does that, and then recurses down the tree of 'owned' items and does pruning there as well.
     *   For example, an item record includes itemvendor, itemwarehouseinfo, and so on.  itemwarehouseinfo includes owned records too.
     *   This should be called when you know it won't have bad side effects for clients of existing APIs.
     *
     *   In addition to 'LESS_GET_FIELDS' you can ask for another list of fields to be pruned in the api section of the entity.
     *   This way you can have a list of fields to prune if, say, some feature is off.
     *
     * @param array           $values
     * @param string|string[] $fields
     * @param string          $additionalApiPrune
     *
     * @return array
     */
    public function API_pruneFieldsOwnedToo(&$values, $fields, $additionalApiPrune = '')
    {
        $unmaskach = 'ee/lists/employees/unmaskach';
        if( ! CheckAuthorization(GetOperationId($unmaskach), 1) && isset($values['BANKACCOUNTNUMBER'])) {
            $values['BANKACCOUNTNUMBER'] = getMaskedAccount($values['BANKACCOUNTNUMBER']);
        }
        return $values;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    public function regularAdd(&$values)
    {
        $source = __CLASS__ . "::" . __FUNCTION__;

        $ok = $this->beginTrx($source);
        if ($ok && !empty($this->getEmployeeBankFileSetupValues($values))) {
            $empId = $values['EMPLOYEEID'] ?? $values['EMPLOYEEKEY'];
            Globals::$g->gErr->addIAError('BF-0045', __FILE__ . ":" . __LINE__,
                "Bank file setup for the employee $empId already exists.", ['EMPLOYEEID' => $empId]);
            $ok = false;
        }

        $ok = $ok && parent::regularAdd($values);

        if ($ok) {
            $ok = $this->commitTrx($source);
        } else {
            $this->rollbackTrx($source);
        }

        return $ok;

    }

    /**
     * @param array $values
     *
     * @return array
     */
    protected function getEmployeeBankFileSetupValues(array $values)
    {
        $bankFileSetup = [];
        if($values['EMPLOYEEKEY']) {
            $filters = [
                [ 'EMPLOYEEKEY', '=', $values['EMPLOYEEKEY'] ],
            ];
        } else {
            $filters = [
                [ 'EMPLOYEEID', '=', $values['EMPLOYEEID'] ],
            ];
        }
        $querySpec = [
            'filters' => [
                $filters
            ],
        ];
        $records = $this->GetList($querySpec);
        if ($records[0]) {
            $bankFileSetup = $records[0];
        }
        return $bankFileSetup;
    }

}
