<?php
/**
 */

class RuleErrorArg extends RuleArg
{

    /**
     * Returns the error code associated with this rule error arg.
     *
     * @return string
     */
    public function getError() : string
    {
        return $this->args['code'];
    }

    /**
     * Resolve a value.  For this class, that means returning the appropriate error, if any.
     *
     * @param mixed $value
     *
     * @return mixed
     */
    public function resolveValue($value)
    {
        $errorCode = $this->getError();
        $errorMsg = null;
        if ($errorCode) {
            $msgArgs = $this->args['args'] ?? [];
            $errorMsg = new ErrorMessage("", $errorCode, $msgArgs);
        }
            
        return $errorMsg;
    }

    /**
     * @return string
     */
    public function __toString() : string
    {
        return 'Error code: '.$this->args['code'].', args: '.ppS($this->args);
    }
}
