<find>
	<target-list>
		<target module="co" label="Company" />
		<target key="co/setup/companymessage" label="Company Messages">
			<field-list>
				<field path="PRIORITY" label="Priority" />
				<field path="LASTUPDATED" label="Last Updated" />
				<field path="EXPIREDATE" label="Valid Until" />
				<field path="USERNAME" label="Posted By" />
				<field path="MESSAGE" label="Message Text" />
				<field path="USERID" label="User Id" />
			</field-list>
		</target>
		<target key="co/lists/creditcard" label="Credit Cards">
			<field-list>
				<field path="CARDID" label="Card ID" />
				<field path="DESCRIPTION" label="Description" />
				<field path="CARDNUM" label="Card Number" />
				<field path="CARDTYPE" label="Card Type" />
			</field-list>
		</target>
		<target key="co/lists/department" label="Departments">
			<field-list>
				<field path="DEPARTMENTID" label="Department Id" />
				<field path="TITLE" label="Department Name" />
				<field path="PARENTID" label="Parent ID" />
				<field path="SUPERVISORNAME" label="" />
			</field-list>
		</target>
		<target key="co/lists/location" label="Locations">
			<field-list>
				<field path="LOCATIONID" label="Location ID" />
				<field path="NAME" label="Location Name" />
			</field-list>
		</target>
		<target key="co/lists/employee" label="Employees">
			<field-list>
				<field path="EMPLOYEEID" label="Employee ID" />
				<field path="PERSONALINFO.LASTNAME" label="Last Name" />
				<field path="PERSONALINFO.FIRSTNAME" label="First Name" />
				<field path="PERSONALINFO.CONTACTNAME" label="Primary Contact Name" />
				<field path="PERSONALINFO.PRINTAS" label="Print As" />
			</field-list>
		</target>
		<target key="co/lists/userinfo" label="Users">
			<field-list>
				<field path="LOGINID" label="User ID" />
				<field path="DESCRIPTION" label="User Name" />
				<field path="RECORD#" label="" />
				<field path="ADMIN AD" label="" />
			</field-list>
		</target>
		<target key="co/lists/reportingperiod" label="Reporting Periods">
			<field-list>
				<field path="NAME" label="Name" />
				<field path="HEADER1" label="Column Header" />
				<field path="HEADER2" label="Column Header 2" />
				<field path="START_DATE" label="Start Date" />
				<field path="END_DATE" label="End Date" />
			</field-list>
		</target>
		<target key="co/lists/territory" label="Territories">
			<field-list>
				<field path="TERRITORYID" label="Territory Id" />
				<field path="NAME" label="Territory Name" />
				<field path="PARENT.TERRITORYID" label="Parent ID" />
				<field path="MANAGER.NAME" label="Manager" />
				<field path="STATUS" label="Status" />
			</field-list>
		</target>
		<target key="co/lists/contact" label="Contacts">
			<field-list>
				<field path="CONTACTNAME" label="Contact Name" />
				<field path="PRINTAS" label="Print As" />
				<field path="LASTNAME" label="Last Name" />
				<field path="FIRSTNAME" label="First Name" />
			</field-list>
		</target>
		<target key="co/lists/seqnum" label="Document Numbering">
			<field-list>
				<field path="TITLE" label="Sequence ID" />
				<field path="NEXT" label="Next Number" />
				<field path="WHENUPDATED" label="When Updated" />
			</field-list>
		</target>
		<target module="gl" label="General Ledger" />
		<target key="gl/lists/glaccount" label="Accounts">
			<field-list>
				<field path="ACCOUNTNO" label="Account Number" />
				<field path="TITLE" label="Title" />
				<field path="RECORD#" label="" />
			</field-list>
		</target>
		<target key="gl/lists/glacctgrp" label="Account Groups">
			<field-list>
				<field path="NAME" label="Name" />
				<field path="ASOF" label="" />
				<field path="RECORD#" label="" />
			</field-list>
		</target>
		<target key="gl/lists/journal" label="Journals" >
			<field-list>
				<field path="SYMBOL" label="Symbol" />
				<field path="TITLE" label="Title" />
			</field-list>
		</target>
		<target key="gl/lists/adjjournal" label="Adj Journals" >
			<field-list>
				<field path="SYMBOL" label="Symbol" />
				<field path="TITLE" label="Title" />
			</field-list>
		</target>
		<target key="gl/reports/graphs" label="Graphs">
			<field-list>
				<field path="NAME" label="Name" />
				<field path="TITLE" label="Title" />
				<field path="TITLECOMMENT" label="Title Comment" />
			</field-list>
		</target>
		<target module="ap" label="Accounts Payable" />
		<target key="ap/activities/apbatch" label="Manage Batches">
			<field-list>
				<field path="TITLE" label="Batch Title" />
				<field path="TOTAL" label="Total" />
				<field path="CREATED" label="Date" />
				<field path="OPEN" label="Open/Close" />
			</field-list>
		</target>
		<target key="ap/lists/vendor" label="Vendors">
			<field-list>
				<field path="VENDORID" label="Vendor ID" />
				<field path="NAME" label="Vendor Name" />
				<field path="CONTACTINFO.MAILADDRESS.CITY" label="City" />
				<field path="CONTACTINFO.MAILADDRESS.STATE" label="State" />
				<field path="CONTACTINFO.MAILADDRESS.ZIP" label="Zip" />
				<field path="VENDTYPE" label="Vendor Type ID" />
			</field-list>
		</target>
		<target key="ap/lists/vendtype" label="Vendor Types">
			<field-list>
				<field path="NAME" label="Name" />
				<field path="PARENT.NAME" label="Parent Type" />
				<field path="VENDTYPE.RECORD#" label="" />
			</field-list>
		</target>
		<target key="ap/lists/apterm" label="AP Terms">
			<field-list>
				<field path="NAME" label="Name" />
				<field path="DESCRIPTION" label="Description" />
			</field-list>
		</target>
		<target key="ap/lists/apaccountlabel" label="AP Account Labels">
			<field-list>
				<field path="ACCOUNTNO" label="Account Number" />
				<field path="TITLE" label="Account Title" />
				<field path="ACCOUNTLABEL" label="AP Account Label" />
				<field path="DESCRIPTION" label="Description" />
				<field path="APACCOUNTLABEL.RECORD#" label="" />
			</field-list>
		</target>
		<target key="ap/reports/vendor_aging" label="Vendor Aging Reports">
			<field-list>
				<field path="NAME" label="Name" />
				<field path="TITLE" label="Title" />
				<field path="TITLECOMMENT" label="Title Comment" />
			</field-list>
		</target>
		<target key="ap/reports/vendor_aging/delete" label="Delete Vendor Aging Reports">
			<field-list>
				<field path="NAME" label="Name" />
				<field path="TITLE" label="Title" />
				<field path="TITLECOMMENT" label="Title Comment" />
			</field-list>
		</target>
		<target key="ap/reports/apagegraph" label="Vendor Aging Graphs">
			<field-list>
				<field path="NAME" label="Name" />
				<field path="TITLE" label="Title" />
				<field path="TITLECOMMENT" label="Title Comment" />
			</field-list>
		</target>
		<target key="ap/reports/apagegraph/delete" label="Delete Vendor Aging Graphs">
			<field-list>
				<field path="NAME" label="Name" />
				<field path="TITLE" label="Title" />
				<field path="TITLECOMMENT" label="Title Comment" />
			</field-list>
		</target>
		<target module="ar" label="Accounts Receivable" />
		<target key="ar/activities/arbatch" label="Manage Batches">
			<field-list>
				<field path="TITLE" label="Batch Title" />
				<field path="TOTAL" label="Total" />
				<field path="CREATED" label="Date" />
				<field path="OPEN" label="Open/Close" />
			</field-list>
		</target>
		<target key="ar/lists/customer" label="Customers">
			<field-list>
				<field path="CUSTOMERID" label="Customer ID" />
				<field path="NAME" label="Customer Name" />
				<field path="CONTACTINFO.MAILADDRESS.CITY" label="City" />
				<field path="CONTACTINFO.MAILADDRESS.STATE" label="State" />
				<field path="CONTACTINFO.MAILADDRESS.ZIP" label="Zip" />
				<field path="CUSTTYPE" label="Customer Type ID" />
			</field-list>
		</target>
		<target key="ar/lists/custtype" label="Customer Types">
			<field-list>
				<field path="NAME" label="Customer Type" />
				<field path="PARENT.NAME" label="Parent Type" />
			</field-list>
		</target>
		<target key="ar/lists/arterm" label="AR Terms">
			<field-list>
				<field path="NAME" label="Name" />
				<field path="DESCRIPTION" label="Description" />
			</field-list>
		</target>
		<target key="ar/lists/shipmethod" label="Shipping Methods">
			<field-list>
				<field path="NAME" label="Shipping Method" />
			</field-list>
		</target>
		<target key="ar/lists/araccountlabel" label="AR Account Labels">
			<field-list>
				<field path="ACCOUNTNO" label="Account Number" />
				<field path="TITLE" label="Account Title" />
				<field path="ACCOUNTLABEL" label="AR Account Label" />
				<field path="DESCRIPTION" label="Description" />
			</field-list>
		</target>
		<target key="ar/reports/customeraging_report" label="Customer Aging Reports">
			<field-list>
				<field path="NAME" label="Name" />
				<field path="TITLE" label="Title" />
				<field path="TITLECOMMENT" label="Title Comment" />
			</field-list>
		</target>
		<target key="ar/reports/customeraging_report/delete" label="Delete Customer Aging Reports">
			<field-list>
				<field path="NAME" label="Name" />
				<field path="TITLE" label="Title" />
				<field path="TITLECOMMENT" label="Title Comment" />
			</field-list>
		</target>
		<target key="ar/reports/aragegraph" label="Customer Aging Graph">
			<field-list>
				<field path="NAME" label="Name" />
				<field path="TITLE" label="Title" />
				<field path="TITLECOMMENT" label="Title Comment" />
			</field-list>
		</target>
		<target key="ar/reports/aragegraph/delete" label="Delete Customer Aging Graph">
			<field-list>
				<field path="NAME" label="Name" />
				<field path="TITLE" label="Title" />
				<field path="TITLECOMMENT" label="Title Comment" />
			</field-list>
		</target>
		<target module="ee" label="Employee Expenses" />
		<target key="ee/activities/eebatch" label="Expense Batches">
			<field-list>
				<field path="TITLE" label="Batch Title" />
				<field path="TOTAL" label="Total" />
				<field path="CREATED" label="Date" />
				<field path="OPEN" label="Open/Close" />
			</field-list>
		</target>
		<target key="ee/lists/employee" label="Employees">
			<field-list>
				<field path="EMPLOYEEID" label="Employee ID" />
				<field path="PERSONALINFO.LASTNAME" label="Last Name" />
				<field path="PERSONALINFO.FIRSTNAME" label="First Name" />
				<field path="PERSONALINFO.CONTACTNAME" label="Primary Contact Name" />
				<field path="PERSONALINFO.PRINTAS" label="Print As" />
			</field-list>
		</target>
		<target key="ee/lists/eeaccountlabel" label="Expense Types">
			<field-list>
				<field path="EXPENSETYPE" label="Expense Type" />
				<field path="DESCRIPTION" label="Description" />
				<field path="ACCOUNTNO" label="Account Number" />
			</field-list>
		</target>
		<target module="cs" label="Consolidation" />
		<target key="cs/setup/subsidiary" label="Link Subsidiaries">
			<field-list>
				<field path="SUBSIDIARYID" label="Subsidiary ID" />
				<field path="SUBSIDIARYNAME" label="Subsidiary Name" />
				<field path="LOCATION.LOCATIONID" label="Subsidiary Location ID" />
				<field path="DEFAULTPCT" label="Default Percentage Ownership (20 - 100)" />
				<field path="SUBSIDIARYKEY" label="" />
			</field-list>
		</target>
		<target key="cs/setup/cnsrequests" label="Data Consolidation Status">
			<field-list>
				<field path="REPORTINGPERIOD.NAME" label="Reporting Period Name" />
				<field path="STARTTIME" label="Request Started" />
				<field path="ENDTIME" label="Request Completed" />
				<field path="CHILDCOUNT" label="Requests In Process" />
			</field-list>
		</target>
		<target key="cs/setup/glbudgettype" label="Reporting Periods">
			<field-list>
				<field path="NAME" label="Name" />
				<field path="HEADER1" label="Header 1" />
				<field path="START_DATE" label="Start Date" />
				<field path="END_DATE" label="End Date" />
			</field-list>
		</target>
		<target key="cs/lists/cnsperiod" label="Subsidiary Reporting Periods">
			<field-list>
				<field path="SUBPERIODNAME" label="Subsidiary Reporting Period Name" />
				<field path="REPORTINGPERIOD.NAME" label="Parent Reporting Period Name" />
			</field-list>
		</target>
		<target key="cs/lists/cnsaccount" label="Subsidiary Accounts">
			<field-list>
				<field path="SUBACCOUNTNO" label="Subsidiary Account No" />
				<field path="SUBTITLE" label="Subsidiary Account Title" />
				<field path="GLACCOUNT.ACCT_NO" label="Parent Account No" />
			</field-list>
		</target>
		<target key="cs/lists/cnsdepartment" label="Subsidiary Departments">
			<field-list>
				<field path="SUBDEPTNO" label="Subsidiary Department ID" />
				<field path="SUBTITLE" label="Subsidiary Department Name" />
				<field path="DEPARTMENT.DEPARTMENTID" label="Parent Department ID" />
			</field-list>
		</target>
		<target key="cs/lists/cnslocation" label="Subsidiary Locations">
			<field-list>
				<field path="SUBLOCATIONNO" label="Subsidiary Location ID" />
				<field path="SUBNAME" label="Subsidiary Location Name" />
				<field path="LOCATION.LOCATIONID" label="Parent Location ID" />
			</field-list>
		</target>
		<target module="hrp" label="HR Compliance" />
		<target module="dec" label="The Online 401(k)" />
		<target module="mp" label="My Practice" />
		<target key="mp/setup/companymessage" label="Practice Messages">
			<field-list>
				<field path="PRIORITY" label="Priority" />
				<field path="LASTUPDATED" label="Last Updated" />
				<field path="EXPIREDATE" label="Valid Until" />
				<field path="USERNAME" label="Posted By" />
				<field path="MESSAGE" label="Message Text" />
				<field path="USERID" label="User Id" />
			</field-list>
		</target>
		<target key="mp/lists/creditcard" label="Credit Cards">
			<field-list>
				<field path="CARDID" label="Card ID" />
				<field path="DESCRIPTION" label="Description" />
				<field path="CARDNUM" label="Card Number" />
				<field path="CARDTYPE" label="Card Type" />
			</field-list>
		</target>
		<target key="mp/lists/userinfo" label="Users">
			<field-list>
				<field path="LOGINID" label="User ID" />
				<field path="DESCRIPTION" label="User Name" />
				<field path="RECORD#" label="" />
				<field path="ADMIN AD" label="" />
			</field-list>
		</target>
		<target key="mp/lists/contact" label="Contacts">
			<field-list>
				<field path="CONTACTNAME" label="Contact Name" />
				<field path="PRINTAS" label="Print As" />
				<field path="LASTNAME" label="Last Name" />
				<field path="FIRSTNAME" label="First Name" />
			</field-list>
		</target>
		<target module="cm" label="My Clients" />
	</target-list>
</find>
