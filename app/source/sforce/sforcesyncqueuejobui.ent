<?php

/**
 * File description
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Intacct Corporation, All Rights Reserved
 */

require 'fifodispatcherqueueui.ent';
require 'sforcequeue.ent';

$kSchemas['sforcesyncqueuejobui'] = $kSchemas['fifodispatcherqueueui'];
unset($kSchemas['sforcesyncqueuejobui']['ownedobjects']);
$kSchemas['sforcesyncqueuejobui'] = EntityManager::inheritEnts($kSchemas['sforcequeue'], $kSchemas['sforcesyncqueuejobui']);
$kSchemas['sforcesyncqueuejobui']['printas'] = 'IA.SFORCE_EVENT_JOBS';
$kSchemas['sforcesyncqueuejobui']['pluralprintas'] = 'IA.SFORCE_EVENT_JOBS';
