openapi: 3.0.0
info:
  title: order-entry-txn-definition-cogs-gl-detail
  description: order-entry.txn-definition-cogs-gl-detail API
  version: '1.0'
  contact:
    name: <PERSON><PERSON>
    email: An<PERSON>.<PERSON><EMAIL>
tags:
  - name: Transaction definition COGS details
    description: |
     This object holds the COGS account and Inventory account mapping for the owning transaction definition. This account mapping is supported if:

      * Your company subscribes to Inventory Control. 
      * Your company does not subscribe to Inventory Control, but `enableCosting` is set to `true` in the owning transaction definition.

     The item GL group, warehouse, and customer GL group together form the criteria set for the account mapping. For more information, see [Transaction definitions - Order Entry](https://www.intacct.com/ia/docs/en_US/help_action/Default.htm#cshid=Order_Entry_transaction_definitions) in the Sage Intacct Help Center.  
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/order-entry/txn-definition-cogs-gl-detail:
    get:
      summary: List transaction definition COGS detail objects
      description: Returns a collection with a key, ID, and link for each transaction definition COGS detail object. This operation is mostly for use in testing; use query to find objects that meet certain criteria and to specify properties that are returned.
      x-documentationFlags:
        subscription: Order Entry
        userPermissions:
          - userType: Business, Employee, Project Manager, Warehouse
            permissions: List, View, transaction definitions
      tags:
        - Transaction definition COGS details
      operationId: list-order-entry-txn-definition-cogs-gl-detail
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of order-entry-txn-definition-cogs-gl-detail objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List transaction definition COGS detail objects:
                  value:
                    ia::result:
                      - key: '159'
                        id: '159'
                        href: "/objects/order-entry/txn-definition-cogs-gl-detail/159"
                      - key: '160'
                        id: '160'
                        href: "/objects/order-entry/txn-definition-cogs-gl-detail/160"
                    ia::meta:
                      totalCount: 2
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  /objects/order-entry/txn-definition-cogs-gl-detail/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the transaction definition COGS detail object.
        example: '160'
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a transaction definition COGS detail object
      description: Returns detailed information for a specified transaction definition COGS detail object.
      x-documentationFlags:
        subscription: Order Entry
        userPermissions:
          - userType: Business, Employee, Project Manager, Warehouse
            permissions: List, View, transaction definitions
      tags:
        - Transaction definition COGS details
      operationId: get-order-entry-txn-definition-cogs-gl-detail-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the order-entry-txn-definition-cogs-gl-detail
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/order-entry-txn-definition-cogs-gl-detail'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a transaction definition COGS detail object:
                  value:
                    ia::result:
                      key: '160'
                      id: '160'
                      order-entry-txn-definition:
                        key: '43'
                        id: Sales Invoice
                        href: "/objects/order-entry/txn-definition/43"
                      itemGLGroup:
                        key: '16'
                        id: HW GL Group
                        href: "/objects/inventory-control/item-gl-group/16"
                      txnType: debit
                      moduleType: inventory
                      glAccount:
                        id: '5000'
                        key: '72'
                        href: "/objects/general-ledger/account/72"
                      lineNumber: 11
                      href: "/objects/order-entry/txn-definition-cogs-gl-detail/160"
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a transaction definition COGS detail object
      description: Updates an existing transaction definition COGS detail object by setting field values. Any fields not provided remain unchanged.
      x-documentationFlags:
        subscription: Order Entry
        userPermissions:
          - userType: Business, Employee, Project Manager, Warehouse
            permissions: List, View, Edit transaction definitions
      tags:
        - Transaction definition COGS details
      operationId: update-order-entry-txn-definition-cogs-gl-detail-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/order-entry-txn-definition-cogs-gl-detail'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Updates a transaction definition COGS detail object:
                value:
                  txnType: credit
                  isOffset: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated order-entry-txn-definition-cogs-gl-detail
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to updated transaction definition COGS detail object:
                  value:
                    ia::result:
                      key: '191'
                      id: '191'
                      href: "/objects/order-entry/txn-definition-cogs-gl-detail/191"
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a transaction definition COGS detail object
      description: Deletes a transaction definition COGS detail object.
      x-documentationFlags:
        subscription: Order Entry
        userPermissions:
          - userType: Business, Employee, Project Manager, Warehouse
            permissions: List, View, Delete transaction definitions
      tags:
        - Transaction definition COGS details
      operationId: delete-order-entry-txn-definition-cogs-gl-detail-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    order-entry-txn-definition-cogs-gl-detail:
      $ref: ../models/order-entry.txn-definition-cogs-gl-detail.s1.schema.yaml
    order-entry-txn-definition-cogs-gl-detailRequiredProperties:
      required:
        - id
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml