title: sodocument
type: object
x-mappedTo: sodocument
properties:
  customer:
    type: object
    properties:
      key:
        type: string
        description: Customer key
        x-mappedTo: CUSTREC
        readOnly: true
      id:
        type: string
        description: Customer Id
        x-mappedTo: CUSTVENDID
        x-mutable: false
      name:
        type: string
        description: Customer Name
        x-mappedTo: CUSTVENDNAME
  documentRevised:
    type: boolean
    description: Document has a new revision
    x-mappedTo: HASPOSTEDREVREC
  priceList:
    type: object
    description: Price List
    x-object: inventory-price-list
    x-mappedTo: VSOEPRICELIST
    properties:
      key:
        type: string
        x-mappedTo: VSOEPRCLSTKEY
        readOnly: true
      id:
        type: string
        x-mappedTo: VSOEPRICELIST
      href:
        type: string
        readOnly: true
  contract:
    type: object
    properties:
      id:
        type: string
        description: Contract ID
        x-mappedTo: CONTRACTID
      name:
        type: string
        description: Contract description
        x-mappedTo: CONTRACTDESC
  invoice:
    type: object
    properties:
      key:
        type: string
        description: Purchase invoice key
        x-mappedTo: PRRECORDKEY
      href:
        type: string
  invoiceDate:
    type: string
    description: Original invoice date
    x-mappedTo: ORIGDOCDATE
  allowAdvancedBilling:
    type: boolean
    description: Has advanced billing
    x-mappedTo: HASADVBILLING
  INVOICERUN_EXPENSEPRICEMARKUP:
    type: string
    description: ???
    x-mappedTo: INVOICERUN_EXPENSEPRICEMARKUP
  INVOICERUN_DESCRIPTION:
    type: string
    description: ???
    x-mappedTo: INVOICERUN_DESCRIPTION
  dimensions:
    type: object
    properties:
      project:
        type: object
        x-object: project
        properties:
          key:
            type: string
            description: Project Key
            x-mappedTo: PROJECTKEY
          id:
            type: string
            description: Project ID
            x-mappedTo: PROJECT
          name:
            type: string
            description: Project Name
            x-mappedTo: PROJECTNAME
  serviceDeliveryDate:
    type: string
    description: Service delivery date
    format: date
    x-mappedTo: SERVICEDELIVERYDATE
  trackingNumber:
    type: string
    description: Tracking number
    x-mappedTo: TRACKINGNUMBER
  shipByDate:
    type: string
    description: Ship By date
    format: date
    x-mappedTo: SHIPBYDATE
  shippedDate:
    type: string
    description: Shipped on date
    format: date
    x-mappedTo: SHIPPEDDATE
  customerPONumber:
    type: string
    description: Customer PO Number
    x-mappedTo: CUSTOMERPONUMBER
