<?xml version="1.0" encoding='UTF-8'?>
<ROOT assoc="T">
	<entity>apbill</entity>
	<title>IA.BILL</title>
	<pages>
		<page assoc="T">
			<title>IA.HEADER</title>
			<fields>
				<field assoc="T">
					<path>WHENCREATED</path>
				</field>
				<field assoc="T">
					<path>VENDOR</path>
				</field>
				<field assoc="T">
					<path>RECORDID</path>
				</field>
				<field assoc="T">
					<path>WHENPOSTED</path>
				</field>
				<field assoc="T">
					<path>STATE</path>
				</field>
				<field assoc="T">
					<path>DOCNUMBER</path>
				</field>
				<field assoc="T">
					<path>DESCRIPTION2</path>
				</field>
				<field assoc="T">
					<path>DESCRIPTION</path>
				</field>
				<field assoc="T">
					<path>TERMNAME</path>
				</field>
				<field assoc="T">
					<path>WHENDUE</path>
				</field>
				<field assoc="T">
					<path>PRBATCH</path>
				</field>
				<field assoc="T">
					<path>RECPAYMENTDATE</path>
				</field>
				<field assoc="T">
					<path>PAYMENTPRIORITY</path>
				</field>
				<field assoc="T">
					<path>ONHOLD</path>
				</field>
				<field assoc="T">
					<path>ALLOCATIONHDR</path>
				</field>
				<field assoc="T">
					<path>CURRENCY</path>
				</field>
				<field assoc="T">
					<path>EXCHRATEDATE</path>
				</field>
				<field assoc="T">
					<path>EXCHRATETYPE</path>
				</field>
				<field assoc="T">
					<path>EXCHRATE</path>
				</field>
				<field assoc="T">
					<path>TOTALENTERED</path>
				</field>
				<field assoc="T">
					<path>TOTALPAID</path>
				</field>
				<field assoc="T">
					<path>TOTALDUE</path>
				</field>
				<field assoc="T">
					<path>TRX_TOTALENTERED</path>
				</field>
				<field assoc="T">
					<path>TRX_TOTALPAID</path>
				</field>
				<field assoc="T">
					<path>TRX_TOTALDUE</path>
				</field>
				<field assoc="T">
					<path>WHENPAID</path>
				</field>

				<MultilineLayout assoc="T" key="field">
					<path>PRENTRY</path>
					<title>IA.ITEMS</title>
					<columns>
						<vbox assoc="T">
							<_func>vbox</_func>
							<_args>
								<_arg assoc="T">
									<path>LINE_NO</path>
									<hidden>1</hidden>
								</_arg>
							</_args>
						</vbox>
						<column assoc="T">
							<path>GLACCOUNT</path>
							<autofill>1</autofill>
						</column>
						<column assoc="T">
							<path>FORM1099</path>
						</column>
						<column assoc="T">
							<path>TRX_AMOUNT</path>
						</column>
						<column assoc="T">
							<path>AMOUNT</path>
						</column>
						<column assoc="T">
							<path>ALLOCATION</path>
						</column>
						<column assoc="T">
							<path>ENTRYDESCRIPTION</path>
						</column>
					</columns>
					<_func>MultilineLayout</_func>
				</MultilineLayout>
				
			</fields>
		</page>
		<page assoc="T">
			<title>IA.ADDITIONAL_INFORMATION</title>
			<fields>
				<field assoc="T">
					<path>WHENMODIFIED</path>
				</field>
				<field assoc="T">
					<path>SUPDOCID</path>
				</field>
				<SinglelineLayout assoc="T"  key="hbox" >
				    <fullname>IA.PAY_TO_CONTACT</fullname>
				    <columns>
					<column assoc="T" >
					    <path>PAYTOCONTACTNAME</path>
					</column>
					<column assoc="T" >
					    <path>DUMMYSPACE</path>
					</column>
					<column assoc="T" >
					    <path>GETLATESTPAYTO</path>
					</column>
				    </columns>
				    <_func>SinglelineLayout</_func>
				</SinglelineLayout>
				<SinglelineLayout assoc="T"  key="hbox" >
				    <fullname>IA.RETURN_TO_CONTACT</fullname>
				    <columns>
					<column assoc="T" >
					    <path>RETURNTOCONTACTNAME</path>
					</column>
					<column assoc="T" >
					    <path>DUMMYSPACE</path>
					</column>
					<column assoc="T" >
					    <path>GETLATESTRETURNTO</path>
					</column>
				    </columns>
				    <_func>SinglelineLayout</_func>
				</SinglelineLayout>
			</fields>
		</page>
	</pages>
	<helpfile>Editing_Bill_Information</helpfile>

</ROOT>
