#Makefile.in
#

ENTITY_XMLS=                           \
	$(EMPTY)

ENTITY_ENTS=                           \
        digitalnetworksyncqueue.ent \
        digitalnetworksyncqueuehistory.ent \
        digitalnetworksetup.ent \
        stxsetup.ent \
        dnsyncsetup.ent \
        podigitalnetworksyncqueue.ent \
        podigitalnetworksyncqueuehistory.ent \
        cashflowdigitalnetworksyncqueue.ent \
        cashflowdigitalnetworksyncqueuehistory.ent \
	$(EMPTY)

QUERY_OBJECTS=$(ENTITY_ENTS:.ent=.qry)


#
# no generated queries, but custom queries:
#   (list the custom query files for which no .qry file is generated from a .ent file)
#
CUSTOM_ONLY_QUERIES=    \
	$(EMPTY)


LAYOUT_XMLS=                           \
	$(EMPTY)

LAYOUT_PHPS=$(LAYOUT_XMLS:.xml=.php)
	
LAYOUT_XSLS=$(LAYOUT_XMLS:.xml=.xsl)

OTHER_DEPENDENCIES=$(BUILD_DIR)security_new.inc

include ../Makefile.in
