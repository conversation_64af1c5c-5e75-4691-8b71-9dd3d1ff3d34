function loadProcessingType()
{
    var modeFlag = document.getElementById(".modeflag").value;
    var asyncSerialRow = document.getElementById("asyncSerialRow");
    var serialized = document.getElementById(".serialized");
    var asyncFullvalidationRow = document.getElementById('asyncFullvalidationRow');
    var fullvalidation = document.getElementById('.fullvalidation');

    if (modeFlag == 'A') {
        asyncSerialRow.style.display = "table-row";
        asyncFullvalidationRow.style.display = 'table-row';

    } else {
        asyncSerialRow.style.display = "none";
        serialized.value = 'F';

        asyncFullvalidationRow.style.display = 'none';
        fullvalidation.value = 'F';
    }
}

function loadProtocolType()
{
    var protocolType = document.getElementById(".protocol").value;
    var urlEncodeRow = document.getElementById("urlEncodeRow");
    var urlencode = document.getElementById(".urlencode");

    // todo - There is a possible UE improvement <PERSON> has suggested is to move
    //  URL Encoding to the right of Protocol dropdown as a check box
    if (protocolType == 'HTTP' || protocolType == 'HTTPS') {
        urlEncodeRow.style.display = "table-row";
        urlencode.value = 'T';
    } else {
        urlEncodeRow.style.display = "none";
        urlencode.value = 'F';
    }
}


/**
 * Password field cosmetic changes as per the user selection
 */
function updatePasswordField()
{
    var pwdBlankEle  = document.getElementsByName('passwordblank')[0];
    var changepwdEle = document.getElementsByName('changepwd')[0];
    var passwdEle    = document.getElementsByName('.password')[0];
    if ( changepwdEle.checked ) {
        passwdEle.disabled = false;
        passwdEle.value = "";
    } else {
        passwdEle.disabled = true;
        passwdEle.value = pwdBlankEle.value == "1" ? "" : "******";
    }
}

/**
 * Change password confirmation message - stop accidental change of the same
 *
 * @return bool
 */
function confirmTransportPolicyPasswordChange()
{
    var changepwdEle = document.getElementsByName('changepwd')[0];
    var warningMessage = "Do you really want to set the transport policy password?";
    if ( changepwdEle.checked ) {
        if (confirm(warningMessage)) {
            return true;
        } else {
            return false;
        }
    }
}
