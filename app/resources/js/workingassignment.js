/*
 * Functions to support Working Checklist Assignment
 */

/**
 * Validate the planned dates and the actual dates intervals
 * @returns {boolean}
 */
function validateWorkingAssignmentUI() {
    var returnValue = true;
    var assignmentsCount = 1;
    var errorMessageObj = { message : '' };
    var linesWithErrors = [];

    var plannedStartDate = window.editor.findComponents('PLANNEDSTARTDATE');
    var plannedEndDate = window.editor.findComponents('PLANNEDENDDATE');

    var actualStartDate = window.editor.findComponents('ACTUALSTARTDATE');
    var actualEndDate = window.editor.findComponents('ACTUALENDDATE');

    if ( plannedStartDate.length === plannedEndDate.length &&
         plannedStartDate.length === actualEndDate.length &&
         plannedStartDate.length > 1
    ) {
        assignmentsCount = plannedStartDate.length - 1; // the grid always has one dummy line, so remove it
    }

    for ( i=0; i < assignmentsCount; i++) {
        validDates = true;
        if ( plannedStartDate && plannedEndDate ) {
            validDates = validateStartAndEndDates(plannedStartDate[i], plannedEndDate[i], GT("IA.START_DATE"),
                                                  GT("IA.A_DUE_DATE"), errorMessageObj, '');
        }
        if ( validDates && actualStartDate && actualEndDate ) {
            validDates = validateStartAndEndDates(actualStartDate[i], actualEndDate[i], GT("IA.ACTUAL_START_DATE"),
                                                   GT("IA.AN_ACTUAL_END_DATE"), errorMessageObj, '');
        }
        if ( validDates && plannedStartDate && actualEndDate ) {
            validDates = validateStartAndEndDates(plannedStartDate[i], actualEndDate[i], GT("IA.START_DATE"),
                                                  GT("IA.AN_ACTUAL_END_DATE"), errorMessageObj, '');
        }
        if ( validDates && actualStartDate && plannedEndDate ) {
            validDates = validateStartAndEndDates(actualStartDate[i], plannedEndDate[i], GT("IA.ACTUAL_START_DATE"),
                                                  GT("IA.A_DUE_DATE"), errorMessageObj, '');
        }
        if ( validDates === false ) {
            returnValue = false;
            linesWithErrors.push(i+1);
        }
    }
    if ( returnValue === false ) {
        if ( assignmentsCount === 1) {
            alert(errorMessageObj.message);
        } else if ( assignmentsCount > 1 ) {
            var lineString = linesWithErrors.length === 1 ? GT("IA.INVALID_DATES_ON_ASSIGNMENT_GRID_LINE") : GT("IA.INVALID_DATES_ON_ASSIGNMENT_GRID_LINES");
            alert(errorMessageObj.message + ' ' + lineString + ' '  + linesWithErrors.toString() + ".");
        }
    }
    return returnValue;
}

/**
 * Compare start date and end date and show an alert when start date is after end date.
 *
 * @param startDate
 * @param endDate
 * @param startDateLabel The actual name to be printed in the alert message (apply gettext on the string before passing
 *     it to this method)
 * @param endDateLabel   The actual name to be printed in the alert message (apply gettext on the string before passing
 *     it to this method)
 * @param returnedErrMessage The message returned after validation
 * @param errMsgObj
 * @returns {boolean}
 */
function validateStartAndEndDates(startDate, endDate, startDateLabel, endDateLabel, errMsgObj, errMsgPrefix) {
    var returnValue = true;
    if (startDate && endDate) {
        var startDateVal = startDate.getValue();
        var endDateVal = endDate.getValue();
        if (startDateVal && endDateVal) {
            if (ReformatDate(endDateVal, STANDARDFORMAT, ' Ymd') < ReformatDate(startDateVal, STANDARDFORMAT, ' Ymd')) {
                errMsgObj.message = GT({
                       'id': 'IA.ASSIGNMENT_ENTER_VALUE_THAT_OCCURS_AFTER_THE_VALUE',
                       'placeHolders': [
                           {'name': 'ERR_MSG_PREFIX', 'value': errMsgPrefix},
                           {'name': 'END_DATE_LABEL', 'value': endDateLabel.toLowerCase()},
                           {'name': 'START_DATE_LABEL', 'value': startDateLabel.toLowerCase()}
                       ]
                   }
                );

                returnValue = false;
            }
        }
    }
    return returnValue;
}

// We're overwriting the addRequest and sendRequest to add the assignment key in the requests made by the dropdowns.
// For each dropdown in the page, it is created a request to the qrequest.phtml.
// qrequest.phtml uses then the *Picker.cls files to fetch data.
// With this overwrites, I send the current assignemnt key to the WorkingAssignmentPicker.cls,
//    so we can filter out the current assignment from the Assignment dropdown.

// These overwrites will be used by other pages only if they import this particular .js file, otherwise, they use the qrequest.js functions

QRequestBatch.prototype.addRequest = function(handler, params, updateFunc, updateArgs, convertToXML, fieldMeta)
{
    var request = {
        handler: handler,
        params: params,
        updateFunc: updateFunc,
        updateArgs: updateArgs,
        convertToXML: convertToXML
    };
    if ( fieldMeta.parentValue['ASSIGNMENTID']  && fieldMeta.parentValue['RECORDNO'] ) {
        request.params['otherparams'] = 'assignmentKey';
        request.params['assignmentKey'] = fieldMeta.parentValue['RECORDNO'];
    }
    if ( ! this.started ) {
        new QRequestBatchProcessor().sendRequests( [request], fieldMeta ? fieldMeta.keepCallOrder : false );
    } else this.requests.push(request);
};

QRequestBatchProcessor.prototype.sendRequests = function(requests, keepCallOrder)
{
    if( ! requests ) return;
    this.requests = requests;
    var jsonData = [];
    var assignmentKey = null;
    for(var ix = 0; ix < requests.length; ix++ )
    {
        if ( ! assignmentKey && requests[ix].params['assignmentKey'] ) {
            assignmentKey = requests[ix].params['assignmentKey'];
        }

        data = { handler: requests[ix].handler, params: requests[ix].params };
        jsonData.push(data);
    }

    var params = {
        '.handler' : 'QRequestJSONBatch',
        '.qop' : window._op,
        '.op' : window._op,
        '.batchData': JSON.stringify(jsonData),
        '.assignmentKey': assignmentKey
    };

    // do we have an entity in the update args? if so pass it to server
    if ( requests[0].updateArgs && requests[0].updateArgs.entity ) {
        params['.entity'] = requests[0].updateArgs.entity;
    }

    if ( window._sess ) {
        params['.sess'] = window._sess;
    }

    var qrequest = new QRequest();
    qrequest.setUseResponseText(true);
    var url = "qrequest.phtml";
    var updateFunc = function(obj, data) {
        obj.processBatchResponse(JSON.parse(data));
    };
    this.quickRequest({qrequest: qrequest, keepCallOrder: keepCallOrder, url: url, updateFunc: updateFunc, async: true, doPost: true, params: params});
};


/**
 * assignment input control ptr
 * @param meta
 * @constructor
 */
function AssignmentControlPtr(meta)
{
    this.meta = meta;
}
AssignmentControlPtr.inheritsFrom( InputControlPtr );

AssignmentControlPtr.prototype.generateListAssist = function() {
    var assist = null;

    var fieldValue = this.getSimpleValue();
    var displayLink = !this.meta.readonly || fieldValue;
    var afterFunction = this.meta.afterFunction ? this.meta.afterFunction : '';
    var value = this.meta.readonly ? fieldValue : null;

    // this is the assist for pick
    if ( !this.meta.nopick && this.meta.listAllowed ) {
        url = new URL(window.location.href);
        // current assignmentKey is sent as GET param and used by WorkingAssignmentPicker.cls
        var assignmentKey = url.searchParams.get('.r');

        assist = {
            'func': InputControlAbstractPtrListClick,
            'script': this.getAssistUrl(this.meta.type.pick_url, this.meta.type.evaluateUrl) +
                      '?.assignmentKey=' + assignmentKey,
            'action': this.meta.type.listAction ? this.meta.type.listAction : 'list',
            'assistEntity': this.meta.type.pickentity,
            'entity': this.meta.type.entity,
            'text': 'pick',
            'afterFunction': afterFunction,
            'varname': this.getHTMLName(),
            'form': this.meta.getFormName(),
            'value': value,
            'object': this,
            'onmouseover': this.meta.type.pick_onmouseover,
            'onmouseout': this.meta.type.pick_onmouseout,
            'popupWidth': this.meta.type.width,
            'popupHeight': this.meta.type.height
        };
    }
    return assist;
};

jq(document).ready(function() {
    if (window.editor) {
        var AssignmentsGridComponent = window.editor.findComponents('ASSIGNMENTS', 'Grid');
        var ConstraintsGridComponent = window.editor.findComponents('CONSTRAINTS', 'Grid');

        AssignmentsGrid = AssignmentsGridComponent && AssignmentsGridComponent.length ? AssignmentsGridComponent[0]
                                                                                      : null;
        ConstraintsGrid = ConstraintsGridComponent && ConstraintsGridComponent.length ? ConstraintsGridComponent[0]
                                                                                      : null;
        if ( AssignmentsGrid ) {
            AssignmentsGrid.canDeleteRow = function(rowIndex, isTotal) {
                if ( this.showDelete != undefined ) {
                    return this.showDelete;
                } else {
                    return true;
                }
            };
        }

        if ( ConstraintsGrid ) {
            ConstraintsGrid.canDeleteRow = function(rowIndex, isTotal) {
                if ( this.showDelete != undefined ) {
                    return this.showDelete;
                } else {
                    return true;
                }
            };

            // We need to overwrite the base validateRowData in order do catch a possible missing item from the first column of the grid.
            ConstraintsGrid.validateRowData = function(rowIndex) {
                if ( !this.value ||
                     !this.value[rowIndex] ||
                     Object.keys(this.value[rowIndex]).length == 2 ) {
                    return false;
                }

                return true;
            }
        }
    }
});


