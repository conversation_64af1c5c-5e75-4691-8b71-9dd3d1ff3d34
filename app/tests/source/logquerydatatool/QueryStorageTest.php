<?php

namespace tests\source\ldap;

use Exception;
use LogManager;
use MongoCollection;
use MongoCursor;
use MongoDB\Collection;
use MongoDB\Database;
use MongoDBClientBase;
use MongoDBHostsManager;
use PHPUnit\Framework\MockObject\MockObject;
use QueryStorage;
use unitTest\core\UnitTestBase;

require_once 'QueryStorageTestCallbacks.inc';

require_once '../../private/lib/MongoDB/autoload.php';

/**
 * Class QueryStorageTest
 * @group unit
 */
class QueryStorageTest extends UnitTestBase
{

    /**
     * @covers QueryStorage::gatherCollectionDataForCompany
     * @dataProvider dataProviderGatherCollectionDataForCompany
     *
     * @param int        $cny             Used to call gatherCollectionDataForCompany
     * @param string     $collectionName Return for toArray call
     * @param array|null $expectedResult
     */
    public function testGatherCollectionDataForCompany(int $cny, string $collectionName, ?array $expectedResult) : void
    {
        /** @var MockObject|MongoDBHostsManager $hostsManagerMock */
        $hostsManagerMock = $this->getMockBuilder(MongoDBHostsManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getMDBClientByRSKey', 'gatherHostsDBStats'])
            ->getMock();
        $hostsManagerMock->expects(self::any())
            ->method('getMDBClientByRSKey')
            ->willReturnCallback(
                function (string $host): MockObject {
                    lastHost($host);
                    /** @var MockObject $mdbcMock */
                    $mdbcMock = $this->getMockBuilder(MongoDBClientBase::class)
                        ->disableOriginalConstructor()
                        ->onlyMethods(['selectDBbyName'])
                        ->getMockForAbstractClass();
                    $mdbcMock->expects(self::any())
                        ->method('selectDBbyName')
                        ->withAnyParameters()
                        ->willReturnCallback(
                            function (string $dbName) {
                                lastDBName($dbName);

                                /** @var MockObject $databaseMock */
                                $databaseMock = $this->getMockBuilder(Database::class)
                                    ->disableOriginalConstructor()
                                    ->onlyMethods(['selectCollection'])
                                    ->getMock();
                                $databaseMock->expects(self::any())
                                    ->method('selectCollection')
                                    ->willReturnCallback(
                                        function (string $collectionName){
                                            $array = getCollections(lastHost(), lastDBName(), $collectionName);

                                            /** @var MockObject|MongoCursor $mongoCursorMock */
                                            $mongoCursorMock = $this->getMockBuilder(MongoCursor::class)
                                                ->disableOriginalConstructor()
                                                ->setMethods(['toArray'])
                                                ->getMock();
                                            $mongoCursorMock->expects(self::any())
                                                ->method('toArray')
                                                ->willReturn($array);

                                            /** @var MockObject|MongoCollection $collectionMock */
                                            $collectionMock = $this->getMockBuilder(MongoCollection::class)
                                                ->disableOriginalConstructor()
                                                ->setMethods(['find'])
                                                ->getMock();
                                            $collectionMock->expects(self::any())
                                                ->method('find')
                                                ->withAnyParameters()
                                                ->willReturn($mongoCursorMock);
                                            return $collectionMock;
                                        }
                                    );
                                return $databaseMock;
                            }
                        );

                    return $mdbcMock;
                }
            );
        $hostsManagerMock->expects(self::any())
            ->method('gatherHostsDBStats')
            ->willReturnCallback('callbackGatherHostsDBStats');

        // Mock for log manager
        /** @var MockObject|LogManager $logManagerMock */
        $logManagerMock = $this->getMockBuilder(LogManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['log'])
            ->getMock();
        $logManagerMock->expects(self::any())
            ->method('log')
            ->withAnyParameters();

        //Mock for QueryStorage object
        /** @var MockObject|QueryStorage $qStorageMock */
        $qStorageMock = new QueryStorage($hostsManagerMock, $logManagerMock);

        self::assertEquals($expectedResult, $qStorageMock->gatherCollectionDataForCompany($cny, $collectionName));
    }

    /**
     * Arrays of $cny, $collectionName, $expectedResult
     *
     * @return array
     */
    public function dataProviderGatherCollectionDataForCompany() : array
    {
        return [
            [
                1237,
                'WrongName',
                null
            ],
            [
                1236,
                'KV.aggregated',
                [
                    'documentID1' => 'value',
                    'documentID2' => 'value',
                    'documentID3' => 'value',
                    'documentID4' => 'value'
                ]
            ],
            [
                0000, //no valid cny -> unexisting database
                'KV.aggregated',
                null
            ],
            [
                1239,
                'KV.aggregated',
                []
            ]
        ];
    }

    /**
     * @dataProvider dataProviderDropCollections
     *
     * @param array $gatherHostsParams Return for gatherHostsDBStats call
     * @param int   $cny               Used to call dropCollection
     * @param bool  $expectedResult
     * @param array $collectionNames   Array of collections to be deleted
     *
     * @covers       QueryStorage::dropCollections
     *
     */
    public function testDropCollections(array $gatherHostsParams, int $cny, bool $expectedResult, array $collectionNames) : void
    {
        // Mock for database
        $databaseMock = $this->getMockBuilder(Database::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['selectCollection'])
            ->getMock();
        $databaseMock->expects(self::any())
            ->method('selectCollection')
            ->withAnyParameters()
            ->willReturnCallback(
                function ($string) {
                    if($string === 'KV.aggregated' or $string === 'KV.logquerydata'){
                        $collectionMock = $this->getMockBuilder(MongoCollection::class)
                            ->disableOriginalConstructor()
                            ->setMethods(['drop'])
                            ->getMock();
                        $collectionMock->expects(self::any())
                            ->method('drop')
                            ->willReturn([]);

                        return $collectionMock;
                    } else {
                        $collectionExceptionMock = $this->getMockBuilder(MongoCollection::class)
                            ->disableOriginalConstructor()
                            ->setMethods(['drop'])
                            ->getMock();
                        $collectionExceptionMock->expects(self::any())
                            ->method('drop')
                            ->willThrowException(new Exception);

                        return $collectionExceptionMock;
                    }
                }
            );

        $mdbcMock = $this->getMockBuilder(MongoDBClientBase::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['selectDBbyName'])
            ->getMockForAbstractClass();
        $mdbcMock->expects(self::any())
            ->method('selectDBbyName')
            ->withAnyParameters()
            ->willReturn($databaseMock);

        // Mock for mongoDB hosts manager
        /** @var MockObject|MongoDBHostsManager $hostsManagerMock */
        $hostsManagerMock = $this->getMockBuilder(MongoDBHostsManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getMDBClientByRSKey', 'gatherHostsDBStats'])
            ->getMock();
        $hostsManagerMock->expects(self::any())
            ->method('getMDBClientByRSKey')
            ->willReturn($mdbcMock);
        $hostsManagerMock->expects(self::any())
            ->method('gatherHostsDBStats')
            ->willReturn($gatherHostsParams);

        $logManagerMock = $this->getMockBuilder(LogManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['log'])
            ->getMock();
        $logManagerMock->expects(self::any())
            ->method('log')
            ->withAnyParameters();

        //Mock for QueryStorage object
        /** @var MockObject|QueryStorage $qStorageMock */
        $qStorageMock = $this->getMockBuilder(QueryStorage::class)
            ->setConstructorArgs([$hostsManagerMock, $logManagerMock])
            ->onlyMethods(['log'])// an unused method must be added here, otherwise it will set all methods from class
            ->getMock();

        self::assertEquals($expectedResult, $qStorageMock->dropCollections($cny, $collectionNames));
    }

    /**
     * @return array
     */
    public function dataProviderDropCollections() : array
    {
        return [
            [
                ['dev_rs2' => ['baseDB' => 'db', 'dbStats' => ['dbs' => ["db_1234" => 1]]]],
                1234,
                true,
                ['KV.logquerydata', 'KV.aggregated']
            ],
            [
                ['Exeption' => ['baseDB' => 'db', 'dbStats' => ['dbs' => ["db_1234" => 1]]]],
                1234,
                false,
                ['Exception']
            ],
        ];
    }

    /**
     * @covers QueryStorage::insertValues
     * @dataProvider dataProviderInsertValues
     * @param array $arrayToInsert Used as parameter for insertValues call
     * @param int   $existValue    Used as parameter for insertValues call
     * @param bool  $expectedResult
     */
    public function testInsertValues(array $arrayToInsert, int $existValue, bool $expectedResult) : void
    {
        $collectionMock = $this->getMockBuilder(Collection::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['insertOne'])
            ->getMock();
        if ($expectedResult) {
            $collectionMock->expects(self::any())
                ->method('insertOne')
                ->withAnyParameters()
                ->willReturn(true);
        } else {
            $collectionMock->expects(self::any())
                ->method('insertOne')
                ->withAnyParameters()
                ->willThrowException(new Exception());
        }

        $logManagerMock = $this->getMockBuilder(LogManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['log'])
            ->getMock();
        $logManagerMock->expects(self::any())
            ->method('log')
            ->withAnyParameters();

        $hostsManagerMock = $this->getMockBuilder(MongoDBHostsManager::class)
            ->disableOriginalConstructor()
            ->getMock();

        /** @var MockObject|QueryStorage $qStorageMock */
        $qStorageMock = $this->getMockBuilder(QueryStorage::class)
            ->setConstructorArgs([$hostsManagerMock, $logManagerMock])
            ->getMock();

        $result = $this->invokePrivate(
            $qStorageMock,
            'insertValues',
            $arrayToInsert,
            $collectionMock,
            $existValue
        );
        self::assertEquals($expectedResult, $result);
    }

    /**
     * @return array
     */
    public function dataProviderInsertValues() : array
    {
        return [
            [
                [['_id' => 'result1'], ['_id' => 'result2'], ['_id' => 'result3'], ['_id' => 'result4']],
                4,
                true
            ],
            [
                [['_id' => 'result1'], ['_id' => 'result2'], ['_id' => 0], ['_id' => 'result4']],
                4,
                false
            ],
        ];
    }
}
