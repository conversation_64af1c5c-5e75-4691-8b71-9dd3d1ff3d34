<?php
/**
 * Lister for the RateTable object
 *
 * <AUTHOR> / <PERSON>
 * @copyright    2000-2021 Sage Intacct Inc.
 *
 * This document contains trade secret data that belongs to Sage Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class RateTableLister extends NLister
{

    /* @var array $fields */
    private $fields = [];

    /* @var array $fieldLabels */
    private $fieldLabels = [];

    public function __construct()
    {
        $this->fields = [
            'RATETABLEID',
            'NAME',
            'DESCRIPTION',
        ];

        $helpfile = 'Viewing_and_Managing_Projects';

        parent::__construct(
            [
                'entity'                => 'ratetable',
                'importperm'            => 'pa/lists/ratetable/create',
                'helpfile'              => $helpfile,
                'fields'                => $this->fields,
                'fieldlabels'           => [],
                'nofilteronthesefields' => [ 'RECORDNO' ],
                'nosort'                => [ 'RECORDNO' => true ],
                'enablemultidelete'     => false,
                'nonencodedfields'      => [ 'RECORDNO' ],
            ]
        );
    }

}