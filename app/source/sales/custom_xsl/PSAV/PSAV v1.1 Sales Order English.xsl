<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" 
	xmlns:xsl="http://www.w3.org/1999/XSL/Transform" 
	xmlns:fo="http://www.w3.org/1999/XSL/Format" 
	xmlns:svg	="http://www.w3.org/2000/svg"
	xmlns:fox="http://xml.apache.org/fop/extensions">
<xsl:include href="../../private/xslinc/miscdocument_inc.xsl"/>
<xsl:variable name="fontsize">8pt</xsl:variable>

<xsl:template match="/">
	<fo:root 
		xmlns:fo="http://www.w3.org/1999/XSL/Format" 
		xmlns:svg="http://www.w3.org/2000/svg" 
		xmlns:fox="http://xml.apache.org/fop/extensions">
		
		<xsl:call-template name="pagemaster_PSAV"/>
		<xsl:apply-templates/>
	</fo:root>
</xsl:template>

<xsl:template match="DOCUMENT">
	<xsl:apply-templates/>
</xsl:template>

<xsl:template match="OLDROOT">
	<fo:page-sequence 
		master-reference="psmA"
		initial-page-number="1"
		>
	<fo:static-content 
			flow-name="xsl-region-after"
			>
			<xsl:call-template name="footer_PSAV"/> 

		</fo:static-content> 
		<fo:flow 
			flow-name="xsl-region-body"
			>
				<xsl:call-template name="pagetop_PSAV"/>
				<xsl:call-template name="titleordernumber_PSAV"/>  
				<xsl:call-template name="titleorderdate_PSAV"/>  

				<xsl:call-template name="generictitle_PSAV">
					<xsl:with-param name="label" 		select="'Sales Representative'"/>
					<xsl:with-param name="data" 		select="REC/CREATEDUSERINFO/CONTACTINFO/CONTACTNAME"/>
					<xsl:with-param name="position"		select="'2.5cm'"/>
					<xsl:with-param name="myfontsize"		select="'8pt'"/>					
				</xsl:call-template>			
				<xsl:call-template name="generictitle_PSAV">
					<xsl:with-param name="label" 		select="'Sales Tel'"/>
					<xsl:with-param name="data" 		select="REC/CREATEDUSERINFO/CONTACTINFO/PHONE1"/>
					<xsl:with-param name="position"		select="'2.9cm'"/>
					<xsl:with-param name="myfontsize"		select="'8pt'"/>					
				</xsl:call-template>
				<xsl:call-template name="generictitle_PSAV">
					<xsl:with-param name="label" 		select="'Sales Email'"/>
					<xsl:with-param name="data" 		select="REC/CREATEDUSERINFO/CONTACTINFO/EMAIL1"/>
					<xsl:with-param name="position"		select="'3.3cm'"/>
					<xsl:with-param name="myfontsize"		select="'8pt'"/>					
				</xsl:call-template>
												
				<xsl:call-template name="generictitle_PSAV">
					<xsl:with-param name="label" 		select="'Delivery Date'"/>
					<xsl:with-param name="data" 		select="REC/DDELIVERY_DATE"/>
					<xsl:with-param name="position"		select="'4.0cm'"/>
					<xsl:with-param name="myfontsize"		select="'8pt'"/>					
				</xsl:call-template>		
				<xsl:call-template name="generictitle_PSAV">
					<xsl:with-param name="label" 		select="'Function Start Date'"/>
					<xsl:with-param name="data" 		select="REC/DSTART_DATE"/>
					<xsl:with-param name="position"		select="'4.4cm'"/>
					<xsl:with-param name="myfontsize"		select="'8pt'"/>					
				</xsl:call-template>								
				<xsl:call-template name="generictitle_PSAV">
					<xsl:with-param name="label" 		select="'Function End Date'"/>
					<xsl:with-param name="data" 		select="REC/EEND_DATE"/>
					<xsl:with-param name="position"		select="'4.8cm'"/>
					<xsl:with-param name="myfontsize"		select="'8pt'"/>					
				</xsl:call-template>								
				<xsl:call-template name="generictitle_PSAV">
					<xsl:with-param name="label" 		select="'Billing Code'"/>
					<xsl:with-param name="data" 		select="REC/DEPARTMENT"/>
					<xsl:with-param name="position"		select="'5.2cm'"/>
					<xsl:with-param name="myfontsize"		select="'8pt'"/>					
				</xsl:call-template>										
				



<!--
				<xsl:call-template name="topnotes_PSAV">
					<xsl:with-param name="label0"        select="'Reference #'"/>
					<xsl:with-param name="data0"         select="REC/PONUMBER"/>
					<xsl:with-param name="label1"        select="'Terms'"/>
					<xsl:with-param name="data1"         select="REC/TERM/NAME"/>
				</xsl:call-template>
	-->				
				<xsl:call-template name="quotebody_PSAV"/>
<!--				<xsl:call-template name="displayfineprint_PSAV" />  -->

			</fo:flow>
		</fo:page-sequence>
</xsl:template>

<xsl:template name="titleordernumber_PSAV">
	<xsl:call-template name="generictitle_PSAV">
		<xsl:with-param name="label" 		select="'Reference #'"/>
		<xsl:with-param name="data" 		select="REC/DOCNO"/>
		<xsl:with-param name="position"		select="'1.2cm'"/>
		<xsl:with-param name="myfontsize"		select="'10pt'"/>
		
	</xsl:call-template>
</xsl:template>

<xsl:template name="titleordershipdate_PSAV">
	<xsl:call-template name="generictitle_PSAV">
		<xsl:with-param name="label" 		select="'Ship Date'"/>
		<xsl:with-param name="data" 		select="REC/WHENDUE"/>
		<xsl:with-param name="position"		select="'2.2cm'"/>
		<xsl:with-param name="myfontsize"		select="'10pt'"/>		
	</xsl:call-template>
</xsl:template>
	
<xsl:template name="titleorderdate_PSAV">
	<xsl:call-template name="generictitle_PSAV">
		<xsl:with-param name="label" 		select="'Order Date'"/>
		<xsl:with-param name="data" 		select="REC/WHENCREATED"/>
		<xsl:with-param name="position"		select="'1.7cm'"/>
		<xsl:with-param name="myfontsize"		select="'10pt'"/>
		
	</xsl:call-template>
</xsl:template>

<xsl:template name="titleordershipvia_PSAV"> 
	<xsl:if test="REC/SHIPVIA != ''">
		<xsl:call-template name="generictitle_PSAV">
			<xsl:with-param name="label" 		select="'Ship Via'"/>
			<xsl:with-param name="data" 		select="REC/SHIPVIA"/>
			<xsl:with-param name="position"		select="'2.7cm'"/>
		</xsl:call-template>
	</xsl:if>
</xsl:template>


	<xsl:template name="pagetop_PSAV">
		<xsl:call-template name="companyaddress_PSAV"/>
		<xsl:call-template name="displaycontacts_PSAV" />	
		<xsl:call-template name="documentlabel_PSAV"/>
	</xsl:template>

	<xsl:template name="displaydetails_PSAV">
		<fo:block space-before="0.1cm">
		<fo:table table-layout="fixed" border-color="white" border-style="solid" border-bottom-width="0.000pt" border-top-width="0.000pt" border-left-width="0.000pt" border-right-width="0.000pt">
			<fo:table-column columnwidth="8in" />
			<fo:table-column columnwidth="5in" />			
			<fo:table-body>
				<fo:table-row>
					<fo:table-cell>
<!--						<xsl:call-template name="displaycontacts_PSAV" />						-->
					</fo:table-cell>
					<fo:table-cell padding-before="3pt" cue-before="6pt">
						<xsl:call-template name="displaycustomfields_PSAV" />						
					</fo:table-cell>
				</fo:table-row>
			</fo:table-body>
		</fo:table>		
		</fo:block>	
	</xsl:template>
	
<xsl:template name="generictitle_PSAV">
	<xsl:param name="label"/>
	<xsl:param name="data"/>
	<xsl:param name="position"/>
	<xsl:param name="myfontsize"/>
   		<fo:block-container
			border-color	="white"
			border-style	="solid"
			border-bottom-width="0.000pt"
			border-top-width="0.000pt"
			border-left-width="0.000pt"
			border-right-width="0.000pt"
			height			="0.5cm"
			width			="2.5in"
			top				="{$position}"
			left		="6.6in"
			position		="absolute">

		<fo:block font-family			="{$myfont}"
				font-size			="{$myfontsize}">
		<fo:table table-layout="fixed" border-color= "white" border-style="solid"  >
			<fo:table-column column-width="1.5in" />
			<fo:table-column column-width="1.5in" />
			<fo:table-body >
					<fo:table-row >
						<fo:table-cell >
							<fo:block text-align="right"><xsl:value-of select="$label"/></fo:block>
						</fo:table-cell>
						<fo:table-cell >
							<fo:block text-align="left"><xsl:text>: </xsl:text><xsl:value-of select="$data"/></fo:block>
						</fo:table-cell>						
					</fo:table-row>
			</fo:table-body>
			</fo:table>
		</fo:block>
		</fo:block-container>
	</xsl:template>
	
	<xsl:template name="displaycustomfields_PSAV">
	 <fo:table   border-style="solid" border-color="white" border-bottom-width="0.000pt" border-top-width="0.000pt" border-left-width="0.000pt" border-right-width="0.000pt">
	 	<fo:table-column column-width="4in" />
			<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="8pt">
				<fo:table-row>
					<fo:table-cell   >
						<fo:inline start-indent="1.42in">
						<fo:block start-indent="1cm" text-align="start" margin-left="2.2in" space-before="3pt" >Sales Representative : <xsl:value-of select="REC/CREATEDUSERINFO/LOGINID"/> </fo:block>
						<fo:block start-indent="1cm" text-align="start" margin-left="2.2in" space-before="3pt">Sales Tel : <xsl:value-of select="REC/CREATEDUSERINFO/CONTACTINFO/PHONE1"/></fo:block>
						<fo:block start-indent="1cm" text-align="start" margin-left="2.2in" space-before="3pt">Sales Email : <xsl:value-of select="REC/CREATEDUSERINFO/CONTACTINFO/EMAIL1"/> </fo:block>
						
						<fo:block start-indent="1cm" text-align="start" margin-left="2.2in" space-before="3pt" >Delivery Date : <xsl:value-of select="REC/DDELIVERY_DATE"/>		</fo:block>
						<fo:block start-indent="1cm" text-align="start" margin-left="2.2in" space-before="3pt">Function Start Date : <xsl:value-of select="REC/DSTART_DATE"/></fo:block>
						<fo:block start-indent="1cm" text-align="start" margin-left="2.2in" space-before="3pt">Function End Date : <xsl:value-of select="REC/EEND_DATE"/> </fo:block>
						</fo:inline>					
					</fo:table-cell>					
				</fo:table-row>
			</fo:table-body>
		</fo:table>
	</xsl:template>
	<xsl:template name="displaycontacts_PSAV">
		<xsl:variable name="showtitle1">
			<xsl:value-of select="REC/_DOCPAR/SHOWTITLE1"/>
		</xsl:variable>
		<xsl:variable name="showtitle2">
			<xsl:value-of select="REC/_DOCPAR/SHOWTITLE2"/>
		</xsl:variable> 

   <fo:block-container
   		height		="5in"
		width		="3in"
		top		="0in"
		left		="3in"
		position	="absolute">
		<fo:block  border-color="black">
			<fo:table table-layout="fixed" border-color="black" border-style="solid" border-bottom-width="0.001pt" border-top-width="0.001pt" border-left-width="0.001pt" border-right-width="0.001pt">
				<fo:table-column column-width="3in"/>
<!--				<fo:table-column column-width="3in"/> -->
				<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="{$fontsize}">
					<fo:table-row line-height="15pt">
						<fo:table-cell padding="6pt" border-bottom="solid" border-bottom-width="0.001pt">
							<!-- title1 is always billto and title2 is always shipto-->
									<xsl:call-template name="genericbilltoaddress_PSAV">
										<xsl:with-param name="billorship" select="REC/_DOCPAR/CONTACTTITLE1"/>
									</xsl:call-template>
									<xsl:call-template name="displaycontactcustomfields_PSAV" />						

						</fo:table-cell>
						</fo:table-row>	
						<fo:table-row line-height="15pt">						
						<fo:table-cell padding="6pt">
								<xsl:call-template name="genericshiptoaddress_PSAV">
									<xsl:with-param name="billorship" select="REC/_DOCPAR/CONTACTTITLE2"/>
								</xsl:call-template>
						</fo:table-cell>
											
					</fo:table-row>
				</fo:table-body>
			</fo:table>
		</fo:block>
		</fo:block-container>		
	</xsl:template>
	


	<xsl:template name="displaycontactcustomfields_PSAV">
	 <fo:table border-style="solid" border-color="white" border-bottom-width="0.000pt" border-top-width="0.000pt" border-left-width="0.000pt" border-right-width="0.000pt">
	 	<fo:table-column column-width="8cm" />
			<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="10pt">
				<fo:table-row line-height="11pt" >
					<fo:table-cell   >
						<fo:block  text-align="start"  font-size="8pt" white-space-collapse="false" >Name: <xsl:value-of select="REC/ACC_NAME"/>
Phone/Fax: <xsl:value-of select="REC/ACC_PHONE"/>
Email: <xsl:value-of select="REC/BCC_EMAIL"/></fo:block>			
					</fo:table-cell>					
				</fo:table-row>
			</fo:table-body>
		</fo:table>
	
<!--	 <fo:table border-style="solid" border-color="white" border-bottom-width="0.000pt" border-top-width="0.000pt" border-left-width="0.000pt" border-right-width="0.000pt">
	 	<fo:table-column column-width="12cm" />
			<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="{$fontsize}">
				<fo:table-row>
					<fo:table-cell   >
					<fo:inline start-indent="0.02in"> 
						<fo:block  text-align="start"   font-weight="bold"  >Client Contact :</fo:block>
						<fo:block  text-align="start"   >Name : <xsl:value-of select="REC/ACC_NAME"/> </fo:block>
						<fo:block  text-align="start"  >Phone/Fax : <xsl:value-of select="REC/ACC_PHONE"/></fo:block>
						<fo:block  text-align="start" >Email : <xsl:value-of select="REC/BCC_EMAIL"/></fo:block>			
					</fo:inline> 
					</fo:table-cell>					
				</fo:table-row>
			</fo:table-body>
		</fo:table> -->
	</xsl:template>	
	
<xsl:template name="genericbillshipaddress_PSAV">
	<xsl:param name="billorship"/>
	<xsl:param name="companyname"/>
	<xsl:param name="printas"/>
	<xsl:param name="address1"/>
	<xsl:param name="address2"/>
	<xsl:param name="city"/>
	<xsl:param name="state"/>
	<xsl:param name="zip"/>
	<xsl:param name="country"/>
	<xsl:param name="phone1"/>
	<xsl:param name="fax"/>
	<xsl:param name="email"/>
	<xsl:param name="startindent" />
<fo:table table-layout="fixed" border-style="solid"  border-color="white" border-after-width="0.000pt" border-before-width="0.000pt" border-bottom-width="0.000pt" border-top-width="0.000pt">
	<fo:table-column width="5.5cm" />

	<fo:table-body  >
		<fo:table-row  line-height="11pt" >
			<fo:table-cell>		
		<fo:block
			text-align		="start"
			font-size="10pt" font-weight	="bold" font-family="{$myfont}" >
			<xsl:value-of select="$billorship"/>
			<xsl:text>: </xsl:text>
		</fo:block>
			</fo:table-cell>
		</fo:table-row>		
		<xsl:if test="$companyname != '' and $companyname != $printas">
		<fo:table-row  line-height="11pt" >
			<fo:table-cell>		
			<fo:block
				text-align		="start"
				font-weight		="normal" font-size="8pt" padding="0pt" margin="0pt">
				<xsl:value-of select="$companyname"/>
			</fo:block>
			</fo:table-cell>
		</fo:table-row>					
		</xsl:if>
		<fo:table-row  line-height="11pt" >
			<fo:table-cell>		
		<fo:block
			text-align		="start"
			font-weight		="normal"  font-size="8pt" >
			<xsl:if test="$companyname != '' and $companyname != $printas and $billorship != 'Remit To'">
				<xsl:text>Attn: </xsl:text>
			</xsl:if>
			<xsl:value-of select="$printas"/>
		</fo:block>
			</fo:table-cell>
		</fo:table-row>				
		<fo:table-row  line-height="11pt" >
			<fo:table-cell>		
		<fo:block
			text-align		="start" font-size="8pt" >
			<xsl:value-of select="$address1"/>
		</fo:block>
			</fo:table-cell>
		</fo:table-row>				
		<fo:table-row  line-height="11pt" >
			<fo:table-cell>		
		<fo:block
			text-align		="start" font-size="8pt">
			<xsl:value-of select="$address2"/>
		</fo:block>
			</fo:table-cell>
		</fo:table-row>				
		<fo:table-row  line-height="11pt" >
			<fo:table-cell>				
		<fo:block
			text-align		="start" font-size="8pt">

			<xsl:value-of select="$city"/>
			<xsl:if test="$state != '' and $city != ''">
				<xsl:text>,  </xsl:text>
			</xsl:if>

			<xsl:value-of select="$state"/>
			<xsl:text>  </xsl:text>
			<xsl:value-of select="$zip"/>

			</fo:block>
			</fo:table-cell>
		</fo:table-row>				
		<fo:table-row  line-height="11pt" >
			<fo:table-cell>		
			
			<fo:block
				text-align		="start"  font-size="8pt">
				<xsl:value-of select="$country"/>
			</fo:block>
			</fo:table-cell>
		</fo:table-row>				

			
			<xsl:if test="$phone1 != ''">
		<fo:table-row  line-height="11pt" >
			<fo:table-cell>		
			
				<fo:block text-align		="start" font-size="8pt">
					<xsl:text>Ph: </xsl:text><xsl:value-of select="$phone1"/>
				</fo:block>
			</fo:table-cell>
		</fo:table-row>				
				
			</xsl:if>
			<xsl:if test="$fax != ''">
					<fo:table-row  line-height="11pt" >
			<fo:table-cell>		

				<fo:block text-align		="start" font-size="8pt" >
					<xsl:text>Fax: </xsl:text><xsl:value-of select="$fax"/>
				</fo:block>
			</fo:table-cell>
		</fo:table-row>							
			</xsl:if>
			<xsl:if test="$email != ''">
					<fo:table-row  line-height="11pt" >
			<fo:table-cell>					
				<fo:block text-align		="start" font-size="8pt">
					<xsl:text>Email: </xsl:text><xsl:value-of select="$email"/>
				</fo:block>
			</fo:table-cell>
		</fo:table-row>					
			</xsl:if>	

	</fo:table-body>
</fo:table>		
<!--		<fo:inline start-indent="{$startindent}"> 
		<fo:block
			text-align		="start"
			font-weight		="bold" >
			<xsl:value-of select="$billorship"/>
			<xsl:text> : </xsl:text>
		</fo:block>
		<xsl:if test="$companyname != '' and $companyname != $printas">
			<fo:block
				text-align		="start"
				font-weight		="bold" >
				<xsl:value-of select="$companyname"/>
			</fo:block>
		</xsl:if>
		<fo:block
			text-align		="start"
			font-weight		="bold" >
			<xsl:if test="$companyname != '' and $companyname != $printas and $billorship != 'Remit To'">
				<xsl:text>Attn: </xsl:text>
			</xsl:if>
			<xsl:value-of select="$printas"/>
		</fo:block>
		<fo:block
			text-align		="start" >
			<xsl:value-of select="$address1"/>
		</fo:block>
		<fo:block
			text-align		="start">
			<xsl:value-of select="$address2"/>
		</fo:block>
		<fo:block
			text-align		="start" >

			<xsl:value-of select="$city"/>
			<xsl:if test="$state != '' and $city != ''">
				<xsl:text>,  </xsl:text>
			</xsl:if>

			<xsl:value-of select="$state"/>
			<xsl:text>  </xsl:text>
			<xsl:value-of select="$zip"/>

			</fo:block>
			<fo:block
				text-align		="start" >
				<xsl:value-of select="$country"/>
			</fo:block>
			<xsl:if test="$phone1 != ''">
				<fo:block text-align		="start" >
					<xsl:text>Ph: </xsl:text><xsl:value-of select="$phone1"/>
				</fo:block>
			</xsl:if>
			<xsl:if test="$fax != ''">
				<fo:block text-align		="start" >
					<xsl:text>Fax: </xsl:text><xsl:value-of select="$fax"/>
				</fo:block>
			</xsl:if>
			<xsl:if test="$email != ''">
				<fo:block text-align		="start" >
					<xsl:text>Email: </xsl:text><xsl:value-of select="$email"/>
				</fo:block>
			</xsl:if>			
		</fo:inline> -->
</xsl:template>

<xsl:template name="genericbilltoaddress_PSAV">
	<xsl:param name="billorship"/>

	<xsl:call-template name="genericbillshipaddress_PSAV">
		<xsl:with-param name	="billorship" select	="$billorship"/>	
		<xsl:with-param name	="companyname" 	select	=".//BILLTO/COMPANYNAME"/>
		<xsl:with-param name	="printas" 		select	=".//BILLTO/PRINTAS"/>	
		<xsl:with-param name	="address1" 	select	=".//BILLTO/MAILADDRESS/ADDRESS1"/>	
		<xsl:with-param name	="address2" 	select	=".//BILLTO/MAILADDRESS/ADDRESS2"/>	
		<xsl:with-param name	="city" 		select	=".//BILLTO/MAILADDRESS/CITY"/>	
		<xsl:with-param name	="state" 		select	=".//BILLTO/MAILADDRESS/STATE"/>	
		<xsl:with-param name	="zip" 			select	=".//BILLTO/MAILADDRESS/ZIP"/>	
		<xsl:with-param name	="country" 		select	=".//BILLTO/MAILADDRESS/COUNTRY"/>	
		<xsl:with-param name	="startindent" 		select	="'0.02in'"/>			
<!--		<xsl:with-param name	="phone1" 		select	=".//BILLTO/PHONE1"/>	
		<xsl:with-param name	="fax" 			select	=".//BILLTO/FAX"/>-->
	</xsl:call-template>	
	
</xsl:template>	

<xsl:template name="genericshiptoaddress_PSAV">
	<xsl:param name="billorship"/>

	<xsl:call-template name="genericbillshipaddress_PSAV">
		<xsl:with-param name	="billorship" select	="$billorship"/>
		<xsl:with-param name	="companyname" 	select	=".//SHIPTO/COMPANYNAME"/>
		<xsl:with-param name	="printas" 		select	=".//SHIPTO/PRINTAS"/>
		<xsl:with-param name	="address1" 	select	=".//SHIPTO/MAILADDRESS/ADDRESS1"/>
		<xsl:with-param name	="address2" 	select	=".//SHIPTO/MAILADDRESS/ADDRESS2"/>
		<xsl:with-param name	="city" 		select	=".//SHIPTO/MAILADDRESS/CITY"/>
		<xsl:with-param name	="state" 		select	=".//SHIPTO/MAILADDRESS/STATE"/>
		<xsl:with-param name	="zip" 			select	=".//SHIPTO/MAILADDRESS/ZIP"/>
		<xsl:with-param name	="country" 		select	=".//SHIPTO/MAILADDRESS/COUNTRY"/>
		<xsl:with-param name	="startindent" 		select	="'0.02in'"/>			
		
<!--		<xsl:with-param name	="phone1" 		select	=".//SHIPTO/PHONE1"/>
		<xsl:with-param name	="fax" 			select	=".//SHIPTO/FAX"/> -->
	</xsl:call-template>	

</xsl:template>


<xsl:template name="quotebody_PSAV">

	<fo:block space-before="15pt">
		<fo:table table-layout="fixed"  table-top="7.5cm" border-color="black" border-style="solid" border-width="0.05pt" >
			<fo:table-column column-width="1.0in" border-color="black" border-style="solid" border-width="0.05pt"/>
		
			<!-- 1.5 -->
<!--			<fo:table-column column-width="2.0in" border-color="black" border-style="solid" border-width="0.05pt"/> -->
			<!-- 2.0 --> 
			<fo:table-column column-width="6.0in" border-color="black" border-style="solid" border-width="0.05pt"/>
<!--			<fo:table-column column-width="1.0in" border-color="black" border-style="solid" border-width="0.05pt"/> -->
			<fo:table-column column-width="1.0in" border-color="black" border-style="solid" border-width="0.05pt"/>
			<!-- site --> 
<!--			<fo:table-column column-width="0.5in" border-color="black" border-style="solid" border-width="0.05pt"/>	 -->
					
			<fo:table-column column-width="1.0in" border-color="black" border-style="solid" border-width="0.05pt"/>
			<fo:table-column column-width="1.0in" border-color="black" border-style="solid" border-width="0.05pt"/>

			<fo:table-header font-family="{$myfont}" font-weight="normal" font-size="8pt">

				<fo:table-row line-height="12pt" background-color="#BBBBBB">
					<xsl:call-template name="columnheader_qty_PSAV"/>

<!--					<xsl:call-template name="columnheader_item_PSAV"/> --> 
					<xsl:call-template name="columnheader_description_PSAV"/>
<!--					<xsl:call-template name="columnheader_billingcode_PSAV"/>  -->
					
					<xsl:call-template name="columnheader_unit_PSAV"/>
					<!-- site --> 
<!--					<xsl:call-template name="columnheader_site_PSAV"/> -->
					
<!--					<xsl:call-template name="columnheader_qty_PSAV"/> -->
					<xsl:call-template name="columnheader_unitprice_PSAV"/>
					<xsl:call-template name="columnheader_amount_PSAV"/>

				</fo:table-row>

			</fo:table-header>

    		<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="8pt">
			<xsl:apply-templates select="REC/ENTRIES" mode="withamounts_PSAV"/>
    		</fo:table-body>
		</fo:table>
	<xsl:call-template name="quotesubtotalmessage"/>
	</fo:block>

	<xsl:call-template name="message"/>

</xsl:template>

<xsl:template name="quotesubtotalmessage">

<fo:table >
<fo:table-column column-width="7.5in"/>
<fo:table-column column-width="3.5in"/>
	<fo:table-body>
		<fo:table-row>
			<fo:table-cell >
				<xsl:call-template name="displayfineprint_PSAV"/>
			</fo:table-cell>
			<fo:table-cell >
				<xsl:call-template name="quotesubtotals_PSAV"/>
			</fo:table-cell>			
		</fo:table-row>
	</fo:table-body>
</fo:table>

</xsl:template>


<xsl:template name="columnheader_item_PSAV">
	<xsl:call-template name="genericcolumnheader_PSAV">
		<xsl:with-param name="label" 	select="'Item'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="columnheader_description_PSAV">
	<xsl:call-template name="genericcolumnheader_PSAV">
		<xsl:with-param name="label" 	select="'Description'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>
<xsl:template name="columnheader_billingcode_PSAV">
	<xsl:call-template name="genericcolumnheader_PSAV">
		<xsl:with-param name="label" 	select="'Billing Code'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>
<xsl:template name="columnheader_unit_PSAV">
	<xsl:call-template name="genericcolumnheader_PSAV">
		<xsl:with-param name="label" 	select="'Unit'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="columnheader_qtyshipped_PSAV">
	<xsl:call-template name="genericcolumnheader_PSAV">
		<xsl:with-param name="label" 	select="'Quantity Shipped'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="columnheader_qty_PSAV">
	<xsl:call-template name="genericcolumnheader_PSAV">
		<xsl:with-param name="label" 	select="'Quantity'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="columnheader_unitprice_PSAV">
	<xsl:call-template name="genericcolumnheader_PSAV">
		<xsl:with-param name="label" 	select="'Unit Price'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="columnheader_amount_PSAV">
	<xsl:call-template name="genericcolumnheader_PSAV">
		<xsl:with-param name="label" 	select="'Amount'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>
<xsl:template name="columnheader_site_PSAV">
	<xsl:call-template name="genericcolumnheader_PSAV">
		<xsl:with-param name="label" 	select="'Code'"/>
		<xsl:with-param name="position" select="'center'"/>
	</xsl:call-template>
</xsl:template>

<xsl:template name="genericcolumnheader_PSAV">

	<xsl:param name="label"/>
	<xsl:param name="position"/>

  	<fo:table-cell
  		border-width	="0.05pt"
		border-style	="solid"
		padding			="1pt">

		<fo:block
			text-align="{$position}">
			<xsl:value-of select="$label"/>
		</fo:block>
	</fo:table-cell>

</xsl:template>

<xsl:template match="ENTRIES" mode="withamounts_PSAV">
	<xsl:if test="child::UIQTY!=0">
		<xsl:variable name="theprice">
			<xsl:call-template name="zeropadwithdollarandprecision">
				<xsl:with-param name="data" select="child::UIPRICE"/>
			</xsl:call-template>
		</xsl:variable>

		<xsl:variable name="thecalculatedvalue">
			<xsl:call-template name="zeropadwithdollar">
				<xsl:with-param name="data" select="child::UIVALUE"/>
			</xsl:call-template>
		</xsl:variable>
		<!-- {$fontsize} -->
		<fo:table-row line-height="3pt" border-style="solid" border-color="black">
			<xsl:call-template name="generictableentry_PSAV">
				<xsl:with-param name="data" select="child::UIQTY"/>
				<xsl:with-param name="position" select="'right'"/>
			</xsl:call-template>


<!--			<xsl:call-template name="tableentry_item">
				<xsl:with-param name="data" select="child::ITEMID"/>
			</xsl:call-template>
-->
<!-- TM 02.21.08 Tweaked due to Feb08 Release -->

			<xsl:variable name="ItemName">
				<xsl:value-of select="child::ITEMNAME"/>
			</xsl:variable>

			<xsl:variable name="ItemDesc">
				<xsl:choose>
					<xsl:when test="child::ITEMDESC !=child::EXTENDED_DESCRIPTION">
						<xsl:value-of select="child::ITEMDESC"/>
					</xsl:when>
					<xsl:otherwise>
						<xsl:value-of select="$ItemName"/>
					</xsl:otherwise>
				</xsl:choose>
			</xsl:variable>

			<xsl:call-template name="generictableentry_PSAV">
				<xsl:with-param name="data" select="$ItemDesc"/>
				<xsl:with-param name="data2" select="child::MEMO"/>
				<xsl:with-param name="data3" select="child::STOCK_NUMBER"/>
<!--				<xsl:with-param name="data4" select="child::EXTENDED_DESCRIPTION"/> -->
				<xsl:with-param name="position" select="'start'"/>
			</xsl:call-template>
<!--			
			<xsl:call-template name="generictableentry_PSAV">
				<xsl:with-param name="data" select="child::DEPARTMENT"/>
				<xsl:with-param name="position" select="'center'"/>
			</xsl:call-template>
-->			
			<xsl:call-template name="generictableentry_PSAV">
				<xsl:with-param name="data" select="child::UNIT"/>
				<xsl:with-param name="position" select="'center'"/>
			</xsl:call-template>

			<!-- site -->
<!--			<xsl:apply-templates select="child::WAREHOUSE" mode="site"/> -->
<!--			<xsl:call-template name="tableentry_site">
				<xsl:with-param name="data" select="child::WAREHOUSE"/>
			</xsl:call-template>
-->
			<xsl:call-template name="generictableentry_PSAV">
				<xsl:with-param name="data" select="$theprice"/>
				<xsl:with-param name="position" select="'right'"/>
				
			</xsl:call-template>

			<xsl:call-template name="generictableentry_PSAV">
				<xsl:with-param name="data" select="$thecalculatedvalue"/>
				<xsl:with-param name="position" select="'right'"/>
				<xsl:with-param name="checktax" select="'true'"/>				
			</xsl:call-template>

		</fo:table-row>
	</xsl:if>
</xsl:template>

<xsl:template name="tableentry_site_PSAV" match="WAREHOUSE" mode="site">
	<xsl:param name="data"/>
	<xsl:call-template name="generictableentry_PSAV">
		<xsl:with-param name="data" 	select="child::LOCATION_NO"/>
				<xsl:with-param name="position" select="'right'"/>
		
	</xsl:call-template>
</xsl:template>

<xsl:template name="displayfineprint_PSAV">
<fo:block font-size="7pt" space-after="6pt" white-space-collapse = "false">
Renter agrees it has received the above items in good and working condition, and will promptly return them in that condition on the Return Date. Renter agrees it will pay for the repair cost of any damaged items, and the replacement cost of any lost or unrepairable items (including any spare or blown lamps not returned).  
Renter agrees to pay a service charge of 1.5% per month on the unpaid balance of any charges not paid in full within 15 days of invoice date. Renter agrees it will pay all costs incurred by PRESENTATION SERVICES to collect the Total Due and/or recover it's items, including reasonable attorney's fees. Renter agrees these terms can only be modified in a writing signed by an officer of PRESENTATION SERVICES. PRESENTATION SERVICES reserves the right to charge a damage waiver to cover damage or loss of equipment while in the customers care.  This damage waiver indemnifies the customer from being charged for damage arising out of normal use of the equipment.  
It does not, however, indemnify the customer against willful neglect of PRESENTATION SERVICES property.</fo:block>

</xsl:template>

<xsl:template name="quotesubtotals_PSAV">
	<fo:table table-layout="fixed">
<!--		<fo:table-column column-width="7.5in"   border-color="white" border-style="solid"  border-bottom-width="0.01pt" border-top-width="0.000pt" border-left-width="0.01pt" border-right-width="0.01pt"/> -->
		<fo:table-column column-width="1.5in" border-color="black" border-style="solid"  border-bottom-width="0.01pt" border-top-width="0.000pt" border-left-width="0.01pt" border-right-width="0.01pt"/>
		<fo:table-column column-width="1.0in" border-color="black" border-style="solid"  border-bottom-width="0.01pt" border-top-width="0.000pt" border-left-width="0.01pt" border-right-width="0.01pt"/>

		<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="8pt">
			<xsl:apply-templates select="REC/SUBTOTALS" mode="PSAV"/>
		</fo:table-body>
	</fo:table>
</xsl:template>

<xsl:template name="genericsubtotals_PSAV" match="SUBTOTALS" mode="PSAV">
		<xsl:choose>
			<xsl:when test="position()=1">
				<fo:table-row line-height="8pt">
<!--					<fo:table-cell padding="6pt">
						<fo:block text-align="end"/>
					</fo:table-cell> -->
					<fo:table-cell padding="4pt">
						<fo:block text-align="left">
							<xsl:value-of select="child::DESCRIPTION"/>
						</fo:block>
					</fo:table-cell>
					<fo:table-cell padding="4pt">
						<fo:block text-align="end">
							<xsl:call-template name="zeropadwithdollar">
								<xsl:with-param name="data" select="child::TOTAL"/>
							</xsl:call-template>
							<fo:inline color="white">
								<xsl:text> T</xsl:text>
							</fo:inline>
							<xsl:text>  </xsl:text>
						</fo:block>
					</fo:table-cell>
				</fo:table-row>
			</xsl:when>
			<xsl:when test="child::ABSVAL=0"/>
			<xsl:when test="position()=last()">
				<fo:table-row line-height="8pt">
<!--					<fo:table-cell padding="6pt">
						<fo:block text-align="end"/>
					</fo:table-cell> -->
					<fo:table-cell border-width="0.05pt" border-color="black" border-style="solid" font-weight="bold" background-color="{$backgroundcolor}" padding="5pt">
						<fo:block text-align="left">
							<xsl:value-of select="child::DESCRIPTION"/>
							<xsl:if test="//REC/MULTICURRENCY = 'true'">
								<xsl:text> ( </xsl:text>
								<xsl:value-of select="//REC/CURRENCY"/>
								<xsl:text> ) </xsl:text>
							</xsl:if>
						</fo:block>
					</fo:table-cell>
					<fo:table-cell border-width="0.05pt" border-color="black" border-style="solid" font-weight="bold" background-color="{$backgroundcolor}" padding="5pt">
						<fo:block text-align="end">
							<xsl:call-template name="zeropadwithdollar">
								<xsl:with-param name="data" select="child::TOTAL"/>
							</xsl:call-template>
							<fo:inline color="{$backgroundcolor}">
								<xsl:text> T</xsl:text>
							</fo:inline>
						</fo:block>
					</fo:table-cell>
				</fo:table-row>
			</xsl:when>
			<xsl:otherwise>
				<fo:table-row line-height="8pt">

							<xsl:choose>
								<xsl:when test=" child::TAXDETAIL != ''">
								<xsl:if test="($VarExpandSubtotals = 'true')">
<!--									<fo:table-cell padding="6pt">
										<fo:block text-align="end"/>
									</fo:table-cell> -->
									<fo:table-cell padding="4pt">
										<fo:block text-align="left">								
									<xsl:value-of select="translate(child::DESCRIPTION,'%',' ' )"/>(  <xsl:value-of select="child::PERCENTVAL"/>%)
										</fo:block>
									</fo:table-cell>
								</xsl:if>
                                </xsl:when>
								<xsl:otherwise>
<!--										<fo:table-cell padding="6pt">
											<fo:block text-align="end"/>
										</fo:table-cell> -->
										<fo:table-cell padding="4pt">
											<fo:block text-align="left">									
									<xsl:value-of select="child::DESCRIPTION"/>
										</fo:block>
									</fo:table-cell>
								</xsl:otherwise>
							</xsl:choose>


							<xsl:choose>
								<xsl:when test=" child::TAXDETAIL != ''">
									<xsl:if test="($VarExpandSubtotals = 'true')">
										<fo:table-cell padding="6pt">
											<fo:block text-align="end">
										<xsl:call-template name="zeropadwithdollar">
											<xsl:with-param name="data" select="child::ABSVAL"/>
										</xsl:call-template>
											<fo:inline color="white">
												<xsl:text> T</xsl:text>
											</fo:inline>
											</fo:block>
										</fo:table-cell>
									</xsl:if>
								</xsl:when>
								<xsl:otherwise>
									<fo:table-cell padding="6pt">
										<fo:block text-align="end">
										 	<xsl:choose>
													<xsl:when test=" child::DISC_CHARGE = 'Discount'">
														(<xsl:call-template name="zeropadwithdollar">
															<xsl:with-param name="data" select="child::ABSVAL"/>
														</xsl:call-template>)
													</xsl:when>
													<xsl:otherwise>
														<xsl:call-template name="zeropadwithdollar">
															<xsl:with-param name="data" select="child::ABSVAL"/>
														</xsl:call-template>													
													</xsl:otherwise>
												</xsl:choose>
									<fo:inline color="white">
										<xsl:text> T</xsl:text>
									</fo:inline>
									</fo:block>
									</fo:table-cell>
								</xsl:otherwise>
							</xsl:choose>


				</fo:table-row>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>




<xsl:template name="topnotes_PSAV">

	<xsl:param name="label0"/>
	<xsl:param name="data0"/>
	<xsl:param name="label1"/>
	<xsl:param name="data1"/>
	<xsl:param name="label2"/>
	<xsl:param name="data2"/>

<!--	<xsl:call-template name="topnotesspacer"/> -->


	<fo:block space-before="0.5cm"  border-color="white">
	<fo:table
		table-layout="fixed"
		border-color	="white"
		border-style	="solid"
		border-width	="0.005pt"
		height		="0.1cm">
		<fo:table-column column-width="6.0cm"/>
		<fo:table-column column-width="6.0cm"/>
		<fo:table-column column-width="7.0cm"/>
		<fo:table-body
			font-family		="{$myfont}"
			font-weight		="normal"
			font-size		="{$fontsize}">

			<fo:table-row
				>

				<xsl:call-template name="topnotescell">
					<xsl:with-param name="label" 	select="$label0"/>
					<xsl:with-param name="data" 	select="$data0"/>
					<xsl:with-param name="location" select="'start'"/>
				</xsl:call-template>

				<xsl:call-template name="topnotescell">
					<xsl:with-param name="label" select="$label1"/>
					<xsl:with-param name="data" select="$data1"/>
					<xsl:with-param name="location" select="'center'"/>
				</xsl:call-template>

				<xsl:call-template name="topnotescell">
					<xsl:with-param name="label" select="$label2"/>
					<xsl:with-param name="data" select="$data2"/>
					<xsl:with-param name="location" select="'end'"/>
				</xsl:call-template>

			</fo:table-row>
		</fo:table-body>
	</fo:table>
	</fo:block>
	<xsl:call-template name="topspacer"/>
</xsl:template>

<xsl:template name="documentlabel_PSAV">
<xsl:variable name="printtitle">Event Order</xsl:variable>

<!--   <xsl:variable name="printtitle">
	   <xsl:choose>
		<xsl:when test="REC/_DOCPAR/PRINTTITLE !=''">
			<xsl:value-of select="REC/_DOCPAR/PRINTTITLE"/>
		</xsl:when>
		<xsl:otherwise>
			<xsl:value-of select="REC/_DOCPAR/SEQUENCE"/>
		</xsl:otherwise>
	   </xsl:choose>
   </xsl:variable>
-->
   <xsl:variable name="sizeoffset">
           <xsl:choose>
		<xsl:when test="string-length($printtitle) > 20">
			<xsl:value-of select="(string-length($printtitle) - 20) * 0.125"/>
		</xsl:when>
		<xsl:otherwise>0</xsl:otherwise>
	   </xsl:choose>
   </xsl:variable>
   
  

   <fo:block-container
   		height		="0.61in"
		width		="{2.6 + $sizeoffset}in"
		top		="0in"
		left		="{7 - $sizeoffset}in"
		position	="absolute">

	<fo:block>
		<fo:instream-foreign-object>
			<svg:svg
				width		="{2.6 + $sizeoffset}in"
				height		="0.61in">

				<svg:rect
					x	="0.15in"
					y	="0in"
					width	="{2.3 + $sizeoffset}in"
					height	="0.3in"
					style	="fill: #000000"/>

				<svg:circle
					cx	="0.15in"
					cy	="0.15in"
					r	="0.15in"
					style	="fill: #000000"/>

				<svg:circle
					cx	="{2.45 + $sizeoffset}in"
					cy	="0.15in"
					r	="0.15in"
					style	="fill: #000000"/>

			</svg:svg>
		</fo:instream-foreign-object>
	</fo:block>
   </fo:block-container>

   <fo:block-container
   		height		="0.61in"
		width		="{2.3 + $sizeoffset}in"
		top		=".08in"
	        left		="{7.15 - $sizeoffset}in"
		position	="absolute">

   		<fo:block
			text-align		="center"
			color			="white"
     			font-family		="Times Roman"
			font-size		="16pt"
			font-weight		="bold">
       			<xsl:value-of select="$printtitle"/>

   		</fo:block>
   		
 	</fo:block-container>
	
</xsl:template>

<xsl:template name="billto_PSAV">
</xsl:template>

<xsl:template name="pagemaster_PSAV">
	<xsl:variable name="pgwidth">11in</xsl:variable>
	<xsl:variable name="pgheight">8.5in</xsl:variable>

    <fo:layout-master-set>
        <fo:simple-page-master
			master-name			="first"
			margin-right		="1.2cm"
			margin-left			="1.2cm"
			margin-bottom		="0.5cm"
			margin-top			="1.0cm"
			page-width			="{$pgwidth}"
			page-height			="{$pgheight}">
			<fo:region-body
				overflow			="auto"
				margin-top			="0.20in"
				margin-bottom		="0.20in"/>
			<fo:region-before extent="0.25in"/>
			<fo:region-after extent="0.20in"/>
        </fo:simple-page-master>


		<fo:simple-page-master
			master-name			="rest"
			margin-right		="1.2cm"
			margin-left			="1.2cm"
			margin-bottom		="0.5cm"
			margin-top			="1.0cm"
			page-width			="{$pgwidth}"
			page-height			="{$pgheight}">
			<fo:region-body
				overflow			="auto"
				margin-top			="0.20in"
				margin-bottom		="0.20in"/>
			<fo:region-before extent="0.25in"/>
			<fo:region-after extent="0.20in"/>
        </fo:simple-page-master>

		<fo:page-sequence-master master-name="psmA">
			<fo:repeatable-page-master-alternatives>
				<fo:conditional-page-master-reference master-reference="first"
					page-position="first" />
				<fo:conditional-page-master-reference master-reference="rest"
					page-position="rest" />
				<!-- recommended fallback procedure -->
				<fo:conditional-page-master-reference master-reference="rest" />
			</fo:repeatable-page-master-alternatives>
		</fo:page-sequence-master>
    </fo:layout-master-set>
</xsl:template>

<xsl:template name="footer_PSAV">
    <fo:block-container
		height		="0.2in"
		width		="10.0in"
		top			="0.01in"
        left		="0.4in"
		position	="center">
        <fo:block>
        	<fo:leader
		   		leader-pattern		="rule"
				rule-thickness		="0.003in"/>
        </fo:block>
	</fo:block-container>
	<fo:block-container
		height		="0.25in"
		width		="11.0in"
		top			="0.20in"
		left		="0.0in"
		position	="absolute">
		<fo:table
			table-layout="fixed"
			height="0.25in">
			<fo:table-column column-width="8.75in"/>
			<fo:table-column column-width="1.25in"/>

			<fo:table-body font-family="{$myfont}" font-weight="normal" font-size="8pt">
				<fo:table-row line-height="11pt">
					<fo:table-cell>
<!--						<fo:block>
							<xsl:call-template name="genericfooter">
								<xsl:with-param name="label" select="'Order Date'"/>
								<xsl:with-param name="data" select="REC/WHENCREATED"/>
							</xsl:call-template>
							
							<xsl:call-template name="genericfooter">
								<xsl:with-param name="label" select="'Terms'"/>
								<xsl:with-param name="data" select="REC/TERM/NAME"/>
							</xsl:call-template>
							<xsl:call-template name="genericfooter">
								<xsl:with-param name="label" select="'Ship Date'"/>
								<xsl:with-param name="data" select="REC/WHENDUE"/>
							</xsl:call-template>
							<xsl:call-template name="genericfooter">
								<xsl:with-param name="label" select="'Customer Id'"/>
								<xsl:with-param name="data" select="REC/CUSTVENDID"/>
							</xsl:call-template>
						</fo:block> -->
					</fo:table-cell>
					<fo:table-cell>
						<fo:block text-align="end">
							<fo:inline font-weight="bold">
								<xsl:text>Page </xsl:text>
								<fo:page-number/>
							</fo:inline>						
						</fo:block>
					</fo:table-cell>
				</fo:table-row>
			</fo:table-body>

		</fo:table>
	</fo:block-container>
</xsl:template>


<xsl:template name="companyaddress_PSAV">
	<xsl:variable name="companynameindent">0.1in</xsl:variable>

		<xsl:if test="(.//LOGO != '')">
				<fo:block
					start-indent="$companynameindent">
					<fo:external-graphic  
						height		="auto" 
						max-width	="auto" 
						src			="{.//COMPANY/LOGO}"  />
				</fo:block>
		</xsl:if>
<!--		<fo:block
			text-align		="start"
			line-height		="18pt"
			font-family		="{$myfont}"
			font-weight		="bold"
			font-size		="12pt"
			height			="4cm"
			width			="12cm"
			start-indent	="{$companynameindent}">
				<xsl:value-of select=".//COMPANY/TITLE"/>
		</fo:block> -->
		<fo:block
			font-size		="{$fontsize}"
			start-indent	="{$companynameindent}">
				<xsl:value-of select=".//COMPANY/ADDRESS1"/>
		</fo:block>
		<xsl:if test=".//COMPANY/ADDRESS2!=''">
			<fo:block
				font-size		="{$fontsize}"
				start-indent	="{$companynameindent}">
					<xsl:value-of select=".//COMPANY/ADDRESS2"/>
			</fo:block>
		</xsl:if>
		<fo:block
			font-size		="{$fontsize}"
			start-indent	="{$companynameindent}">
			<xsl:value-of select=".//COMPANY/CITY"/>
			<xsl:if test=".//COMPANY/STATE != '' and .//COMPANY/CITY != ''">
				<xsl:text>,  </xsl:text>
			</xsl:if>
			<xsl:value-of select=".//COMPANY/STATE"/>
			<xsl:text>  </xsl:text>
			<xsl:value-of select=".//COMPANY/ZIPCODE"/>
		</fo:block>
		<xsl:if test=".//COMPANY/CONTACTPHONE != ''">
			<fo:block
				font-size		="{$fontsize}"
				start-indent	="{$companynameindent}">
					<xsl:text>Ph:  </xsl:text>
					<xsl:value-of select="..//COMPANY/CONTACTPHONE"/>
			</fo:block>
		</xsl:if>
		<xsl:if test=".//COMPANY/FAX != ''">
			<fo:block
				font-size		="{$fontsize}"
				start-indent	="{$companynameindent}">
					<xsl:text>Fax: </xsl:text>
					<xsl:value-of select=".//COMPANY/FAX"/>
			</fo:block>
		</xsl:if>
<fo:block text-align="start">
<fo:table table-layout="fixed" table-width="2.0cm" border-style="solid"  border-color="white" space-before="1.3cm" border-after-width="0.000pt" border-before-width="0.000pt" border-bottom-width="0.000pt" border-top-width="0.000pt" >
	<fo:table-column column-width="1.0in" />
	<fo:table-column column-width="1.0in" />
	
	<fo:table-body font-family="{$myfont}" 	font-size="{$fontsize}" >
		<fo:table-row  line-height="11pt" >
		<fo:table-cell>		
		<fo:block text-align="right" >Venue </fo:block>
		</fo:table-cell>
		<fo:table-cell>
			<fo:block text-align="left">: <xsl:value-of select="REC/VENUE"/></fo:block>
		</fo:table-cell>
		</fo:table-row>		
		<fo:table-row  line-height="11pt" >		
		<fo:table-cell>
		<fo:block text-align="right" >Customer PO </fo:block>
		</fo:table-cell>
		<fo:table-cell>
		<fo:block text-align="left">: <xsl:value-of select="REC/PONUMBER"/></fo:block>
		</fo:table-cell>
		</fo:table-row>		
		<fo:table-row  line-height="11pt" >				
		<fo:table-cell>	
		<fo:block text-align="right" >Hotel Reference</fo:block>
		</fo:table-cell>
		<fo:table-cell>	
		<fo:block text-align="left">: <xsl:value-of select="REC/HOTEL_REFERENCE"/></fo:block>	
		</fo:table-cell>
	</fo:table-row>
	</fo:table-body>
</fo:table>	
</fo:block>		
</xsl:template>

<xsl:template name="generictableentry_PSAV">
	<xsl:param name="data"/>
	<xsl:param name="data2"/>
	<xsl:param name="data3"/>
	<xsl:param name="data4"/>
	<xsl:param name="checktax"/>
	<xsl:param name="position"/>
	<fo:table-cell padding="4pt">

		<fo:block text-align="{$position}">
			<xsl:value-of select="$data"/>
			<xsl:if test="$checktax='true'">
				<xsl:choose>
					<xsl:when test="child::ITEM/TAXABLE='true' ">
						<xsl:text> T</xsl:text>
					</xsl:when>
					<xsl:otherwise>
						<fo:inline color="white">
							<xsl:text> X</xsl:text>
						</fo:inline>
					</xsl:otherwise>
				</xsl:choose>
			</xsl:if>
		<xsl:if test="($data2 != '' ) and (string-length($data2) > 1)">			
			<xsl:text> -- </xsl:text><xsl:value-of select="$data2"/>
		</xsl:if>
		<xsl:if test="$data3 != ''">
			 <xsl:text> -- </xsl:text><xsl:value-of select="$data3"/>
		</xsl:if>
		<xsl:if test="$data4 != ''">
				<xsl:text> -- Stock#: </xsl:text>
				<xsl:value-of select="$data4"/>

			</xsl:if>			
		</fo:block>
<!--		<fo:block
			text-align		="{$position}"
			start-indent	=".25in"
			font-size		="7pt">
			

		</fo:block>
		<fo:block
			text-align		="{$position}"
			start-indent	=".25in"
			font-size		="7pt">
			

		</fo:block>
		<xsl:if test="$data4 != ''">
			<fo:block
				text-align		="{$position}"
				start-indent	=".25in"
				font-size		="7pt">
				<xsl:text>Stock#: </xsl:text>
				<xsl:value-of select="$data4"/>

			</fo:block>
		</xsl:if> -->
	</fo:table-cell>
</xsl:template>

</xsl:stylesheet>
