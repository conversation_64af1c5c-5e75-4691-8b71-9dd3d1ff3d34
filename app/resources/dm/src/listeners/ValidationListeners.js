export const ValidationListeners = (listener, dependencies) => {
    const { api, axios, OPERATION, BACKEND_URL } = dependencies;

    const validate = async (context, isPartial, dataUrl, existingJobId) => {
        let sheet = {};
        let canRunPartialValidation = false;

        if(context.sheetId){
            const response = await api.sheets.get(context.sheetId);
            sheet = response.data;
            canRunPartialValidation = sheet.metadata?.canRunPartialValidation ?? false;
        }

        // stop the partial validation if is not coming from the user edit
        // it should be stopped in the case:
        // - we are doing the full validation on upload file
        // - we are doing the transfer functionality
        if ( isPartial && !canRunPartialValidation ) {
            return;
        }
        let validateDataJob;
        if (existingJobId) {
            validateDataJob = await api.jobs.get(existingJobId);
        } else {

            const validateJobRequest = {
                operation: isPartial ? 'validate-data-partial' : 'validate-data',
                type: 'sheet',
                source: context.sheetId,
                trigger: 'immediate',
                mode: isPartial ? 'background' : 'foreground'
            };
            validateDataJob = await api.jobs.create(validateJobRequest);
        }

        await api.jobs.ack(validateDataJob.data.id);

        const validateBackendData = {
            params: {
                '.jID': validateDataJob.data.id,
                '.action': isPartial ? 'platform-validate-partial' : 'platform-validate',
                '.op': OPERATION,
                '.pURL': dataUrl
            }
        };

        if(isPartial) {
            validateBackendData.params['.cID'] = context.commitId;
        }

        const outcome = {
            outcome: {
                heading: GT('IA.VALIDATION_RESULTS'),
                message: GT('IA.VALIDATION_SUCCESS'),
                acknowledge: false,
                trigger: "automatic_silent"
            }
        };

        await axios.get(BACKEND_URL, validateBackendData).then(
            (response) => {
                if ( response.data.success ) {
                    outcome.outcome.message = [
                        GT({
                               "id": "IA.VALIDATION_SUCCESS",
                               "placeHolders": [{
                                   "name": "TOTAL_SUCCESS",
                                   "value": `${response.data.success?.totalSuccess}`
                               }]
                           }),
                        GT({
                               "id": "IA.VALIDATION_FAIL",
                               "placeHolders": [{
                                   "name": "TOTAL_FAIL",
                                   "value": `${response.data.success?.totalError}`
                               }]
                           })
                    ].join(', ');
                    api.jobs.complete(validateDataJob.data.id, outcome);
                } else {
                    outcome.outcome.message = GT('IA.VALIDATION_FAILED');
                    api.jobs.fail(validateDataJob.data.id, outcome);
                }
            }
        ).catch(
            (error) => {
                outcome.outcome.message = GT('IA.ERROR_OCCURRED_WHILE_MAKING_REQUEST');
                api.jobs.fail(validateDataJob.id, outcome);
            }
        ).finally(() => {
                         if ( !isPartial ) {

                             api.sheets.updateSheet(context.sheetId, {
                                 metadata: {
                                     'canRunPartialValidation': true,
                                     'selectedTabName': sheet?.metadata?.selectedTabName ?? ''
                                 }
                             });
                         }
                     }
            );
    }


    listener.on('job:ready', { job: 'workbook:re-validate' },
        async({ context }) => {
            await validate(context, false, context.dataUrl, context.jobId);
        }
    );
};
