#!/usr/local/php/bin/php

<?php
/**
 * convert Email Token to tokens for I18N standard
 */

print( "Email JSON Token to I18N TOKEN Converter started.\n\n" );
// print( __FILE__ . ':' . __LINE__ . "\n" );

define('INTACCTCONTEXT', "true");

chdir(trim(getcwd()));

$basePath = "../../";

if (! file_exists($basePath . "link")) {
    // print( __FILE__ . ':' . __LINE__ . "\n" );
    echo "Can't find 'link' in '$basePath'";
    exit;
}

// email token files are in a flat directory structure (locale is part of filename)
//$EMAIL_SOURCE_RESOURCE_DIR = $basePath . 'lcemail/';
// json files in build/email will be linked via appconnect
$EMAIL_TARGET_RESOURCE_DIR = $basePath . 'build/email/';

// print( __FILE__ . ':' . __LINE__ . "\n" );
ini_set('include_path', ini_get('include_path') . ":"
    . $basePath . "source/*:"
    . $basePath . "link/private/etc:"
    . $basePath . "link/private/inc:"
    . $basePath . "link/private/lib/log4php:"
    . $basePath . "link/private/lib/misc:"
    . $basePath . "link/private/lib/nusoap:"
    . $basePath . "link/private/lib/pear:"
    . $basePath . "link/private/lib/php4:"
    . $basePath . "link/private/lib/symfony-yaml:"
    . $basePath . "link/private/lib/sforcev2:"
);

// print( __FILE__ . ':' . __LINE__ . "\n" );
require_once('util.inc');

try {
    $finalEmailTokenSet = buildEntireEmailTokenI18N($EMAIL_TARGET_RESOURCE_DIR);
    file_put_contents("email.en-US.json", json_encode($finalEmailTokenSet, JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT));
    //print("**DEBUG**\n");
    //var_dump($finalEmailTokenSet);
} catch ( Throwable $t ) {
    echo "Caught exception: " . $t->getMessage() . "\n";
    exit(1);
}




/**
 * @param string $emailResourceDir
 *
 * @return array
 * @throws Exception
 */

function buildEntireEmailTokenI18N(string $emailResourceDir): array
{
    // multiple email json files into one big structure to be put into a single file
    $glob = glob($emailResourceDir . "*.json");
    $size = count($glob);

    $finalEmailTokenSet = [];

    print ("Converting $size email JSON token files to single I18N email token file\n");

    foreach ($glob as $emailTokenFile) {
        print('Found Email Token file: ' . $emailTokenFile . "\n" );
        $finalEmailTokenSet = array_merge(
            $finalEmailTokenSet,
            convertEmailResourceToToken($emailTokenFile, $emailResourceDir)
        );
    }

    return $finalEmailTokenSet;
}

/**
 * @param string $emailTokenFile
 * @param array  $finalEmailTokenSet
 *
 * @return array
 * @throws Exception
 */
function convertEmailResourceToToken(string $emailTokenFile, string $emailResourceDir) : array
{
    $tokenName = basename($emailTokenFile, '.en_US.UTF-8.json');
    $emailToken = EmailToken::buildFromResource($tokenName, null, $emailResourceDir);
    $data = $emailToken->convertEmailDataToTokenSet();
//    echo "Data for token $emailTokenFile:\n";
//    var_export($data);
    return $data;
}
