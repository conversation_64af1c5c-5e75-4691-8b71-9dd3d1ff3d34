<?php
/**
 * test cases for GLBatch mockup versions
 * Uses Company and login ID defined in unitTest/core/unitTest.cfg
 *
 * <AUTHOR>
 * @copyright Copyright (C)2020 Sage Intacct Corporation, All Rights Reserved
 */

namespace tests\source\api\framework\crud;
require_once (__DIR__.'/../APITestHelper.cls');

/**
 * Class APIAdapterGLBatchVersionTest
 * @package tests\source\api\framework\crud
 */
class APIAdapterGLBatchVersionTest extends \unitTest\core\UnitTestBaseContext
{

    public function testNothing()
    {
        $this->assertTrue(true);
    }

    /**
     * @return array
     * @throws \APIAdapterException
     */
    public function testReadV5GLCollection()
    {
        $objectName = '__testglbatch';
        $operation = 'read';
        $version = 5 . '-test';

        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $this->assertTrue($orcCRUD !== null);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName]];
        $result = $orcCRUD->execute(
            $operation, [], $extraParams
        );

        $this->printResult('['. __METHOD__ . ' RESULT]: ', $result);
        return $result;
    }

    /**
     * @return array
     * @throws \APIAdapterException
     */
    public function testAdapterCreateV2GLBatch()
    {
        if (0 === count($request = $this->getGLBatchV02Input())) {
            echo 'skip test: all glaccounts require dimensions';
            return [];
        }
        $this->printResult('['. __METHOD__ . ' REQUEST]: ', $request);
        $objectName = '__testglbatch';
        $operation = 'create';
        $version = 2 . '-test';

        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $this->assertTrue($orcCRUD !== null);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName]];
        $result = $orcCRUD->execute(
            $operation, $request, $extraParams
        );

        print_r($request);
        $this->assertNotEmpty($result['id'],
                              \APITestHelper::getDebugDetails(__METHOD__, $result ?? [], 'result', \Globals::$g->gErr->getCDescriptionErrors()));
        $this->printResult('['. __METHOD__ . ' RESULT]: ', $result);
        $recordno = $result['id'];

        return [ 'KEY' => $recordno, 'DATA' => $request ];
    }

    /**
     * @depends testAdapterCreateV2GLBatch
     * @param   array $input
     * @return  array
     * @throws  \APIAdapterException
     */
    public function testV2ReadV2GLBatch($input)
    {
        $objectName = '__testglbatch';
        $operation = 'read';
        $version = 2 . '-test';
        $key = $input['KEY'];

        $this->assertTrue(key_exists('KEY', $input));
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $this->assertTrue($orcCRUD !== null);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $key,'','']];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION]   = $version;
        $result = $orcCRUD->execute(
            $operation, [], $extraParams
        );

        $this->printResult('['. __METHOD__ . ' RESULT]: ', $result);
        $this->assertNotEmpty(count(array_intersect(['key', 'id'], array_keys($result))) > 0);
        $this->assertTrue(key_exists('items', $result));
        $this->assertTrue(key_exists('DATA', $input));
        $this->assertTrue(key_exists('items', $input['DATA']));
        $this->assertTrue(count($result['items']) == count($input['DATA']['items']));
        foreach($result['items'] as $item) {
            $this->assertTrue(in_array($item['state'],
                                         ['DRAFT', 'SUBMITTED', 'PARTIALLY APPROVED',
                                          'APPROVED', 'POSTED', 'DECLINED', 'REVERSAL PENDING',
                                          'REVERSED']));
            $this->assertTrue(ctype_lower($item['currency']));
        }

        return $input;
    }

    /**
     * @depends testV2ReadV2GLBatch
     *
     * @param   array $input
     *
     * @return  array
     * @throws  \APIAdapterException
     */
    public function testV3ReadV2GLBatch($input)
    {
        $objectName = '__testglbatch';
        $operation = 'read';
        $version = 3 . '-test';
        $key = $input['KEY'];

        $this->assertTrue(key_exists('KEY', $input));
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $this->assertTrue($orcCRUD !== null);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $key,'','']];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION]   = $version;
        $result = $orcCRUD->execute(
            $operation, [], $extraParams
        );

        $this->printResult('['. __METHOD__ . ' RESULT]: ', $result);
        $this->assertNotEmpty(count(array_intersect(['key', 'id'], array_keys($result))) > 0);
        $this->assertTrue(key_exists('journalKey', $result));
        $this->assertTrue(key_exists('items', $result));
        $this->assertTrue(key_exists('DATA', $input));
        $this->assertTrue(key_exists('items', $input['DATA']));
        $this->assertTrue(count($result['items']) == count($input['DATA']['items']));
        foreach($result['items'] as $item) {
            $this->assertTrue(in_array($item['state'],
                                         ['DRAFT', 'SUBMITTED', 'PARTIALLY APPROVED',
                                          'APPROVED', 'POSTED', 'DECLINED', 'REVERSAL PENDING',
                                          'REVERSED']));
            $this->assertTrue(ctype_lower($item['currency']));
        }

        return $input;
    }


    /**
     * @depends testV3ReadV2GLBatch
     *
     * @param   array $input
     *
     * @return  array
     * @throws  \APIAdapterException
     */
    public function testV4ReadV2GLBatch($input)
    {
        $objectName = '__testglbatch';
        $operation = 'read';
        $version = 4 . '-test';
        $key = $input['KEY'];

        $this->assertTrue(key_exists('KEY', $input));
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $this->assertTrue($orcCRUD !== null);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $key,'','']];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        $result = $orcCRUD->execute(
            $operation, [], $extraParams
        );

        $this->printResult('['. __METHOD__ . ' RESULT]: ', $result);
        $this->assertNotEmpty(count(array_intersect(['key', 'id'], array_keys($result))) > 0);
        $this->assertTrue(key_exists('journalKey', $result));
        $this->assertTrue(key_exists('items', $result));
        $this->assertTrue(key_exists('DATA', $input));
        $this->assertTrue(key_exists('items', $input['DATA']));
        $this->assertTrue(count($result['items']) == count($input['DATA']['items']));
        foreach($result['items'] as $item) {
            $this->assertTrue(in_array($item['state'],
                                       ['draft', 'submitted', 'partially approved',
                                        'approved', 'posted', 'declined', 'reversal pending',
                                        'reversed']));
            $this->assertTrue(ctype_upper($item['currency']));
        }

        return $input;
    }


    /**
     * @depends testV4ReadV2GLBatch
     *
     * @param   array $input
     *
     * @return  array
     * @throws  \APIAdapterException
     */
    public function testV5ReadV2GLBatch($input)
    {
        $objectName = '__testglbatch';
        $operation = 'read';
        $version = 5 . '-test';
        $key = $input['KEY'];

        $this->assertTrue(key_exists('KEY', $input));
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $this->assertTrue($orcCRUD !== null);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $key,'','']];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        $result = $orcCRUD->execute(
            $operation, [], $extraParams
        );

        $this->printResult('['. __METHOD__ . ' RESULT]: ', $result);
        $this->assertNotEmpty(count(array_intersect(['key', 'id'], array_keys($result))) > 0);
        $this->assertTrue(key_exists('journalKey', $result));
        $this->assertTrue(key_exists('entries', $result));
        $this->assertTrue(key_exists('DATA', $input));
        $this->assertTrue(key_exists('items', $input['DATA']));
        $this->assertTrue(count($result['entries']) == count($input['DATA']['items']));
        foreach($result['entries'] as $item) {
            $this->assertTrue(in_array($item['state'],
                                       ['Draft', 'Submitted', 'Partially Approved', 'Approved',
                                        'Posted', 'Declined', 'Reversal Pending', 'Reversed']));
            $this->assertTrue(ctype_upper($item['currency']));
        }

        return $input;
    }


    /**
     * @depends testV5ReadV2GLBatch
     * @param   array $input
     * @return  array
     * @throws  \APIAdapterException
     */
    public function testV2PatchV2GLBatch($input)
    {
        $objectName = '__testglbatch';
        $operation = 'read';
        $version = 2 . '-test';
        $key = $input['KEY'];

        $this->assertTrue(key_exists('KEY', $input));
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $this->assertTrue($orcCRUD !== null);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $key,'','']];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        $result = $orcCRUD->execute(
            $operation, [], $extraParams
        );
        $this->printResult('['. __METHOD__ . ' EXISTING GLBATCH]: ', $result);

        $this->assertTrue(key_exists('id', $result));
        $result = $this->preparePatchInputFromGetOutput($result);
        $this->printResult('['. __METHOD__ . ' UPDATE INPUT]: ', $result);

        $operation = 'patch';
        $version = 2 . '-test';

        $this->assertTrue(key_exists('KEY', $input));
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $this->assertTrue($orcCRUD !== null);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $result['key'],'',''],
                        \APIConstants::API_EXTRA_PARAM_REQ => [\APIConstants::API_EXTRA_PARAM_REQ_VERSION => $version]
        ];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION]   = $version;
        unset($result[\APIConstants::API_OBJECT_KEY_FIELD_NAME]);
        unset($result[\APIConstants::API_WEB_URL_FIELD_NAME]);
        \APITestHelper::unsetRecursive($result, \APIConstants::API_HREF_FIELD_NAME);
        \APITestHelper::unsetRecursive($result, 'currency'); // currency is mutable:false

        $updateResult = $orcCRUD->execute(
            $operation, $result, $extraParams
        );
        $this->printResult('['. __METHOD__ . ' UPDATE RESULT]: ', $updateResult);
        $this->assertTrue(key_exists('id', $updateResult), json_encode($updateResult));

        return ['INPUT' => $input, 'UPDATE' => $result];
    }

    /**
     * @return void
     * @throws \APIAdapterException
     * @throws \APIException
     */
    public function testV5PatchGLBatchAddItem()
    {
        if (0 === count($request = $this->getGLBatchV05Input())) {
            $this->assertTrue(true);
            echo 'skip : ' . __METHOD__;
            return;
        }
        $this->printResult('['. __METHOD__ . ' INITIAL REQUEST]: ', $request);
        $objectName = '__testglbatch';
        $operation = 'create';
        $version = 5 . '-test';

        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName]];
        $result = $orcCRUD->execute(
            $operation, $request, $extraParams
        );

        $key = $result['id'];
        $this->assertNotNull($key, json_encode($result));

        // Now test the actual item add
        $operation = 'patch';

        // get new orchestrator
        $orcCRUD = $handler->getOrchestrator();

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $key,'',''],
                        \APIConstants::API_EXTRA_PARAM_REQ => [\APIConstants::API_EXTRA_PARAM_REQ_VERSION => $version]
        ];

        $updateRequest = [
            "batchTitle" => $request["batchTitle"] . "-updated",
            "entries" => [
                0 => \APITestHelper::getGLEntryV05Input()
            ]
        ];

        // set amount to 0 not to update total amounts
        $updateRequest["entries"][0]["amount"] = 0;
        // print_r($updateRequest);
        $updateResult = $orcCRUD->execute(
            $operation, $updateRequest, $extraParams
        );
        $this->printResult('['. __METHOD__ . ' UPDATE RESULT]: ', $updateResult);
        $this->assertTrue(key_exists('id', $updateResult), json_encode($updateResult));
    }


    /**
     * @depends testV2PatchV2GLBatch
     * @param   array $patchedInput
     * @return  array
     * @throws  \APIAdapterException
     */
    public function testV2ReadV2PatchedGLBatch($patchedInput)
    {
        $objectName = '__testglbatch';
        $operation = 'read';
        $version = 2 . '-test';

        $this->assertTrue(key_exists('INPUT', $patchedInput));
        $input = $patchedInput['INPUT'];
        $key = $input['KEY'];
        $this->assertTrue(key_exists('KEY', $input));
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $this->assertTrue($orcCRUD !== null);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $key,'','']];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        $result = $orcCRUD->execute(
            $operation, [], $extraParams
        );

        $this->printResult('['. __METHOD__ . ' RESULT]: ', $result);
        $this->assertNotEmpty(count(array_intersect(['key', 'id'], array_keys($result))) > 0);
        $this->assertTrue(key_exists('items', $result));
        $this->assertTrue(key_exists('DATA', $input));
        $this->assertTrue(key_exists('items', $input['DATA']));
        $this->assertTrue(count($result['items']) == count($input['DATA']['items']));
        foreach($result['items'] as $item) {
            $this->assertTrue(key_exists('description', $item));
            $this->assertTrue(key_exists('currency', $item));
            $this->assertTrue(key_exists('id', $item));
            $this->assertTrue(strtolower($item['description']) == strtolower($item['currency']));
        }

        return $input;
    }

    /**
     * @return void
     * @throws \APIException
     * @throws \JsonException
     */
    public function testBeta2WithV5GLBatch()
    {
        if ( 0 === count($request = $this->getGLBatchV05Input()) ) {
            echo 'skip test: all glaccounts require dimensions';
            return;
        }
        $this->printResult('[' . __METHOD__ . ' REQUEST]: ', $request);

        $urlPost = "v5-test-beta2/objects/gl/__testglbatch";
        $resultPost = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $urlPost,
                                                                       \APIConstants::API_HTTP_METHOD_POST,
                                                                       json_encode($request, JSON_THROW_ON_ERROR));
        $this->assertNotNull($resultPost);

        $this->assertNotEmpty($resultPost['ia::result']['key']);
        $urlKey = "v5-test-beta2/objects/gl/__testglbatch/" . $resultPost['ia::result']['key'];
        $resultGet = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $urlKey,
                                                                      \APIConstants::API_HTTP_METHOD_GET, null);
        $this->assertNotNull($resultGet);

        $this->assertNotNull($resultGet['ia::result']);
        $requestPatch = $this->preparePatchInputFromGetOutput($resultGet['ia::result'], 'entries', false);
        $postfix = ' patch';
        $requestPatch['batchTitle'] .= $postfix;
        $resultPatch = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $urlKey,
                                                                        \APIConstants::API_HTTP_METHOD_PATCH,
                                                                        json_encode($requestPatch, JSON_THROW_ON_ERROR));
        $this->assertNotNull($resultPatch);

        $resultGetPatched = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $urlKey,
                                                                             \APIConstants::API_HTTP_METHOD_GET, null);
        $this->assertNotNull($resultGetPatched);
        $this->assertTrue(!empty($resultGetPatched['ia::result']['batchTitle']));
        $this->assertTrue(str_ends_with($resultGetPatched['ia::result']['batchTitle'], $postfix));

        $resultDelete = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $urlKey,
                                                                             \APIConstants::API_HTTP_METHOD_DELETE, null);
        $this->assertNull($resultDelete);

        $resultGetDeleted = \APITestHelper::executeAndValidateAPIDispatcher(1, 1, $urlKey,
                                                                            \APIConstants::API_HTTP_METHOD_GET, null);
        $this->assertNotNull($resultGetDeleted);
    }


    /**
     * @return array
     * @throws \APIAdapterException
     */
    public function testAdapterCreateV5GLBatch()
    {
        if (0 === count($request = $this->getGLBatchV05Input())) {
            echo 'skip test: all glaccounts require dimensions';
            return [];
        }
        $this->printResult('['. __METHOD__ . ' REQUEST]: ', $request);
        $objectName = '__testglbatch';
        $operation = 'create';
        $version = 5 . '-test';

        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $this->assertTrue($orcCRUD !== null);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName]];
        $result = $orcCRUD->execute(
            $operation, $request, $extraParams
        );

        print_r($request);
        $this->assertNotEmpty($result['id'],
                              \APITestHelper::getDebugDetails(__METHOD__, $result ?? [], 'result', \Globals::$g->gErr->getCDescriptionErrors()));
        $this->printResult('['. __METHOD__ . ' RESULT]: ', $result);
        $recordno = $result['id'];

        return [ 'KEY' => $recordno, 'DATA' => $request ];
    }

    /**
     * @depends testAdapterCreateV5GLBatch
     * @param   array $input
     * @return  array
     * @throws  \APIAdapterException
     */
    public function testV2ReadV5GLBatch($input)
    {
        $objectName = '__testglbatch';
        $operation = 'read';
        $version = 2 . '-test';
        $key = $input['KEY'];

        $this->assertTrue(key_exists('KEY', $input));
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $this->assertTrue($orcCRUD !== null);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $key,'','']];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION]   = $version;
        $result = $orcCRUD->execute(
            $operation, [], $extraParams
        );

        $this->printResult('['. __METHOD__ . ' RESULT]: ', $result);
        $this->assertNotEmpty(count(array_intersect(['key', 'id'], array_keys($result))) > 0);
        $this->assertTrue(key_exists('items', $result));
        $this->assertTrue(key_exists('DATA', $input));
        $this->assertTrue(key_exists('entries', $input['DATA']));
        $this->assertTrue(count($result['items']) == count($input['DATA']['entries']));
        foreach($result['items'] as $item) {
            $this->assertTrue(in_array($item['state'],
                                       ['DRAFT', 'SUBMITTED', 'PARTIALLY APPROVED',
                                        'APPROVED', 'POSTED', 'DECLINED', 'REVERSAL PENDING',
                                        'REVERSED']));
            $this->assertTrue(ctype_lower($item['currency']));
        }

        return $input;
    }

    /**
     * @depends testV2ReadV5GLBatch
     * @param   array $input
     * @return  array
     * @throws  \APIAdapterException
     */
    public function testV3ReadV5GLBatch($input)
    {
        $objectName = '__testglbatch';
        $operation = 'read';
        $version = 3 . '-test';
        $key = $input['KEY'];

        $this->assertTrue(key_exists('KEY', $input));
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $this->assertTrue($orcCRUD !== null);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $key,'','']];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        $result = $orcCRUD->execute(
            $operation, [], $extraParams
        );

        $this->printResult('['. __METHOD__ . ' RESULT]: ', $result);
        $this->assertNotEmpty(count(array_intersect(['key', 'id'], array_keys($result))) > 0);
        $this->assertTrue(key_exists('journalKey', $result));
        $this->assertTrue(key_exists('items', $result));
        $this->assertTrue(key_exists('DATA', $input));
        $this->assertTrue(key_exists('entries', $input['DATA']));
        $this->assertTrue(count($result['items']) == count($input['DATA']['entries']));
        foreach($result['items'] as $item) {
            $this->assertTrue(in_array($item['state'],
                                       ['DRAFT', 'SUBMITTED', 'PARTIALLY APPROVED',
                                        'APPROVED', 'POSTED', 'DECLINED', 'REVERSAL PENDING',
                                        'REVERSED']));
            $this->assertTrue(ctype_lower($item['currency']));
        }

        return $input;
    }


    /**
     * @depends testV3ReadV5GLBatch
     * @param   array $input
     * @return  array
     * @throws  \APIAdapterException
     */
    public function testV4ReadV5GLBatch($input)
    {
        $objectName = '__testglbatch';
        $operation = 'read';
        $version = 4 . '-test';
        $key = $input['KEY'];

        $this->assertTrue(key_exists('KEY', $input));
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $this->assertTrue($orcCRUD !== null);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $key,'','']];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        $result = $orcCRUD->execute(
            $operation, [], $extraParams
        );

        $this->printResult('['. __METHOD__ . ' RESULT]: ', $result);
        $this->assertNotEmpty(count(array_intersect(['key', 'id'], array_keys($result))) > 0);
        $this->assertTrue(key_exists('journalKey', $result));
        $this->assertTrue(key_exists('items', $result));
        $this->assertTrue(key_exists('DATA', $input));
        $this->assertTrue(key_exists('entries', $input['DATA']));
        $this->assertTrue(count($result['items']) == count($input['DATA']['entries']));
        foreach($result['items'] as $item) {
            $this->assertTrue(in_array($item['state'],
                                       ['draft', 'submitted', 'partially approved',
                                        'approved', 'posted', 'declined', 'reversal pending',
                                        'reversed']));
            $this->assertTrue(ctype_upper($item['currency']));
        }

        return $input;
    }


    /**
     * @depends testV4ReadV5GLBatch
     * @param   array $input
     * @return  array
     * @throws  \APIAdapterException
     */
    public function testV5ReadV5GLBatch($input)
    {
        $objectName = '__testglbatch';
        $operation = 'read';
        $version = 5 . '-test';
        $key = $input['KEY'];

        $this->assertTrue(key_exists('KEY', $input));
        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $this->assertTrue($orcCRUD !== null);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $key,'','']];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        $result = $orcCRUD->execute(
            $operation, [], $extraParams
        );

        $this->printResult('['. __METHOD__ . ' RESULT]: ', $result);
        $this->assertNotEmpty(count(array_intersect(['key', 'id'], array_keys($result))) > 0);
        $this->assertTrue(key_exists('journalKey', $result));
        $this->assertTrue(key_exists('entries', $result));
        $this->assertTrue(key_exists('DATA', $input));
        $this->assertTrue(key_exists('entries', $input['DATA']));
        $this->assertTrue(count($result['entries']) == count($input['DATA']['entries']));
        foreach($result['entries'] as $item) {
            $this->assertTrue(in_array($item['state'],
                                       ['Draft', 'Submitted', 'Partially Approved', 'Approved',
                                        'Posted', 'Declined', 'Reversal Pending', 'Reversed']));
            $this->assertTrue(ctype_upper($item['currency']));
        }

        return $input;
    }



    /**
     * @throws  \APIAdapterException
     */
    public function testV2CRUD()
    {
        $this->assertTrue(true);
        $objectName = '__testglbatch';
        $version = 2 . '-test';

        if (0 === count($data = $this->getCompactGLBatchV02Input())) {
            echo 'skip test: all glaccounts require dimensions';
        }
        $postInput = $data['POST'];
        $patchInput = $data['PATCH'];

        $handler = \RegistryLoader::getInstance($version)->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
        $orcCRUD = $handler->getOrchestrator();
        $this->assertTrue($orcCRUD !== null);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName]];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        $postResult = $orcCRUD->execute(
            'create', $postInput, $extraParams
        );
        $this->printResult('['. __METHOD__ . ' POST GLBATCH]: ', $postResult);

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $postResult['id'],'','']];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        $readResult = $orcCRUD->execute(
            'read', [], $extraParams
        );
        $this->printResult('[' . __METHOD__ . ' READ Version#' . $version . ' GLBATCH]: ', $readResult);

        $patchInputKey = $readResult['id'];
        $this->assertTrue($this->copyOwnedObjectKey($patchInput, 'key', $readResult, 'id', 'items'),
                          __METHOD__ . " has failed in copyOwnedObjectKey()");
        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $patchInputKey,'','']];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        $patchResult = $orcCRUD->execute(
            'patch', $patchInput, $extraParams
        );
        $this->printResult('['. __METHOD__ . ' PATCH GLBATCH]: ', $patchResult);

        for ($i = 2; $i < 6; $i++) {
            $handler = \RegistryLoader::getInstance($i . '-test')->getHandler(\RegistryLoader::CRUD_SERVICE_NAME);
            $orcCRUD = $handler->getOrchestrator();
            $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $postResult['id'],'','']];
            $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
            $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
            $readResult = $orcCRUD->execute(
                'read', [], $extraParams
            );
            $this->printResult('[' . __METHOD__ . ' READ Version#' . $i . ' GLBATCH]: ', $readResult);
        }

        $extraParams = [\APIConstants::API_EXTRA_PARAM_HIERARCHY => [$objectName, $postResult['id'],'','']];
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_OBJECT] = $objectName;
        $extraParams[\APIConstants::API_EXTRA_PARAM_REQ][\APIConstants::API_EXTRA_PARAM_REQ_VERSION] = $version;
        $readResult = $orcCRUD->execute(
            'delete', [], $extraParams
        );
        $this->printResult('['. __METHOD__ . ' DELETE GLBATCH]: ', $readResult);
    }


    public static function setUpBeforeClass() : void
    {
        parent::setUpBeforeClass();
        print_r("\n" .'==================== [' . __CLASS__ . ' START] ====================' . "\n");

    }

    public static function tearDownAfterClass() : void
    {
        print_r("\n" . '==================== [' . __CLASS__ . ' END] ====================' . "\n\n");
        parent::tearDownAfterClass();
    }


    /**
     * ==================================
     *  Helping Functions
     * ==================================
     */

    /**
     * @param array  $target
     * @param string $targetKeyName
     * @param array  $source
     * @param string $sourceKeyName
     * @param string $ownedObjectPath
     *
     * @return bool
     */
    public function copyOwnedObjectKey(&$target, $targetKeyName, $source, $sourceKeyName, $ownedObjectPath)
    {
        if (!key_exists($ownedObjectPath, $target)
            || !key_exists($ownedObjectPath, $source)
            || count($target[$ownedObjectPath]) > count($source[$ownedObjectPath])) {
            echo __METHOD__ . "\n"
                 . "\t[source]: " . print_r($source ?? [], true) . "\n"
                 . "\t[target]: " . print_r($target ?? [], true) . "\n";
            return false;
        }

        for ($i=0; $i < count($target[$ownedObjectPath]); $i++) {
            $target[$ownedObjectPath][$i][$targetKeyName] = $source[$ownedObjectPath][$i][$sourceKeyName];
        }
        return true;
    }

    /**
     * @param string   $caseName
     * @param string[] $result
     */
    private function printResult($caseName, $result)
    {
        $msg = $caseName . " : " . print_r($result, true);
        $this->assertTrue($msg != '');
        //print_r($msg);
    }


    /**
     * @return mixed
     * @throws \Exception
     */
    public function getVendor()
    {
        $locationManager = \Globals::$g->gManagerFactory->getManager('vendor');
        // $query = ['selects' => ['LOCATIONID', 'NAME'], 'filters' => [[['STATUS', '=', 'active']]]];
        // $result = $locationManager->GetList($query);
        $result = $locationManager->GetList();
        $this->assertTrue($result != null);
        foreach($result as $vendor) {
            if ($vendor['VENDORID'] && $vendor['NAME']) {
                return $vendor['VENDORID'];
            }
        }
        throw new \Exception('Cannot find a valid VENDORID');
    }


    /**
     * @return array|mixed
     * @throws \Exception
     */
    public function getJournal()
    {
        $journalManager = \Globals::$g->gManagerFactory->getManager('journal');
        $result = $journalManager->GetList();
        $this->assertTrue($result != null);
        $symbolList = [];
        foreach($result as $symbol) {
            $symbolList[$symbol['SYMBOL']] = $symbol['TITLE'];
        }
        ksort($symbolList);
        $this->assertTrue($symbolList != null);
        $this->assertTrue(count($symbolList) > 0);
        // print_r($symbolList);
        foreach($symbolList as $symbolKey => $symbolTitle) {
            if ($symbolKey && trim($symbolKey) !== '') {
                return $symbolKey;
            }
        }
        throw new \Exception('Cannot find valid JOURNAL');
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function getAccount()
    {
        $accountManager = \Globals::$g->gManagerFactory->getManager('glaccount');
        $result = $accountManager->GetList();
        $this->assertTrue($result != null);
        $this->assertTrue(count($result) > 1, 'Cannot get valid accounts.');
        $accounts = [];
        foreach($result as $glaccount) {
            $controlFlag = $glaccount['SUBLEDGERCONTROLON'];
            $recordNo = $glaccount['RECORDNO'];
            $accountKey = $glaccount['ACCOUNTNO'];
            if ($controlFlag === "false") {
                $isRequireDimension = false;
                foreach ($glaccount as $key=>$value) {
                    if ((0 === strpos($key, 'REQUIRE') && "true" === $value)) {
                        // we cannot use this gl account, otherwise we need to prepare all required dimensions
                        $isRequireDimension = true;
                        break;
                    }
                }
                if ($isRequireDimension) {
                    echo 'glAccount requires dimension(s)' . print_r($glaccount, true) . "\n";
                    continue;
                }
                echo 'glAccount[' . count($accounts) . ']='. print_r($glaccount, true) . "\n";
                $accounts[] = array(
                    'RECORDNO'=>$recordNo,
                    'ACCOUNTNO'=>$accountKey
                );

                if (count($accounts) > 1) {
                    break;
                }
            }
        }
        if (1 === count($accounts)) {
            $accounts[1] = $accounts[0];
        }
        return $accounts;
    }


    /**
     * @return mixed
     * @throws \Exception
     */
    public function getLocation()
    {
        $locationManager = \Globals::$g->gManagerFactory->getManager('location');
        $query = ['selects' => ['LOCATIONID', 'NAME'], 'filters' => [[['STATUS', '=', 'active']]]];
        $result = $locationManager->GetList($query);
        $this->assertTrue($result != null);
        foreach($result as $location) {
            if ($location['LOCATIONID'] && $location['NAME']) {
                return $location['LOCATIONID'];
            }
        }
        throw new \Exception('Cannot find a valid LOCATION');
    }

    /**
     * @return string
     * @throws \Exception
     */
    public function getCurrency()
    {
        $cny = GetMyCompany();
        $result = QueryResult(array("select distinct(code) from companycurrencies where cny#=$cny"));
        if ($result[0]) {
            return $result[0]['CODE'];
        }
        throw new \Exception('Cannot find valid CURRENCY');
    }

    /**
     * @param array  $record
     * @param string $ownedApiName
     *
     * @return array
     */
    function getOwnedObjectKeys($record, $ownedApiName)
    {
        $result = [];
        $ownedCollection = $record[$ownedApiName];
        foreach ($ownedCollection as $ownedReord) {
            $result[] = $ownedReord['key'];
        }
        return $result;
    }

    /**
     * ==================================
     *  Data Provider
     * ==================================
     */

    /**
     * @return array
     */
    function getCompactGLBatchV02Input()
    {
        $journal = $this->getJournal();
        $date = \APITestHelper:: getDateInISOFormat(true, 0, 10, $journal);
        if (0 === count($accountList = $this->getAccount())) {
            echo 'all glaccounts require dimensions';
            return [];
        }
        $location = $this->getLocation();
        $currency = $this->getCurrency();
        $initInput = Array (
            'batchTitle' => 'Receipt Journal Entry # 3.0',
            'journal' => $journal,
            'batchDate' => $date,
            'items' => Array (
                Array (
                    'trType' => '-1',
                    'entryDate' => $date,
                    'amount' => 17250,
                    'accountKey' => $accountList[0]['RECORDNO'],
                    'accountNO' => $accountList[0]['ACCOUNTNO'],
                    'location' => $location,
                    'currency' => strtolower($currency)
                ),
                Array (
                    'trType' => '1',
                    'entryDate' => $date,
                    'amount' => 17250,
                    'accountKey' => $accountList[1]['RECORDNO'],
                    'accountNO' => $accountList[1]['ACCOUNTNO'],
                    'location' => $location,
                    'currency' => strtolower($currency)
                )
            )
        );

        // todo - this kind of patch is not valid, but working for XML 3.0 based merge, needs to enhance the code
        $patchInput = Array (
            'batchTitle' => 'Receipt Journal Entry # 3.0',
            'items' => Array (
                Array (
                    'trType' => '-1',
                    'description' => 'updated: ' . __METHOD__ . ' ' . time(),
                    'currency' => strtoupper($currency)
                ),
                Array (

                    'trType' => '1',
                    'description' => 'updated: ' . __METHOD__ . ' ' . time(),
                    'currency' => strtoupper($currency)
                )
            )
        );
        return ['POST'=>$initInput, 'PATCH'=>$patchInput];
    }

    /**
     * @return array
     */
    function getGLBatchV02Input()
    {
        if (0 === count($accountList = $this->getAccount())) {
            echo 'all glaccounts require dimensions';
            return [];
        }
        $location = $this->getLocation();
        $currencyLower = strtolower($this->getCurrency());
        $journal = $this->getJournal();
        $date = \APITestHelper:: getDateInISOFormat(true, 0, 5, $journal);
        return Array (
                "batchTitle" => "Time entries for 12/26/2010 - $date",
                "journal" => $journal,
                "batchDate" => $date,
                "items" => Array
                    (
                        Array (
                                "trType" => "1",
                                "entryDate" => $date,
                                "amount" => 7.5,
                                'accountKey' => $accountList[1]['RECORDNO'],
                                'accountNO' => $accountList[1]['ACCOUNTNO'],
                                'location' => $location,
                                "description" => "",
                                "currency" => $currencyLower
                        ),
                        Array (
                                "trType" => "1",
                                "entryDate" => $date,
                                "amount" => 18,
                                'accountKey' => $accountList[0]['RECORDNO'],
                                'accountNO' => $accountList[0]['ACCOUNTNO'],
                                'location' => $location,
                                "description" => "",
                                "currency" => $currencyLower
                        ),
                        Array (
                                "trType" => "-1",
                                "entryDate" => $date,
                                "amount" => 18,
                                'accountKey' => $accountList[1]['RECORDNO'],
                                'accountNO' => $accountList[1]['ACCOUNTNO'],
                                'location' => $location,
                                "description" => "",
                                "currency" => $currencyLower
                        ),
                        Array (
                                "trType" => "1",
                                "entryDate" => $date,
                                "amount" => 1.5,
                                'accountKey' => $accountList[1]['RECORDNO'],
                                'accountNO' => $accountList[1]['ACCOUNTNO'],
                                'location' => $location,
                                "description" => "",
                                "currency" => $currencyLower
                        ),
                        Array (
                                "trType" => "-1",
                                "entryDate" => $date,
                                "amount" => 9,
                                'accountKey' => $accountList[1]['RECORDNO'],
                                'accountNO' => $accountList[1]['ACCOUNTNO'],
                                'location' => $location,
                                "description" => "",
                                "currency" => $currencyLower
                        ),
                        Array (
                                "trType" => "1",
                                "entryDate" => $date,
                                "amount" => 12,
                                'accountKey' => $accountList[0]['RECORDNO'],
                                'accountNO' => $accountList[0]['ACCOUNTNO'],
                                'location' => $location,
                                "description" => "",
                                "currency" => $currencyLower
                        ),
                        Array (
                            "trType" => "-1",
                            "entryDate" => $date,
                            "amount" => 12,
                            'accountKey' => $accountList[0]['RECORDNO'],
                            'accountNO' => $accountList[0]['ACCOUNTNO'],
                            'location' => $location,
                            "description" => "",
                            "currency" => $currencyLower
                        )
                    )
                );
    }


    /**
     * @return array
     */
    function getGLBatchV05Input()
    {
        $journal = $this->getJournal();
        $date = \APITestHelper:: getDateInISOFormat(true, 0, 5, $journal);
        if (0 === count($accountList = $this->getAccount())) {
            echo 'all glaccounts require dimensions';
            return [];
        }
        $location = $this->getLocation();
        $currencyUpper = strtoupper($this->getCurrency());
        return Array (
            "batchTitle" => "Time entries for 12/26/2010 - $date",
            "journal" => $journal,
            "batchDate" => $date,
            "entries" => Array
            (
                Array (
                    "trType" => "1",
                    "entryDate" => $date,
                    "amount" => 7.5,
                    'accountKey' => $accountList[1]['RECORDNO'],
                    'accountNO' => $accountList[1]['ACCOUNTNO'],
                    'location' => $location,
                    "description" => "",
                    "currency" => $currencyUpper
                ),
                Array (
                    "trType" => "1",
                    "entryDate" => $date,
                    "amount" => 18,
                    'accountKey' => $accountList[0]['RECORDNO'],
                    'accountNO' => $accountList[0]['ACCOUNTNO'],
                    'location' => $location,
                    "description" => "",
                    "currency" => $currencyUpper
                ),
                Array (
                    "trType" => "-1",
                    "entryDate" => $date,
                    "amount" => 18,
                    'accountKey' => $accountList[0]['RECORDNO'],
                    'accountNO' => $accountList[0]['ACCOUNTNO'],
                    'location' => $location,
                    "description" => "",
                    "currency" => $currencyUpper
                ),
                Array (
                    "trType" => "1",
                    "entryDate" => $date,
                    "amount" => 1.5,
                    'accountKey' => $accountList[1]['RECORDNO'],
                    'accountNO' => $accountList[1]['ACCOUNTNO'],
                    'location' => $location,
                    "description" => "",
                    "currency" => $currencyUpper
                ),
                Array (
                    "trType" => "-1",
                    "entryDate" => $date,
                    "amount" => 9,
                    'accountKey' => $accountList[1]['RECORDNO'],
                    'accountNO' => $accountList[1]['ACCOUNTNO'],
                    'location' => $location,
                    "description" => "",
                    "currency" => $currencyUpper
                ),
                Array (
                    "trType" => "1",
                    "entryDate" => $date,
                    "amount" => 12,
                    'accountKey' => $accountList[0]['RECORDNO'],
                    'accountNO' => $accountList[0]['ACCOUNTNO'],
                    'location' => $location,
                    "description" => "",
                    "currency" => $currencyUpper
                ),
                Array (
                    "trType" => "-1",
                    "entryDate" => $date,
                    "amount" => 12,
                    'accountKey' => $accountList[0]['RECORDNO'],
                    'accountNO' => $accountList[0]['ACCOUNTNO'],
                    'location' => $location,
                    "description" => "",
                    "currency" => $currencyUpper
                )
            )
        );
    }

    /**
     * @return array
     */
    function getCompactGLEntryV02BaseInput()
    {
        $journal = $this->getJournal();
        $date = \APITestHelper:: getDateInISOFormat(true, 0, 10, $journal);
        if (0 === count($accountList = $this->getAccount())) {
            echo 'all glaccounts require dimensions';
            return [];
        }
        $location = $this->getLocation();
        $currency = $this->getCurrency();

        $initGLBatchCreateInput = [
            'batchTitle' => 'NextGen API Gl Entry Test',
            'journal'    => $journal,
            'batchDate'  => $date,
            'state'      => 'Draft',
            'items'      => [
                [
                    'trType'     => '-1',
                    'entryDate'  => $date,
                    'amount'     => 17250,
                    'accountKey' => $accountList[0]['RECORDNO'],
                    'accountNO'  => $accountList[0]['ACCOUNTNO'],
                    'location'   => $location,
                    'currency'   => strtolower($currency)
                ],
                [
                    'trType'     => '1',
                    'entryDate'  => $date,
                    'amount'     => 17250,
                    'accountKey' => $accountList[1]['RECORDNO'],
                    'accountNO'  => $accountList[1]['ACCOUNTNO'],
                    'location'   => $location,
                    'currency'   => strtolower($currency)
                ],
                [
                    'trType'     => '-1',
                    'entryDate'  => $date,
                    'amount'     => 250,
                    'accountKey' => $accountList[0]['RECORDNO'],
                    'accountNO'  => $accountList[0]['ACCOUNTNO'],
                    'location'   => $location,
                    'currency'   => strtolower($currency)
                ],
                [
                    'trType'     => '1',
                    'entryDate'  => $date,
                    'amount'     => 250,
                    'accountKey' => $accountList[1]['RECORDNO'],
                    'accountNO'  => $accountList[1]['ACCOUNTNO'],
                    'location'   => $location,
                    'currency'   => strtolower($currency)
                ]
            ]
        ];

        return $initGLBatchCreateInput;
    }

    /**
     * @param array  $record
     * @param string $ownedApiName
     *
     * @return array
     * @throws \Exception
     */
    function getGLEntryV02DeleteInput($record, $ownedApiName)
    {
        $ownedRecordKeys = $this->getOwnedObjectKeys($record, $ownedApiName);
        $numOfKeys = count($ownedRecordKeys);
        return [
            ['key' => $ownedRecordKeys[$numOfKeys - 1]],
            ['key' => $ownedRecordKeys[$numOfKeys - 2]]
        ];
    }

    /**
     * @param array  $record
     * @param string $ownedApiName
     *
     * @return array
     * @throws \Exception
     */
    function getGLEntryV02SinglePatchInput($record, $ownedApiName)
    {
        $ownedRecordKeys = $this->getOwnedObjectKeys($record, $ownedApiName);
        $description = 'single patch';
        $numOfKeys = count($ownedRecordKeys);
        $updatedEntryKey = $ownedRecordKeys[$numOfKeys - 1];
        $payload = [
            ['key' => $updatedEntryKey, 'description' => $description]
        ];
        return [
            'PAYLOAD' => $payload,
            'PATCHKEY' => $updatedEntryKey,
            'DESCRIPTION' => $description
        ];
    }

    /**
     * @param array $result
     *
     * @return array
     */
    private function preparePatchInputFromGetOutput(array $result, string $itemWrapper = 'items', bool $shouldKeepItems = true) : array
    {
        $result['key'] = $result['id'];
        unset($result['id']);
        unset($result['batchNo']);
        unset($result['balance']);
        unset($result['journal']);
        unset($result['batchDate']);
        unset($result['audit']);
        unset($result['state']);
        unset($result['vat']);
        unset($result['webURL']);
        unset($result['href']);
        $this->assertTrue(key_exists($itemWrapper, $result));
        if (!$shouldKeepItems) {
            unset($result[$itemWrapper]);
            return $result;
        }
        foreach ( $result[$itemWrapper] as &$item ) {
            $this->assertTrue(key_exists('currency', $item));
            $this->assertTrue(key_exists('description', $item));
            $item['description'] = $item['currency'];
            // $item['currency'] = ($item['description'] == 'usd') ? 'eur' : 'usd';
            unset($item['__testglbatch']);
            unset($item['id']);
            unset($item['trType']);
            unset($item['entryDate']);
            unset($item['amount']);
            unset($item['accountKey']);
            unset($item['accountNO']);
            unset($item['department']);
            unset($item['departmentKey']);
            unset($item['location']);
            unset($item['audit']);
            unset($item['state']);

            unset($item['glbatch']['id']);
        }
        unset($item);

        return $result;
    }
}
