<?xml version='1.0' encoding='UTF-8'?>
<ROOT assoc="T" >
    <entity>invdocument</entity>
    <pages>
        <page assoc="T" >
            <title>IA.HEADER</title>
            <fields>
	       	<field>WHENCREATED</field>
       	        <field>DOCPARID</field>
                <field>DOCNO</field>
                <field>DOCID</field>
		<field>COPYFROM</field>
		<field>COPYMODE</field>
		<field>CREATEDFROM</field>
                <field>PONUMBER</field>
                <field>VENDORDOCNO</field>
                <field>MESSAGE</field>
                <field>WAREHOUSE.LOCATIONID</field>
		<field>DEPARTMENT</field>
		<field>LOCATION</field>
		<field>SUPDOCID</field>
		<field>NOTE</field>
		<field>CREATEDUSERID</field>
		<field>USERID</field>
		<field>PRINTED</field>
		<field assoc="T">
			<path>SCHOPKEY</path>
			<hidden>1</hidden>
		</field>
		<field>STATE</field>
		<field assoc="T">
			<path>HDRPICKLINK</path>
			<hidden>1</hidden>
		</field>
        </fields>
        </page>
        <page assoc="T" >
        <title>IA.LINE_ITEMS</title>
        <fields>
        	<field>VIRTUAL.WHENCREATED</field>
        	<field>VIRTUAL.DOCPARID</field>
			<field>BASECURR</field>
		<field>VIRTUAL.DOCNO</field>
		<field>PRCLINENUM</field>
		<field>RECURRINGSCHEDULE</field>
            <MultilineLayout assoc="T"  key="field" >
            <path>ENTRIES</path>
            <title>IA.ENTRY_ROWS</title>
            <columns>
		<column assoc="T"><path>RECORDNO</path><hidden>1</hidden></column>
                <vbox assoc="T"  key="column" >
                    <_func>vbox</_func>
                    <_args>
                        <_arg assoc="T" >
                            <path>ITEMID</path>
                            <size>12</size>
			    <autofill>1</autofill>
                            <fullname>IA.ITEM</fullname>
                        </_arg>
                        <_arg assoc="T" >
                        <path>WAREHOUSEAVAIL</path>
			<autofill>1</autofill>
                        <fullname>IA.WAREHOUSE</fullname>
                    </_arg>
                    </_args>
                </vbox>
                <vbox assoc="T"  key="column" >
                    <_func>vbox</_func>
                    <_args>
                        <_arg assoc="T" >
                            <path>ITEMDESC</path>
			    <autofill>1</autofill>
                            <size>45</size>
                            <fullname>IA.DESCRIPTION</fullname>
                        </_arg>
                        <_arg assoc="T" >
                        <path>MEMO</path>
                        <size>45</size>
                        <fullname>IA.MEMO</fullname>
                    </_arg>
                    </_args>
                </vbox>
                <vbox assoc="T"  key="column" >
                    <_func>vbox</_func>
                    <_args>
                        <_arg assoc="T" >
                            <path>UIQTY</path>
			    <autofill>1</autofill>
                            <fullname>IA.QUANTITY</fullname>
                        </_arg>
                        <_arg assoc="T" >
                        <path>UNITFACTOR</path>
			<autofill>1</autofill>
                        <fullname>IA.UNIT</fullname>
                    </_arg>
                    </_args>
                </vbox>
				<column assoc="T" >
                <path>QTY_CONVERTED</path>
                <size>30</size>
                <fullname>IA.QTY_CONVERTED</fullname>
				</column>
      	    <column assoc="T" >
                <path>AVAILLINK</path>
	    </column>
		 <column assoc="T" >
               <path>PICKLIST</path>
               <hidden>1</hidden>
		</column>
      	    <column assoc="T" >
                <path>COSTLINK</path>
	    </column>
      	    <column assoc="T" >
                <path>SLBINFO</path>
	    </column>
             <vbox assoc="T"  key="column" >
                    <_func>vbox</_func>
                    <_args>
                        <_arg assoc="T" >
                           <path>UIPRICE</path>
			   <autofill>1</autofill>
			   <fullname>IA.COST</fullname>
                        </_arg>
                    </_args>
                  </vbox>
		  <vbox assoc="T"  key="column" >
                    <_func>vbox</_func>
                    <_args>
                        <_arg assoc="T" >
                           <path>UIVALUE</path>
			   <totaled>1</totaled>
			   <autofill>1</autofill>
			   <fullname>IA.EXTENDED_COST</fullname>
                        </_arg>
                    </_args>
                  </vbox>
				 <vbox assoc="T"  key="column" >
                    <_func>vbox</_func>
                    <_args>
                        <_arg assoc="T" >
                           <path>DUMMYSPACE</path>
						   <fullname></fullname>
						   <readonly>1</readonly>
                        </_arg>
                    </_args>
                  </vbox>				  
    	    <column assoc="T" >
                <path>ITEMCURRENT</path>
                <size>1</size>
                <hidden>1</hidden>
                <fullname>IA.CURRENT_ITEM</fullname>
            </column>
       	    <column assoc="T" >
                <path>UNIT</path>
                <size>1</size>
                <hidden>1</hidden>
                <fullname>IA.UNIT_TEXT</fullname>
            </column>
       	    <column assoc="T" >
                <path>WAREHOUSE.LOCATION_NO</path>
                <size>1</size>
                <hidden>1</hidden>
                <fullname>IA.WAREHOUSE_TEXT</fullname>
            </column>
	    	<column assoc="T" >
                <path>PRODLINE</path>
                <size>1</size>
                <hidden>1</hidden>
                <fullname>IA.PRODUCT_LINE</fullname>
            </column>
            <column assoc="T" >
                <path>ITEM.TAXABLE</path>
                <size>1</size>
                <hidden>1</hidden>
                <fullname>IA.IS_TAXABLE</fullname>
            </column>
            <column assoc="T" >
                <path>ITEM.TAXGROUP.RECORDNO</path>
                <size>30</size>
                <hidden>1</hidden>
                <fullname>IA.TAX_GROUP</fullname>
            </column>
	    <column assoc="T" >
                <path>ITEMGLGROUP</path>
                <size>30</size>
                <hidden>1</hidden>
                <fullname>IA.TAX_GROUP</fullname>
            </column>
			
			 <column assoc="T" >
                <path>SOURCE_DOCKEY</path>
                <size>30</size>
                <hidden>1</hidden>
                <fullname>IA.SOURCE_DOC_KEY</fullname>
            </column>
			 <column assoc="T" >
                <path>SOURCE_DOCLINEKEY</path>
                <size>30</size>
                <hidden>1</hidden>
                <fullname>IA.SOURCE_DOC_LINE_KEY</fullname>
            </column>
			<column assoc="T" >
                <path>SOURCE_DOCID</path>
                <size>30</size>
                <hidden>1</hidden>
                <fullname>IA.SOURCE_DOC_KEY</fullname>
            </column>
			<column assoc="T" >
                <path>RETAILPRICE</path>
                <size>40</size>
                <hidden>1</hidden>
                <fullname>IA.RETAIL_PRICE</fullname>
            </column>
			<column assoc="T" >
                <path>COST_METHOD</path>
                <size>10</size>
                <hidden>1</hidden>
                <fullname>IA.COST_METHOD</fullname>
            </column>
			<column assoc="T" >
                <path>ENABLESNO</path>
                <size>10</size>
                <hidden>1</hidden>
                <fullname>IA.ENABLE_SERIAL_NO</fullname>
            </column>
            <column assoc="T" >
                <path>ITEM.UOMGRPKEY</path>
                <hidden>1</hidden>
            </column>
            </columns>
            <_func>MultilineLayout</_func>
        </MultilineLayout>
        </fields>
    </page>

       	<page assoc="T" > 
        <title>IA.HISTORY</title>
	<readonly>1</readonly>
        <fields> 
		<field>VIRTUAL.WHENCREATED</field>
		<field>VIRTUAL.DOCPARID</field>
		<field>VIRTUAL.DOCNO</field>
		 <MultilineLayout assoc="T"  key="field" >
		    <norefreshlink>1</norefreshlink>
		    <path>HISTORY</path>
		    <title>IA.HISTORY</title>
		    <columns>
		    <column assoc="T" >
			<path>DOCUMENTCREATED</path>
		    </column>
		    <column assoc="T" >
			<path>DOCUMENTCREATEDBY</path>
		    </column>
		    <column assoc="T" >
			<path>DOCUMENTMODIFIED</path>
		    </column>
		    <column assoc="T" >
			<path>DOCUMENTMODIFIEDBY</path>
		    </column>
		    <column assoc="T" >
			<path>DOCUMENTID</path>
		    </column>
		    <column assoc="T" >
			<path>SOURCEDOCUMENTID</path>
		    </column>
		    <column assoc="T" >
			<path>DOCCONFIG</path>
		    </column>
		    <column assoc="T" >
			<path>DOCSTATE</path>
		    </column>
		   </columns>
		  <_func>MultilineLayout</_func>
		</MultilineLayout>

	</fields>
	</page>
    </pages>
     <helpfile>Creating_Editing_Viewing_Inventory_Transactions</helpfile>
</ROOT>
