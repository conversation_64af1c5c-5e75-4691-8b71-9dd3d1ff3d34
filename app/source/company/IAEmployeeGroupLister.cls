<?php
/**
 * Lister class for IA Employee Group
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Lister class for IA Employee Group
 */
class IAEmployeeGroupLister extends NLister
{
    /**
     * IAEmployeeGroupLister constructor.
     */
    public function __construct()
    {

        $params = array(
            'entity' => 'iaemployeegroup',
            'fields' => array('ID', 'NAME', 'INDUSTRYCODE'),
            'helpfile' => 'Employee_Groups_Lister',
        );

        parent::__construct($params);
    }

    /**
     * @param int         $i
     * @param string|null $owner
     * @param string|null $ownerloc
     * @param string|null $ownedObj
     *
     * @return string
     */
    public function calcDeleteUrl($i, $owner = null, $ownerloc = null, $ownedObj = null)
    {
        return "";
    }
}
