openapi: 3.0.0
info:
  title: console.template.refresh
  description: Refresh the console template list
  version: '1.0'
  contact:
    name: <PERSON>
    email: <EMAIL>
tags:
  - name: Console templates
    description: Function service for refreshing the console template list
servers:
  - url: 'http://localhost:3000'
paths:
  /services/console/template/refresh:
    post:
      summary: Refresh the console template list
      description: This function service makes a force refresh on cached data from database
      tags:
        - Console templates
      operationId: refresh-console-template
      requestBody:
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/console.template.refresh-request'
            examples:
              Refresh console template list request:
                value: {}
      responses:
        '200':
          description: ok
          content:
            application/json:
              schema:
                type: object
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/console.template.refresh-response'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Refresh console template list response:
                  value:
                    'ia::result':
                      status: true
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    console.template.refresh-request:
      description: Console template refresh request
      type: object
      x-mappedTo: __custom__
    console.template.refresh-response:
      description: Console template refresh response
      type: object
      x-mappedTo: __custom__
      properties:
        status:
          type: boolean
          description: The status of the request
          x-mappedTo: STATUS
          default: false
          example: true
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
