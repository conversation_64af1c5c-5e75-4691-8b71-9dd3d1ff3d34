<?

/**
*    FILE:            InvoicesAnalysisGraphReport.cls
*    AUTHOR:            Senthil
*    DESCRIPTION:    
*
*    (C)2000, Intacct Corporation, All Rights Reserved
*
*    Intacct Corporation Proprietary Information.
*    This document contains trade secret data that belongs to Intacct 
*    corporation and is protected by the copyright laws. Information herein 
*    may not be used, copied or disclosed in whole or part without prior 
*    written consent from Intacct Corporation.
*/

import('APARAnalysisReport');
require_once 'Dictionary.cls';

class InvoicesAnalysisGraphReport extends APARAnalysisReport
{
    /**
     * @param array $params
     */
    function __construct($params)
    {
        parent::__construct(
            INTACCTarray_merge(
                $params, 
                array( 
                'report' => 'invoicesanalysisgraph',                         
                '2stage' => 'false',
                ) 
            )
        );                
        $this->params['isgraph'] = '1';        
        $this->params['2stage'] = false;
        $token = ['IA.DATE', 'IA.CUSTOMER_TYPE', 'IA.CUSTOMER_ID', 'IA.CUSTOMER_NAME', 'IA.ACCOUNT_LABEL_OR_ACCOUNT'];
        I18N::addTokens(I18N::tokenArrayToObjectArray($token));
        I18N::getText();
    } 

    /**
     * @return bool
     */
    function DoQuery()
    {
        
        //To set Additional fields, tables & conditions if needed
        $extraSelects = '';
        $extraTables = '';
        $extraConditions = '';
        switch ($this->params['GROUPMODE']) {
        case 'SALESPERSON' :
            $extraSelects .= " ,EMPLOYEE.EMPLOYEEID||'--'||CONTACT.NAME AS SALESPERSON";
            $extraTables .= " ,EMPLOYEE, CONTACT";
            $extraConditions .= " AND ENTITY.CNY# = EMPLOYEE.CNY# (+)  
									 AND ENTITY.CUSTREPKEY = EMPLOYEE.EMPLOYEEID (+) 
									 AND EMPLOYEE.CNY# = CONTACT.CNY# (+) 
									 AND EMPLOYEE.CONTACTKEY = CONTACT.RECORD# (+)";
            break;
        case 'TERRITORY' :
            $extraSelects .= " ,TERRITORY.TERRITORYID||'--'||TERRITORY.NAME AS TERRITORY";
            $extraTables .= " ,TERRITORY";
            $extraConditions .= " AND ENTITY.CNY# = TERRITORY.CNY# (+)  
									 AND ENTITY.TERRITORYKEY = TERRITORY.TERRITORYID (+)";
            break;            
        default:
            break;
        }
        $this->extraSelects = $extraSelects;
        $this->extraTables = $extraTables;
        $this->extraConditions = $extraConditions;

        //To set filters
        $filters = '';
        if ($this->params['FROMCUSTOMER']) {
            list($custid) = explode(PICK_RECVAL_SEP, isl_trim($this->params['FROMCUSTOMER']));
            $filters = " AND ENTITY.CUSTOMERID >= '$custid'";
        }
        if ($this->params['TOCUSTOMER']) {
            list($custid) = explode(PICK_RECVAL_SEP, isl_trim($this->params['TOCUSTOMER']));
            $filters .= " AND ENTITY.CUSTOMERID <= '$custid'";
        }
        if ($this->params['TERRITORY']) {
            $filters .= " AND ENTITY.TERRITORYKEY IN (".join(', ', $this->params['TERRITORY']).")";
        }
        if ($this->params['SALESPERSON']) {
            list($empid) = explode(PICK_RECVAL_SEP, isl_trim($this->params['SALESPERSON']));
            $filters .= " AND ENTITY.CUSTREPKEY = '$empid'";
        }
        $this->filters = $filters;
        
        // To Set the select for depends on Group by selected
        switch ($this->params['GROUPMODE']) {
        case 'DATEENTERED' :
            $dynamicSummarySelect = "PRRECORD.WHENCREATED";
            break;
        case 'ENTITYTYPE' :
            $dynamicSummarySelect = "ENTITYOBJECT.NAME";
            break;
        case 'ENTITYID' :
            $dynamicSummarySelect = "ENTITY.CUSTOMERID||'--'||ENTITY.NAME";
            break;
        case 'ENTITYNAME' :
            $dynamicSummarySelect = "ENTITY.NAME";
            break;
        case 'ACCOUNT' :
            $dynamicSummarySelect = "ACCOUNTTABLE.ACCT_NO||'--'|| ACCOUNTTABLE.TITLE";
            break;
        case 'DEPARTMENT' :
            $dynamicSummarySelect = "DEPARTMENT.DEPT_NO||'--'||DEPARTMENT.TITLE";
            break;
        case 'LOCATION' :
            $dynamicSummarySelect = "LOCATION.LOCATION_NO||'--'||LOCATION.NAME";
            break;
        case 'TERRITORY' :
            $dynamicSummarySelect = "TERRITORY.TERRITORYID||'--'||TERRITORY.NAME";
            break;
        case 'SALESPERSON' :
            $dynamicSummarySelect = "EMPLOYEE.EMPLOYEEID||'--'||CONTACT.NAME";
            break;
        default:
            dieFL("Summary mode will not work for this Group");
        }
        /** @noinspection PhpUndefinedVariableInspection */
        $this->dynamicSummarySelect = $dynamicSummarySelect;

        return parent::DoQuery();
    }

    /**
     * @return array
     */
    function DoMap()
    {
        
        $lines = array();
        $lines['report']['0']['sess'] = $this->sess;
        $lines['report']['0']['op'] = $this->op;
        $lines['report']["0"]['co']    = GetMyCompanyName();
        $lines['report']['0']['title'] = $this->title;
        $lines['report']['0']['title2'] = $this->title2;        
        $lines['report']['0']['titlecomment'] = $this->titlecomment;
        $orientation = 'Portrait';
        if(isset($this->params['ORIENTATION']) && $this->params['ORIENTATION']!='') {
            $orientation = $this->params['ORIENTATION'];
        }
        $lines['report']['0']['orientation'] = $orientation; 

        //To display Created Date and Time
        $lines['report']['0']['reportdate'] = GetCurrentDate(IADATE_USRFORMAT);
        $lines['report']["0"]['reporttime'] = GetCurrentTZTime();

        $lines['report']["0"]['location'] = ($this->params['LOCATION'])? I18N::getSingleToken("IA.LOCATION_LOCID", [ [ 'name' => 'LOCATIONID', 'value' => $this->params['LOCATION'][0] ] ]) : '';
        $lines['report']["0"]['department'] = ($this->params['DEPARTMENT'])? I18N::getSingleToken("IA.DEPARTMENT_DEPTID", [ [ 'name' => 'DEPARTMENTID', 'value' => $this->params['DEPARTMENT'][0] ] ]) : '';
        $lines['report']["0"]['territory'] = ($this->params['TERRITORY']) ? I18N::getSingleToken("IA.TERRITORY_TERTID", [ [ 'name' => 'TERRITORYID', 'value' => $this->params['TERRITORY'][0] ] ]) : '';
        
        $this->reportdata = parent::DoMap();
        switch ($this->params['GROUPMODE']) {
        case 'DATEENTERED' :
            $grpName = I18N::getSingleToken('IA.DATE');
            break;
        case 'ENTITYTYPE' :
            $grpName = I18N::getSingleToken('IA.CUSTOMER_TYPE');
            break;
        case 'ENTITYID' :
            $grpName = I18N::getSingleToken('IA.CUSTOMER_ID');
            break;
        case 'ENTITYNAME' :
            $grpName = I18N::getSingleToken('IA.CUSTOMER_NAME');
            break;
        default:
            $grpName = isl_ucfirst(isl_strtolower($this->params['GROUPMODE']));
        }
        $this->xaxisLabel = ($grpName=='Account')? I18N::getSingleToken('IA.ACCOUNT_LABEL_OR_ACCOUNT') : $grpName;
        $this->DoGraphMap();

        $lines['report']['0']["graph"][0]["isgraph"] = '1';
        $lines['report']['0']["graph"][0]["furl"] = $this->fo_url;
        $lines['report']['0']["graph"][0]["htmlfurl"] = $this->furl;

        return $lines;
    }

    /**
     * @param string $xml
     * @param string $offline
     *
     * @return string|true  text if offline, true if echo'd directly to output
     */
    function GenerateHTML(&$xml, $offline = 'false')
    {
        $this->insertReportChart();
        return true;
    }
}

