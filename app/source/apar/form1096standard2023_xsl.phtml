<?
$toform1096xsl = '<?xml version="1.0" encoding="iso-8859-1"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format">
	<xsl:template match="/">
		<fo:root>
			<fo:layout-master-set>

				<xsl:variable name="margin-top"><xsl:value-of select="//MARGIN/TOP"/></xsl:variable>
				<xsl:variable name="margin-left"><xsl:value-of select="//MARGIN/LEFT"/></xsl:variable>

				<fo:simple-page-master master-name="page"
						page-height="11in" page-width="8.5in"
						margin-top="0mm" margin-bottom="0mm"
						margin-left="0mm" margin-right="0mm"
						>
					<fo:region-body
						margin-top="{$margin-top}in" margin-bottom="0in"
						margin-left="{$margin-left}in" margin-right="0.25in"
						overflow1="auto"/>
				</fo:simple-page-master>

			</fo:layout-master-set>

			<fo:page-sequence master-reference="page">
				<fo:flow flow-name="xsl-region-body" font-family="Helvetica" font-size="9.5pt">
					<xsl:apply-templates/>
				</fo:flow>
			</fo:page-sequence>
		</fo:root>
	</xsl:template>

	<xsl:template match="Company">
		<fo:block-container left="0.0in" top="0.60in" height="0.4in" width="4in" position="absolute">
			<fo:block><xsl:value-of select="COMPANYNAME"/></fo:block>
		</fo:block-container>
		<fo:block-container left="0.0in" top="1.10in" height="0.4in" width="4in" position="absolute">
			<fo:block><xsl:value-of select="ADDRESS1"/></fo:block>
			<xsl:variable name="add2" select="ADDRESS2"/>
			<xsl:choose>
				<xsl:when test="$add2!=\'\'">
					<fo:block><xsl:value-of select="ADDRESS2"/></fo:block>
				</xsl:when>
			</xsl:choose>
		</fo:block-container>
		<fo:block-container left="0.0in" top="1.62in" height="0.2in" width="4in" position="absolute">
			<fo:block>
				<xsl:value-of select="CITY"/><xsl:text>, </xsl:text><xsl:value-of select="STATE"/><xsl:text> </xsl:text><xsl:value-of select="ZIPCODE"/>
			</fo:block>
		</fo:block-container>
		<fo:block-container left="0.0in" top="1.95in" height="0.2in" width="2.5in" position="absolute">
			<fo:block><xsl:value-of select="CONTACTNAME"/></fo:block>
		</fo:block-container>
		<fo:block-container left="2.6in" top="1.95in" height="0.2in" width="2in" position="absolute">
			<fo:block><xsl:value-of select="CONTACTPHONE"/></fo:block>
		</fo:block-container>
		<fo:block-container left="0.0in" top="2.25in" height="0.2in" width="2.5in" position="absolute">
			<fo:block><xsl:value-of select="CONTACTEMAIL"/></fo:block>
		</fo:block-container>
		<fo:block-container left="2.6in" top="2.30in" height="0.2in" width="2in" position="absolute">
			<fo:block><xsl:value-of select="FAX"/></fo:block>
		</fo:block-container>
		<fo:block-container left="0.0in" top="2.62in" height="0.2in" width="1.1in" position="absolute">
			<fo:block><xsl:value-of select="FEDERALID"/></fo:block>
		</fo:block-container>
		</xsl:template>

        <xsl:template match="FORM1096">
		<xsl:variable name="type" select="FORMTYPE"/>
		<fo:block-container left="2.6in" top="2.62in" height="0.2in" width="1.1in" position="absolute">
			<fo:block>
				<xsl:value-of select="FORMCOUNT"/>
			</fo:block>
		</fo:block-container>
		<fo:block-container left="5.4in" top="2.62in" height="0.2in" width="1.6in" position="absolute">
			<fo:block>
				<xsl:if test="$type!=\'A\' and $type!=\'G\' and $type!=\'T\'">
					<xsl:value-of select="format-number(AMOUNT, \'######0.00\')"/>
				</xsl:if>
			</fo:block>
		</fo:block-container>

                <xsl:variable name="topAlign">
                    <xsl:choose>
                         <xsl:when test="$type=\'MISC\' or $type=\'PATR\' or  $type=\'NEC\' or $type=\'Q\' or $type=\'R\' or $type=\'S\' or $type=\'SA\' ">4.004</xsl:when>
                         <xsl:otherwise>3.505</xsl:otherwise>
                    </xsl:choose>
                </xsl:variable>
                <xsl:variable name="leftAlign">
                     <xsl:choose>
                         <xsl:when test="$type=\'W-2G\'">-0.07</xsl:when>
                         <xsl:when test="$type=\'BTC\'">0.14</xsl:when>
                         <xsl:when test="$type=\'1098\'">0.54</xsl:when>
                         <xsl:when test="$type=\'C\'">0.96</xsl:when>
                         <xsl:when test="$type=\'E\'">1.38</xsl:when>
                         <xsl:when test="$type=\'T\'">1.78</xsl:when>
                         <xsl:when test="$type=\'A\'">2.14</xsl:when>
                         <xsl:when test="$type=\'B\'">2.52</xsl:when>
                         <xsl:when test="$type=\'C\'">2.92</xsl:when>
                         <xsl:when test="$type=\'CAP\'">3.32</xsl:when>
                         <xsl:when test="$type=\'DIV\'">5.53</xsl:when>
                         <xsl:when test="$type=\'G\'">5.92</xsl:when>
                         <xsl:when test="$type=\'INT\'">6.33</xsl:when>

                         <xsl:when test="$type=\'K\'">6.72</xsl:when>
                         <xsl:when test="$type=\'LTC\'">0.4</xsl:when>
                         <xsl:when test="$type=\'MISC\'">0.74</xsl:when>
                         <xsl:when test="$type=\'NEC\'">1.23</xsl:when>
                         <xsl:when test="$type=\'OID\'">1.64</xsl:when>
                         <xsl:when test="$type=\'PATR\'">2.03</xsl:when>
                         <xsl:when test="$type=\'Q\'">2.26</xsl:when>
                         <xsl:when test="$type=\'R\'">3.32</xsl:when>
                         <xsl:when test="$type=\'S\'">3.72</xsl:when>
                         <xsl:otherwise>0.05</xsl:otherwise>
                    </xsl:choose>
                </xsl:variable>
                <fo:block-container font-family="Helvetica" font-size="10pt" top="{$topAlign - 0.20}in" left="{$leftAlign}in" height="0.2in" width="0.2in" position="absolute">
			<fo:block>X</fo:block>
		</fo:block-container>
	</xsl:template>	
</xsl:stylesheet>';