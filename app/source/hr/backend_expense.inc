<?php
//=============================================================================
//
//	FILE:			backend_expense.inc
//	AUTHOR:			<PERSON>
//	DESCRIPTION:	API for employee expenses
//
//	(C)2000 - 2001, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================


//================= DEFINITIONS ===============================================
// The following values are relevant for the state field:
//      D = 'Draft'
//      R = 'Submitted'
//      A = 'Approved'
//      P = 'Paid'
//      Z = 'Resubmitted'
//=============================================================================

//================= INCLUDES ==================================================
include_once 'backend_prbatch.inc';
include_once 'backend_entity.inc';
include_once 'backend_employee.inc';
require_once 'backend_autobatch.inc';
include_once 'ManagerFactory.cls';
require_once 'prrecordhistory.inc';
include_once 'groom_lister.inc';
include_once 'genxml.inc';

//================= GLOBALS ===================================================

// approve types
define ("APPROVE", 1);
define ("UNAPPROVE", 0);

// states
define ("DRAFT", 'D');
define ("SUBMITTED", 'R');
define ("APPROVED", 'A');
define ("UNAPPROVED", 'U');
define ("RESUBMITTED", 'Z');

const MAX_COMMENTS_LENGTH = 500;

//================= PUBLIC FUNCTIONS ==========================================


//----------------- CreateExpense ---------------------------------------------
// Create an expense report.  This function relies heavily on CreatePRRecord()
// and adds the following expense report specific functions:
// (1) Checks for expense report specific validity
// (2) Automatically creates a batch for the expense if one is not specified
//
// INPUTS:
//		expense		The expense report.  The function assumes all keys are
//					user visible.  CreatePRRecord translates the keys
//
// OUTPUTS:
//		expense		Now containing the record# and translated keys
//
// RETURNS:
//		boolean signifying success or failure.
//-----------------------------------------------------------------------------
/**
 * createExpense
 *
 * @param array $expense
 * @param int   $dontvalidate
 *
 * @return bool true on success, false on failure
 */
function CreateExpense(&$expense, $dontvalidate = 0)
{
    $owner = 'CreateExpense';

    $basecurr = GetBaseCurrency();
    if (!$basecurr) {
        SetCurrencyContext($expense['BASECURR']);
    }

    // RECORD THE EXPENSE REPORT
    XACT_BEGIN($owner);
    if (!PostExpenseReport($expense, $dontvalidate)) {
        XACT_ABORT($owner);
        return false;
    }

    // CHECK FOR EXISTENCE OF DOCUMENT ID AND CREATE A MAP BETWEEN THE DOCUMENT AND THE PRRECORD
    if ($expense['SUPDOCID'] != '' || $expense['EXTERNALURL'] != '') {
        if (!AddSupportingDocumentMap($expense['RECORD#'], $expense['SUPDOCID'], $expense['EXTERNALURL'], $expense['SUPDOCENTITY']) ) {
            XACT_ABORT($owner);
            return false;
        }
    }

    if (!$basecurr) {
        SetCurrencyContext();
    }

    XACT_COMMIT($owner);

    return true;
}

/**
 * Create an expense report without the corresponding event management
 *
 * @param array $expensereport the expense report object
 * @param int   $dontvalidate
 *
 * @return bool true on success, false on failure
 *
 */
function PostExpenseReport(&$expensereport, $dontvalidate = 0) {

    global $gErr;
    global $kEEid, $modprefs;

    $owner = 'PostExpenseReport';

    // get the company preferences for expense batches
    if (!GetModulePreferences($kEEid, $modprefs)) {
        $gErr->addError('EE-0281', __FILE__ . '.' . __LINE__,
                        sprintf("Unable to retrieve the %s Expenses module preferences", 'Employee')
        );
        return false;
    }

    if (!IsExpenseReportValid($expensereport, $dontvalidate, 'Add')) {
        // some error handling here
        epp("The expense report is invalid.");
        return false;
    }

    $basecurr = GetBaseCurrency();
    if (!$basecurr) {
        if ($expensereport['BASECURR'] != '') {
            $entbasecurr = $expensereport['BASECURR'];
        } else {
            $entbasecurr = GetLocationBaseCurrency($expensereport['ITEMS'][0]['LOCATION#']);
        }
        // need to set currency context
        SetCurrencyContext($entbasecurr);
    }

    XACT_BEGIN($owner);

    // if the expense does not have a batch, stick it in one
    if (!$expensereport['PRBATCHKEY']) {
        $batchDate = $expensereport['WHENPOSTED'] ?: $expensereport['WHENCREATED'];

    $rectype = $expensereport['RECORDTYPE'];
        /** @noinspection PhpUndefinedVariableInspection */
        if (!GetExpenseBatch($batchDate, $batchno, $entbasecurr, $rectype)) {
            // some error handling here
            epp("unable to get an expense batch.");
            XACT_ABORT($owner);
            return false;
        }
        $expensereport['PRBATCHKEY'] = $batchno;
    } elseif ($expensereport['BASECURR'] != '' && IsMCMESubscribed()) {
        $results = QueryResult(array("select locationmst.currency from prrecordmst, prentrymst, prbatchmst, locationmst where prrecordmst.cny# = :1 and prentrymst.cny# = :1 and prbatchmst.cny# = :1 and locationmst.cny# = :1 and prbatchmst.record# = :2 and prbatchmst.record# = prrecordmst.prbatchkey and prrecordmst.record# = prentrymst.recordkey and prentrymst.lineitem = 'T' and locationmst.record# = prentrymst.location# and ROWNUM = 1 ", GetMyCompany(), $expensereport['PRBATCHKEY']));
                $res = $results[0]['CURRENCY'];
        if ($res != '' && $res != $expensereport['BASECURR']) {
            $gErr->addError('EE-0001', __FILE__ . ":" . __LINE__, "You cannot create a record with a different base currency from the batch.");
            XACT_ABORT($owner);
            return false;
        }
    }

    if ($expensereport['CURRENCY'] == '') {
        /** @noinspection PhpUndefinedVariableInspection */
        $expensereport['CURRENCY'] = ($basecurr ?: $entbasecurr);
    }

    // IS THE BATCH OPEN?
    if (!$dontvalidate) {
        $where = array("RECORD# = :2", $expensereport['PRBATCHKEY']);
        $res = GetNObjects('prbatch', 'OPEN', $where);

        if ($res[0]['OPEN'] == 'F')  {
            /** @noinspection PhpUndefinedVariableInspection */
            $gErr->addIAError('EE-0282', __FILE__ . ':' . __LINE__,
                'Batch ' . $batchobj['TITLE'] . ' is closed. Please check with your administrator',
                ['BATCHOBJ_TITLE' => $batchobj['TITLE']]
            );
            epp("The expense batch is closed.  Please check with your administrator");
            XACT_ABORT($owner);
            return false;
        }
    }

    //Set Line Numbers for Line Items
    SetLineNum($expensereport['ITEMS']);

    // maturate the items structure
    if (!MatureItems($expensereport, true, 'ee')) {
        XACT_ABORT($owner);
        return false;
    }

    // Cache custom gl dimensions
    if (!util_isPlatformDisabled()) {
        global $gManagerFactory;
        $lineItems = & $expensereport['ITEMS'];

        $line_entity = getLineItemEntityByRecordType($expensereport['RECORDTYPE']);

        if ( $line_entity != '' ) {
            $lineMgr =  $gManagerFactory->getManager($line_entity);

            // ITEMS
            foreach ( $lineItems as & $lineItem ) {
                foreach ( $lineItem['ITEMS'] as & $item ) {
                    if ( ! $lineMgr->setCustomDimensionsCache( $item ) ) {
                        /** @noinspection PhpUnusedLocalVariableInspection */
                        $ok = false;
                        break;
                    }
                }
                unset($item);

                // OFFSET
                if ( ! $lineMgr->setCustomDimensionsCache( $lineItem['OFFSET'] ) ) {
                    /** @noinspection PhpUnusedLocalVariableInspection */
                    $ok = false;
                        break;
                }
            }
            unset($lineItem);
        }
    }

    if (!CreatePRRecord($expensereport, $dontvalidate ? 0 : 1)) {
        epp("Unable to create the expense report.");
        XACT_ABORT($owner);
        return false;
    }

    // Create custom dimension relatinships
    if (!util_isPlatformDisabled()) {
        global $gManagerFactory;
        $lineItems = & $expensereport['ITEMS'];

        $line_entity = getLineItemEntityByRecordType($expensereport['RECORDTYPE']);

        if ( $line_entity != '' ) {
            $ok = true;
            $lineMgr =  $gManagerFactory->getManager($line_entity);

            // ITEMS
            foreach ( $lineItems as $lineItem ) {
                foreach ( $lineItem['ITEMS'] as $item ) {
                    if ( ! $lineMgr->handleCustomRelationships( $item ) ) {
                        $ok = false;
                        break;
                    }
                }

                // OFFSET
                if ( ! $lineMgr->handleCustomRelationships( $lineItem['OFFSET'] ) ) {
                        $ok = false;
                        break;
                }
            }

            if ( !$ok ) {
                epp ("Unable to create custom relationships with expense report detail.");
                XACT_ABORT($owner);
                return false;
            }
        }
    }

    // create the expense report record
    $expensereportdtls = array(
        'RECORDKEY' => $expensereport['RECORD#'],
        'COMMENTS'	=> $expensereport['COMMENTS']
    );

    if (!CreateExpenseReport($expensereportdtls)) {
        epp ("Unable to create the expense report detail.");
        XACT_ABORT($owner);
        return false;
    }

    // need to reset currency context if needed
    if (!$basecurr) {
        SetCurrencyContext();
    }

    XACT_COMMIT($owner);
    return true;
}

/**
 * Updates an existing expense report
 *
 * @param string $id      The expense report record key
 * @param array  $expense contains the expense report to update.  On response, will include the record# and other internal keys
 *
 * @return bool true on success, false on failure
 */
function SetExpense($id, &$expense)
{
    global $gErr, $gManagerFactory;

    $_r = Request::$r->_r;
    // AN EXPENSE REPORT CAN ONLY BE UPDATED IF ITS CURRENT STATE IS
    // DRAFT OR UNAPPROVED / SUBSEQUENT STATES CAN BE DRAFT,
    // UNAPPROVED, SUBMITTED, OR RESUBMITTED

    $assertTest = GetExpense($id, $tmp, false);
    assert($assertTest);
    $curstate = $tmp['STATE'];
    if($expense['RECORDTYPE'] == 'ei')
    {
        // Are we in full edit mode ?
        if(in_array($curstate, array('A', 'S')) ) {
            // Get the old and new total due
            $oldtotaldue = $expense['TRX_TOTALDUE'];
            $newtotaldue = ERTotal($expense);

            // The total due can never be increased
            if($newtotaldue > $oldtotaldue) {

                $gErr->addError('EE-0002', __FILE__ . ':' . __LINE__, "The total amount due of an expense report can not be increased after it has been submitted.");
                return false;

            } else {
                /** @noinspection PhpUnusedLocalVariableInspection */
                $oldNumItems = count($tmp['ITEMS']);
                /** @noinspection PhpUnusedLocalVariableInspection */
                $newNumItems = count($expense['ITEMS']);

                // The total due in inferior
                if($newtotaldue < $oldtotaldue) {

                    //Throw error if total of expense report is negative
                    if ( ibcabs($newtotaldue) != $newtotaldue ) {
                        $gErr->addError('EE-0283', __FILE__ . '.' . __LINE__,
                            "The reimbursable total of the expense report cannot be negative.",
                            ' ',
                            'Try using Expense Adjustment for negative expense items.'
                        );
                        return false;
                    }

                    // if the amount has changed we will add a line in the history
                    $contactname = GetMyContactName();
                    $submissionhistory = array(
                        'PRRECORD' => $tmp['RECORD#'],
                        'EVENTDATE' => GetCurrentDate(),
                        'ACTION' => PRRECORD_ACTION_EDIT,
                        'CONTACT' => $contactname,
                        'COMMENTS' => "The total amount due has been changed from $oldtotaldue to $newtotaldue."
                    );

                    // GET THE PRRECORDHISTORY MANAGER
                    $objPh = $gManagerFactory->getManager('prrecordhistory');
                    $objPh->SetWorkflow('expensereport');

                    // CREATE THE SUBMISSION HISTORY
                    if ($submissionhistory) {
                        if (!$objPh->add($submissionhistory)) {
                            // add some error here?
                            /** @noinspection PhpUndefinedVariableInspection */
                            XACT_ABORT($owner);
                            return false;
                        }
                    }
                }
            }
        }
        else if ( ! in_array($curstate, array(PRRECORD_STATE_DRAFT, PRRECORD_STATE_UNAPPROVED, PRRECORD_STATE_PARTIALLYAPPROVED, PRRECORD_STATE_PARTIALLYUNAPPROVED)) )
        {
            $gErr->addError('EE-0003', __FILE__ . '.' . __LINE__, "The expense report can be edited only if it is declined or if its a draft. Change the state before editing.");
            return false;
        }
    }
    $owner = 'SetExpense';

    if (!IsExpenseReportValid($expense)) {
        // some error handling here please
        epp("The expense report is invalid.");
        return false;
    }

    // make sure there are no invoice runs against the record
    if ( !SODocumentEntryManager::validateInvoiceRunLink('EEXPENSE', $id) ) {
        return false;
    }

    // Set the base currency context to set it in the DB for new added line
    $basecurr = GetBaseCurrency();
    if ( ! isset($basecurr) || $basecurr == '') {
        if ( isset($expense['BASECURR']) && $expense['BASECURR'] != '') {
            $entbasecurr = $expense['BASECURR'];
        } else {
            $entbasecurr = GetLocationBaseCurrency($expense['ITEMS'][0]['LOCATION#']);
        }
        SetCurrencyContext($entbasecurr);
    }

    // GET THE EXPENSEDETAIL. DO THIS FOR updating MEMO field
    $assertTest = GetNObject('expensereport', $id, $details);
    assert($assertTest);

    XACT_BEGIN($owner);

    // UPDATE THE DETAIL
    $details['COMMENTS'] = isl_substr($expense['COMMENTS'], 0, MAX_COMMENTS_LENGTH);
    if (!SetNObject('expensereport', $details, $id)) {
        epp("Unable to update the expense details.");
        XACT_ABORT($owner);
        return false;
    }

    // IS THE BATCH OPEN?
    $where = array("RECORD# = :2", $expense['PRBATCHKEY']);
    $res = GetNObjects('prbatch', 'OPEN', $where);
    if ($res[0]['OPEN'] == 'F')  {
        // some error handling here please
        epp("The expense batch is closed.  Please check with your administrator");
        XACT_ABORT($owner);
        return false;
    }

    //Set Line Numbers for Line Items
    SetLineNum($expense['ITEMS']);

    // MATURATE THE ITEMS
    if (!MatureItems($expense)) {
        XACT_ABORT($owner);
        return false;
    }

    //Get the old prrecord
    $oldprrecord = [];
    if (!GetNObject('prrecord', $id, $oldprrecord)){
        XACT_ABORT($owner);
        $gErr->addError('EE-0304', __FILE__ . ':' . __LINE__, $id);
        return false;
    }

    $oldprbatch = [];
    if (!GetNObject('prbatch', $oldprrecord['PRBATCHKEY'], $oldprbatch)){
        XACT_ABORT($owner);
        $gErr->addError('EE-0303', __FILE__ . ':' . __LINE__, $_r);
        return false;
    }

        //If Whenposted is there then Auto Batches will be created based on that
        $batchDate = $expense['WHENPOSTED']?: $expense['WHENCREATED'];
        $oldbatchDate = $oldprbatch['CREATED']?: $oldprrecord['WHENCREATED'];
        // No need to create auto batch if old batch created by manual.
        if ($batchDate != $oldbatchDate && $oldprbatch['AUTOCREATED'] == 'T' ) {
            unset($expense['PRBATCHKEY']);
            if (!GetInvoiceAutoBatch($batchDate, $expense['RECORDTYPE'], $expense['PRBATCHKEY'], $expense['BASECURR'])) {
                // some error handling here
                XACT_ABORT($owner);
                epp("unable	to get an invoice batch.");
                return false;
            }
        } else {
            $expense['PRBATCHKEY'] = $oldprrecord['PRBATCHKEY'];
        }

    //epp('expense:');eppp($expense);
    //dieFL();

    // Cache custom gl dimensions
    if (!util_isPlatformDisabled()) {
        global $gManagerFactory;
        $lineItems = & $expense['ITEMS'];

        $line_entity = getLineItemEntityByRecordType($expense['RECORDTYPE']);

        if ( $line_entity != '' ) {
            $lineMgr =  $gManagerFactory->getManager($line_entity);

            // ITEMS
            foreach ( $lineItems as & $lineItem ) {
                foreach ( $lineItem['ITEMS'] as & $item ) {
                    if ( ! $lineMgr->setCustomDimensionsCache( $item ) ) {
                        epp("Unable to set custom dimensions cache.");
                        XACT_ABORT($owner);
                        return false;
                    }
                }
                unset($item);

                // OFFSET
                if ( ! $lineMgr->setCustomDimensionsCache( $lineItem['OFFSET'] ) ) {
                    epp("Unable to set custom dimensions cache.");
                    XACT_ABORT($owner);
                    return false;
                }
            }
            unset($lineItem);
        }
    }

    /** UPDATE HERE **/
    // SET THE PRRECORD
    if (!SetPRRecord($id, $expense)) {
        // some error handling here please
        epp("Unable to set the prrecord.");
        XACT_ABORT($owner);
        return false;
    }else {

        //Update the new prrecord's prbatch
        if (!UpdatePRBatch($expense['PRBATCHKEY'])) {
            XACT_ABORT($owner);
            $gErr->AddDBError('SetInvoice', __FILE__.':'.__LINE__,$expense['PRBATCHKEY']);
            return false;
        } else {

            //If the prbatch changed as part of this transaction, run UpdatePRBatch on the old batch as well
            if ($oldprrecord['PRBATCHKEY'] != $expense['PRBATCHKEY']) {
                $journalSymbol = GetPRBatchJournal($oldprrecord['PRBATCHKEY']);

                if ( !UpdatePRBatch($oldprrecord['PRBATCHKEY'], '', $journalSymbol) ) {
                    XACT_ABORT($owner);
                    $gErr->AddDBError('SetInvoice', __FILE__.':'.__LINE__,$oldprrecord['PRBATCHKEY']);
                    return false;
                }

                $where = array("prrecord.prbatchkey = :2", $oldprrecord['PRBATCHKEY']);
                $precs = GetNObjects("prrecord","prrecord.record#", $where);
                if ( count($precs) < 1 ) {
                    if (!DeletePRBatch($oldprrecord['PRBATCHKEY']) ) {
                        XACT_ABORT($owner);
                        return false;
                    }
                }
            }

            //XACT_COMMIT($owner);
        }
    }

    // Create custom dimension relatinships
    if ( !util_isPlatformDisabled() ) {
        global $gManagerFactory;
        $lineItems = & $expense['ITEMS'];

        $line_entity = getLineItemEntityByRecordType($expense['RECORDTYPE']);

        if ( $line_entity != '' ) {
            $lineMgr =  $gManagerFactory->getManager($line_entity);

            // ITEMS
            foreach ( $lineItems as $lineItem ) {
                foreach ( $lineItem['ITEMS'] as $item ) {
                    if ( ! $lineMgr->handleCustomRelationships( $item ) ) {
                        epp("Unable to store platform relationships.");
                        XACT_ABORT($owner);
                        return false;
                    }
                }

                // OFFSET
                if ( ! $lineMgr->handleCustomRelationships( $lineItem['OFFSET'] ) ) {
                    epp("Unable to store platform relationships.");
                    XACT_ABORT($owner);
                    return false;
                }
            }
        }
    }


    // SET SUPPORTING DOCUMENTS MAP HERE
    if (!SetSupportingDocumentMap($id, $expense['SUPDOCID'], $expense['EXTERNALURL'], $expense['SUPDOCENTITY']) ) {
        XACT_ABORT($owner);
        return false;
    }

    XACT_COMMIT($owner);

    return true;
}

/**
 * deleteExpense
 *
 * @param string $id
 * @param bool   $deleteOrUpdate
 * @param bool   $fromXMLGateway
 *
 * @return bool
 */
function DeleteExpense($id, $deleteOrUpdate, $fromXMLGateway=false)
{
    global $kEEid;
    $gErr = Globals::$g->gErr;
    $kEEid = Globals::$g->kEEid;

    $owner = 'DeleteExpense';

    // get the expense
    if (!GetPRRecord($id, $expense)) {
        epp("Unable to retrieve the expense");
        return false;
    }

    // We will allow Approved expenses to be deleted in Expense Batch only.
    // We have no clean way to know if we are deleting from My Expense/Expense Batch or Gateway for now
    // We will just make sure the user deleting the expense is a business user and has permission to delete expenses
    // in expense batch. Once we refactor the code and have clean manager to handle the logic we can clean up the logic.
    if ( $expense['MODULEKEY'] == $kEEid ) {

        if ( $expense['RECORDTYPE'] == 'ei' ) {

            $userType = GetMyUserType();
            $isDeleteAllowed = IsOperationAllowed(GetOperationId('ee/lists/eexpenses/delete'));
            $reverseUnpaidExpense = 'F';
            if (! $fromXMLGateway) {
                $reverseUnpaidExpense = GetPreferenceForProperty($kEEid, 'REVERSE_UNPAID_EXPENSE');
            }

            $stateForDelete = array(PRRECORD_STATE_DRAFT, PRRECORD_STATE_SUBMITTED,PRRECORD_STATE_UNAPPROVED, PRRECORD_STATE_PARTIALLYAPPROVED, PRRECORD_STATE_PARTIALLYUNAPPROVED);
            if(isset($reverseUnpaidExpense) && $reverseUnpaidExpense == 'T' && !(in_array($expense['STATE'], $stateForDelete))){
                 $gErr->addError('EE-0004', __FILE__ . ':' . __LINE__,
                                 'This expense can only be reversed.');
                 return false;
            } else if ( $userType != 'B' || ! $isDeleteAllowed ) {

                if ( in_array($expense['STATE'], array(
                        PRRECORD_STATE_APPROVED,
                        PRRECORD_STATE_PARTIALLYAPPROVED,
                        PRRECORD_STATE_PARTIALLYUNAPPROVED)
                     )
                ) {
                    $gErr->addError(
                        'EE-0005',
                        __FILE__.":".__LINE__,
                        "Cannot delete an approved , partially approved or partially declined expense."
                    );
                    return false;
                }
            }
        }
    }

    // make sure there are no invoice runs against the record
    if ( !SODocumentEntryManager::validateInvoiceRunLink('EEXPENSE', $id) ) {
        return false; 
    }

    XACT_BEGIN($owner);

    // make sure there are no payments against it
    $wheres = array("(recordkey = :2 or paymentkey = :2) and amount != 0", $id);
    $payQResult = GetNObjects('prentrypymtrecs', "*", $wheres);

    if (count($payQResult)) {
        $gErr->addError('EE-0006', __FILE__ . ':' . __LINE__,
                        "A payment has been made against this Expense");
        XACT_ABORT($owner);
        return false;
    } else {

        // If no prentrypymtrecs found earlier, still fire the following qry as there may be records with zero amount
        $qry = array("delete from prentrypymtrecs where cny# = :1 and (recordkey = :2 or paymentkey = :2)",
                     GetMyCompany(), $id);

        if (!ExecStmt($qry)) {
            XACT_ABORT($owner);
            return false;
        }
    }

    // delete the expense report
    if (!DeleteNObject('expensereport', $id)) {
        epp("Unable to delete the expense report detail.");
        XACT_ABORT($owner);
        return false;
    }

    // delete the prrecord
    if (!DeletePRRecord($id, $deleteOrUpdate, FALSE)) {
        epp("Unable to delete the expense report.");
        XACT_ABORT($owner);
        return false;
    }

    // update the prbatch if it is connected to a GLBATCH
    #$ret = GetNObjects('prbatch', 'glbatchkey', 'record# = ' . $expense['PRBATCHKEY']);
    $ret = HasGLBatch($expense['PRBATCHKEY']);

    if (count($ret) > 0) {
        if ($ret[0]['RECORD#'] != '') {
            if (!UpdatePRBatch($expense['PRBATCHKEY'])) {
                epp("Unable to update the journal entries.");
                XACT_ABORT($owner);
                return false;
            } else {
                //to check if generated by system
                $autoGeneratedBatch = GetNObjects("prbatch", "prbatch.autocreated", "prbatch.record#=" . $expense['PRBATCHKEY']);

                if ($autoGeneratedBatch[0]['AUTOCREATED'] == 'T') {
                    // to check if there are no other transaction associated with this batch
                    $prRecordQry = QueryResult(["select count(record#) prcnt from prrecordmst where cny#=:1 and prbatchkey=:2",
                                                GetMyCompany(), $expense['PRBATCHKEY']]);

                    if ($prRecordQry[0]['PRCNT'] == 0) {
                        if (!DeletePRBatch($expense['PRBATCHKEY']) ) {
                            XACT_ABORT($owner);
                            return false;
                        }
                    }
                }
            }
        }
    }

    XACT_COMMIT($owner);
    return true;

}

/**
 * getExpense
 *
 * @param string $id        expense report record#
 * @param array  $expense   expense data
 * @param bool   $translate
 *
 * @return bool true on success, false on failure
 */
function GetExpense($id, &$expense, $translate=true) {

    if (!GetNObject('expensereport', $id, $details)) {
        epp("Unable to retrieve the expense report details.");
        // TODO: WTF?!?!
        // uncomment in production
        // return false;
    }

    // TODO: what are all these epp's doing as a substitute for logging an error?!?!
    if (!GetPRRecord($id, $expense, $translate)) {
        epp("Unable to retrieve the expense report.");
        return false;
    }

    if (!GetNObject('prbatch', $expense['PRBATCHKEY'], $batchtitle, 'title')) {
        epp("Unable to retrieve the expense batch name");
    }
     if (!GetNObject('prbatch', $expense['PRBATCHKEY'], $batchcreated, 'created')) {
        epp("Unable to retrieve the expense batch name");
    }

    if(in_array($expense['RECORDTYPE'] , array('ei', 'ea')) && IsMCPEnabled('ee')){
        if (!GetPRCurrencyDetail($id, $expense)){
            return false;
        }
    }

    $details['BATCHNAME'] = $batchtitle['TITLE'];
    $details['BATCHCREATED'] = $batchcreated['CREATED'];
    $expense = INTACCTarray_merge($expense, $details);
    if ($translate) {
        GroomExpenseReportOut($expense, false);
    }
    return true;

}

/**
 * getPRCurrencyDetail
 *
 * @param string $prrec   prrecord record#
 * @param array  $expense expense object
 *
 * @return bool true on success, false on failure
 */
function GetPRCurrencyDetail($prrec, &$expense){
    $currdetail = QueryResult(array("SELECT precurr.prentrykey, precurr.currency, precurr.amount, precurr.exch_rate_date,
            precurr.exch_rate_type_id, precurr.exchange_rate, precurr.user_exchrate
            from prentrycurrdetail precurr, prrecord pr, prentry pre WHERE
            pr.cny#=:1 and pr.record#=:2 and pre.cny#=pr.cny# and pre.recordkey=pr.record#
            and precurr.cny#=pre.cny# and precurr.prentrykey=pre.record#", GetMyCompany(), $prrec));
    if(count($currdetail) > 0){
        global $gManagerFactory;
        $exchTypeMgr = $gManagerFactory->getManager('exchangeratetypes');
         foreach($expense['ITEMS'] as $key => $items){
            $itemRecNo = (!is_null($items['RECORDNO'])) ? $items['RECORDNO'] : $items['RECORD#'];
            foreach($currdetail as $curr){
               if($itemRecNo == $curr['PRENTRYKEY']){
                    $expense['ITEMS'][$key]['ORG_CURRENCY'] = $curr['CURRENCY'];
                    $expense['ITEMS'][$key]['ORG_AMOUNT'] = $curr['AMOUNT'];
                    $expense['ITEMS'][$key]['ORG_EXCHRATEDATE'] = $curr['EXCH_RATE_DATE'];
                    $expense['ITEMS'][$key]['ORG_EXCHRATETYPE'] = $exchTypeMgr->GetExchangeRateTypeName($curr['EXCH_RATE_TYPE_ID']);
                    $expense['ITEMS'][$key]['ORG_EXCHRATE'] = $curr['EXCHANGE_RATE'];
                    $expense['ITEMS'][$key]['USER_EXCHRATE'] = $curr['USER_EXCHRATE'];
               }
            }
        }
    }

    return true;
}

//----------------- GetMultiPRCurrencyDetail --------------------------------------
// Get multiple prentry currency details
//
// INPUTS:
//		$prrecs		prrecord record#s to get.  Array indexed by parent recordno, returns ITEMS array
//
// OUTPUTS:
//		$expense			currency details
//
//-----------------------------------------------------------------------------

/**
 * GetMultiPRCurrencyDetail
 *
 * @param array $prrecs
 */
function GetMultiPRCurrencyDetail(&$prrecs)
{
    if (empty($prrecs)) {
        return;
    }
    foreach ($prrecs as $recno => $details) {
        GetPRCurrencyDetail($recno, $prrecs[$recno]);
    }
}

/**
 * GetExpensesToApprove - get list of expenses needing approval
 *
 * @param string      $empid employee who will approve the expense reports
 * @param array|false $ers   expense reports
 *
 * @return bool true on success, false on failure
 */
function GetExpensesToApprove($empid, &$ers) {
    /** @noinspection PhpUnusedLocalVariableInspection */
    $cny = GetMyCompany();

    // TODO: query below uses employeemst this needs to  be changed to employee as soon as DB can
    // handle connect by with subquery for MEGL schema
    $qry = "select ENTITY from employeemst where CNY# = :1 start with employeeid= :2 and cny#= :1 connect by prior employeeid = parentkey and cny# = :1";
    $where = array("entity in ($qry) and RECORDTYPE = 'ei' and EMPENTITY != :3", $empid, GetMyEmpentity());
    $expenses = GetNObjects('prrecord', 'RECORD#, empentity', $where);

    $ers = $expenses;
    return true;
}

// TODO: update to this function changed what is returned from a list of employees to an array with a subquery to fetch such a list
// TODO: how is this working?!?!
/**
 * Get a list of employees under the current user's administration
 *
 * @param string $empid The employee id to get list of employees for
 * @param array  $employees
 * @param bool   $getFirstLevelOnly
 *
 * @return bool
 */
function GetSubEmployees($empid, &$employees, $getFirstLevelOnly = false)
{
    $cny = GetMyCompany();

    // KLUDGE: query below uses employeemst this needs to  be changed to employee as soon as DB can
    // handle connect by with subquery for MEGL schema

    $select = "select ENTITY from employeemst where CNY# = $cny start with employeeid= '$empid' and cny#=$cny
                        connect by nocycle prior record# = parentkey and cny#=$cny";

    if ($getFirstLevelOnly) {
        $select .= " and level <= 2";
    }
    $employees = array($select);

    return true;
}

/**
 * Return boolean signifying whether expense report has had any payments made against it
 *
 * @param string $expensekey Expense record#
 *
 * @return bool
 *
 * @return bool true if there are payments for the expense report, false if there aren't
 */
function ExpenseHasPayments($expensekey) {
    $haspymts = "select count(*) as payments from prentrypymtrecs where cny# = :1 and (recordkey = :2  or paymentkey = :2)";
    $qry = array($haspymts,GetMyCompany(),$expensekey);
    $res = QueryResult($qry);
    if($res[0]['PAYMENTS'] != 0) {
        return true;
    }
    return false;
}


/**
 * @param string $id
 *
 * @return string
 */
function RetrieveExpenseBatchKey($id) {

    $where = array("record# = :2", $id);
    $ret = GetNObjects('prrecord', 'prbatchkey', $where);
    if (count($ret) > 0) {
        return $ret[0]['PRBATCHKEY'];
    }
    return '';
}

/**
 * getExpenseBatch - For a given expense report, get the appropriate expense batch.  \
 *
 * If an appropriate expense batch does not exist, create it.
 * he automatic batch format is:
 *   "Scheduled Expense Batch: [date]" where [date] is in the user's format
 *
 * @param string $batchDate   the date the batch was recorded
 * @param string $recordno    the expense batch record number
 * @param string $currency    the transaction currency
 * @param string $recordtype  the recordtype of the transaction
 *
 * @return bool true on success, false on failure
 */
function GetExpenseBatch($batchDate, &$recordno, $currency, $recordtype) {
    $ok = GetSubsysDocBatch($recordtype, $batchDate, $recordno, '', '', '', '', '', '', '', $currency);
    return $ok;
}

/**
 * @param string $batchname
 * @param string $batchdate
 * @param string $batchno
 *
 * @return bool
 */
function CreateExpenseBatch($batchname, $batchdate, &$batchno) {
    $ok = CreateSubsysDocBatch('ei', $batchname, $batchdate, $batchno);
    return $ok;
}


/**
 * IsExpenseReportValid
 *
 * @param array  $er           expense report
 * @param int    $dontvalidate
 * @param string $mode
 *
 * @return bool true on success, false on failure
 */
function IsExpenseReportValid($er, $dontvalidate = 0, $mode = '') {

    global $gErr, $kEEid;

    // Get prefs for the module
    $prefs = array();
    GetModulePreferences($kEEid, $prefs);

    // if the entity is not supplied, we want to make this a very nice error.
    if ( isNullOrBlank($er['ENTITY']) )
    {
        $msg = sprintf('The expense report %1$s is not assigned to an %2$s. ', $er['RECORDID'], 'employee');
        $errorCode = 'EE-0284';
        $placeHolder = ['ER_RECORDID' => $er['RECORDID']];
        if ( $er['CONTACT']['NAME'] )
        {
            $msg =  sprintf('The expense report %1$s is not assigned to an %2$s. Please create an %2$s record for %3$s.', $er['RECORDID'], 'employee', $er['CONTACT']['NAME']);
            $errorCode = 'EE-0285';
            $placeHolder = ['ER_RECORDID' => $er['RECORDID'], 'ER_CONTACT_NAME' => $er['CONTACT']['NAME']];
        }
        $gErr->addIAError($errorCode, __FILE__ . '.' . __LINE__, $msg, $placeHolder);
        return false;
    }

    // what are the required fields?
    $reqfields = array(
        'WHENCREATED' => "'Date Filed' date"
    );
    $isExpense = ($er['RECORDTYPE'] == 'ei') ? true : false ;
    // Do we require expense report number ?
    if ( $isExpense && isset($prefs['REQUIRERECORDID']) && $prefs['REQUIRERECORDID'] == 'T' &&
        ( ! isset($prefs['EISEQUENCE']) || $prefs['EISEQUENCE'] == '' ) )
    {
        $reqfields['RECORDID'] = "'Expense Report Number'";
    }

    // Do we reason for expense ?
    if ( $isExpense && isset($prefs['REQUIREREASONFOREXPENSE']) && $prefs['REQUIREREASONFOREXPENSE'] == 'T' )
    {
        $reqfields['DESCRIPTION'] = "'Reason For Expense' note";
    }

    foreach ( $reqfields as $rval => $rkey )
    {
        if ( ! isset($er[$rval]) || trim($er[$rval]) == '' )
        {
            $gErr->addIAError('EE-0287', __FILE__ . '.' . __LINE__,
                "The expense report requires a valid $rkey. '$er[$rval]' is invalid.",
                ['RKEY' => $rkey, 'ER_RVAL' => $er[$rval]]
            );
            return false;
        }
    }

    // IS THE EMPLOYEE ACTIVE OR NO LONGER EMPLOYED?
    // at this point, ENTITY has not been converted from employee id yet?
    $assertTest = GetEmployee($er['ENTITY'], $emp);
    assert($assertTest);

    if (!$dontvalidate) {
        if ($emp['STATUS'] == 'F') {
            $gErr->addIAError(
                'EE-0286',
                __FILE__.'.'.__LINE__,
                sprintf('The %1$s %2$s is inactive.', 'employee' ,$emp['EMPLOYEEID']),
                ['EMP_EMPLOYEEID' => $emp['EMPLOYEEID']]
            );
            return false;
        }
    }
    $er['WHENPOSTED'] = $er['WHENPOSTED'] ?: $er['WHENCREATED'];
    /* cannot create expense before joining */
    if(!ValidateDate($er['WHENCREATED'])){
    $gErr->addError(
        'EE-0007',
        __FILE__.'.'.__LINE__,
        "Expense Date is not a valid date ");
        return false;
    }
    if(!ValidateDate($er['WHENPOSTED'])){
    $gErr->addError(
        'EE-0008',
        __FILE__.'.'.__LINE__,
        "GL Posting Date is not a valid date ");
        return false;
    }
    if (GetTimestampFromDate($emp['STARTDATE']) > GetTimestampFromDate($er['WHENCREATED'])) {
        $displayDate = FormatDateForDisplay($emp['STARTDATE']);
        $gErr->addIAError(
            'EE-0288',
            __FILE__.'.'.__LINE__,
            sprintf('Cannot create expense before %1$s joining date %2$s', 'employee', $displayDate),
            ['DISPLAY_DATE' => $displayDate]
        );
        return false;
    }
    if ((GetTimestampFromDate($emp['STARTDATE']) > GetTimestampFromDate($er['WHENPOSTED'])) ) {
        $displayDate_1 = FormatDateForDisplay($emp['STARTDATE']);
        $gErr->addIAError(
            'EE-0299',
            __FILE__.'.'.__LINE__,
            sprintf('GL Posting Date Can not be before %1$s\'s joining date %2$s', 'employee', $displayDate_1),
            ['DISPLAY_DATE' => $displayDate_1]
        );
        return false;
    }

    if ($emp['ENDDATE']) {
        if (GetTimestampFromDate($emp['ENDDATE']) <= GetTimestampFromDate($er['WHENCREATED'])) {
            $gErr->addIAError(
                'EE-0289',
                __FILE__.'.'.__LINE__,
                sprintf('The %1$s $2$s has been terminated.', 'employee', $emp['EMPLOYEEID']),
                ['EMP_EMPLOYEEID' => $emp['EMPLOYEEID']]
            );
            return false;
        }
    }

    /** @noinspection PhpUnusedLocalVariableInspection */
    $sequence = ( $er['RECORDTYPE'] == 'ea') ? (( $er['ADJTYPE'] == 'debit memo') ? 'EADMSEQUENCE' : 'EACMSEQUENCE') : 'EISEQUENCE' ;
    // Do we disallow duplicated expense report number ?
    // WW: Always check for dup recordid/docnum because you can provide a custom one through the api and we don't want
    // to break integrations by changing their recordis.
    if( isset($prefs['DUPLICATEDNO']) && $prefs['DUPLICATEDNO'] == 'D'
        && isset($er['RECORDID']) && $er['RECORDID'] != ''
        && $mode == 'Add')
    {

        global $gManagerFactory;
        $entity = ($er['RECORDTYPE'] == 'ea') ? 'expenseadjustments' : 'eexpenses';
        $transaction = ($er['RECORDTYPE'] == 'ea') ? 'Expense Adjustment' : 'expense' ;
        $eeMgr = $gManagerFactory->getManager($entity);

        // If we Add a new expense report number we will check for duplicate
        $filters = array(
            'selects' => array('RECORDNO'),
            'filters' => array(array(array('RECORDID', '=', $er['RECORDID'])))
        );

        // Make sure we do not allow duplicate
        $mec = IsMultiEntityCompany();
        if ($mec) {
            SetReportViewContext();
        }

        $results = $eeMgr->GetList($filters);

        if ($mec) {
            SetTransactionViewContext();
        }

        // if there is a expense report with this number we throw an error
        if(isset($results[0])) {
            $gErr->addIAError('EE-0290', __FILE__ . ':' . __LINE__,
                'An '.$transaction.' with the report number '.$er['RECORDID'].' already exists. Please choose a different '.$transaction.' number.',
                ['TRANSACTION' => $transaction, 'ER_RECORDID' => $er['RECORDID']]
            );
            return false;
        }
    }

    // do simple validation on the line items
    $ok = true;
    $err = [];
    $placeholderText = [];
    $reqfields = array(
        'ACCOUNTKEY' => 'Expense Type',
        'AMOUNT' => 'Amount'
    );

    // Do we required notes on line items ?
    // Note: notes i.e. 'Paid To' and 'Paid For' is not available for Expense Adjustments.
    if( $isExpense && isset($prefs['REQUIREDESCRIPTION']) && $prefs['REQUIREDESCRIPTION'] == 'T' ) {
        $reqfields['DESCRIPTION'] = "'Paid To' note";
        $reqfields['DESCRIPTION2'] = "'Paid For' note";
    }

    // Do we required date on line items ?
    if( $isExpense && isset($prefs['REQUIRELINELEVELDATE']) && $prefs['REQUIRELINELEVELDATE'] == 'T' )
    {
        $reqfields['ENTRY_DATE'] = "'Date'";
    }

    // define line item fields and API against which field value needs checked
    // API may return boolean and in case of FALSE, error is added
    // 1. validate line item dates against user date format
    $validateFieldWithAPI = array(
        'ENTRY_DATE' => array (
                            'api' => 'ValidateInputDate',
                            'errmsg' => 'invalid format specified for entry date.'
                        )
        );

    $billable = isBillableEnableForModule('ee');

    $reimbursableTotal = '0.00';
    if (is_array($er['ITEMS'][0]['ITEMS'])) {
        foreach($er['ITEMS'][0]['ITEMS'] as $itemkey => $item) {
            if ($item['NONREIMBURSABLE'] == 'F') {
                $reimbursableTotal = bcadd($reimbursableTotal, $item['AMOUNT'], 2);
            }
            foreach($reqfields as $key => $value) {
                if ( ! isset($item[$key]) || trim($item[$key]) == '' ) {
                    if ($key == 'AMOUNT' && !empty($item['NR_AMOUNT'])) {
                        continue;
                    }
                    $ok = false;
                    /** @noinspection PhpUnusedLocalVariableInspection */
                    $val = ( $key == 'ACCOUNTKEY' ) ? $item['ACCOUNTLABEL'] : $item[$key] ;
                    $err[] = "The expense report item no ".($itemkey+1)." requires a valid $value.";
                    $placeholderText[] = I18N::getSingleToken('IA.THE_EXPENSE_REPORT_ITEM_NO_REQUIRES_A_VALID', [
                        ["name" => 'ITEMKEY_PLUS_1', "value" => ($itemkey+1)],
                        ["name" => 'VALUE', "value" => $value]
                    ]);
                }
            }
        }
    } else {

        $itemList = array();

        foreach($er['ITEMS'] as $itemkey => $item) {
            if ($item['NONREIMBURSABLE'] == 'F') {
                $reimbursableTotal = bcadd($reimbursableTotal, $item['AMOUNT'], 2);
            }
            foreach($reqfields as $key => $value) {
                if ( ! isset($item[$key]) || trim($item[$key]) == '' ) {
                    if ($key == 'AMOUNT' && !empty($item['NR_AMOUNT'])) {
                        continue;
                    }
                    $ok = false;
                    /** @noinspection PhpUnusedLocalVariableInspection */
                    $val = ( $key == 'ACCOUNTKEY' ) ? $item['ACCOUNTLABEL'] : $item[$key] ;
                    $err[] = "The expense report item no ".($itemkey+1)." requires a valid $value.";
                    $placeholderText[] = I18N::getSingleToken('IA.THE_EXPENSE_REPORT_ITEM_NO_REQUIRES_A_VALID', [
                        ["name" => 'ITEMKEY_PLUS_1', "value" => ($itemkey+1)],
                        ["name" => 'VALUE', "value" => $value]
                    ]);
                }
            }
            foreach($validateFieldWithAPI as $field => $apiArr) {
                $api = $apiArr['api'];
                if ( isset($api) && function_exists($api) ) {
                    $fldVal = FormatDateForDisplay($item[$field]);
                    if (isset($fldVal) && $fldVal != '') {
                        if ( !$api($fldVal) ) {
                            $errmsg = $apiArr['errmsg'];
                            $ok = false;
                            $err[] = "The expense report item no ".(string) ($itemkey+1). " has error - ".$errmsg;
                            $placeholderText[] = I18N::getSingleToken('IA.THE_EXPENSE_REPORT_ITEM_NO_HAS_ERROR', [
                                ["name" => 'ITEMKEY_PLUS_1', "value" => ($itemkey+1)],
                                ["name" => 'ERR_MSG', "value" => $errmsg]
                            ]);
                        }
                    }
                }
            }

            // Check if dimension are set with the billable option
            if ( $billable ) {
                if( ! PREntryManager::ValidateBillableOption($item) ) {
                    $ok = false;
                }

                // Let keep a list of all item selected
                if( isset($item['BILLABLE']) && $item['BILLABLE'] == 'T' &&
                    isset($item['ITEMID']) && $item['ITEMID'] != '' &&
                    ! in_array($item['ITEMID'], $itemList, true) )
                {
                    $itemList[] = $item['ITEMID'];
                }

            } else if( isset($item['BILLABLE']) && $item['BILLABLE'] == 'T' )  {
                $item['BILLABLE'] = 'F';
            }
        }

        // For billable all item types can not be selected.
        // Let do the validation here so we will limit the number of queries
        if( count($itemList) )
        {
            global $gManagerFactory;
            $itemMgr = $gManagerFactory->getManager('item');

            $params = array(
                'selects' => array('ITEMID', 'ITEMTYPE'),
                'filters' => array(
                    array(array('ITEMID', 'in', $itemList))
                )
            );

            $result = $itemMgr->GetList($params);

            foreach($result as $item)
            {
                if( $item['ITEMTYPE'] != NONINV )
                {
                    global $gErr;
                    $msg = "Item type not valid for billing.";
                    $corr = "The item '".$item['ITEMID']."' can not be tagged as billable. Please select a " . NONINV . " item.";
                    $gErr->addIAError('EE-0291', __FILE__ . ':' . __LINE__,
                        $msg,
                        [],
                        '',
                        [],
                        $corr,
                        ['ITEM_ITEMID' => $item['ITEMID']]
                    );
                    $ok = false;
                }
            }
        }
    }
    //Throw error if reimbursable total of expense report is negative
    if ( $er['RECORDTYPE'] == 'ei' && ( ibcabs($reimbursableTotal) != $reimbursableTotal && $er['RECSTATE'] == 'S')) {
        $gErr->addError('EE-0283', __FILE__ . '.' . __LINE__,
                        "The reimbursable total of the expense report cannot be negative.", ' ',
                        'Try using Expense Adjustment for negative expense items.');
        /** @noinspection PhpUndefinedVariableInspection */
        /** @noinspection PhpUnusedLocalVariableInspection */
        $errcnt++;
        $ok = false;
    }

    if (!$ok && !empty($err)) {
        /** @noinspection PhpUndefinedVariableInspection */
        $errmsg = join("<br>", $err);
        $gErr->addIAError(
            'EE-0302',
            __FILE__.'.'.__LINE__,
            $errmsg, ['JOIN_PLACEHOLDER' => join("<br>", $placeholderText)]
        );
        return false;
    }

    return true;
}

/**
 * GroomExpenseReportOut
 *
 * @param array $er
 * @param bool  $format
 */
function GroomExpenseReportOut(&$er, $format=true) 
{
    // Translate the value of the expense
    if ( $er['STATE'] == PRRECORD_STATE_DVOIDED )
    {
        $er['STATE'] = ( $er['TOTALENTERED'] > 0 ) ? "Reversed" : "Reversal";
    }
    else if( $er['TOTALENTERED'] <> 0
             && $er['TOTALPAID'] == bcsub($er['TOTALENTERED'], $er['NR_TOTALENTERED'])
             && bcsub($er['TOTALENTERED'], $er['NR_TOTALENTERED']) > 0)
    {
        $er['STATE'] = 'Paid';
    } else if ( $er['TOTALPAID'] <> 0 ) {
        $er['STATE'] = 'Partially Paid';
    } else if ( $er['TOTALSELECTED'] <> 0 ) {
        $er['STATE'] = 'Selected';
    } else {
        $statevals = array(
            PRRECORD_STATE_DRAFT => 'Draft',
            PRRECORD_STATE_APPROVED => 'Approved',
            PRRECORD_STATE_PARTIALLYAPPROVED => 'Partially Approved',
            PRRECORD_STATE_SUBMITTED => 'Submitted',
            PRRECORD_STATE_UNAPPROVED => 'Declined',
            PRRECORD_STATE_PARTIALLYUNAPPROVED => 'Partially Declined'
        );

        $er['STATE'] = ( ! isset($er['STATE']) || $er['STATE'] == '' ) ? 'Submitted' : $statevals[$er['STATE']];
    }

    if ($format) {
        $er['TOTALENTERED'] = glFormatCurrency($er['TOTALENTERED']);
    }
}

/**
 * CreateExpenseReport
 *
 * @param array $expensereport
 *
 * @return bool true on success, false on failure
 */
function CreateExpenseReport(&$expensereport) {

    $owner = 'CreateExpenseReport';

    XACT_BEGIN($owner);
    if (!CreateNObject('expensereport', $expensereport)) {
        epp(__FILE__ . '.' . __LINE__ . " CreateNObject failed.");
        XACT_ABORT($owner);
        return false;
    }

    XACT_COMMIT($owner);
    return true;

}

/**
 * GetApprovalHistory
 *
 * @param string $r prrecordhistory key
 *
 * @return bool|string[][]
 */
function GetApprovalHistory($r)
{
    static $textMap = null;

    if ($textMap === null) {
        $tokens = [
            'IA.APPROVED',
            'IA.UNAPPROVED',
            'IA.SUBMITTED',
            'IA.CREATED',
            'IA.POSTED'
        ];

        $textMap = getLocalizedTextWithThrow(I18N::tokenArrayToObjectArray($tokens));
    }
    $decodeaction = "decode(PRRECORDHISTORY.ACTION, 'A ', '". GT($textMap, 'IA.APPROVED') ."', 'U ', '". GT($textMap, 'IA.UNAPPROVED') ."', 'S ', '". GT($textMap, 'IA.SUBMITTED') ."', 'C ', '". GT($textMap, 'IA.CREATED') ."', '". GT($textMap, 'IA.POSTED') ."') as ACTION";
    $decodedate = "to_char(PRRECORDHISTORY.EVENTDATE, 'MM/DD/YYYY') as WHENREVIEWED";
    $orderby = " ORDER BY PRRECORDHISTORY.RECORD#";
    $qry = array(
        'QUERY' => "select PRRECORDHISTORY.RECORDKEY, CONTACT.NAME ENTITY, $decodedate, $decodeaction, PRRECORDHISTORY.COMMENTS " .
        "from PRRECORDHISTORY, CONTACT where PRRECORDHISTORY.CONTACTKEY = CONTACT.RECORD# and PRRECORDHISTORY.RECORDKEY = ? and " .
        "PRRECORDHISTORY.CNY# = ? and CONTACT.CNY# = ? $orderby ",
        'ARGTYPES' => array('integer', 'integer', 'integer')
        );
    $cny = GetMyCompany();
    $qryargs = array($r, $cny, $cny);
    global $gQueryMgr;
    $history = $gQueryMgr->DoCustomQuery($qry, $qryargs, true);
    return $history;

}


/**
 * Get the line item totals
 *
 * @param array  $expense the expense report
 * @param string $approval
 *
 * @return string the total expenses filed.
 *
 */
function ERTotal($expense, /** @noinspection PhpUnusedParameterInspection */ $approval='') {
    $_ismcpEnabled = IsMCPEnabled('ee');
    $amtfld = 'AMOUNT';
    $nr_amtfld = 'NR_AMOUNT';
    if($_ismcpEnabled){
        if(isset($expense['CURRENCY']) && ($expense['CURRENCY'] != GetBaseCurrency())){
            $amtfld = 'TRX_AMOUNT';
            $nr_amtfld = 'NR_TRX_AMOUNT';
        }
    }

    $total = bcadd('0', '0', 2);

    if (is_array($expense['ITEMS'])) {
        foreach( $expense['ITEMS'] as $couplet) {
            if (isset($couplet['ITEMS'])) {
                foreach($couplet['ITEMS'] as $item) {
                    $total = bcadd($total, bcsub($item[$amtfld], $item[$nr_amtfld], 2), 2);
                }
            }
            else {
                $total = bcadd($total, bcsub($couplet[$amtfld], $couplet[$nr_amtfld], 2));
            }
        }
    }
    return $total;
}

/**
 * GetExpenseMap
 *
 * @param string $_r
 * @param array  $fullData
 *
 * @return bool true on success, false on failure
 */
function GetExpenseMap($_r, &$fullData) {
    /** @noinspection PhpUnusedLocalVariableInspection */
    $ok = true;

    $_ismcpEnabled = IsMCPEnabled('ee');
    $_r = $_r ?? "1";
    $company = GetCompanyDataForXML();

    /** @noinspection PhpUnusedLocalVariableInspection */
    $ok = GetPRRecord($_r, $rec);
    $rec['WHENCREATED'] = FormatDateForDisplay($rec['WHENCREATED']);
    $rec['WHENPOSTED'] = FormatDateForDisplay($rec['WHENPOSTED']);
    $rec['WHENDUE'] = FormatDateForDisplay($rec['WHENDUE']);

    if($_ismcpEnabled){
        $totalentred = $rec['TRX_TOTALENTERED']=='' ? $rec['TOTALENTERED'] : $rec['TRX_TOTALENTERED'];
        $totalpaid = $rec['TRX_TOTALPAID']==''? $rec['TOTALPAID'] : $rec['TRX_TOTALPAID'];
        $totaldue = $rec['TRX_TOTALDUE']=='' ? $rec['TOTALDUE'] : $rec['TRX_TOTALDUE'];
    }else{
        $totalentred = $rec['TOTALENTERED'];
        $totalpaid = $rec['TOTALPAID'];
        $totaldue = $rec['TOTALDUE'];
    }

    $rec['TOTALENTERED'] = Currency($totalentred,1,0, $rec['CURRENCY']);
    $rec['TOTALPAID'] = Currency($totalpaid,1,0, $rec['CURRENCY']);
    $rec['TOTALDUE'] = Currency($totaldue,1,0, $rec['CURRENCY']);
    $rec['MCPENABLED'] = $_ismcpEnabled;
    if (isl_strlen($rec['DESCRIPTION']) > 260)  {
        $rec['DESCRIPTION'] = isl_substr($rec['DESCRIPTION'], 0, 260) . '...';
    }

    $select = " prentry.record#, prentry.status, prentry.entry_date, prentry.recordkey, prentry.description as DESCRIPTION,
                prentry.DESCRIPTION2,
                prentry.accountkey, glaccount.acct_no, glaccount.title as acct_title, prentry.amount as AMOUNT, nvl(prentry.trx_amount, prentry.amount)  as TRX_AMOUNT, prentry.nr_amount as NR_AMOUNT, nvl(prentry.nr_trx_amount, prentry.nr_amount) as NR_TRX_AMOUNT, prentry.location#, prentry.dept#, prentry.line_no,
                prentry.accountlabelkey, accountlabel.description as acctlabeldescription";
    $where = array("recordkey = :2 and lineitem = 'T'", $_r);
    $order = "line_no,record#";
    $prentry = GetNObjects('prentry', $select, $where, $order);
    
    // We will keep the account label property value to change the output from Expense Type to GL account
    $hasLabel = GetLabelStatus('ee');
    $rec['LABELSTATUS'] = $hasLabel;
    
    foreach ( $prentry as & $prEntryRow ) {
        
        $amt = ($_ismcpEnabled) ? $prEntryRow['TRX_AMOUNT'] : $prEntryRow['AMOUNT'];
        $nr_amt = ($_ismcpEnabled) ? $prEntryRow['NR_TRX_AMOUNT'] : $prEntryRow['NR_AMOUNT'];
        $prEntryRow['ENTRY_DATE'] = FormatDateForDisplay($prEntryRow['ENTRY_DATE']);
        $prEntryRow['AMOUNT'] = Currency($amt, 1, 0, $rec['CURRENCY']);
        $prEntryRow['NR_AMOUNT'] = Currency($nr_amt, 1, 0, $rec['CURRENCY']);

        $locno = $prEntryRow['LOCATION#'];
        $depno = $prEntryRow['DEPT#'];
        
        $locwhere = array("record# = :2", $locno);
        $deptwhere = array("record# = :2", $depno);
        
        $loc = GetNObjects("location", "(location_no || ' - ' || name) as NAME", $locwhere);
        $dep = GetNObjects("department", "(dept_no || ' - ' || title) as TITLE", $deptwhere);
        
        $prEntryRow['NAME'] = $loc[0]['NAME'];
        $prEntryRow['TITLE'] = $dep[0]['TITLE'];
        
        if ( ! $hasLabel ) {
            $prEntryRow['EXPENSETITLE'] = $prEntryRow['ACCT_NO'] . '--' . $prEntryRow['ACCT_TITLE'];
        } else { 
            $prEntryRow['EXPENSETITLE'] = $prEntryRow['ACCTLABELDESCRIPTION'];
        }
        
        if ( $_ismcpEnabled ) {
            list($description2) = explode("|", $prEntryRow['DESCRIPTION2']);
            $prEntryRow['DESCRIPTION2'] = $description2;
        }
    }
    unset($prEntryRow);

    if( $rec['EMPENTITY'] != '' ){

        $params = array(
            'selects' => array(
                'RECORDNO', 'EMPLOYEEID', 'ENTITY', 'TITLE', 'PERSONALINFO.PRINTAS',
                'PERSONALINFO.MAILADDRESS.ADDRESS1', 'PERSONALINFO.MAILADDRESS.ADDRESS2',
                'PERSONALINFO.MAILADDRESS.ADDRESS3',
                'PERSONALINFO.MAILADDRESS.CITY', 'PERSONALINFO.MAILADDRESS.STATE',
                'PERSONALINFO.MAILADDRESS.COUNTRY', 'PERSONALINFO.MAILADDRESS.ZIP',
                'PARENTKEY', 'SUPERVISORID', 'SUPERVISORNAME',
                'DEPARTMENT.DEPT_NO', 'DEPARTMENT.TITLE',
                'LOCATION.LOCATION_NO', 'LOCATION.NAME',
            ),
            'filters' => array (array (array('ENTITY', '=', $rec['EMPENTITY'])))
        );

        global $gManagerFactory;
        $empMgr = $gManagerFactory->getManager('employee');
        $empInfo = $empMgr->GetList($params);

        $employee = array();
        if ( isset($empInfo[0]) && $empInfo[0]['RECORDNO'] != '' ) {
            $employee = $empInfo[0];
            $employee['PRINTAS'] = $empInfo[0]['PERSONALINFO.PRINTAS'];
            $employee['SUPERVISOR'] = $empInfo[0]['SUPERVISORNAME'];
            $employee['TITLE'] = $empInfo[0]['DEPARTMENT.DEPT_NO'] . ' - ' . $empInfo[0]['DEPARTMENT.TITLE'];
            $employee['NAME'] = $empInfo[0]['LOCATION.LOCATION_NO'] . ' - ' . $empInfo[0]['LOCATION.NAME'];
            $employee['ADDR1'] = $empInfo[0]['PERSONALINFO.MAILADDRESS.ADDRESS1'];
            $employee['ADDR2'] = $empInfo[0]['PERSONALINFO.MAILADDRESS.ADDRESS2'];
            $employee['ADDR3'] = $empInfo[0]['PERSONALINFO.MAILADDRESS.ADDRESS3'];
            $employee['EMPCITY'] = $empInfo[0]['PERSONALINFO.MAILADDRESS.CITY'];
            $employee['EMPSTATE'] = $empInfo[0]['PERSONALINFO.MAILADDRESS.STATE'];
            $employee['COUNTRY'] = $empInfo[0]['PERSONALINFO.MAILADDRESS.COUNTRY'];
            $employee['EMPZIP'] = $empInfo[0]['PERSONALINFO.MAILADDRESS.ZIP'];
        }

        // To fetch the expense report details
        /** @noinspection PhpUnusedLocalVariableInspection */
        $ok = GetNObject('expensereport', $_r, $details);

        if (isl_strlen($details['COMMENTS']) > 260)  {
            $details['COMMENTS'] = isl_substr($details['COMMENTS'], 0, 260) . '...';
        }

        //  make an comple array of all the invoice data
        $renameTerms = array();
        $renameTerms['term_Department'] = 'IA.DEPARTMENT';
        $renameTerms['term_Location'] = 'IA.LOCATION';
        $renameTerms['term_DepartmentU'] = 'IA.DEPARTMENT_UPPERCASE';
        $renameTerms['term_LocationU'] = 'IA.LOCATION_UPPERCASE';
      
        $fullData['expensereport'] =  array();
        $fullData['expensereport']['COMPANY'][] = GenSML($company);
        $fullData['expensereport']['EMPLOYEE'][] = GenSML($employee);
        $fullData['expensereport']['EXPENSE'][] =  GenSML($rec);
        $fullData['expensereport']['EXPITEMS'] = GenSML($prentry);
        $fullData['expensereport']['TERM'][] = GenSML($renameTerms);
        $fullData['expensereport']['MEMO'][] = GenSML(array('COMMENTS' => $details['COMMENTS']));
        
        $ok = true;
    }
    else{
        $ok = false;
    }

    return $ok;
}

/**
 * GetExpenseXML
 *
 * @param string $root root name
 * @param array  $raw
 *
 * @return string
 */
function GetExpenseXML($root,$raw)
{
    $g = new genxml(0,'UTF-8');
    $xml = $g->build($root,$raw);
    return $xml;
}

/**
 * GetExpenseFO
 *
 * @param string $xml
 *
 * @return string
 */
function GetExpenseFO($xml){
    $xsl = GetExpenseXSL();

    /** @noinspection PhpUnusedLocalVariableInspection */
    $res = XSLTInProcess($xsl, $xml, $fo_result);
    return $fo_result;
}

/**
 * GetExpenseXSL
 *
 * @return string
 */
function GetExpenseXSL(){
    $filename = "expense.xsl";
    return join("\n", file("../../private/xslinc/".$filename));
}


/**
 * Updates an existing submitted, approved or paid expense report
 *
 * @param string $id          The expense report record key
 * @param array  $expense     contains the expense report to update.  On response, will include the record# and other internal keys.
 * @param bool   $getstate
 * @param string $objTypeName EntityManager object type (_entity) name
 *
 * @return bool true on success, false on failure
 *
 * @return bool true on success, false on failure
 */
function SetExpensePartial($id, &$expense, $getstate=true, $objTypeName='')
{
    global $gErr, $gManagerFactory;

    // AN EXPENSE REPORT CAN ONLY BE UPDATED IF ITS CURRENT STATE IS
    // DRAFT OR UNAPPROVED / SUBSEQUENT STATES CAN BE DRAFT,
    // UNAPPROVED, SUBMITTED, OR RESUBMITTED

    if ($getstate) {
        $assertTest = GetExpense($id, $tmp, false);
        assert($assertTest);
        $curstate = $tmp['STATE'];
        $expense['STATE'] = $curstate;
    } else {
        $curstate = $expense['STATE'];
    }

    $prBatchMgr = $gManagerFactory->getManager('prbatch');
    $filters = array(
        'selects' => array('OPEN', 'NOGL'),
        'filters' => array(array(array('RECORDNO', '=', $expense['PRBATCHKEY']))),
    );
    $batch = $prBatchMgr->GetList($filters);
    
    // Is the batch open ?
    if ( $batch[0]['OPEN'] == 'Closed' )  {
        $gErr->addError(
            'EE-0009', 
            GetFL(),
            "The expense batch is closed. Please check with your administrator."
        );
        return false;
    }

    // CSV uploaded expense will be in partial edit mode so the best we can do is to by pass this check
    if ( $batch[0]['NOGL'] != 'T' && ( $curstate == PRRECORD_STATE_DRAFT || $curstate == PRRECORD_STATE_UNAPPROVED ) ) {
        $gErr->addError(
            'EE-0010', 
            GetFL(),
            "The expense report can be partially edited only if it is not declined or if it is not a draft."
        );
        return false;
    }
    $owner = 'SetExpensePartial';

    if (!IsExpenseReportValid($expense)) {
        // some error handling here please
        epp("The expense report is invalid.");
        return false;
    }

    // CREATE A HISTORY EVENT -- A USER EDITED THE EXPENSE REPORT.
    // if the user is simply submitting, there may not be any edits.

    // GET THE PRRECORDHISTORY MANAGER
    $objPh = $gManagerFactory->getManager('prrecordhistory');
    $objPh->SetWorkflow('expensereport');

    // we need to create a history entry for the submission
    // GET THE CONTACT KEY FOR THIS USER.
    $contactname = GetMyContactName();

    $comments = 'partial edit';
    if (isset($expense['RECLASS_NOTES']) && $expense['RECLASS_NOTES'] != '') {
        $comments = $comments . ' - ' . $expense['RECLASS_NOTES'];
    }

    /** @noinspection PhpUndefinedVariableInspection */
    $submissionhistory = array(
        'PRRECORD' => ($getstate ? $tmp['RECORD#'] : $id),
        'EVENTDATE' => GetCurrentDate(),
        'ACTION' => PRRECORD_ACTION_EDIT,
        'CONTACT' => $contactname,
        'COMMENTS' => $comments,
    );

    // GET THE EXPENSEDETAIL. DO THIS FOR updating MEMO field
    $assertTest = GetNObject('expensereport', $id, $details);
    assert($assertTest);

    XACT_BEGIN($owner);

    // UPDATE THE DETAIL
    $details['COMMENTS'] = isl_substr($expense['COMMENTS'], 0, MAX_COMMENTS_LENGTH);
    if (!SetNObject('expensereport', $details, $id)) {
        epp("Unable to update the expense details.");
        XACT_ABORT($owner);
        return false;
    }

    //Set Line Numbers for Line Items
    SetLineNum($expense['ITEMS']);

    $expense['ENTITY'] = 'E'.$expense['ENTITY'];
    $expense['PRENTRY'] = $expense['ITEMS'];

    /** UPDATE HERE **/
    // SET THE PRRECORD - use SetPartialInvoice

    if (!SetPartialInvoice($id, $expense, [], $objTypeName)) {
        // some error handling here please
        epp("Unable to set the prrecord.");
        XACT_ABORT($owner);
        return false;
    }

    // CREATE THE PARTIAL EDIT HISTORY
    if ($submissionhistory) {
        if (!$objPh->add($submissionhistory)) {
            // add some error here?
            XACT_ABORT($owner);
            return false;
        }
    }

    // SET SUPPORTING DOCUMENTS MAP HERE
    if (!SetSupportingDocumentMap($id, $expense['SUPDOCID'], $expense['EXTERNALURL'], $expense['SUPDOCENTITY']) ) {
        XACT_ABORT($owner);
        return false;
    }

    XACT_COMMIT($owner);

    return true;
}


/**
 * Reclass an existing submitted, approved or paid expense report
 *
 * @param string $id     The expense report record key
 * @param array $expense contains the expense report to update.  On response, will include the record# and other internal keys.
 *
 * @return bool true on success, false on failure
 *
 * @return bool true on success, false on failure
 */
function SetExpenseForReclass($id, &$expense)
{
    global $gErr, $gManagerFactory;
    global $gExpense;

    // AN EXPENSE REPORT CAN ONLY BE UPDATED IF ITS CURRENT STATE IS
    // DRAFT OR UNAPPROVED / SUBSEQUENT STATES CAN BE DRAFT,
    // UNAPPROVED, SUBMITTED, OR RESUBMITTED

    $assertTest = GetExpense($id, $tmp, false);
    assert($assertTest);
    $curstate = $tmp['STATE'];
    $expense['STATE'] = $curstate;

    $curbatchkey = $tmp['PRBATCHKEY'];
    // If somehow expense PRBATCHKEY is not sent, use current expense's PRBATCHKEY
    if (!isset($expense['PRBATCHKEY']) || $expense['PRBATCHKEY'] == '') {
        if (isset($curbatchkey) && $curbatchkey != '') {
            $expense['PRBATCHKEY'] = $curbatchkey;
        }
    }

    if ($curstate == PRRECORD_STATE_DRAFT || $curstate == PRRECORD_STATE_UNAPPROVED) {
        $gErr->addError('EE-0011', GetFL(),
            "The expense report can be reclassified only if it is ".
            "not declined or if it is not a draft.");
        return false;
    }

    $owner = 'SetExpenseForReclass';
    // check if this record has been re-classed
    $reclassed = IsExpenseReclassed($tmp, $expense);

    if (!IsExpenseReportValidForReclass($expense, $tmp)) {
        // some error handling here please
        epp("The expense report is invalid.");
        return false;
    }

    $gExpense = $tmp;
        if (!$reclassed) {
                // if none of the re-class fields has been modified, just
                // treat it as partial edit
                return (SetExpensePartial($id, $expense, false));
        }

    // make the change in term and support doc, if any
    if (!SetTermKeyandSupDocEntity($id, $expense)) {
        return false;
    }

    $expense['ENTITY'] = 'E'.$expense['ENTITY'];
    $expense['RECORD#'] = $id;
    // $expense['PRENTRY'] = $expense['ITEMS'];

    // CREATE A HISTORY EVENT -- A USER EDITED THE EXPENSE REPORT.
    // if the user is simply submitting, there may not be any edits.

    // GET THE PRRECORDHISTORY MANAGER
    $objPh = $gManagerFactory->getManager('prrecordhistory');
    $objPh->SetWorkflow('expensereport');

    // we need to create a history entry for the submission
    // GET THE CONTACT KEY FOR THIS USER.
    $contactname = GetMyContactName();

    $submissionhistory = array(
        'PRRECORD' => $tmp['RECORD#'],
        'EVENTDATE' => GetCurrentDate(),
        'ACTION' => PRRECORD_ACTION_RECLASS,
        'CONTACT' => $contactname,
        'COMMENTS' => 'reclassified - ' . $expense['RECLASS_NOTES'],
    );

    // GET THE EXPENSEDETAIL. DO THIS FOR updating MEMO field
    $assertTest = GetNObject('expensereport', $id, $details);
    assert($assertTest);

    XACT_BEGIN($owner);

    // UPDATE THE DETAIL
    $details['COMMENTS'] = $expense['COMMENTS'];
    if (!SetNObject('expensereport', $details, $id)) {
        epp("Unable to update the expense details.");
        XACT_ABORT($owner);
        return false;
    }

    // IS THE BATCH OPEN?
    $res = GetNObjects('prbatch', 'OPEN', "RECORD# = " . $expense['PRBATCHKEY']);
    if ($res[0]['OPEN'] == 'F')  {
        // some error handling here please
        epp("The expense batch is closed.  Please check with your administrator");
        XACT_ABORT($owner);
        return false;
    }

    $ismega = IsMultiEntityCompany();
    $ctxloc = GetContextLocation();

    if ( $ismega && $ctxloc === false ) {
        import('IETManager');
        $IETMgr = new IETManager();
    }

    // process the lines
    $inlineCreditPmts = array();
    foreach ($tmp['ITEMS'] as $lineitemno => $olditem) {

        $newitem = $expense['ITEMS'][$lineitemno];

        // if the line is only partially edited, just update the line
        // mod will not be set to true if reclassed
        if ($olditem['mod'] === true) {
            $linerec = $olditem;
            foreach ($newitem as $itemkey => $itemvalue) {
                $linerec[$itemkey] = $itemvalue;
            }
            if (!SetNObjectByRef('prentry', $linerec, $olditem['RECORD#'])) {
                XACT_ABORT($owner);
                $gErr->addError('EE-0012', GetFL(), "Cannot save prentry record");
                return false;
            }
            continue;

        } elseif (!$olditem['reclassed']) {
            // skip this line if this line is not reclassed
            continue;
        }

        // check to see if we need to re-generate IETs (MEGA)
        $ietneeded = false;
        if ($ismega) {
            $ietneeded = $olditem['locchanged'] === true || $olditem['ietneeded'] === true;
        }

        if ($ismega && $ietneeded) {

            // For credit line items we need to check whether it has IET associated with it
            // If so, then we should not allow user to reclassify the line item
            if ($ctxloc === false && $tmp['ITEMS'][$lineitemno]['AMOUNT'] < 0) {

                // Get associated expense lines
                $expenseinfo = GetEPExpenseInfo($olditem['RECORD#']);

                foreach ($expenseinfo as $exp) {
                    epp(pp(array("Going to compare entities E1:", $olditem['BASELOCATION'], " E2: ", $exp['BASELOCATION'])));
                    /** @noinspection PhpUndefinedVariableInspection */
                    if ( $olditem['BASELOCATION'] != $exp['BASELOCATION'] &&
                         $IETMgr->IsIETRequired($olditem['BASELOCATION'], $exp['BASELOCATION'])
                    ) {
                        $gErr->addError('EE-0013', GetFL(),
                                        "Reclassification of transactions with inline credits that result in an inter-entity transaction are not permitted at this time. We recommend the following actions instead:",
                                        "", "Create a journal entry or Reverse the original transaction and create a new tranaction with the correct accounting elements");
                        epp("Reclassification of transactions with inline credits that result in an inter-entity transaction are not permitted at this time");
                        XACT_ABORT($owner);
                        return false;
                    }
                }

                // If credit line item is reclassified and it involves IETs to be created/regenerated,
                // then we are not allowing reclassification
                /** @noinspection PhpUndefinedVariableInspection */
                if ( $tmp['ITEMS'][$lineitemno]['BASELOCATION'] != $expense['ITEMS'][$lineitemno]['LOCATION#'] &&
                     $IETMgr->IsIETRequired($tmp['ITEMS'][$lineitemno]['BASELOCATION'], $expense['ITEMS'][$lineitemno]['LOCATION#'])
                ) {
                    $gErr->addError('EE-0014', GetFL(),
                                    "Reclassification of transactions with inline credits that result in an inter-entity transaction are not permitted at this time. We recommend the following actions instead:",
                                    "", "Create a journal entry or Reverse the original transaction and create a new tranaction with the correct accounting elements");
                    epp("Reclassification of transactions with inline credits that result in an inter-entity transaction are not permitted at this time");
                    XACT_ABORT($owner);
                    return false;
                }
            }

            // delete all the existing IETs
            foreach ($olditem['payinfo'] ?? [] as $payment) {
                if (!DeleteEEIETsForReclass($payment)) {
                    XACT_ABORT($owner);
                    return false;
                }

            }
        }

        // we can update the line now
        $linerec = array();
        $newitem = $expense['ITEMS'][$lineitemno];
        //Set default location if empty:
        $newitem['LOCATION#'] = DefaultPRItemLocation($newitem['LOCATION#']);
        foreach ($newitem as $itemkey => $itemvalue) {
            $linerec[$itemkey] = $itemvalue;
        }
        if ($ismega) {
            // set BASELOCATION to the new LOCATION# separately -> it is not set in $newitem and was not copied above
            $linerec['BASELOCATION'] = $linerec['LOCATION#'];
        }

        if (!SetNObjectByRef('prentry', $linerec, $olditem['RECORD#'])) {
            XACT_ABORT($owner);
            $gErr->addError('EE-0015', GetFL(), "Cannot save prentry record");
            return false;
        }

        // take care of the offset line
        if (!HandleEEOffsetForReclass($olditem, $newitem, $curoffsetacct)) {
            XACT_ABORT($owner);
            return false;
        }

        // take care of the associated payment lines, if any
        if ($olditem['payinfo']) {
            $lkeys = array();
            foreach ($olditem['payinfo'] as $payrec) {
                // skip the inline credit case
                if ($payrec['PAYMENTKEY'] != $olditem['RECORDKEY']) {
                    if (!HandleEEPaymentForReclass($olditem, $newitem, $payrec, $curoffsetacct, $lkeys)) {
                        XACT_ABORT($owner);
                        return false;
                    }
                }
                $paymenthistory = array(
                    'PRRECORD' => $payrec['PAYMENTKEY'],
                    'EVENTDATE' => GetCurrentDate(),
                    'ACTION' => PRRECORD_ACTION_RECLASS,
                    'CONTACT' => $contactname,
                    'COMMENTS' => 'reclassified - for expense report '.$gExpense['RECORDID'],
                );
                // CREATE THE RECLASS HISTORY
                if (!$gExpense['history'][$payrec['PAYMENTKEY']]) {
                    if (!$objPh->addWithWorkflow($paymenthistory, false)) {
                        // add some error here?
                        XACT_ABORT($owner);
                        return false;
                    }
                    $gExpense['history'][$payrec['PAYMENTKEY']] = 1;
                }
            }
        }

        // Inter Entity Transfer
        if (!$ismega || !$ietneeded) {
            continue;
        }

        // rebuild IETs
        if ($olditem['payinfo']) {
            foreach ($olditem['payinfo'] as $payrec) {
                if (!ProcessEEIETsForReclass($olditem, $payrec)) {
                    XACT_ABORT($owner);
                    return false;
                }

                if ($payrec['PAYMENTKEY'] == $olditem['RECORDKEY']) {
                    // this is an inline-credit item - special handling will be needed:
                    // we'll create an array of (paymentkey, payitemkey) pairs to process later
                    $key = $payrec['PAYMENTKEY'] . ',' . $payrec['PAYITEMKEY'];
                    if (!array_key_exists($key, $inlineCreditPmts)) {
                        // not a repeat -> add to array:
                        $inlineCreditPmts[$key] = array('PAYMENTKEY' => $payrec['PAYMENTKEY'], 'PAYITEMKEY' => $payrec['PAYITEMKEY']);
                    }
                }
            }
        }
    }

    // process inline credit IETs if needed
    if (!empty($inlineCreditPmts)) {
        $payments = array();
        foreach ($inlineCreditPmts as $pmtPair) {
            // get all payments that use this payitem for payment and re-generate their IETs (they were deleted before)
            $res = QueryResult(
                array("select * from prentrypymtrecs where cny# = :1 and paymentkey = :2 and payitemkey = :3",
                    GetMyCompany(), $pmtPair['PAYMENTKEY'], $pmtPair['PAYITEMKEY'])
            );
            if (!empty($res)) {
                $payments = array_merge($payments, $res);
            }
        }
        if (!empty($payments)) {
            import('ItemPaymentManager');
            $ipMgr = new ItemPaymentManager();
            $IETMgr = new IETManager();
            foreach ($payments as $keyPymt => $pmt) {
                // create IET records for the payment
                if (!$ipMgr->_HandleBSBL($pmt,$IETMgr)) {
                    XACT_ABORT($owner);
                    $gErr->addError('EE-0016', GetFL(), "Could not create Inter Entity Transaction records for inline credit item");
                    return false;
                }
            }
            if (!$IETMgr->createIETs(true)) {
                XACT_ABORT($owner);
                $gErr->addError('EE-0017', GetFL(), "Could not create Inter Entity Transaction records for inline credit item");
                return false;
            }
        }
    }

    // SET SUPPORTING DOCUMENTS MAP HERE
    if (!SetSupportingDocumentMap($id, $expense['SUPDOCID'], $expense['EXTERNALURL'], $expense['SUPDOCENTITY'])) {
        XACT_ABORT($owner);
        return false;
    }

    if (!SetNObjectByRef('prrecord', $expense, $id)) {
        XACT_ABORT($owner);
        $gErr->addError('EE-0018', GetFL(), "Cannot save prrecord record");
        return false;
    }

    // update the expense record implied location
    if ($ismega) {
        $imploc = GetContextLocation();
        $cny = GetMyCompany();
        if (!$imploc) {
            $impliedlocation = array();
            /** @noinspection PhpUnusedLocalVariableInspection */
            $impliedlocationkey = array();
            /** @noinspection PhpUnusedLocalVariableInspection */
            $args = array();
            foreach ($tmp['ITEMS'] as $olditem) {
                $impliedlocation[] = $olditem['LOCATION#'];
            }
            $impliedlocationkey = array_unique($impliedlocation);
            $varlist = VariablePlaceHolders(2, count($impliedlocationkey));
            $res = QueryResult(INTACCTarray_merge(array("select distinct entity# from v_locationent where cny# = :1 ".($varlist ? "and location# in ($varlist) " : ""), $cny), $impliedlocationkey));
            $imploc = ((count($res) == 1 && $res[0]['ENTITY#']) ? $res[0]['ENTITY#'] : '');
        }

        if ($imploc) {
            $args = array();
            $args[0] = "update prrecordmst set impliedlocation = :1 where cny# = :2 and record# = :3 ";
            $args[1] = $imploc;
            $args[2] = $cny;
            $args[3] = $tmp['RECORD#'];
            if (!ExecStmt($args)) {
                $gErr->addError('EE-0019', GetFL(),
                                "Error updating implied location of the expense report.");
                                eppp("Error updating implied location of the expense report.");
                                XACT_ABORT($owner);
                                return false;
            }
        }
    }

    // update the batch
    $prbatchkey = $expense['PRBATCHKEY'];
    $method = GetPRBatchAccountingMethod($expense['PRBATCHKEY']);

    // update the new prrecord's prbatch if needed
    if (!UpdatePRBatch($prbatchkey, $method)) {
        XACT_ABORT($owner);
        /** @noinspection PhpUndefinedVariableInspection */
        $gErr->addIAError('EE-0292', __FILE__ . ':' . __LINE__,
            "Unable to update PR batch ".$oldprrecord['PRBATCHKEY'],
            ['OLDPRRECORD_PRBATCHKEY' => $oldprrecord['PRBATCHKEY']]
        );
        return false;
    }

    // Update payment's PR Batches
    /** @noinspection PhpUndefinedVariableInspection */
    $lkeys = array_unique($lkeys);
    $requireReportView = (IsMultiEntityCompany() && !GetContextLocation());
    if ($requireReportView) {
        SetReportViewContext();
    }
    $payprbatchkeys = GetPaymentPRBatchesOfExpenseLines($lkeys);

    // update PR Batches
    foreach ( $payprbatchkeys as $payprbatchkey ) {
        $key = $payprbatchkey['PRBATCHKEY'];
        // no need to run updateprbatch again if reclassifying a
        // paying line in an invoice
        if ($key && $key != $prbatchkey) {
            /** @noinspection PhpUndefinedVariableInspection */
            if ( $requireReportView && $cnt > 0) {
                SetReportViewContext();
            }
            $ajkey = GetPRBatchJournal($key);
            $paymethod = GetPRBatchAccountingMethod($key);
            if (!UpdatePRBatch($key, $paymethod, $ajkey)) {
                XACT_ABORT($owner);
                $gErr->addIAError('EE-0292', __FILE__ . ':' . __LINE__,
                    "Unable to update PR batch ".$key,
                    ['OLDPRRECORD_PRBATCHKEY' => $key]
                );
                return false;
            }
        }
    }

    // unset to view entity transactions only if user is at root of
    // a MEGA shared company.
    /** @noinspection PhpUndefinedVariableInspection */
    if ($requiredReptViewContext) {
        SetTransactionViewContext();
    }

    // CREATE THE RECLASS HISTORY
    if ($submissionhistory) {
        if (!$objPh->add($submissionhistory)) {
            // add some error here?
            XACT_ABORT($owner);
            return false;
        }
    }

    XACT_COMMIT($owner);

    return true;
}

/**
 * IsExpenseReclassed
 *
 * @param array $oldexpense
 * @param array $expense
 *
 * @return bool
 */
function IsExpenseReclassed(&$oldexpense, &$expense) {      // TODO: remove & - only reference to this parameter reads a cell

    $expreclassed = false;
    $islabel = GetLabelStatus($oldexpense['RECORDTYPE']);

    // checking the lines
    foreach ($oldexpense['ITEMS'] as $itemlineno => $olditem) {

        $reclassed = false;
        $newitem = $expense['ITEMS'][$itemlineno];

        if ($islabel) {
            if ($olditem['ACCOUNTLABELKEY'] != $newitem['ACCOUNTLABELKEY']) {
                $reclassed = true;
            }
        } else {
            if ($olditem['ACCOUNTKEY'] != $newitem['ACCOUNTKEY']) {
                $reclassed = true;
            }
        }

        $old = ($olditem['DEPT#'] ?: '');
        $cur = ($newitem['DEPT#'] ?: '');
        if ($cur != $old) {
            $reclassed = true;
        }

        $old = ($olditem['LOCATION#'] ?: '');
        $cur = ($newitem['LOCATION#'] ?: '');
        if ($cur != $old) {
            $reclassed = true;
            $oldexpense['ITEMS'][$itemlineno]['locchanged'] = true;
        }
        $dimKeys = IADimensions::GetDimensionKeys();
        foreach ( $dimKeys as $dimid ) {
            if ( $olditem[$dimid] != $newitem[$dimid] ) {
                $reclassed = true;
                $oldexpense['ITEMS'][$itemlineno]['ietneeded'] = true;
                
            }
        }  

        if ($reclassed) {
            $oldexpense['ITEMS'][$itemlineno]['reclassed'] = true;
            $expreclassed = true;
        }

        // obtain payment info if the line has been paid
        $payinfo = GetEPPaymentInfo($olditem['RECORD#']);
        if ($payinfo) {
            foreach ($payinfo as $pkey => $pinfo) {
                $offsetrec = EEGetOffsetPREntryKey($pinfo['PAYITEMKEY']);
                $payinfo[$pkey]['PAYOFFSETKEY'] = $offsetrec['OFFSETKEY'];
                $oldexpense['ITEMS'][$itemlineno]['payinfo'] = $payinfo;
            }
        }

        if ($reclassed) {
            continue;
        }

        // if the line is not reclassed, see if the line is partially edited
        $old = ($olditem['DESCRIPTION'] ?: '');
        $cur = ($newitem['DESCRIPTION'] ?: '');
        if ($cur != $old) {
            $oldexpense['ITEMS'][$itemlineno]['mod'] = true;
            continue;
        }

        $old = ($olditem['DESCRIPTION2'] ?: '');
        $cur = ($newitem['DESCRIPTION2'] ?: '');
        if ($cur != $old) {
            $oldexpense['ITEMS'][$itemlineno]['mod'] = true;
            continue;
        }

        $old = ($olditem['ENTRY_DATE'] ?: '');
        $cur = ($newitem['ENTRY_DATE'] ?: '');
        if ($cur != $old) {
            $oldexpense['ITEMS'][$itemlineno]['mod'] = true;
            continue;
        }
    }

    return $expreclassed;
}

/**
 * GetEPPaymentInfo
 *
 * @param int|string $paiditemkey
 *
 * @return array|false|string[][]
 */
function GetEPPaymentInfo($paiditemkey) {
    if (empty($paiditemkey)) {
        return (array());
    }

    $args = array();
    $args[0] = "select prp.*, pr.recordtype, pr.financialentity from prentrypymtrecs prp, prrecordmst pr where ";
    $args[0] .= "prp.cny# = :1 and prp.paiditemkey = :2 and pr.cny# = prp.cny# and pr.record# = prp.paymentkey";
    $args[1] = GetMyCompany();
    $args[2] = $paiditemkey;

    return (QueryResult($args));
}

/**
 * GetEPExpenseInfo
 *
 * @param string $payitemkey
 *
 * @return array|false|string[][]
 */
function GetEPExpenseInfo($payitemkey) {
    if (empty($payitemkey) || !is_string($payitemkey)) {
        return (array());
    }

    $args = array();

    $stmt = "select pp.*, pay.recordtype RECORDTYPE ";
    $stmt .= " from prentrypymtrecs prp, prentry pp, prrecordmst pay";
    $stmt .= " where pp.cny# = :1 and prp.cny# = :1 and pay.cny# = :1";
    $stmt .= " and pp.recordkey = pay.record#";
    $stmt .= " and prp.payitemkey = :2";
    $stmt .= " and prp.paiditemkey = pp.record#";

    $args[0] = $stmt;
    $args[1] = GetMyCompany();
    $args[2] = $payitemkey;

    return (QueryResult($args));
}

/**
 * IsReimbursementReconciled
 *
 * @param string $paymentkey
 *
 * @return bool true on success, false on failure
 */
function IsReimbursementReconciled($paymentkey) {
    if ($paymentkey == '') {
        return false;
    }

    $res = QueryResult(array("select cleared CLEARED from prrecordmst where cny# = :1 and record# = :2 ", GetMyCompany(), $paymentkey));
    if ($res[0]['CLEARED'] == 'T') {
        return true;
    }
    return false;
}

/**
 * IsExpenseReportValidForReclass
 *
 * @param array $expense
 * @param array $origexpense
 *
 * @return bool true on success, false on failure
 */
function IsExpenseReportValidForReclass($expense, $origexpense) {

    global $gErr;
    $errcnt = 0;

    if (! in_array($expense['RECORDTYPE'], array('ei' , 'ea'))) {
        $gErr->addError('EE-0020', GetFL(),
                        "Wrong reclassification document type");
        eppp('error: wrong reclassification document type');
        $errcnt++;
    }

    // no reclass on reversed document
        if ($expense['STATE'] == 'V') {
        $gErr->addError('EE-0021', GetFL(),
                        "Cannot reclassify a reversed document");
        eppp('error: reclassing a reversed document');
        $errcnt++;
    }

    if (!$expense['ITEMS']) {
        $gErr->addError('EE-0022', GetFL(),
                        "No item specified for the reclassifying record");
        eppp('error: No item specified for the reclassifying record');
        $errcnt++;
    }

    if (count($expense['ITEMS']) != count($origexpense['ITEMS'])) {
        $gErr->addError('EE-0023', GetFL(),
                        "Number of lines in the reclassifying record differs.",
                        "", "You cannot change the number of lines during reclassification.");
        eppp('error: Number of lines in the reclassifying record differs.');
        $errcnt++;
    }

    $testloc = (IsMultiEntityCompany() && !GetContextLocation() && !GetRootContextLocation());
    if ($testloc) {
        import('IETManager');
        $ietMgr = new IETManager();
        global $locmaprec;
    }
    //Apply Bank restrictions On Reclassfiction
    $mvsubscribed = false;
    if(IsMultiVisibilitySubscribed('checkingaccount') || IsMultiVisibilitySubscribed('savingsaccount')){
        $mvsubscribed = true;
    }

    foreach ($expense['ITEMS'] as $line => $rec) {
        if (!$rec['ACCOUNTLABEL'] && !$rec['GLACCOUNTNO']) {
            $linePlus = ($line+1);
            $gErr->addIAError('EE-0293', GetFL(),
                "No account specified for reclassification for line ".$linePlus.".",
                ['LINE_PLUS' => $linePlus]
            );

            eppp('error: No account specified for reclassification for line'.($line+1));
            $errcnt++;
        }
        if ($testloc) {
            $location = $rec['LOCATION#'];
            /** @noinspection PhpUndefinedVariableInspection */
            $locname = $locmaprec[$rec['LOCATION#']];
            /** @noinspection PhpUndefinedVariableInspection */
            $entityArr = $ietMgr->GetEntityLocationRelationInfo($location);
            if ($entityArr == NULL || (!isset($entityArr['ENTITY#']) || $entityArr['ENTITY#'] == '')) {
                $loctext = "Location";
                $errorCode_1 = '';
                $placeHolder_1 = [];
                if (!isset($location) || $location == '') {
                    $text = "Use of empty $loctext is invalid.";
                    $errorCode_1 = 'EE-0294';
                    $placeHolder_1 = [];
                } else {
                    $text = "$loctext ID ($locname) is invalid or does not belong to an entity.";
                    $errorCode_1 = 'EE-0295';
                    $placeHolder_1 = ['LOCNAME' => $locname];
                }
                $gErr->addIAError($errorCode_1, __FILE__ . '.' . __LINE__,
                    $text . " A valid Multi-Entity Location must be specified",
                    $placeHolder_1
                );
                $errcnt++;
            }
        }
        // skip if the line is not reclassed
        if (!$origexpense['ITEMS'][$line]['reclassed']) {
            continue;
        }
        //If the line item is marked as billable and we have generated the Invoice, we dont allow to reclassify.
        if ( IsBilled($rec['RECORD#']) ) {
            /** @noinspection PhpUndefinedVariableInspection */
            $lineOneAdd = ($line+1);
            $gErr->addIAError('EE-0298', "$source",
                "Line ".
                $lineOneAdd." is already billed. You cannot reclassify this".
                " line",
                ['LINE_PLUS_1' => $lineOneAdd]
            );
            $errcnt++;
        }

        //Gather All Line Items for MultiVisbility Checking
        if($mvsubscribed){
            $paymentinfo = GetAssociatedPaymentLines($rec['RECORD#']);
            foreach ($paymentinfo as $payment) {
                /** @noinspection PhpUnusedLocalVariableInspection */
                $paykey = $payment['RECORD#'];
                $payrecordtype = $payment['RECORDTYPE'];
                //Build an array of payed line items with Bank Account for Checking Bank Restrictions
                if($payment['FINANCIALENTITY'] != '' && $payment['STATE'] != 'V'){
                    $bankRes[] = array('RECORDID' => $expense['RECORDID'], 'FINANCIALENTITY' => $payment['FINANCIALENTITY'], 'RECORDTYPE' => $payrecordtype, 'AMOUNT' => $payment['AMOUNT'], 'LOCATION#' => $rec['LOCATION#']);
                }
            }
        }
    }

    if($mvsubscribed && !empty($bankRes)){
        if(!ValidateBRReclass($bankRes)){
            $errcnt++;
        }
    }

    if ($errcnt > 0) {
        return false;
    }

    return (IsExpenseReportValid($expense));
}

/**
 * EEGetOffsetPREntryKey
 *
 * @param string $prentryreckey
 *
 * @return array
 */
function EEGetOffsetPREntryKey($prentryreckey) {
    $wheres = "prentryoffset.prentrykey = ".$prentryreckey;
    $res = GetNObjects('prentryoffset', '*', $wheres);
    return ($res[0]);
}

/**
 * GetExpenseReportLineOffset
 *
 * @param string $linekey
 *
 * @return string
 */
function GetExpenseReportLineOffset($linekey) {
    global $gExpense;
    $offsetkey = $gExpense['OFFSET'][$linekey];
    if ($offsetkey == '') {
        // no offset info for this item -> looks like this is the first-time lookup of the offset cache
        // for this expense report so we'll add offsets for all items while we're at it
        foreach ($gExpense['ITEMS'] as $item) {
            $gExpense['OFFSET'][$item['RECORD#']] = $item['OFFSETKEY'];
            if (!$offsetkey && $item['RECORD#'] == $linekey) {
                $offsetkey = $item['OFFSETKEY'];
            }
        }
    }
    return $offsetkey;
}

/**
 * DeleteExpenseReportIETs
 *
 * @param string $reckey
 * @param string $linekey
 *
 * @return bool true on success, false on failure
 */
function DeleteExpenseReportIETs($reckey, $linekey) {

    if ($reckey == '' || $linekey == '') {
        return false;
    }

    import('IETManager');
    $IETMgr = new IETManager();

    $source = "backend_expense:DeleteExpenseReportIETs";

    $args = array();
    $args[0] = "select recordkey, paiditemkey from prentrypymtrecs where cny# = :1 and paymentkey = :2 and payitemkey = :3 ";
    $args[1] = GetMyCompany();
    $args[2] = $reckey;
    $args[3] = $linekey;

    $res = QueryResult($args);
    if (count($res) > 0) {

        XACT_BEGIN($source);

        foreach($res as $rec) {
            $offsetkey = GetExpenseReportLineOffset($rec['PAIDITEMKEY']);
            if (!$IETMgr->DeleteIETByLine($rec['RECORDKEY'], $offsetkey, true) ){
                XACT_ABORT($source);
                return false;
            }
        }

        XACT_COMMIT($source);
    }

    return true;
}

/**
 * DeleteEEIETsForReclass
 *
 * @param array $payment
 *
 * @return bool true on success, false on failure
 */
function DeleteEEIETsForReclass($payment) {

    $source = "backend_expense:DeleteIETsForReclass";

    import('IETManager');
    $IETMgr = new IETManager();

    $paymentkey = $payment['PAYMENTKEY'];
    $payitemkey = $payment['PAYITEMKEY'];
    $paidreckey = $payment['RECORDKEY'];
    $paiditemkey = $payment['PAIDITEMKEY'];
    $payoffsetkey = $payment['PAYOFFSETKEY'];

    XACT_BEGIN($source);

    // delete the IETs of the payment line
    if ($payoffsetkey && !$IETMgr->DeleteIETByLine($paymentkey, $payoffsetkey, true)) {
        XACT_ABORT($source);
        return false;
    }

    // if reclassifying the inline credit line, need
    // to delete the IETs of the paid line as well
    if (!DeleteExpenseReportIETs($paymentkey, $payitemkey)) {
        XACT_ABORT($source);
        return false;
    }

    $offsetrec = EEGetOffsetPREntryKey($paiditemkey);
    $offsetkey = $offsetrec['OFFSETKEY'];

    if ($offsetkey && !$IETMgr->DeleteIETByLine($paidreckey, $payoffsetkey, true)) {
        XACT_ABORT($source);
        return false;
    }

    XACT_COMMIT($source);

    return true;
}


/**
 * @param string $acctlabelkey
 * @param string $expensePaymentTypeKey
 *
 * @return string
 */
function GetExpenseReportOffsetAcct($acctlabelkey, $expensePaymentTypeKey) {
    $islabel = GetLabelStatus('ee');
    $defoffset = GetDefaultOsAcct('ei');

    $where = " acct_no = '".$defoffset."'";
    $result = GetNObjects('glaccount', 'RECORD#', $where);
    $defoffset = $result[0]['RECORD#'];

    // if ExpensePaymentType has an offset account, it has priority
    $offset = getExpensePaymentTypeOffsetAccount($expensePaymentTypeKey);

    // first priority is the offset account of the expense payment type, if any
    if (!isset($offset) || $offset == '') {
        // then the offset account of the expense type, if in use
        if (!$islabel) {
            $offset = $defoffset;
        } else {
            $offset = GetLabelOffsetAcct($acctlabelkey);
            // lastly, the default offset account
            if (!$offset) {
                $offset = $defoffset;
            }
        }
    }

    return $offset;
}


/**
 * EEOffsetExists
 *
 * @param array $offsetitem
 * @param array $offsetrec
 *
 * @return bool true on success, false on failure
 */
function EEOffsetExists($offsetitem, &$offsetrec) {
    $wheres = "prentry.accountkey = ".$offsetitem['ACCOUNTKEY'];
    if ($offsetitem['DEPT#'] != '') {
        $wheres .= " and prentry.dept# = ".$offsetitem['DEPT#'];
    } else {
        $wheres .= " and prentry.dept# is null";
    }
    if ($offsetitem['LOCATION#'] != '') {
        $wheres .= " and prentry.location# = ".$offsetitem['LOCATION#'];
    } else {
        $wheres .= " and prentry.location# is null";
    }
    AddDimOffsetWhereClause($offsetitem, $wheres);

    $wheres .= " and prentry.recordkey = ".$offsetitem['RECORDKEY'];
    $wheres .= " and prentry.lineitem = 'F'";
    $res = GetNObjects('prentry', 'prentry.*', $wheres);
    if (count($res) != 1) {
        return false;
    }
    $offsetrec = $res[0];
    return true;
}


/**
 * Check if an offset record is used for only one expense item line
 *
 * @param int $offsetkey record# of the prentry offset record to check
 *
 * @return bool true if it's a single-line offset, otherwise false
 */
function isSingleEntryOffset($offsetkey)
{
    $stmt = "select count(*) as entries from prentryoffset where cny# = :1 and offsetkey = :2";
    $qry = array($stmt, GetMyCompany(), $offsetkey);
    $res = QueryResult($qry);
    if ($res[0]['ENTRIES'] == 1) {
        return true;
    }
    return false;
}


/**
 * HandleEEOffsetForReclass
 *
 * @param array  $oldlineitem
 * @param array  $newlineitem
 * @param string $curoffsetacct
 *
 * @return bool true on success, false on failure
 */
function HandleEEOffsetForReclass($oldlineitem, &$newlineitem, &$curoffsetacct) {
    global $gErr, $acctmapno;

    $source = "backend_expense:HandleEEOffsetForReclass";
    $locText = I18N::getSingleToken('IA.LOCATION');
    $deptText = I18N::getSingleToken('IA.DEPARTMENT');
    $cny = GetMyCompany();
    $preorec = array();

    $linekey = $oldlineitem['RECORD#'];
    $oldoffsetkey = $oldlineitem['OFFSETKEY'];
    if (!GetNObject('prentry', $oldoffsetkey, $offsetrec)) {
        $gErr->addError('EE-0024', GetFL(),
                        "Cannot find offset prentry record");
        return false;
    }

    $oldoffsetacct = $offsetrec['ACCOUNTKEY'];
    $curoffsetacct = GetExpenseReportOffsetAcct($newlineitem['ACCOUNTLABELKEY'], $newlineitem['EXPPMTTYPEKEY']);

    $oldloc = $oldlineitem['LOCATION#'];
    $curloc = $newlineitem['LOCATION#'];
    $olddept = $oldlineitem['DEPT#'];
    $curdept = $newlineitem['DEPT#'];

    $rec = array();
    //Dimension specific
    $IsDimenReclassified = IsDimensionReclassified($newlineitem, $oldlineitem);
    // we need to set dimension fields for reclass
    IADimensions::CopyIADimensionValues($newlineitem, $rec, false, true);

    if ($oldoffsetacct != $curoffsetacct || $oldloc != $curloc || $olddept != $curdept || $IsDimenReclassified) {
        if ($curloc == '' && $acctmapno[$newlineitem['ACCOUNTKEY']]['REQUIRELOC'] == 'T') {
            $oldLineItemNO = ($oldlineitem['LINENO']+1);
            $gErr->addIAError('EE-0296', GetFL(),
                "In line ".$oldLineItemNO.
                " location is required for ".
                " offset account ".$newlineitem['ACCOUNTKEY'],
                ['OLD_LINE_ITEM' => $oldLineItemNO, 'NEWLINEITEM_ACCOUNTKEY' => $newlineitem['ACCOUNTKEY']]
            );

                        eppp("In line ".($oldlineitem['LINENO']+1)." ".
                $locText." is required ".
                                "for offset account ".$rec['ACCOUNTKEY']);
                        return false;
        }

        if ($curdept == '' && $acctmapno[$newlineitem['ACCOUNTKEY']]['REQUIREDEPT'] == 'T') {
            $oldLineItemNO_1 = ($oldlineitem['LINENO']+1);
            $gErr->addIAError('EE-0297', GetFL(),
                "In line ".$oldLineItemNO_1.
                " department is required for ".
                " offset account ".$newlineitem['ACCOUNTKEY'],
                ['OLD_LINE_ITEM' => $oldLineItemNO_1, 'NEWLINEITEM_ACCOUNTKEY' => $newlineitem['ACCOUNTKEY']]
            );

                        eppp("In line ".($oldlineitem['LINENO']+1)." ".
                $deptText." is required ".
                                "for offset account ".$rec['ACCOUNTKEY']);
                        return false;
        }

        XACT_BEGIN($source);

        $rec['ACCOUNTKEY'] = $curoffsetacct;
        $rec['DEPT#'] = $curdept;
        $rec['LOCATION#'] = $curloc;
        $rec['RECORDKEY'] = $oldlineitem['RECORDKEY'];
        if ($curloc != '') {
            $rec['BASELOCATION'] = $curloc;
        }

        $singleEntryOffset = isSingleEntryOffset($oldoffsetkey);
        if (EEOffsetExists($rec, $newoffsetrec)) {
            // there's already an offset record for the dimensions/settings of the "new" item ->
            // transfer the item's amount to the existing offset ($newoffsetrec) and subtract from the old one:
            $preorec['AMOUNT'] = ibcadd($newoffsetrec['AMOUNT'], $newlineitem['AMOUNT']);
            //As of now we are allowing only base currency transactions to reclassify.
            $preorec['TRX_AMOUNT'] = $preorec['AMOUNT'];

            // update prentryoffset (found line)
            $wheres = " and offsetkey = ".$newoffsetrec['RECORD#'];
            if (!SetNObjects('prentryoffset', $preorec, $wheres)) {
                XACT_ABORT($source);
                $gErr->addError('EE-0025', GetFL(),
                                "Cannot save prentryoffset record");
                return false;
            }

            $preorec['OFFSETKEY'] = $newoffsetrec['RECORD#'];

            // update prentryoffset (original line)
            $wheres = " and prentrykey = ".$linekey;
            if (!SetNObjects('prentryoffset', $preorec, $wheres)) {
                XACT_ABORT($source);
                $gErr->addError('EE-0026', GetFL(),
                                "Cannot save prentryoffset record");
                                return false;
            }

            // update prentry (offset line)
            $rec['AMOUNT'] = $preorec['AMOUNT'];
            //As of now we are allowing only base currency transactions to reclassify.
            $rec['TRX_AMOUNT'] = $preorec['AMOUNT'];
            $wheres = " and record# = ".$preorec['OFFSETKEY'];
            if (!SetNObjects('prentry', $rec, $wheres)) {
                XACT_ABORT($source);
                $gErr->addError('EE-0027', GetFL(),
                                "Cannot save prentryoffset record");
                                return false;
            }

            // update prentry (original line)
            if ($singleEntryOffset) {
                // the original offset was a single line offset -> delete it
                if (!ExecStmt(array("delete from prentrymst ".
                        "where cny# = :1 and record# = :2 ",
                        $cny, $oldoffsetkey))) {
                    XACT_ABORT($source);
                    $gErr->addError('EE-0028', GetFL(),
                                    "Cannot delete prentry record");
                    return false;
                }
            } else {
                $offsetrec['AMOUNT'] = ibcsub($offsetrec['AMOUNT'],	$newlineitem['AMOUNT']);
                //As of now we are allowing only base currency transactions to reclassify.
                $offsetrec['TRX_AMOUNT'] = $offsetrec['AMOUNT'];

                if (!ExecStmt(array("update prentrymst set amount = :1, trx_amount = :2".
                        " where cny# = :3 and record# = :4 ",
                        $offsetrec['AMOUNT'], $offsetrec['TRX_AMOUNT'], $cny,
                        $oldoffsetkey))) {
                    XACT_ABORT($source);
                    $gErr->addError('EE-0029', GetFL(),
                                    "Cannot save prentry record");
                    return false;
                }
            }
        } elseif ($singleEntryOffset) {
            // the original offset is a single line offset -> just update it with new values:
            $preorec['ACCOUNTKEY'] = $curoffsetacct;
            $preorec['DEPT#'] = $curdept;
            $preorec['LOCATION#'] = $curloc;
            if ($curloc != '') {
                $preorec['BASELOCATION'] = $curloc;
            }
            $wheres = " and record# = ".$oldoffsetkey;

            // we need to set dimension fields for reclass
            IADimensions::CopyIADimensionValues($newlineitem, $preorec, false, true);

            if (!SetNObjects('prentry', $preorec, $wheres)) {
                XACT_ABORT($source);
                $gErr->addError('EE-0030', GetFL(),
                                "Cannot save prentry record");
                return false;
            }
        } else {
            // create new offset for this item and adjust the old offset record
            $offsetitem = $offsetrec;
            $offsetitem['ACCOUNTKEY'] = $curoffsetacct;
            $offsetitem['DEPT#'] = $curdept;
            $offsetitem['LOCATION#'] = $curloc;
            if ($curloc != '') {
                $offsetitem['BASELOCATION'] = $curloc;
            }
            $offsetitem['AMOUNT'] = $newlineitem['AMOUNT'];
            $offsetitem['NR_AMOUNT'] = $newlineitem['NR_AMOUNT'];
            $offsetitem['NONREIMBURSABLE'] = $newlineitem['NONREIMBURSABLE'];

           //As of now we are allowing only base currency transactions to reclassify.
            $offsetitem['TRX_AMOUNT'] = $newlineitem['AMOUNT'];
            $offsetitem['NR_TRX_AMOUNT'] = $newlineitem['NR_AMOUNT'];
            $offsetitem['RECORDKEY'] = $offsetrec['RECORDKEY'];
            unset($offsetitem['RECORD#']);
            unset($offsetitem['GLENTRYKEY']);
            $dimKeys = IADimensions::GetDimensionKeys();
            foreach ( $dimKeys as $dimid ) {
                unset($offsetitem[$dimid]);
            }

            // we need to set dimension fields for reclass
            IADimensions::CopyIADimensionValues($newlineitem, $offsetitem, false, true);

            if (!CreateNObject('prentry', $offsetitem)) {
                XACT_ABORT($source);
                $gErr->addError('EE-0031', GetFL(),
                                "Cannot create prentry record");
                return false;
            }

            // update the prentryoffset table
            $preorec['AMOUNT'] = $newlineitem['AMOUNT'] + $newlineitem['NR_AMOUNT'];
            // As of now we are allowing only base currency transactions to reclassify.
            $preorec['TRX_AMOUNT'] = $newlineitem['AMOUNT'];
            $preorec['OFFSETKEY'] = $offsetitem['RECORD#'];
            $wheres = " and prentrykey = ".$oldlineitem['RECORD#'];
            if (!SetNObjects('prentryoffset', $preorec, $wheres)) {
                XACT_ABORT($source);
                $gErr->addError('EE-0032', GetFL(),
                                "Cannot save prentryoffset record");
                return false;
            }

            // subtract the amount from the original offset line in prentry table
            $offsetrec['AMOUNT'] = ibcsub($offsetrec['AMOUNT'], $newlineitem['AMOUNT']);
            $offsetrec['NR_AMOUNT'] = ibcsub($offsetrec['NR_AMOUNT'], $newlineitem['NR_AMOUNT']);

            // As of now we are allowing only base currency transactions to reclassify.
            $offsetrec['TRX_AMOUNT'] = $offsetrec['AMOUNT'];
            $offsetrec['NR_TRX_AMOUNT'] = $offsetrec['NR_AMOUNT'];
            if (!ExecStmt(array("update prentrymst set amount = :1, trx_amount = :2, nr_amount = :3, nr_trx_amount = :4 " .
                                "where cny# = :5 and record# = :6 ",
                    $offsetrec['AMOUNT'], $offsetrec['TRX_AMOUNT'],
                    $offsetrec['NR_AMOUNT'], $offsetrec['NR_TRX_AMOUNT'],
                    $cny, $oldoffsetkey))) {
                XACT_ABORT($source);
                $gErr->addError('EE-0033', GetFL(),
                                "Cannot save prentry record");
                return false;
            }

            // subtract the amount from the original offset line in prentryoffset table
            if (!ExecStmt(array("update prentryoffset ".
                    "set amount = :1 , trx_amount = :2 where cny# = :3 ".
                    "and offsetkey = :4 ",
                    $offsetrec['AMOUNT'],$offsetrec['AMOUNT'], $cny,
                    $oldoffsetkey))) {
                XACT_ABORT($source);
                $gErr->addError('EE-0034', GetFL(),
                                "Cannot save prentryoffset record");
                return false;
            }
        }

        XACT_COMMIT($source);
    }

    return true;
}

/**
 * HandleEEPaymentForReclass
 *
 * @param array    $olditem
 * @param array    $newitem
 * @param array    $payrec
 * @param string   $offsetaccount
 * @param string[] $lkeys
 *
 * @return bool
 */
function HandleEEPaymentForReclass($olditem, $newitem, $payrec, $offsetaccount, &$lkeys)
{
    global $gErr;

    $source = "backend_expense:HandleEEPaymentForReclass";
    $rec = array();
    /** @noinspection PhpUnusedLocalVariableInspection */
    $wheres = '';

    // update the account/dept/location/baseloc if needed
    $acctfrom = $olditem['ACCOUNTKEY'];
    $acctto = $offsetaccount;
    $deptfrom = ($olditem['DEPT#'] ?: '');
    $deptto = ($newitem['DEPT#'] ?: '');
    $locfrom = ($olditem['LOCATION#'] ?: '');
    $locto = ($newitem['LOCATION#'] ?: '');
    $baselocfrom = $olditem['BASELOCATION'];
    $baselocto = $locto;
    $payitemkey = $payrec['PAYITEMKEY'];

    XACT_BEGIN($source);

    if ($acctfrom != $acctto) {
        $rec['ACCOUNTKEY'] = $acctto;
    }
    if ($deptfrom != $deptto) {
        $rec['DEPT#'] = $deptto;
    }
    if ($locfrom != $locto) {
        $rec['LOCATION#'] = $locto;
    }
    if ($baselocfrom != $baselocto) {
        $rec['BASELOCATION'] = $baselocto;
    }
    $dimKeys = IADimensions::GetDimensionKeys();
    foreach ( $dimKeys as $dimid ) {
        if ( $olditem[$dimid] != $newitem[$dimid] ) {

            $rec[$dimid] = $newitem[$dimid];
        }
    }
    if (count($rec) > 0) {
        $wheres = " and record# = ".$payitemkey;
        if (!SetNObjects('prentry', $rec, $wheres)) {
            XACT_ABORT($source);
            $gErr->addError('EE-0035', GetFL(),
                            "Cannot save payment's prentry record");
            return false;
        }
        $lkeys[] = $payitemkey;
    }

    // update the offset of the payment
    $offsetkey = $payrec['PAYOFFSETKEY'];
    if (!$offsetkey) {
        XACT_ABORT($source);
        epp("cannot find offset key");
        return false;
    }
    unset($rec['ACCOUNTKEY']);
    
    if (!IETManager::isIETDisabled('DISABLEIETFINACCT', $payrec['FINANCIALENTITY'])) {
        unset($rec['BASELOCATION']);
    }
    if (count($rec) > 0) {
        $wheres = " and record# = ".$offsetkey;
        if (!SetNObjects('prentry', $rec, $wheres)) {
            XACT_ABORT($source);
            $gErr->addError('EE-0036', GetFL(),
                            "Cannot save payment prentry's offset record");
            return false;
        }
    }

    XACT_COMMIT($source);

    return true;
}

/**
 * TrimEECouplets
 *
 * @param array $offsetlist
 * @param array $paylist
 * @param array $couplets
 *
 * @return bool true on succes, false on failure
 */
function TrimEECouplets($offsetlist, $paylist, &$couplets) {
    $output = array();
    foreach ( $couplets as $rec) {
        $isl_trimcouplets = array();
        $payoffsetkey = $rec['OFFSET']['RECORD#'];
        if (in_array($payoffsetkey, $offsetlist)) {
            $isl_trimcouplets['OFFSET'] = $rec['OFFSET'];
        }
        foreach ( $rec['ITEMS'] as $itemrec) {
            $payitemkey = $itemrec['RECORD#'];
            if (in_array($payitemkey, $paylist)) {
                $isl_trimcouplets['ITEMS'][] = $itemrec;
            }
        }
        $output[] = $isl_trimcouplets;
    }
    $couplets = $output;
    return true;
}

/**
 * ProcessEEIETsForReclass
 *
 * @param array $lineitem
 * @param array $payinfo
 *
 * @return bool true on success, false on failure
 */
function ProcessEEIETsForReclass($lineitem, $payinfo)
{
    global $gErr;
    $source = "backend_expense::ProcessEEIETsForReclass";

    $ismega = IsMultiEntityCompany();
    $conloc = GetContextLocation();
    $locchanged = $lineitem['locchanged'];

    if ($ismega && !$conloc && !$locchanged) {
        SetReportViewContext();
    }

    // get prrecord (even for inline credit case)
    $paymentkey = $payinfo['PAYMENTKEY'];
    if (!GetPRRecord($paymentkey, $parentrecord, 0)) {
        $msg = " Please check the ownership of the payment.";
        $errorCode = 'EE-0037';
        if ($ismega && !$conloc && !$locchanged) {
            $msg .= " For a payment line that is paid at the ".
                "entity, you cannot change the location of ".
                "the line.";
            $errorCode = 'EE-0150';
        }
        $gErr->addError($errorCode, GetFL(), "Cannot retrieve the payment ", "", $msg);
        epp("Cannot retrieve parent payment record ".$paymentkey);
                return false;
    }

    $paylist = array();
    $offsetlist = array();
    $paymentkey = $parentrecord['RECORD#'];
    // delete all cached prentries - there may have been changes by HandleEEOffsetForReclass and/or HandleEEPaymentForReclass:
    global $gPRLines;
    foreach ( $gPRLines as $key => $record) {
        unset($gPRLines[$key][$paymentkey]);
    }
    
    if ($payinfo['RECORDTYPE'] == 'ep'
        || $payinfo['RECORDTYPE'] == 'eo'
        || $payinfo['RECORDTYPE'] == 'er'
    ) {
        return true;
    }
    
    // now get new item/offset couplets:
    if (!GetCouplets($paymentkey, $couplets)) {
            epp("Unable to get couplets");
            $gErr->addError('EE-0038', __FILE__ . '.' . __LINE__,
                            "Unable to get couplets");
            return false;
    }
    $paylist[] = $payinfo['PAYITEMKEY'];
    $offsetlist[] = $payinfo['PAYOFFSETKEY'];
    TrimEECouplets($offsetlist, $paylist, $couplets);

    XACT_BEGIN($source);

    import('IETManager');
    $IETMgr = new IETManager();
    foreach (array_keys($couplets) as $ckey) {
        $ietitems = &$couplets[$ckey]['ITEMS'];
        $ietoffset = &$couplets[$ckey]['OFFSET'];
        if (!$IETMgr->HandleIET($parentrecord, $ietitems[0], $ietoffset)) {
            XACT_ABORT($source);
            epp("Unable to perform Inter Entity Transfer between Entities");
            $gErr->addError('EE-0039', GetFL(), "Unable to perform " .
                                                   "Inter Entity Transfer between Entities");
            return false;
        }
    }
    
    // Create PE/RE type PRRecords
    if (!$IETMgr->createIETs(true)) {
        XACT_ABORT($source);
        $gErr->addError('EE-0040', GetFL(),
                        "Could not create Inter Entity Transaction records");
        epp("Could not create Inter Entity Transaction records");
        return false;
    }

    if ($ismega && !$conloc && !$locchanged) {
        SetTransactionViewContext();
    }

    XACT_COMMIT($source);

    return true;
}

/**
 * GetPaymentPRBatchesOfExpenseLines
 *
 * @param array $lkeys
 *
 * @return array|false|string[][]
 */
function GetPaymentPRBatchesOfExpenseLines($lkeys) {
    if (empty($lkeys) || !is_array($lkeys)) {
        return array();
    }

    /** @noinspection PhpUndefinedVariableInspection */
    $keylist .= "(" . join(',', $lkeys) . ")";

    $stmt = "select distinct pp.prbatchkey ".
        "from prrecord pp, prentry pre ".
        "where pp.cny# = :1 and pre.cny# = :1 ".
        "and pp.record# = pre.recordkey ".
        "and pre.record# in ".$keylist;

    $res = QueryResult(array($stmt, GetMyCompany()));

    return ($res);

}

/**
 * GetEmployeeDefaultLocDept
 *
 * @param string $empid
 * @param string $columnName
 * @param string $tableName
 *
 * @return string
 */
function GetEmployeeDefaultLocDept($empid, $columnName, $tableName) {
    $query = "select ".$columnName."_NO from ".$tableName." locdept, employee emp where locdept.cny# = :2 and emp.cny# = :2 and locdept.cny# = emp.cny#  and emp.employeeid= :1 and emp.".$columnName."KEY = locdept.record#";
    $res = QueryResult(array($query, $empid, GetMyCompany()));
    if(count($res) && isset($res[0][$columnName."_NO"])) {
        return $res[0][$columnName."_NO"];
    }else {
        return '';
    }
}

/**
* This function prepares the where clause.
*
* @param array  $offsetitem
* @param string $wheres where clause
* 
*/
function AddDimOffsetWhereClause($offsetitem, &$wheres)
{
    $dimensionKeys = IADimensions::GetDimensionKeys();
    foreach( $dimensionKeys as $dim )
    {
        if ( $offsetitem[$dim] ) {
            $wheres .= " and prentry." .$dim ." = ".$offsetitem[$dim];
        } else {
            $wheres .= " and prentry." . $dim ." is null";
        }
    }
}



