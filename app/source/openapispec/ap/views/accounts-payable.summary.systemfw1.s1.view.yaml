key: accounts-payable/summary::systemAccounts-payable/summaryFW1
id: systemAccounts-payable/summaryFW1
object: accounts-payable/summary
name: All
description: Specifies all active accounts-payable/summarys
query:
  object: accounts-payable/summary
  fields:
    - id
    - name
    - summaryType
    - summaryCreationType
    - totalAmount
    - glPostingDate
    - state
    - status
  filters:
    - $eq:
        status: active
  orderBy:
    - id: desc
contexts:
  - __default