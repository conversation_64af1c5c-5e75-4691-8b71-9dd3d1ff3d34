<?
global $gRecordNoFieldInfo;
$kSchemas['customertotal'] = array(
    'children' => array(
        'locationentity' => array(
            'fkey' => 'locationkey',
            'invfkey' => 'record#',
            'table' => 'location',
            'entity' => 'locationentity',
            'join' => 'inner'
        ),
        'customer' => array(
            'fkey' => 'customerkey',
            'invfkey' => 'record#',
            'table' => 'customermst',
            'entity' => 'customer',
            'join' => 'inner',
        ),
    ),

    'object' => array(
        'RECORDNO',
        'ENTITY',
        'LOCATIONKEY',
        'ENTITYID',
        'ENTITYNAME',
        'CURRENCY',
        'CUSTOMERID',
        'CUSTOMERNAME',
        'TOTALDUE',
        'CUSTOMERKEY'
    ),


    'schema' => array(
        'RECORDNO' => 'record#',
        'ENTITY' => 'customer.entity',
        'LOCATIONKEY' => 'locationkey',
        'TOTALDUE' => 'totaldue',
        'locationentity' => array(
            'LOCATION_NO' => 'locationentity.location_no',
        ),
        'ENTITYID' => 'locationentity.location_no',
        'ENTITYNAME' => 'locationentity.name',
        'CURRENCY' => 'locationentity.currency',
        'CUSTOMER' => array(
            'CUSTOMERID' => 'customer.customerid',
        ),
        'CUSTOMERID' => 'customer.customerid',
        'CUSTOMERNAME' => 'customer.name',
        'CUSTOMERKEY' => 'customerkey'
    ),


    'fieldinfo' => array(
        array(
            'fullname' => 'IA.ENTITY',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 200
            ),
            'hidden' => false,
            'required' => false,
            'desc' => 'IA.ENTITY',
            'path' => 'ENTITY'
        ),
        array(
            'fullname' => 'IA.ENTITY_ID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'locationentity',
            ),
            'desc' => 'IA.ENTITY_ID',
            'path' => 'ENTITYID',
            'renameable' => true,
        ),
        array(
            'fullname' => 'IA.ENTITY_NAME',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'locationentity',
            ),
            'desc' => 'IA.ENTITY_NAME',
            'path' => 'ENTITYNAME',
        ),
        array(
            'fullname' => 'IA.ENTITY_BASE_CURRENCY',
            'desc' => 'IA.ENTITY_BASE_CURRENCY',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'size' => 3,
            ),
            'hidden' => true,
            'readonly' => true,
            'path' => 'CURRENCY',
        ),
        array(
            'fullname' => 'IA.CUSTOMER_ID',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'desc' => 'IA.UNIQUE_ID_OF_CUSTOMER',
            'path' => 'CUSTOMERID',
            'renameable' => true,
        ),
        array(
            'fullname' => 'IA.CUSTOMER_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
            ),
            'desc' => 'IA.NAME_OF_CUSTOMER',
            'path' => 'CUSTOMERNAME',
            'renameable' => true,
        ),
        array(
            'fullname' => 'IA.TOTAL_DUE',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'currency',
            ),
            'desc' => 'IA.TOTAL_DUE',
            'path' => 'TOTALDUE'
        ),
        [
            'fullname' => 'IA.CUSTOMERKEY',
            'type' => [
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 15
            ],
            'path' => 'CUSTOMERKEY'
        ],
        $gRecordNoFieldInfo,
    ),

    'table' => 'v_entitytotal',
    'updatetable' => 'entitytotal',
    'printas' => 'IA.CUSTOMER_TOTALS',
    'pluralprintas' => 'IA.CUSTOMER_TOTALS',
    'vid' => 'CUSTOMERKEY',
    'module' => 'ar',
    'nosysview' => true,
    
);

