name: New Commit to PRBranch
on:
  pull_request:
    types: [synchronize]
     
jobs:
# Call <PERSON> Jenkins Job  
  Jenkinsbuild:
    if: contains( github.event.pull_request.labels.*.name, 'Approved') && github.actor != 'github-actions' && github.actor != 'jenkins-sageintacct' && github.event.pull_request.draft == false
    runs-on: self-hosted  
    steps:
      - name: Call the Jenkins PR Build
        if: "!contains( github.event.pull_request.labels.*.name, 'dbmigration_pending')"
        id: Jenkinsjob
        run: |
              #!/bin/bash

               echo ${{ github.event.after }}
           
              COMMIT_MESSAGE=$(curl  -s -X  GET -H "Accept: application/vnd.github.v3+json" -H 'Authorization: token ${{ secrets.GITHUBS_TOKEN }}'  https://api.github.com/repos/Intacct/ia-app/commits/${{ github.event.after }}| jq -r '.commit.message')
              
              echo $COMMIT_MESSAGE
              BASE_BRANCH="${{ github.event.pull_request.base.ref }}"
                      
              
              # Check if the commit message matches the regex pattern
              if [[ "$COMMIT_MESSAGE" == "Merge branch '$BASE_BRANCH'"* ]]; then
                echo "Commit is a merge from main skipping PR JOB."
              else
                echo "Commit is not a merge from main Triggering PR job."
                   
                if [[ ${{ github.event.pull_request.base.ref }} =~ ^main$ ]] || 
                   [[ ${{ github.event.pull_request.base.ref }} =~ ^Feb[2-9][0-9]$ ]] || 
                   [[ ${{ github.event.pull_request.base.ref }} =~ ^Feb[2-9][0-9]-[0-9][0-9]$ ]] ||
                   [[ ${{ github.event.pull_request.base.ref }} =~ ^Feb[2-9][0-9]-[0-9][0-9]-[0-9][0-9]$ ]] ||
                   [[ ${{ github.event.pull_request.base.ref }} =~ ^May[2-9][0-9]$ ]] || 
                   [[ ${{ github.event.pull_request.base.ref }} =~ ^May[2-9][0-9]-[0-9][0-9]$ ]] ||
                   [[ ${{ github.event.pull_request.base.ref }} =~ ^May[2-9][0-9]-[0-9][0-9]-[0-9][0-9]$ ]] ||
                   [[ ${{ github.event.pull_request.base.ref }} =~ ^Aug[2-9][0-9]$ ]] || 
                   [[ ${{ github.event.pull_request.base.ref }} =~ ^Aug[2-9][0-9]-[0-9][0-9]$ ]] ||
                   [[ ${{ github.event.pull_request.base.ref }} =~ ^Aug[2-9][0-9]-[0-9][0-9]-[0-9][0-9]$ ]] ||
                   [[ ${{ github.event.pull_request.base.ref }} =~ ^Nov[2-9][0-9]$ ]] || 
                   [[ ${{ github.event.pull_request.base.ref }} =~ ^Nov[2-9][0-9]-[0-9][0-9]$ ]] ||
                   [[ ${{ github.event.pull_request.base.ref }} =~ ^Nov[2-9][0-9]-[0-9][0-9]-[0-9][0-9]$ ]]; then
                                       
                     PR_NUMBER=$(echo $GITHUB_REF | awk 'BEGIN { FS = "/" } ; { print $3 }')
                     echo "Building PR ---->$PR_NUMBER"
                     curl  -s -X  GET -H "Accept: application/vnd.github.v3+json" -H 'Authorization: token ${{ secrets.GITHUBS_TOKEN }}'  https://api.github.com/repos/Intacct/ia-app/pulls/$PR_NUMBER >log.out
                     merger=$( cat log.out | jq ".merged" )
                     echo "PR state---------> $merger"   
                      if [ $merger == "false" ];
                      then     
                      echo "PR is still Open and triggereing a new Job for the latest commit(s)"
                      curl -k -X  POST -L --user ci-automation:${{ secrets.PROD_JENKINS_TOKEN }}  https://ci.intacct-dev.com/job/Pull_Requests/job/PR-$PR_NUMBER/build
                      fi
                   fi
                fi
 
  Pending_review_Check:
    if: |
         !contains( github.event.pull_request.labels.*.name, 'Approved') && github.actor != 'github-actions' && github.actor != 'jenkins-sageintacct' && github.event.pull_request.draft == false
    runs-on: self-hosted  
    steps:
    - name: Pending reviews Validations
      id: reviews
      continue-on-error: true
      run: |
              echo " !! Reviews Validations !! "
              PR_NUMBER=$(echo $GITHUB_REF | awk 'BEGIN { FS = "/" } ; { print $3 }')
              REQUESTED_REVIEWERS=$(curl -s -X GET  -H "Accept: application/vnd.github.v3+json" -H 'Authorization: token ${{ secrets.GITHUBS_TOKEN }}'  https://api.github.com/repos/Intacct/ia-app/pulls/$PR_NUMBER/requested_reviewers | jq '[.teams[], .users[]] | length')
              REVIEW_DECISION=$(curl -s -X POST -H "Accept: application/vnd.github.v3+json" -H 'Authorization: token ${{ secrets.GITHUBS_TOKEN }}' -d '{"query": "query { repository(owner: \"intacct\", name: \"ia-app\") { pullRequest(number: '$PR_NUMBER') { reviewDecision }}}"}' https://api.github.com/graphql | jq -r '.data.repository.pullRequest.reviewDecision')
              
              if [ "$REQUESTED_REVIEWERS" = '0' ] && [ "$REVIEW_DECISION" = 'APPROVED' ];
              then
                echo "Congratulatons !! All reviews have been Approved"
              else   
                echo " ------> PR has Pending reviews <-------"
                echo "match=true" >> $GITHUB_OUTPUT
              fi
    
    - name: remove Approved Label
      if: steps.reviews.outputs.match == 'true'         
      uses: buildsville/add-remove-label@v2.0.1
      with:
          token: ${{secrets.GITHUBS_TOKEN}}
          label: Approved
          type: remove

    - name: Add pendingreviews Labels
      if: steps.reviews.outputs.match == 'true'
      uses: buildsville/add-remove-label@v2.0.1
      with:
          token: ${{secrets.GITHUBS_TOKEN}}
          label: Review_is_Pending
          type: add  
   
    - name: Remove Reviews_Pending Label
      if: steps.reviews.outputs.match != 'true'       
      uses: buildsville/add-remove-label@v2.0.1
      with:
          token: ${{secrets.GITHUBS_TOKEN}}
          label: Review_is_Pending
          type: remove
                   
    - name: Add Approved Label
      if: steps.reviews.outputs.match != 'true'         
      uses: buildsville/add-remove-label@v2.0.1
      with:
          token: ${{secrets.GITHUBS_TOKEN}}
          label: Approved
          type: add
  
  Valid_Lable_reapply:
    needs: Pending_review_Check 
    if: contains( github.event.pull_request.labels.*.name, 'Valid_Ticket')  && github.event.pull_request.draft == false
    runs-on: self-hosted  
    steps:
    - name: remove Approved Label
      uses: buildsville/add-remove-label@v2.0.1
      with:
          token: ${{secrets.GITHUBS_TOKEN}}
          label: Valid_Ticket
          type: remove

    - name: Add pendingreviews Labels
      uses: buildsville/add-remove-label@v2.0.1
      with:
          token: ${{secrets.GITHUBS_TOKEN}}
          label: Valid_Ticket
          type: add  
          
          
