<?php
/**
 * Created by PhpStorm.
 * User: smirijanyan
 * Date: 9/1/2017
 * Time: 2:12 PM
 */

class IntacctManagedCustomer extends IntacctManagedRecursiveObject
{
    /**
     * Returns the name of the subscription object.
     *
     * @return string
     */
    public function getSubscriptionObject()
    {
        return 'CUSTOMER';
    }

    /**
     * Field pointing to the parent reference.
     *
     * @return string
     */
    public function getIntacctParentReferenceField()
    {
        return 'RECORDNO';
    }

    /**
     * For the CUSTOMER object need to use the record for the ACCOUNT from the mapping.
     *
     * @return string
     */
    public function getMappingKey()
    {
        return 'ACCOUNT';
    }

    /**
     * Implementation of the method returning query parameters for querying Customers (Accounts) from
     * the Intacct database.
     *
     * @return array
     */
    public function getQueryParams()
    {
        $recursive = [
            'start' => [
                [ 'PARENTKEY', 'is null' ],
            ],
        ];
        $params = [ 'recursive' => $recursive ];
        $params['usemst'] = true;

        return $params;
    }

    /**
     * Adds the xxx__IntacctID__c to the list of queried fields from salesforce.
     *
     * @return array
     * @throws IAException
     */
    protected function getAdditionalSalesforceFields()
    {
        return [SforceNamespaceHandler::getPrefix() . 'IntacctID__c'];
    }

    /**
     * List of salesforce fields specifying the composite key for the salesforce record.
     *
     * @return string[]
     * @throws IAException
     */
    protected function getSforceKeyFields()
    {
        return ['Name', SforceNamespaceHandler::getPrefix() . 'IntacctID__c'];
    }

}