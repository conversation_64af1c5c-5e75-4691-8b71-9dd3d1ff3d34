<?php
/**
  * Manager class for EEDetail object
  *
  * <AUTHOR> <<EMAIL>>
  * @copyright 2000-2015 Intacct Corporation
  *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
  */

/**
 * Class EEDetailManager
 * Manager class for EEDetail entity
 */
class EEDetailManager extends EntityManager
{
    /* @var string[] $relevantEntities */
    private $relevantEntities = array ('eexpensesitem', 'eppaymentitem','expenseadjustmentsitem');

    /**
     * getDDSEntities
     *
     * @return array
     */
    public function getDDSEntities()
    {
        $deleteList = $this->relevantEntities;
        return $deleteList;
    }

    /**
     * Union of custom fields from apbillitem, apadjustmentitem, appaymentitem, apadvanceitem
     */
    function mergeFieldInfo()
    {
        if ( $this->forced_customComponentsEntity === null ) {
            $object = &$this->_schemas[$this->_entity]['object'];
            $fieldInfo = &$this->_schemas[$this->_entity]['fieldinfo'];

            VirtualObjectUtil::MergeFieldInfo($this->relevantEntities, $object, $fieldInfo);

            return;
        }

        parent::MergeFieldInfo();
    }

    /**
     * Union of relationship fields from apbillitem, apadjustmentitem, appaymentitem, apadvanceitem
     */
    function mergePlatformRelationships()
    {
        if ( $this->forced_platform_entity === null ) {
            $object = &$this->_schemas[$this->_entity]['object'];
            $fieldInfo = &$this->_schemas[$this->_entity]['fieldinfo'];

            VirtualObjectUtil::MergePlatformRelationships($this->relevantEntities, $object, $fieldInfo);

            return;
        }

        parent::MergePlatformRelationships();
    }
}