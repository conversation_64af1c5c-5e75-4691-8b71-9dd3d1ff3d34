/**
 * Start the sign-in process
 */
function signIn()
{
    window.open('deliveryservice.phtml?.op=' + _op + '&.sess=' + _sess
                + '&.r=' + encodeURIComponent(editor.getGlobalFieldValue('_r')));
}

function retry()
{
    editor.ajax(false, "retryDeliveryService", null, function() { editor.submit(true, "refresh"); });
}

/**
 * Sets the sections corresponding with their respective Cloud Storage type
 */

function setupSections(obj)
{
    var value = obj.meta.getValue();

    if ( value == '--Select--') {
        toggleHTTPSection(false);
        toggleDropboxSection(false);
        toggleAwsSection(false);
        toggleAzureStorageSection(false);
        toggleOneDriveSection(false);
    } else if ( value == 'HTTP' ) {
        toggleDropboxSection(false);
        toggleAwsSection(false);
        toggleHTTPSection(true);
        toggleAzureStorageSection(false);
        toggleOneDriveSection(false);
    } else if ( value == 'Dropbox' || value == 'GoogleDrive' || value == 'Box') {
        toggleHTTPSection(false);
        toggleAwsSection(false);
        toggleDropboxSection(true);
        toggleAzureStorageSection(false);
        toggleOneDriveSection(false);
    } else if ( value == 'Aws' ) {
        toggleHTTPSection(false);
        toggleDropboxSection(false);
        toggleAwsSection(true);
        toggleAzureStorageSection(false);
        toggleOneDriveSection(false);
    } else if ( value == 'AzureStorage' ) {
        toggleHTTPSection(false);
        toggleDropboxSection(false);
        toggleAwsSection(false);
        toggleAzureStorageSection(true);
        toggleOneDriveSection(false);
    } else if ( value == 'OneDrive' ) {
        toggleHTTPSection(false);
        toggleDropboxSection(false);
        toggleAwsSection(false);
        toggleAzureStorageSection(false);
        toggleOneDriveSection(true);
    }
}

function toggleHTTPSection(checked)
{
    showHideSection('HTTP', ['ADDRESSURL'], checked === true || checked == "true");
}

function toggleDropboxSection(checked)
{
    showHideSection('Dropbox', [], checked === true || checked == "true");
}

function toggleAwsSection(checked)
{
    showHideSection('Aws', ['BUCKET'], checked === true || checked == "true");
}

function toggleAzureStorageSection(checked)
{
    showHideSection('AzureStorage', ['ADDRESSURL'], checked === true || checked == "true");
}

function toggleOneDriveSection(checked)
{
    showHideSection('OneDrive', [], checked === true || checked == "true");
}


function showHideSection(id, reqFields, showHide)
{
    setTimeout(function() {
        for (var ix = 0; ix < reqFields.length; ix++) {
            var f = editor.findComponents(reqFields[ix]);
            f[0].updateProperty('required', showHide);
        }

        var s = editor.findComponentsById(id, 'Section');
        s[0].showHide(showHide);
    }, 100);
}
