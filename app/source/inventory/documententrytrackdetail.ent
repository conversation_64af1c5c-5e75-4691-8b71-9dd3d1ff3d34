<?php
$kSchemas['documententrytrackdetail'] = array(
    'children' => array(
        'item' => array(
            'fkey' => 'itemkey', 'invfkey' => 'itemid', 'table' => 'icitem', 'join' => 'outer'
        ),
        'documententry' => array(
             'fkey' => 'docentrykey', 'invfkey' => 'record#', 'table' => 'docentry',
             'children' => array(
                 'docheader' => array(
                     'fkey' => 'dochdrkey',
                     'invfkey' => 'record#',
                     'table' => 'dochdrmst',
                 ),
             )
        ),
        'aisle' => array(
            'fkey' => 'aislekey', 'invfkey' => 'record#', 'table' => 'icaisle', 'join' => 'outer'
        ),
        'bin' => array(
            'fkey' => 'binkey', 'invfkey' => 'record#', 'table' => 'icbin', 'join' => 'outer'
        ),
        'icrow' => array(
            'fkey' => 'rowkey', 'invfkey' => 'record#', 'table' => 'icrow', 'join' => 'outer'
        ),
        'sodocumententry' => array(
            'fkey' => 'docentrykey', 'invfkey' => 'record#', 'join' => 'outer','table' => 'docentry'
        ),
        'invdocumententry' => array(
            'fkey' => 'docentrykey', 'invfkey' => 'record#','join' => 'outer','table' => 'docentry'
        ),
        'podocumententry' => array(
            'fkey' => 'docentrykey', 'invfkey' => 'record#','join' => 'outer','table' => 'docentry'
        ),
    ),
    'object' => array(
        'RECORDNO',
        'DOCENTRYNO',
        'ITEMID',
        'ENTRYITEMID',     // Added for REST API reference only
        'ENTRYITEMKEY',    // Added for REST API reference only
        'KITCOMPONENTID',  // Added for REST API reference only
        'KITCOMPONENTKEY', // Added for REST API reference only
        'ITEMRECORDNO',
        'DOCPARID',
        'QUANTITY',
        'TRACK_QUANTITY',
        'SERIALNO',
        'LOTNO',
        'AISLEKEY',
        'ROWKEY',
        'BINKEY',
        'EXPIRATION',
        'SALE_PUR_TRANS',
        'AISLEID',
        'BINID',
        'ROWID',
        'QUANTITYRESERVED',
        'QUANTITYALLOCATED',
        'QUANTITYPACKED',
        'QUANTITYRESERVEDWHENOPEN',
        'QUANTITYALLOCATEDWHENOPEN',
        'WORKQUEUEKEY',
        'LINE_NO',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ),
    'publish' => array(
        'ITEMID',
        'QUANTITY',
        'SERIALNO',
        'LOTNO',
        'EXPIRATION',
        'SALE_PUR_TRANS',
        'AISLEID',
        'BINID',
        'ROWID',
        'BINKEY',
        'QUANTITYRESERVED',
        'QUANTITYALLOCATED',
        'QUANTITYPACKED',
        'LINE_NO'
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'DOCENTRYNO' => 'docentrykey',
        'WORKQUEUEKEY' => 'workqueuekey',
        'ITEMID' => 'itemkey',
        'ITEMRECORDNO' => 'item.record#',
        'DOCPARID' => 'docheader.docparid',
        'QUANTITY' => 'quantity',
        'TRACK_QUANTITY' => 'quantity',
        'QUANTITYRESERVED' => 'quantityreserved',
        'QUANTITYALLOCATED' => 'quantityallocated',
        'QUANTITYPACKED'    => 'quantitypacked',
        'QUANTITYRESERVEDWHENOPEN' => 'quantityreservedwhenopen',
        'QUANTITYALLOCATEDWHENOPEN' => 'quantityallocatedwhenopen',
        'SERIALNO' => 'serialno',
        'LOTNO' => 'lotno',
        'AISLEKEY' => 'aislekey',
        'ROWKEY' => 'rowkey',                   // NOTE: rowkey is here AND below in icrow.rowkey
        'BINKEY' => 'binkey',
        'EXPIRATION' => 'expirationdate',
        'SALE_PUR_TRANS' => 'sale_pur_trans',
        'AISLEID' => 'aisle.aisleid',
        'BINID' => 'bin.binid',
        'ROWID' => 'icrow.rowkey',              // which one works?  Dunno!  Historical behavior!!
        'LINE_NO' => 'documententry.lineno',
        'DEITEMKEY' => 'documententry.itemkey', // Added for REST API reference only
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENMODIFIED',
        'WHENCREATED',
    ),
    'fieldinfo' => array(
        array(
            'fullname' => 'IA.RECORD_NUMBER',
            'desc' => 'IA.RECORD_NUMBER',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'sequence',
                'size' => 8,
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'readonly' => true,
            'hidden' => true,
            'path' => 'RECORDNO',
            'id' => 1,
        ),
        array(
            'fullname' => 'IA.ENTRY_NUMBER',
            'desc' => 'IA.ENTRY_NUMBER',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'text',
                'size' => 8,
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'hidden' => true,
            'path' => 'DOCENTRYNO',
            'id' => 2,
        ),
        array(
            'fullname' => 'IA.ITEM_ID',
            'desc' => 'IA.ITEM_ID',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'item',
            ),
            'renameable' => true,
            'path' => 'ITEMID',
            'id' => 3,
        ),
        [
            'fullname' => 'IA.ENTRYITEMID',
            'desc' => 'IA.ENTRYITEMID',
            'path' => 'ENTRYITEMID',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
            ],
            'formula' => [
                'typeOf' => 'ITEMID',
                'fields' => [
                    'ITEMID',
                    'DEITEMKEY',
                ],
                'function' => "case when \${1} = \${2} then \${1} else null end",
            ],
            'readonly' => true,
            'calculated' => true,
            'id' => 19,
        ],
        [
            'fullname' => 'IA.KITCOMPONENTID',
            'desc' => 'IA.KITCOMPONENTID',
            'path' => 'KITCOMPONENTID',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
            ],
            'formula' => [
                'typeOf' => 'ITEMID',
                'fields' => [
                    'ITEMID',
                    'DEITEMKEY',
                ],
                'function' => "case when \${1} != \${2} then \${1} else null end",
            ],
            'readonly' => true,
            'calculated' => true,
            'id' => 20,
        ],
        [
            'fullname' => 'IA.ENTRYITEMKEY',
            'desc' => 'IA.ENTRYITEMKEY',
            'path' => 'ENTRYITEMKEY',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
            ],
            'formula' => [
                'typeOf' => 'RECORDNO',
                'fields' => [
                    'ITEMID',
                    'DEITEMKEY',
                    'ITEMRECORDNO'
                ],
                'function' => "case when \${1} = \${2} then \${3} else null end",
            ],
            'readonly' => true,
            'calculated' => true,
            'id' => 21,
        ],
        [
            'fullname' => 'IA.KITCOMPONENTKEY',
            'desc' => 'IA.KITCOMPONENTKEY',
            'path' => 'KITCOMPONENTKEY',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
            ],
            'formula' => [
                'typeOf' => 'RECORDNO',
                'fields' => [
                    'ITEMID',
                    'DEITEMKEY',
                    'ITEMRECORDNO'
                ],
                'function' => "case when \${1} != \${2} then \${3} else null end",
            ],
            'readonly' => true,
            'calculated' => true,
            'id' => 22,
        ],
        array(
            'fullname' => 'IA.QUANTITY',
            'desc' => 'IA.QUANTITY',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'size' => 10,
                'maxlength' => 10,
                'format' => $gDecimalFormat
            ),
            'precision' => 10,
            'noautodecimal' => true,
            'path' => 'QUANTITY',
            'id' => 5,
        ),
        array(
            'fullname' => 'IA.QUANTITY_TO_FULFILL',
            'desc' => 'IA.QUANTITY',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'size' => 12,
                'maxlength' => 12,
                'format' => $gDecimalFormat
            ),
            'precision' => 10,
            'noautodecimal' => true,
            'path' => 'TRACK_QUANTITY',
            'id' => 5,
        ),
        array(
            'fullname' => 'IA.AISLE_ID',
            'desc' => 'IA.AISLE_ID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'aisle',
                'maxlength' => 30,
            ),
            'path' => 'AISLEID',
            'id' => 6
        ),
        array(
            'fullname' => 'IA.BIN_ID',
            'desc' => 'IA.BIN_ID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'bin',
                'maxlength' => 30,
            ),
            'path' => 'BINID',
            'id' => 7
        ),
        array(
            'fullname' => 'IA.ROW_ID',
            'desc' => 'IA.ROW_ID',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 30,
            ),
            'path' => 'ROWID',
            'id' => 8
        ),
        array(
            'fullname' => 'IA.SERIAL_NUMBER',
            'desc' => 'IA.SERIAL_NUMBER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 100,
            ),
            'path' => 'SERIALNO',
            'id' => 9
        ),
        array(
            'fullname' => 'IA.LOT_NUMBER',
            'desc' => 'IA.LOT_NUMBER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 50,
            ),
            'path' => 'LOTNO',
            'id' => 10
        ),
        array(
            'fullname' => 'IA.EXPIRATION_DATE',
            'desc' => 'IA.EXPIRATION_DATE',
            'type' => $gDateType,
            'path' => 'EXPIRATION',
            'id' => 11,
        ),
        array(
            'fullname' => 'IA.SALE_PURCHASE_INTERNAL',
            'desc' => 'IA.SALE_PURCHASE_INTERNAL',
            'type' => array(
                'type' => 'enum',
                'ptype' => 'enum',
                'validvalues' => array('Sale', 'Purchase', 'Internal', 'TimeBill'),
                '_validivalues' => array('S', 'P', 'I', 'T'),
                'validlabels' => array('IA.SALE', 'IA.PURCHASE', 'IA.INTERNAL', 'IA.TIMEBILL'),
            ),
            'path' => 'SALE_PUR_TRANS',
            'id' => 12
        ),
        array(
            'fullname' => 'IA.FULFILLMENT_RECORD_NUMBER',
            'desc' => 'IA.FULFILLMENT_RECORD_NUMBER',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'sequence',
                'size' => 8,
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'readonly' => true,
            'path' => 'WORKQUEUEKEY',
            'id' => 13,
        ),
        array(
            'fullname' => 'IA.QUANTITY_RESERVED',
            'desc' => 'IA.QUANTITY_RESERVED',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'size' => 10,
                'maxlength' => 10,
                'format' => $gDecimalFormat
            ),
            'precision' => 10,
            'noautodecimal' => true,
            'path' => 'QUANTITYRESERVED',
            'id' => 14,
        ),
        array(
            'fullname' => 'IA.QUANTITY_PICKED',
            'desc' => 'IA.QUANTITY_PICKED_ALLOCATED',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'size' => 10,
                'maxlength' => 10,
                'format' => $gDecimalFormat
            ),
            'precision' => 10,
            'noautodecimal' => true,
            'path' => 'QUANTITYALLOCATED',
            'id' => 15,
        ),
        array(
            'fullname' => 'IA.QUANTITY_RESERVED_BEFORE_FULFILLMENT',
            'desc' => 'IA.QUANTITY_RESERVED_BEFORE_FULFILLMENT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'size' => 10,
                'maxlength' => 10,
                'format' => $gDecimalFormat
            ),
            'precision' => 10,
            'noautodecimal' => true,
            'path' => 'QUANTITYRESERVEDWHENOPEN',
            'id' => 16,
        ),
        array(
            'fullname' => 'IA.QUANTITY_ALLOCATED_BEFORE_FULFILLMENT',
            'desc' => 'IA.QUANTITY_ALLOCATED_BEFORE_FULFILLMENT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'size' => 10,
                'maxlength' => 10,
                'format' => $gDecimalFormat
            ),
            'precision' => 10,
            'noautodecimal' => true,
            'path' => 'QUANTITYALLOCATEDWHENOPEN',
            'id' => 17,
        ),
        array(
            'fullname' => 'IA.QUANTITY_PACKED',
            'desc' => 'IA.QUANTITY_PACKED_IN_FULFILLMENT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'size' => 10,
                'maxlength' => 10,
                'format' => $gDecimalFormat
            ),
            'precision' => 10,
            'noautodecimal' => true,
            'path' => 'QUANTITYPACKED',
            'id' => 17,
        ),
        array(
            'fullname' => 'IA.DOCENTRY_LINE_NO',
            'desc' => 'IA.PARENT_DOCENTRY_LINE_NO',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'integer',
                'entity' => 'documententry',
                'maxlength' => 4,
                'size' => 4,
            ),
            'path' => 'LINE_NO',
            'id' => 18,
        ),
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,

    ),

    'nexus' => array(
        'item' => array(
            'object' => 'item',
            'relation' => MANY2ONE,
            'field' => 'itemid',
            'printas' => 'IA.ITEM',
        ),
        'sodocumententry' => array(
            'object' => 'sodocumententry',
            'relation' => MANY2ONE,
            'field' => 'DOCENTRYKEY',
        ),
        'invdocumententry' => array(
            'object' => 'invdocumententry',
            'relation' => MANY2ONE,
            'field' => 'DOCENTRYKEY',
        ),
        'podocumententry' => array(
            'object' => 'podocumententry',
            'relation' => MANY2ONE,
            'field' => 'DOCENTRYKEY',
        ),
    ),

    'parententity' => 'documententry',
    'table' => 'docentrytrackdetail',
    'printas' => 'IA.DOCUMENT_ENTRY_TRACKING_DETAILS',
    'pluralprintas' => 'IA.DOCUMENT_ENTRIES_TRACKING_DETAILS',
    'vid' => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'module' => 'inv',
    'auditcolumns' => true,
    'api' => array(
        'LESS_GET_FIELDS' => [
            'ENTRYITEMKEY',     // Added for REST API reference only
            'ENTRYITEMID',      // Added for REST API reference only
            'KITCOMPONENTKEY',  // Added for REST API reference only
            'KITCOMPONENTID',   // Added for REST API reference only
            'DEITEMKEY',        // Added for REST API reference only
        ],
        'PERMISSION_CREATE' => 'NONE',
        'PERMISSION_UPDATE' => 'NONE',
        'PERMISSION_DELETE' => 'NONE',
    ),
    'allowDDS' => false,
    'nochatter' => true,
    'bulkoperation' => true,
    // note: do NOT enable upsert without paying attention to whether the parent, docentry, is in upsert mode!!!
    // 'upsertEntries' => true,  // TODO: MAKE DYNAMIC; upsertEntries = (UPSERT IS ENABLED FOR DOCENTRY)
    'description' => 'IA.LIST_OF_WAREHOUSE_INVENTORY_DETAILS_SERIAL_NUMBER',
);
