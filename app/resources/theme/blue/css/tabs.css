div.dcontent {
	border: #989898 solid 1px;
	clear: left;
	padding: 1em;
	background-color:#FFF;
}
div.dcontent.inactive {
	display: none;
}
ol#toc {
	height: 2em;
	list-style: none;
	margin: 0;
	padding: 0;
}
/* active state */
ol#toc a {
	background: url(../images/tabs2.png) 0 -54px;
	color: #666;
	display: block;
	float: left;
	height: 2em;
	padding-left: 12px;
	text-decoration: none;
}
ol#toc span {
	background: url(../images/tabs2.png) 100% -54px;
	display: block;
	line-height: 2em;
	padding-right: 12px;
}
/* /active state */
/* hover state */
ol#toc a:hover {
	background-position: 0 -27px;
}
ol#toc a:hover span {
	background-position: 100% -27px;
}
/* /hover state */
ol#toc li {
	float: left;
	margin: 0 3px 0 0;
}
/* selected state */
ol#toc li a.active {
	background-position: 0 0px;
	color: #333;/*font-weight: bold;*/
}
ol#toc li a.active span {
	background-position: 100% 0px;
}
/* /selected state */
