<?
/**
*    FILE:        itemlist.ent
*    AUTHOR:        rpn
*    DESCRIPTION:    ent file item list report filters
*
*    (C) 2000, Intacct Corporation, All Rights Reserved
*
*    This document contains trade secret data that belongs to Intacct
*    Corporation and is protected by the copyright laws.  Information
*    herein may not be used, copied or disclosed in whole or in part
*    without prior written consent from Intacct Corporation.
*/

global $gManagerFactory, $kINVid;

GetModulePreferences($kINVid, $prefs);
//$itemMgr = $gManagerFactory->GetManager('item');
//$allowKit = $itemMgr->Enablekitting();
//$allowLightAssembly = $itemMgr->EnableLightAssembly();

$bHideNonInvTypes = true;
$bHideNonInvSO = false;
$bHideNonInvPO = false;
$bHideKit = false;
$bHideSTKit = false;

$mod = Request::$r->_mod;

if ( $mod == 'inv' ) {
    
} elseif ( $mod == 'po' ) {
    $bHideNonInvSO = true;
} elseif ( $mod == 'so' ) {
    $bHideNonInvPO = true;
}

if ( $prefs['LIGHTASSEMBLY'] != 'T' || $mod == 'po' ) {
    $bHideSTKit = true;
}
if ( !IsKitEnabled() || $mod == 'po' ) {
    $bHideKit = true;
}
$CheckBox = array (    'type'         =>    'char',
            'ptype'     =>    'boolean',
            'validvalues'    => $gBooleanValues,
            '_validivalues' => $gBooleanIValues,                                
           );

$Inventory = array (    'fullname' => 'IA.INVENTORY',
            'type'     => $CheckBox,
            'default'  => 'true',
            'path'     => 'INVENTORY',
        );
$NonInv       = array (    'fullname' => 'IA.NON_INVENTORY',
            'type'     => $CheckBox,
            'default'  => 'false',
            'path'     => 'NON_INV',
        );
$NonInv_PO = array (    'fullname' => 'IA.NON_INVENTORY_PURCHASE_ONLY',
            'type'     => $CheckBox,
            'default'  => 'false',
            'path'     => 'NON_INV_PO',
            'hidden'   => $bHideNonInvPO,
        );
$NonInv_SO = array (    'fullname' => 'IA.NON_INVENTORY_SALES_ONLY',
            'type'     => $CheckBox,
            'default'  => 'false',
            'path'     => 'NON_INV_SO',
            'hidden'   => $bHideNonInvSO,
        );
$Stockable_Kit = array (    'fullname' => 'IA.STOCKABLE_KIT',
            'type'     => $CheckBox,
            'default'  => 'false',
            'path'     => 'STKIT',
            'hidden'   => $bHideSTKit,
        );
$Kit       = array (    'fullname' => 'IA.KIT',
            'type'     => $CheckBox,
            'default'  => 'false',
            'path'     => 'KIT',
            'hidden'   => $bHideKit,
        );

// changing the background color for ItemId & UPC filter labels
$InvRptCtrlGrpORLabel = $gInvRptCtrlORLabel;
$InvRptCtrlGrpORLabel['labelcssclass']    = 'rf_grplabel_cell';
$gInvRptCtrlFromUPC['labelcssclass']    = 'rf_grplabel_cell';
$gInvRptCtrlToUPC['labelcssclass']    = 'rf_grplabel_cell';
$gInvRptCtrlItemOprFilter['fields'][0]['labelcssclass']    = 'rf_grplabel_cell';
$gInvRptCtrlItemOprFilter['fields'][1]['labelcssclass'] = 'rf_grplabel_cell';
$gInvRptCtrlUPCOprFilter['fields'][0]['labelcssclass']    = 'rf_grplabel_cell';
$gInvRptCtrlUPCOprFilter['fields'][1]['labelcssclass']    = 'rf_grplabel_cell';


if ( IsMultiEntityCompany() ) {
    $deptlocationgroup = array('title' => 'IA.LOCATION_DEPARTMENT_FILTERS',
                   'fields' => array(    $gLocationPick,
                            $gDepartmentPick,
                            $gLocationSubFilter
                   )
    );
}

$fromItemID = array (
        'fullname' => 'IA.FROM_ITEM',
        'assist' => 'report',
        'type' => array ( 
            'ptype' => 'ptr',
            'entity' => 'itemsallpick',                                    
            'type' => 'ptr',
            'maxlength' => 20,
            'format' => $gEntityIDFormat
        ),
        'desc' => 'IA.FROMITEMID',
        'path' => 'FROMITEMID',
        'noedit' => true,
        'nonew' => true,
        'noview' => true,
        'renameable' => 1,
        'labelcssclass' => 'rf_grplabel_cell',
        'onchange' => "AutoPopulateToFieldUsingFrom(document.main.elements['_obj__FROMITEMID'],document.main.elements['_obj__TOITEMID']);",
    );

$toItemID = array (
            'fullname' => 'IA.TO_ITEM',
            'assist' => 'report',
            'type' => array ( 
                'ptype' => 'ptr',
                'entity' => 'itemsallpick',                                    
                'type' => 'ptr',
                'maxlength' => 20,
                'format' => $gEntityIDFormat
            ),
            'desc' => 'IA.TOITEMID',
            'path' => 'TOITEMID',
            'noedit' => true,
            'nonew' => true,
            'noview' => true,
            'renameable' => 1,
            'labelcssclass' => 'rf_grplabel_cell',
            'onfocus' => "AutoPopulateToFieldUsingFrom(document.main.elements['_obj__FROMITEMID'],document.main.elements['_obj__TOITEMID']);this.select();",
        );

$kSchemas['itemlist'] = array (
    'schema' => array (
        array (
            'ITEM' => 'item',
            'VENDOR' => 'vendor',
            'STATE' => 'state',
            'START_DATE' => 'start_date',
            'END_DATE' => 'end_date',
        )
    ),
    'individualreport' => array (
        'LOCATION'        => array ( 'default' => false ),
        'DEPARTMENT'    => array ( 'default' => false ),
    ),
    'promptonrun' => array (
        'LOCATION'        => array ( 'default' => false ),
        'DEPARTMENT'    => array ( 'default' => false ),
    ),
    
     'fieldgroup' => array (
                'ITEMTYPES' => array (
                    'layout' => 'landscape',
                    'fields' => array (
                        $Inventory,
                        $NonInv,
                        $NonInv_PO,
                        $NonInv_SO,
                        $Kit,
                        $Stockable_Kit,
                    ),
                ),
                'ITEMOPRFILTER' => $gInvRptCtrlItemOprFilter,
                'UPCOPRFILTER' => $gInvRptCtrlUPCOprFilter,
                'FROMTOITEM' => array(  'layout' => 'landscape',
                            'fields' => array($fromItemID,
                                      $toItemID
                                )
                            ),
                'FROMTOUPC' => array(    'layout' => 'landscape',
                            'fields'=>array($gInvRptCtrlFromUPC,
                                    $gInvRptCtrlToUPC
                                )
                            )
                ),

     'fieldinfo' => array ( 
            'lines' => array(
                        
                        array(
                            'title' => 'IA.FILTERS',
                            'fields' => array(
                                $gInvRptCtlProdLineID,
                                $gInvRptCtrlReportOn,
                                array(    'type' => array('type' => 'fieldgroup'), 
                                    'path' => 'FROMTOITEM'),
                                $InvRptCtrlGrpORLabel,
                                array(
                                    'fullname' => 'IA.ITEM_OPRFILTER',
                                    'type' => array('type' => 'fieldgroup'), 
                                    'path' => 'ITEMOPRFILTER',
                                ),
                                $gInvRptCtrlORLabel,
                                array(    'type' => array('type' => 'fieldgroup'), 
                                    'path' => 'FROMTOUPC'),
                                $InvRptCtrlGrpORLabel,
                                array(
                                    'fullname' => 'IA.UPC_OPRFILTER',
                                    'type' => array('type' => 'fieldgroup'), 
                                    'path' => 'UPCOPRFILTER',
                                ),
                                
                                array (
                                    'fullname' => 'IA.INVENTORY_PRECISION',
                                    'type' => array (
                                        'type'             =>    'char',
                                        'ptype'         =>    'boolean',
                                        'validvalues'    => $gBooleanValues,
                                        '_validivalues' => $gBooleanIValues,                                
                                        ),
                                    'default'         => 'false',
                                    'path' => 'INVPREC',
                                ),
                                array (
                                    'fullname' => 'IA.SALES_PRECISION',
                                    'type' => array (
                                        'type'             =>    'char',
                                        'ptype'         =>    'boolean',
                                        'validvalues'    => $gBooleanValues,
                                        '_validivalues' => $gBooleanIValues,                                
                                        ),
                                    'default'         => 'false',
                                    'path' => 'SALESPREC',
                                ),
                                array (
                                    'fullname' => 'IA.PURCHASING_PRECISION',
                                    'type' => array (
                                        'type'             =>    'char',
                                        'ptype'         =>    'boolean',
                                        'validvalues'    => $gBooleanValues,
                                        '_validivalues' => $gBooleanIValues,                                
                                        ),
                                    'default'         => 'false',
                                    'path' => 'PURSPREC',
                                ),
                                array (
                                    'fullname' => 'IA.BASE_PRICE',
                                    'type' => array (
                                        'type'             =>    'char',
                                        'ptype'         =>    'boolean',
                                        'validvalues'    => $gBooleanValues,
                                        '_validivalues' => $gBooleanIValues,                                
                                        ),
                                    'default'         => 'false',
                                    'path' => 'BASEPRICE',
                                ),
                                array (
                                    'fullname' => 'IA.ITEM_GL_GROUP',
                                    'type' => array (
                                        'type'             =>    'char',
                                        'ptype'         =>    'boolean',
                                        'validvalues'    => $gBooleanValues,
                                        '_validivalues' => $gBooleanIValues,                                
                                        ),
                                    'default'         => 'false',
                                    'path' => 'ITEMGLGROUP',
                                ),
                                array (
                                'fullname' => 'IA.TAX_GL_GROUP',
                                'type' => array (
                                    'type'             =>    'char',
                                    'ptype'         =>    'boolean',
                                    'validvalues'    => $gBooleanValues,
                                    '_validivalues' => $gBooleanIValues,                                
                                    ),
                                'default'         => 'false',
                                'path' => 'TAXGLGROUP',
                                ),
                                array (
                                    'fullname' => 'IA.TAXABLE',
                                    'type' => array (
                                        'type'             =>    'char',
                                        'ptype'         =>    'boolean',
                                        'validvalues'    => $gBooleanValues,
                                        '_validivalues' => $gBooleanIValues,                                
                                        ),
                                    'default'         => 'false',
                                    'path' => 'TAXABLE',
                                ),
                                array (
                                    'fullname' => 'IA.DATE_LAST_SOLD',
                                    'type' => array (
                                        'type'             =>    'char',
                                        'ptype'         =>    'boolean',
                                        'validvalues'    => $gBooleanValues,
                                        '_validivalues' => $gBooleanIValues,                                
                                        ),
                                    'default'         => 'false',
                                    'path' => 'DATELASTSOLD',
                                ),
                            ),
                        ),
                        array(
                            'title' => 'IA.ITEM_TYPE',
                            'fields' => array(
                                array (
                                    'fullname' => 'IA.ITEM_TYPES',
                                    'type' => array('type' => 'fieldgroup'), 
                                    'path' => 'ITEMTYPES',
                                ),
                            ),
                        ),
                        $deptlocationgroup,
                        array(
                            'title' => 'IA.FORMAT',
                            'fields' => array(
                            ),
                        ),
            ),
     ),
     'controls' => $gInvRptControls,
     'layout' => 'frame',
     'layoutproperties' => $gInvRptLayoutProperties,
     'xsl_file' => 'itemlist',
     'printas'=>'IA.ITEM_LIST',
     'module' => 'inv',
     'noreportloccheck' => true,
     //'onloadjs' => $EnableScript,
     'helpfile' => 'Run_an_Order_Entry_Item_List_Report'
);


