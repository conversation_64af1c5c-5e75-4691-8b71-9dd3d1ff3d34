<?php
/**
 * Created by JetBrains PhpStorm.
 * User: rva<PERSON>mani
 * Date: 7/1/15
 * Time: 3:16 PM
 * To change this template use File | Settings | File Templates.
 */

$kSchemas['opbanksubscription'] = array(
    'schema' => array(
        'RECORDNO' => 'record#',
        'OUTSOURCECHECK' => 'outsourcecheck',
        'OUTSOURCEACH' => 'outsourceach',
        'STATE' => 'state',
        'BANKACCOUNTKEY' => 'bankaccountkey',
        'BANKACCOUNTID' => 'bankaccountid',
        'AMEX_PAYVE_ACCOUNTID' => 'amex_payve_accountid',
        'FRACTIONAL_ROUTING_NO'   => 'fractional_routing_no',
        'AMEXCHECKNUM' => 'amexchecknum',
        'ABO_FIRST_NAME' => 'abo_first_name',
        'ABO_MIDDLE_NAME' => 'abo_middle_name',
        'ABO_LAST_NAME' => 'abo_last_name',
        'ABO_PHONE_NUMBER' => 'abo_phone_number',
        'ABO_EMAIL_ID' => 'abo_email_id',
        'AUTOMATE_ACH_PYMT_SEND' => 'automate_ach_pymt_send',
        'AUTOMATE_ACH_PYMT_CONF' => 'automate_ach_pymt_conf',
        'AUTOMATE_CHECK_PRINT' => 'automate_check_print',
        'AUTOMATE_PYMT_CONF' => 'automate_pymt_conf',
        'ABOURL' => 'abourl',
        'RSVPCODE' => 'rsvpcode',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
        'AMEX_BANK_ACCOUNT_ID' => 'amex_bank_account_id',
        'AMEX_BANK_ACCOUNT_ADDRESS_ID' => 'amex_bank_account_address_id',
        'AMEX_BA_CD_RETURN_ADDRESS_ID' => 'amex_ba_cd_return_address_id',
        'AMEX_BA_ACH_AFFILIATE_ID' => 'amex_ba_ach_affiliate_id',
    ),
    'object' => array(
        'RECORDNO',
        'OUTSOURCECHECK',
        'OUTSOURCEACH',
        'STATE',
        'BANKACCOUNTKEY',
        'BANKACCOUNTID',
        'AMEX_PAYVE_ACCOUNTID',
        'FRACTIONAL_ROUTING_NO',
        'AMEXCHECKNUM',
        'ABO_FIRST_NAME',
        'ABO_MIDDLE_NAME',
        'ABO_LAST_NAME',
        'ABO_EMAIL_ID',
        'ABO_PHONE_NUMBER',
        'AUTOMATE_ACH_PYMT_SEND',
        'AUTOMATE_ACH_PYMT_CONF',
        'AUTOMATE_CHECK_PRINT',
        'AUTOMATE_PYMT_CONF',
        'ABOURL',
        'RSVPCODE',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'AMEX_BANK_ACCOUNT_ID',
        'AMEX_BANK_ACCOUNT_ADDRESS_ID',
        'AMEX_BA_CD_RETURN_ADDRESS_ID',
        'AMEX_BA_ACH_AFFILIATE_ID',
    ),
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED'
    ),
    'fieldinfo' => array (
        array (
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD_NO',
            'type' => array (
                'ptype' => 'sequence',
                'type' => 'integer',
            ),
            'required' => true,
            'id' => 1,
        ),
        array(
            'path' => 'OUTSOURCECHECK',
            'fullname' => 'IA.ENABLE_OUTSOURCE_CHECK',
            'type' => array (
                'ptype' => 'enum',
                'type' => 'text',
                'validvalues' => array ('false', 'true', 'Pending Activation', 'queued', 'Subscribed'),
                '_validivalues' => array ('F', 'T', 'P', 'Q', 'A'),
                'validlabels' => array ('IA.FALSE', 'IA.TRUE', 'IA.PENDING_ACTIVATION', 'IA.QUEUED', 'IA.SUBSCRIBED'),
            ),
            'id' => 2,
            'readonly' => false,
        ),
        array(
            'path' => 'OUTSOURCEACH',
            'fullname' => 'IA.ENABLE_OUTSOURCE_ACH',
            'type' => array (
                'ptype' => 'enum',
                'type' => 'text',
                'validvalues' => array ('false', 'true', 'Pending Activation', 'queued', 'Subscribed', 'Inactive'),
                '_validivalues' => array ('F', 'T', 'P', 'Q', 'A', 'I'),
                'validlabels' => array ('IA.FALSE', 'IA.TRUE', 'IA.PENDING_ACTIVATION', 'IA.QUEUED', 'IA.SUBSCRIBED', 'IA.INACTIVE'),
            ),
            'id' => 3,
            'readonly' => false,
        ),
        array(
            'path' => 'BANKACCOUNTKEY',
            'fullname' => 'IA.BANK_ACCOUNT_KEY',
            'required' => false,
            'type' => array (
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 200,
            ),
            'id' => 4,
        ),
        array(
            'path' => 'BANKACCOUNTID',
            'fullname' => 'IA.BANK_ACCOUNT_ID',
            'required' => false,
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 200,
            ),
            'id' => 5,
        ),

        array(
            'path' => 'AMEX_PAYVE_ACCOUNTID',
            // PAYVE (all caps) is a registered trademark of American Express.
            'fullname' => 'IA.PAYVE_ID',
            'required' => false,
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
            ),
            'id' => 6,
        ),
        // Don't understand why global definitions aren't used.
        array(
            'path'      => 'WHENCREATED',
            'fullname'  => 'IA.WHEN_CREATED',
            'desc'      => 'IA.TIMESTAMP_MARKING_LAST_TIME_THIS_WAS_CREATED',
            'readonly'  =>  true,
            'type'      => array(
                'type'      => 'timestamp',
                'maxlength' => 22,
                'size'      => 22,
            ),
            'id'        => 9,
        ),
        array(
            'path'      => 'WHENMODIFIED',
            'fullname'  => 'IA.WHEN_MODIFIED',
            'desc'      => 'IA.TIMESTAMP_MARKING_LAST_TIME_THIS_WAS_CHANGED',
            'readonly'  =>  true,
            'type'      => array(
                'type'      => 'timestamp',
                'maxlength' => 22,
                'size'      => 22,
            ),
            'id'        => 7,
        ),
        array(
            'path'      => 'CREATEDBY',
            'fullname'  => 'IA.CREATED_BY',
            'desc'      => 'IA.USER_WHO_CREATED_THIS',
            'readonly'  =>  true,
            'type'      => array(
                'ptype'     => 'ptr',
                'type'      => 'ptr',
                'entity'    => 'userinfo'
            ),
            'id'       => 8,
        ),
        array(
            'path'      => 'MODIFIEDBY',
            'fullname'  => 'IA.MODIFIED_BY',
            'desc'      => 'IA.USER_WHO_MODIFIED_THIS',
            'readonly'  =>  true,
            'type'      => array(
                'ptype'     => 'ptr',
                'type'      => 'ptr',
                'entity'    => 'userinfo'
            ),
            'id'       => 9,
        ),
        array(
            'path' => 'STATE',
            'fullname' => 'IA.STATE',
            'type' => array (
                'ptype' => 'enum',
                'type' => 'text',
                // Needs labels...
                'validvalues' => array (
                    'Validation Failed', 'Validation Passed', 'Pending Activation', 'queued', 'Subscribed'
                ),
                '_validivalues' => array ('F', 'T', 'P', 'Q', 'A'),
                'validlabels' => array ( 'IA.VALIDATION_FAILED', 'IA.VALIDATION_PASSED', 'IA.PENDING_ACTIVATION', 'IA.QUEUED', 'IA.SUBSCRIBED' ),
            ),
            'id' => 10,
            'readonly' => false,
        ),

        array(
            'path' => 'AUTOMATE_CHECK_PRINT',
            'fullname' => 'IA.AUTOMATED_CHECK_PRINTING',
            'desc' => 'IA.AUTOMATED_CHECK_PRINTING',
            'type' => $gBooleanType,
            'id' => 11,
        ),
        array(
            'path' => 'AUTOMATE_PYMT_CONF',
            'fullname' => 'IA.AUTOMATED_PAYMENT_CONFIRMATION',
            'desc' => 'IA.AUTOMATED_PAYMENT_CONFIRMATION',
            'type' => $gBooleanType,
            'id' => 12,
        ),
        array(
            // I assume these are uppercase for a reason.
            'fullname' => 'IA.FRACTIONAL_ROUTING_NUMBER',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 15,
            ),
            'desc' => 'IA.FRACTIONAL_ROUTING_NUMBER',
            'path' => 'FRACTIONAL_ROUTING_NO',
            'showlabelalways' => true,
            'id' => 13,
        ),
        array(
            'path'     => 'AUTOMATE_ACH_PYMT_SEND',
            'fullname' => 'IA.AUTOMATED_SENDING_ACH_PAYMENT_REQUEST',
            'type'     => $gBooleanType,
            'id' => 15,
            'readonly' => true,
        ),
        array(
            'path'     => 'AUTOMATE_ACH_PYMT_CONF',
            'fullname' => 'IA.AUTOMATED_ACH_PAYMENT_CONFIRMATION',
            'type'     => $gBooleanType,
            'id' => 16,
            'readonly' => true,
        ),
        array(
            'path' => 'AMEXCHECKNUM',
            'fullname' => 'IA.AMEX_CHECK_NUMBER',
            'required' => false,
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 10,
            ),
            'id' => 17,
        ),
        array(
            'path' => 'ABOURL',
            'fullname' => 'IA.ABO_URL',
            'required' => false,
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 500,
            ),
            'id' => 18,
            'readonly' => true,
        ),
        array(
            'path' => 'RSVPCODE',
            'fullname' => 'IA.ACH_ACCOUNT_ENROLLMENT_RSVP_CODE',
            'required' => false,
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 11,
            ),
            'id' => 19,
            'readonly' => true,
        ),
        array(
            'fullname' => 'IA.ABO_FIRST_NAME',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 50,
                'format' => '/^.{1,100}$/'
            ),
            'desc' => 'IA.ABO_FIRST_NAME',
            'path' => 'ABO_FIRST_NAME',
            'showlabelalways' => true,
            'id' => 20,
            'readonly' => true,
        ),
        array(
            'fullname' => 'IA.ABO_MIDDLE_NAME',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 50,
                'format' => '/^.{1,100}$/'
            ),
            'desc' => 'IA.ABO_MIDDLE_NAME',
            'path' => 'ABO_MIDDLE_NAME',
            'showlabelalways' => true,
            'id' => 21,
            'readonly' => true,
        ),
        array(
            'fullname' => 'IA.ABO_LAST_NAME',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 50,
                'format' => '/^.{1,100}$/'
            ),
            'desc' => 'IA.ABO_LAST_NAME',
            'path' => 'ABO_LAST_NAME',
            'showlabelalways' => true,
            'id' => 22,
            'readonly' => true,
        ),
        array(
            'fullname' => 'IA.ABO_PHONE_NUMBER',
            'type' => array ( 'ptype' => 'text', 'type' => 'text', 'maxlength' => 254 ),
            'desc' => 'IA.ABO_PHONE_NUMBER',
            'path' => 'ABO_PHONE_NUMBER',
            'showlabelalways' => true,
            'id' => 23,
            'readonly' => true,
        ),
        array(
            'fullname' => 'IA.ABO_EMAIL_ID',
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 254,
                'format' => '/^.{1,100}$/'
            ),
            'desc' => 'IA.ABO_EMAIL_ID',
            'path' => 'ABO_EMAIL_ID',
            'showlabelalways' => true,
            'id' => 24,
            'readonly' => true,
        ),
        array(
            'path' => 'AMEX_BANK_ACCOUNT_ID',
            'fullname' => 'IA.AMEX_BANK_ACCOUNT_ID',
            'required' => false,
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 100,
            ),
            'id' => 25,
            'readonly' => false,
        ),
        array(
            'path' => 'AMEX_BANK_ACCOUNT_ADDRESS_ID',
            'fullname' => 'IA.AMEX_BANK_ACCOUNT_ADDRESS_ID',
            'required' => false,
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 100,
            ),
            'id' => 26,
            'readonly' => false,
        ),
        array(
            'path' => 'AMEX_BA_CD_RETURN_ADDRESS_ID',
            'fullname' => 'IA.AMEX_BANK_ACCOUNT_CHECK_DELIVERY_RETURN_ADDRESS_ID',
            'required' => false,
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 100,
            ),
            'id' => 27,
            'readonly' => false,
        ),
        array(
            'path' => 'AMEX_BA_ACH_AFFILIATE_ID',
            'fullname' => 'IA.AMEX_BANK_ACCOUNT_ACH_AFFILIATE_ID',
            'required' => false,
            'type' => array (
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 100,
            ),
            'id' => 28,
            'readonly' => false,
        ),
    ),
    'table' => 'opsubscription',
    'printas' => 'IA.OUTSOURCED_PAYMENT_SUBSCRIPTION',
    'pluralprintas' => 'IA.OUTSOURCED_PAYMENT_SUBSCRIPTION',
    'module' => 'co',
    'autoincrement' => 'RECORDNO',
    'vid' => 'RECORDNO',
    'auditcolumns' => true,
);
