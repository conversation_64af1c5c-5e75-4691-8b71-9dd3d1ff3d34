<?php

namespace tests\source\contract;

require_once 'ContractUtil.cls';

use ContractBillingMethodTypes;
use ContractBillingOptions;
use BaseServicePeriodHandler;
use ContractBillingScheduleServicePeriodHandler;
use ContractBillingScheduleCreateParams;
use ContractScheduleEntry;

use Globals;
use PHPUnit\Framework\TestCase;

class ServicePeriodHandlerTest extends TestCase
{
    protected function setUp() : void
    {
        Globals::$g->gErr->Clear();
    }

    /**
     * @dataProvider dataProviderTestServicePeriodStaticValidate
     */
    public function testServicePeriodStaticValidate(string|null $servicePeriodStartDate, string|null $servicePeriodEndDate,
                                                    bool $expectedResult, string $expectErrorCode = null)
    {
        $result = BaseServicePeriodHandler::validate($servicePeriodStartDate, $servicePeriodEndDate);
        $iaError = Globals::$g->gErr->getLastIAError();
        $actual = [
            $result,
            $iaError ? $iaError->getId() : null
        ];
        $expected = [$expectedResult, $expectErrorCode];
        $this->assertEquals($expected, $actual,
                            "Test service period [$servicePeriodStartDate, $servicePeriodEndDate], expecting [ " .
                            var_export($expected, true) . " ] but got [ " . var_export($actual, true) . " ]");
    }

    public function dataProviderTestServicePeriodStaticValidate() : array
    {
        return [
            [ '01/01/2024', '01/31/2024', true ],
            [ '01/16/2024', '01/16/2024', true ],
            [ '02/01/2024', '01/31/2024', false, 'UGI-0117' ],
            [ '02/01/2024', null, false, 'UGI-0118' ],
            [ '', '2/29/2024', false, 'UGI-0118' ],
            [ '---', '01/31/2024', false, 'UGI-0119' ],
            [ '01/01/2024', '---', false, 'UGI-0120' ],
        ];
    }

    /**
     * @param ContractScheduleEntry[] $entries
     * @param ContractBillingScheduleCreateParams $createParames
     * @param array $expected
     *
     * @dataProvider dataProviderTestPopulateBillingScheduleServicePeriodDates
     */
    public function testPopulateBillingScheduleServicePeriodDates(
        array $entries, ContractBillingScheduleCreateParams $createParames, array $expected)
    {
        $handler = new ContractBillingScheduleServicePeriodHandler($entries, $createParames);
        $handler->populateServicePeriodDates();
        $actual = array_map('self::convertActual', $entries);
        $this->assertEquals($actual, $expected, self::getAssertErrorMessage($createParames) .
                                                 " expecting [ " . var_export($expected, true) .
                                                 " ] but got [ " . var_export($actual, true) . " ]");
    }

    private static function getAssertErrorMessage(ContractBillingScheduleCreateParams $createParams) : string
    {
        $msg = $createParams->getBillingMethod() . " | " . $createParams->getTemplateName() .
               " | line: " . $createParams->getLineStartDate() . "-" . $createParams->getLineEndDate() .
               " | billing sch: " . $createParams->getStartDate() . "-" . $createParams->getEndDate();
        $advBillBy = $createParams->getAdvancedBillBy();
        if ($advBillBy) {
            $msg .= " | BIA $advBillBy " . $createParams->getAdvancedBillByType();
        }
        return $msg;
    }

    public function dataProviderTestPopulateBillingScheduleServicePeriodDates() : array
    {
        $tempOneTime = ContractBillingOptions::ONE_TIME;
        $tempIWEI = ContractBillingOptions::INCLUDE_WITH_EVERY_INVOICE;
        $billMethodFixedPrice = ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_FIXED_PRICE;
        $billMethodQtyBased = ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_QTY_BASED;

        $servicePeriodsMonthlyFromJan012024 = [
            ['01/01/2024', '01/31/2024'], ['02/01/2024', '02/29/2024'], ['03/01/2024', '03/31/2024'],
            ['04/01/2024', '04/30/2024'], ['05/01/2024', '05/31/2024'], ['06/01/2024', '06/30/2024'],
            ['07/01/2024', '07/31/2024'], ['08/01/2024', '08/31/2024'], ['09/01/2024', '09/30/2024'],
            ['10/01/2024', '10/31/2024'], ['11/01/2024', '11/30/2024'], ['12/01/2024', '12/31/2024'],
        ];
        // Test data columns:
        // billing method, billing option or template name, line start, line end, bill sch start, bill sch end, prorate partial periods, billing frequency
        // billing schedule entry scheduled posting dates, expected result
        $rawTestData = [
            [   // Fixed price / One-time
                $billMethodFixedPrice, $tempOneTime, '01/01/2024', '12/31/2024', null, null, null, null,
                ['01/01/2024'], [['01/01/2024', '12/31/2024']]
            ],
            [   // Fixed price / One-time / BIA 1 month
                $billMethodFixedPrice, $tempOneTime, '01/01/2024', '12/31/2024', '12/01/2023', '11/30/2024', null, null,
                ['12/01/2023'], [['01/01/2024', '12/31/2024']]
            ],
            [   // Fixed price / One-time /  BIA 15 days
                $billMethodFixedPrice, $tempOneTime, '01/01/2024', '12/31/2024', '12/17/2023', '12/16/2024', null, null,
                ['12/17/2023'], [['01/01/2024', '12/31/2024']]
            ],
            [   // Qty based / One-time /  BIA 1 month with different GL posting date
                $billMethodQtyBased, $tempOneTime, '01/01/2024', '12/31/2024', '12/01/2023', '11/30/2024', null, null,
                ['12/15/2023'], [['01/01/2024', '12/31/2024']]
            ],
            [   // Fixed price / Include with every invoice / monthly starts on January EOM
                $billMethodFixedPrice, $tempIWEI, '01/31/2024', '01/30/2025', null, null, null, null,
                [
                    '01/31/2024', '02/29/2024', '03/31/2024', '04/30/2024', '05/31/2024', '06/30/2024',
                    '07/31/2024', '08/31/2024', '09/30/2024', '10/31/2024', '11/30/2024', '12/31/2024'
                ],
                [
                    ['01/31/2024', '02/28/2024'], ['02/29/2024', '03/30/2024'], ['03/31/2024', '04/29/2024'],
                    ['04/30/2024', '05/30/2024'], ['05/31/2024', '06/29/2024'], ['06/30/2024', '07/30/2024'],
                    ['07/31/2024', '08/30/2024'], ['08/31/2024', '09/29/2024'], ['09/30/2024', '10/30/2024'],
                    ['10/31/2024', '11/29/2024'], ['11/30/2024', '12/30/2024'], ['12/31/2024', '01/30/2025']
                ]
            ],
            [   // Fixed price / Include with every invoice / monthly
                $billMethodFixedPrice, $tempIWEI, '01/01/2024', '12/31/2024', null, null, null, null,
                [
                    '01/01/2024', '02/01/2024', '03/01/2024', '04/01/2024', '05/01/2024', '06/01/2024',
                    '07/01/2024', '08/01/2024', '09/01/2024', '10/01/2024', '11/01/2024', '12/01/2024'
                ],
                $servicePeriodsMonthlyFromJan012024
            ],
            [   // Fixed price / Include with every invoice / prorate partial periods / monthly starts on mid month
                $billMethodFixedPrice, $tempIWEI, '01/25/2024', '12/15/2024', null, null, 'true', 'Monthly',
                [
                    '01/25/2024', '02/01/2024', '03/01/2024', '04/01/2024', '05/01/2024', '06/01/2024',
                    '07/01/2024', '08/01/2024', '09/01/2024', '10/01/2024', '11/01/2024', '12/01/2024'
                ],
                [
                    ['01/25/2024', '01/31/2024'], ['02/01/2024', '02/29/2024'], ['03/01/2024', '03/31/2024'],
                    ['04/01/2024', '04/30/2024'], ['05/01/2024', '05/31/2024'], ['06/01/2024', '06/30/2024'],
                    ['07/01/2024', '07/31/2024'], ['08/01/2024', '08/31/2024'], ['09/01/2024', '09/30/2024'],
                    ['10/01/2024', '10/31/2024'], ['11/01/2024', '11/30/2024'], ['12/01/2024', '12/15/2024'],
                ],
            ],
            [   // Fixed price / Include with every invoice / quarterly
                $billMethodFixedPrice, $tempIWEI, '01/01/2024', '12/31/2024', null, null, null, null,
                [ '01/01/2024', '04/01/2024', '07/01/2024', '10/01/2024' ],
                [
                    ['01/01/2024', '03/31/2024'], ['04/01/2024', '06/30/2024'], ['07/01/2024', '09/30/2024'],
                    ['10/01/2024', '12/31/2024']
                ]
            ],
            [   // Fixed price / Include with every invoice / quarterly
                $billMethodFixedPrice, $tempIWEI, '11/29/2023', '03/30/2026', null, null, null, null,
                [ '11/29/2023', '02/29/2024', '05/29/2024', '08/29/2024', '11/29/2024', '02/28/2025',
                  '05/29/2025', '08/29/2025', '11/29/2025', '02/28/2026' ],
                [
                    ['11/29/2023', '02/28/2024'], ['02/29/2024', '05/28/2024'], ['05/29/2024', '08/28/2024'],
                    ['08/29/2024', '11/28/2024'], ['11/29/2024', '02/27/2025'], ['02/28/2025', '05/28/2025'],
                    ['05/29/2025', '08/28/2025'], ['08/29/2025', '11/28/2025'], ['11/29/2025', '02/27/2026'],
                    ['02/28/2026', '03/30/2026'],
                ]
            ],
            [   // Fixed price / Include with every invoice / quarterly / BIA 1 month
                $billMethodFixedPrice, $tempIWEI, '09/29/2024', '09/28/2025', '08/29/2024', '08/28/2025', null, null,
                [ '09/29/2024', '11/29/2024', '02/28/2025', '05/29/2025' ],
                [
                    ['09/29/2024', '12/28/2024'], ['12/29/2024', '03/28/2025'], ['03/29/2025', '06/28/2025'],
                    ['06/29/2025', '09/28/2025']
                ]
            ],
            [   // Fixed price / Include with every invoice / annually
                $billMethodFixedPrice, $tempIWEI, '01/01/2024', '12/31/2028', null, null, null, null,
                [ '01/01/2024', '01/01/2025', '01/01/2026', '01/01/2027', '01/01/2028' ],
                [
                    ['01/01/2024', '12/31/2024'], ['01/01/2025', '12/31/2025'], ['01/01/2026', '12/31/2026'],
                    ['01/01/2027', '12/31/2027'], ['01/01/2028', '12/31/2028']
                ]
            ],
            [   // Fixed price / Include with every invoice / monthly / BIA 1 month
                $billMethodFixedPrice, $tempIWEI, '01/01/2024', '12/31/2024', '12/01/2023', '11/30/2024', null, null,
                [
                    '12/01/2023', '01/01/2024', '02/01/2024', '03/01/2024', '04/01/2024', '05/01/2024',
                    '06/01/2024', '07/01/2024', '08/01/2024', '09/01/2024', '10/01/2024', '11/01/2024',
                ],
                $servicePeriodsMonthlyFromJan012024
            ],
            [   // Fixed price / Include with every invoice / prorate partial periods / monthly starts on last DOM / BIA 1 month
                $billMethodFixedPrice, $tempIWEI, '01/31/2024', '12/14/2024', '12/31/2023', '11/14/2024', 'true', 'Monthly',
                [
                    '12/31/2023', '01/01/2024', '02/01/2024', '03/01/2024', '04/01/2024', '05/01/2024',
                    '06/01/2024', '07/01/2024', '08/01/2024', '09/01/2024', '10/01/2024', '11/01/2024'
                ],
                [
                    ['01/31/2024', '01/31/2024'], ['02/01/2024', '02/29/2024'], ['03/01/2024', '03/31/2024'],
                    ['04/01/2024', '04/30/2024'], ['05/01/2024', '05/31/2024'], ['06/01/2024', '06/30/2024'],
                    ['07/01/2024', '07/31/2024'], ['08/01/2024', '08/31/2024'], ['09/01/2024', '09/30/2024'],
                    ['10/01/2024', '10/31/2024'], ['11/01/2024', '11/30/2024'], ['12/01/2024', '12/14/2024'],
                ],
            ],
            [   // Fixed price / Include with every invoice / quarterly / BIA 1 month / Overridden first GL posting date
                $billMethodFixedPrice, $tempIWEI, '01/01/2024', '12/31/2024', '12/01/2023', '11/30/2024', null, null,
                [
                    '11/15/2023', '01/01/2024', '02/01/2024', '03/01/2024', '04/01/2024', '05/01/2024',
                    '06/01/2024', '07/01/2024', '08/01/2024', '09/01/2024', '10/01/2024', '11/01/2024',
                ],
                $servicePeriodsMonthlyFromJan012024
            ],
            [   // Fixed price / 3-months-0,1,2
                $billMethodFixedPrice, '3-months-0,1,2', '01/01/2024', '12/31/2024', null, null, null, null,
                [ '01/01/2024', '02/01/2024', '03/01/2024' ],
                [ ['01/01/2024', '01/31/2024'], ['02/01/2024', '02/29/2024'], ['03/01/2024', '12/31/2024'] ]
            ],
            [   // Fixed price / 24-months
                $billMethodFixedPrice, '24-months', '01/01/2024', '12/31/2024', null, null, null, null,
                [ '01/01/2024', '02/01/2024', '03/01/2024' ],
                [ ['01/01/2024', '01/31/2024'], ['02/01/2024', '02/29/2024'], ['03/01/2024', '12/31/2024'] ]
            ],
            [   // Fixed price / 12-months-4-quarters-0,2,5,8 / BIA 1 month
                $billMethodFixedPrice, '12-months-4-quarters-0,2,5,8', '01/01/2024', '12/31/2024', '12/01/2023', '11/30/2024', null, null,
                [ '12/01/2023', '02/01/2024', '05/01/2024', '08/01/2024' ],
                [
                    ['01/01/2024', '02/29/2024'], ['03/01/2024', '05/31/2024'], ['06/01/2024', '08/31/2024'],
                    ['09/01/2024', '12/31/2024'],
                ]
            ],
            [   // Fixed price / 12-months-4-quarters-2,5,8,11 / BIA 1 month
                $billMethodFixedPrice, '12-months-4-quarters-2,5,8,11', '01/31/2024', '01/30/2025', '12/31/2023', '12/30/2024', null, null,
                [ '12/31/2023', '05/31/2024', '08/31/2024', '11/30/2024' ],
                [
                    ['01/31/2024', '06/29/2024'], ['06/30/2024', '09/29/2024'], ['09/30/2024', '12/30/2024'],
                    ['12/31/2024', '01/30/2025'],
                ]
            ],
            [   // Fixed price / 3-months-0,2,3 / BIA 15 days
                $billMethodFixedPrice, '3-months-0,2,3', '01/01/2024', '12/31/2024', '12/17/2023', '12/16/2024', null, null,
                [ '12/17/2023', '02/17/2024', '03/17/2024' ],
                [ ['01/01/2024', '02/29/2024'], ['03/01/2024', '03/31/2024'], ['04/01/2024', '12/31/2024'] ]
            ],
            [   // Fixed price / 3-months-0,2,3 / Override GL posting, billing start & end dates - effective BIA 1 day
                $billMethodFixedPrice, '3-months-0,2,3', '01/01/2024', '12/31/2024', '12/31/2023', '01/30/2025', null, null,
                [ '12/17/2023', '02/29/2024', '03/31/2024' ],
                [ ['01/01/2024', '02/29/2024'], ['03/01/2024', '03/31/2024'], ['04/01/2024', '12/31/2024'] ]
            ],
            [   // Fixed price / 3-months-0,2,3 / BIA 15 days / Line end < billing end
                $billMethodFixedPrice, '3-months-0,2,3', '01/01/2024', '03/15/2024', '12/17/2023', '03/31/2024', null, null,
                [ '12/17/2023', '02/17/2024', '03/17/2024' ],
                [ ['01/01/2024', '02/29/2024'], ['03/01/2024', '03/15/2024'], ['03/15/2024', '03/15/2024'] ]
            ],
        ];
        return array_map('self::getTestData', $rawTestData);
    }

    private static function convertEntry(string $postingDate) : ContractScheduleEntry
    {
        return new ContractScheduleEntry("10", "10", "1", $postingDate);
    }

    private static function convertActual(ContractScheduleEntry $actual) : array
    {
        // Filter $actual to keep only these fields
        static $fields = ['POSTINGDATE', 'SERVICEPERIODSTARTDATE', 'SERVICEPERIODENDDATE'];
        return array_intersect_key($actual->getValues(), array_flip($fields));
    }

    private static function convertExpected(string $postingDate, array $servicePeriodDates) : array
    {
        return [
            'POSTINGDATE' => $postingDate,
            'SERVICEPERIODSTARTDATE' => $servicePeriodDates[0],
            'SERVICEPERIODENDDATE' => $servicePeriodDates[1],
        ];
    }

    private static function getTestData(array $values) : array
    {
        $createParams = self::getCreateParamsInstance($values);
        $entries = array_map('self::convertEntry', $values[8]);
        $expected = array_map('self::convertExpected', $values[8], $values[9]);
        return [ $entries, $createParams, $expected ];
    }

    private static function getCreateParamsInstance(array $values) : ContractBillingScheduleCreateParams
    {
        [ $billMethod, $billOptionOrTemplate, $lineStartDate, $lineEndDate, $schStartDate, $schEndDate, $proratePartialPeriods, $billingFreq ] = $values;
        $arrParams = [
            'BILLINGMETHOD' => $billMethod,
            'LINESTARTDATE' => $lineStartDate,
            'LINEENDDATE' => $lineEndDate,
            'STARTDATE' => $schStartDate ?? $lineStartDate,
            'ENDDATE' => $schEndDate ?? $lineEndDate,
            'PRORATEBILLINGPERIOD' => $proratePartialPeriods ?? 'false',
            'BILLINGFREQUENCY' => $billingFreq,
        ];
        return new ContractBillingScheduleCreateParams($arrParams, $billOptionOrTemplate, false);
    }
}