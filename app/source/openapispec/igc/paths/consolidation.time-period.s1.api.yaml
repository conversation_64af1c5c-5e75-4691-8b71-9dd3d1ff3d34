openapi: 3.0.0
info:
  title: consolidation-time-period
  description: consolidation.time-period API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Time periods
    description: This object is a reference to a standard month or company-defined accounting period. Each object contains the month name and year, and specifies the first and last day of the period.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/consolidation/time-period:
    get:
      summary: List Time periods
      description: Returns a collection with a key, ID, and link for each Time Period.
      tags:
        - Time periods
      operationId: list-consolidation-time-period
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of time-period objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of Time periods:
                  value:
                    'ia::result':
                      - key: '860'
                        id: Sep 2041
                        href: /objects/consolidation/time-period/860
                      - key: '861'
                        id: Oct 2041
                        href: /objects/consolidation/time-period/861
                      - key: '862'
                        id: Nov 2041
                        href: /objects/consolidation/time-period/862
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 3
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  /objects/consolidation/time-period/{key}:
    parameters:
      - name: key
        description: system-assigned unique key for the Time Period.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a/an Time Period
      description: Returns detailed information for a particular Time Period.
      tags:
        - Time periods
      operationId: get-consolidation-time-period-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the time-period
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/consolidation-time-period'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the Time Period:
                  value:
                    'ia::result':
                      id: Sep 2021
                      key: '620'
                      periodName: Sep 2021
                      startDate: '2021-09-01'
                      endDate: '2021-09-30'
                      href: /objects/consolidation/time-period/620
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    consolidation-time-period:
      $ref: ../models/consolidation.time-period.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
