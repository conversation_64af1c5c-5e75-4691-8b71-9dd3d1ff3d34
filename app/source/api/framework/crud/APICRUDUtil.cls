<?php

use APIConstants as ACON;
use APIQueryUtil as AQU;
use APIUtil      as AU;

/**
 * APICRUDUtil is a collection of utility functions that are required for
 * processing NextGen API CRUD requests at orchestrator layer
 *
 * <AUTHOR>
 * @copyright Copyright (C)2020 Sage Intacct Corporation, All Rights Reserved
 */
class APICRUDUtil
{
    const EXISTENCE_CHECK_FIELDS  = [ ACON::API_OBJECT_KEY_FIELD_NAME ];
    const CRUD_CFG_DELETE_NON_EXISTENT_BEHAVIOR = 'DELETE_NON_EXISTENT_BEHAVIOR';
    const CRUD_CFG_IDEMPOTENCY_ALLOW_DELETE     = 'IDEMPOTENCY_ALLOW_DELETE';

    /**
     * @param array $request
     * @return bool
     * @throws APIOrchestratorException
     */
    static function isBulkRequest($request)
    {
        return ((array_keys($request) === range(0, count($request) - 1)) && count($request) >= 1);
    }

    /**
     * @param string  $version
     * @param array $extraParams
     *
     * @return bool
     * @throws APIOrchestratorException
     */
    static function isTopOwnedObject($version, $extraParams)
    {
        return (RegistryLoader::getInstance($version)->getTypeByName(RegistryLoader::getObjectNamePath(self::getTopObjectName($extraParams), $version))
            === APIConstants::API_OWNED_OBJECT_TYPE);
    }

    /**
     * @param array  $extraParams
     *
     * @return string
     */
    static function getTopObjectName($extraParams)
    {
        if (! $extraParams[ACON::API_EXTRA_PARAM_HIERARCHY][0]) {
            throw (new APIAdapterRecordKeyException())->setAPIError(APIError::getInstance(
                APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0007,[],true));
        }
        return $extraParams[ACON::API_EXTRA_PARAM_HIERARCHY][0];
    }

    /**
     * @param string   $version
     * @param array $extraParams
     *
     * @return SchemaHandler
     * @throws APIOrchestratorException
     */
    public static function getTopObjectHandler($version, $extraParams)
    {
        $topObject = self::getTopObjectName($extraParams);
        $topHandler = $topObject === null ? null : RegistryLoader::getHandlerForVersion($topObject, $version);
        if ( $topHandler === null ) {
            throw (new APIException())->setAPIError(APIError::getInstance(
                APIErrorMessages::NOT_FOUND_NOT_FOUND_0010, [ "RESOURCE" => $extraParams[APIConstants::API_EXTRA_PARAM_REQ][APIConstants::API_EXTRA_PARAM_REQ_URL]], true));
        }

        return $topHandler;
    }


    /**
     * Get bulk size setting. Use max size if
     * 1. not explicitly set
     * 2. size is invalid
     * 3. size exceeds max size
     *
     * @param array $extraParams
     *
     * @return int
     */
    static function getBulkSetting($extraParams)
    {
        // todo - create extra param class
        $bulkSizeSetting = intval(($extraParams[APIConstants::API_EXTRA_PARAM_REQ][APIConstants::API_EXTRA_PARAM_REQ_BULK_SIZE])
                                  ?? ACON::API_BULK_SIZE_DEFAULT_MAX_VALUE);
        return ($bulkSizeSetting <= 0 || $bulkSizeSetting > ACON::API_BULK_SIZE_DEFAULT_MAX_VALUE)?
            ACON::API_BULK_SIZE_DEFAULT_MAX_VALUE : $bulkSizeSetting;
    }


    /**
     * Get default bulk transaction atomicity setting
     * Default to false if not explicitly set to true
     *
     * @param array $extraParams
     *
     * @return bool
     */
    static function getAtomicitySetting($extraParams)
    {
        $atomicitySetting = $extraParams[APIConstants::API_EXTRA_PARAM_REQ][APIConstants::API_EXTRA_PARAM_REQ_TRANSACTION] ?? null;

        return ($atomicitySetting != null) &&
                (strtolower($atomicitySetting) == 'true'
                || strtolower($atomicitySetting) == 't'
                || $atomicitySetting === true);
    }

    /**
     * Get [REST_API_CRUD] setting in ia_init.cfg
     *
     * @param  string $propertyName
     * @return mixed|null
     */
    public static function getCRUDCfgProperty(string $propertyName)
    {
        $asyncConf = GetValueForIACFGProperty(ia_cfg::REST_API_CRUD);
        return $asyncConf[$propertyName] ?? null;
    }

    /**
     * The behavior of deleting nonexistent is configurable in ia_init.cfg [REST_API_CRUD] section
     * Since 404 will lead to entire atomic transaction, client may want to force it as 204
     *
     * @return bool
     */
    public static function isDeleteNonexistentForce204(): bool
    {
        $behavior = self::getCRUDCfgProperty(self::CRUD_CFG_DELETE_NON_EXISTENT_BEHAVIOR) ?? '204';
        return $behavior === '204';
    }

    /**
     * By default, idempotency playback does not DELETE method since DELETE is already an idempotent method
     * However 204 or 404 is a controversial topic for deleting a nonexistent record.
     *
     * When idempotency playback is allowed, delete behavior is explicitly recorded and managed
     *
     * @return bool
     */
    public static function isIdempotencyAllowDeletePlayback(): bool
    {
        $behavior = self::getCRUDCfgProperty(self::CRUD_CFG_IDEMPOTENCY_ALLOW_DELETE) ?? false;
        return $behavior;
    }


    /**
     * Construct hierarchy parameters from a path
     *
     * hierarchy is similar to a tree, the hierarchy layer counts from the first element after "/objects/"
     * for example,
     * objects/glbatch ===> count == 1
     * objects/glentry ===> count == 1
     *    the above two examples have only one top node (regardless the type of the objects) specified in the path
     * objects/glentry/456 ===> count == 2
     *    2 layers are glentry and 456 where glentry is the object class and 456 is the object instance
     * objects/glbatch/123/glentry ===> count == 3
     *    3 layers are glbatch, 123, and glentry
     *
     * @param string $operation
     * @param array $extraParams
     *
     * @return array
     */
    static function initHierarchyParams($operation, &$extraParams)
    {
        $hierarchyParams = null;
        if (empty($extraParams[ACON::API_EXTRA_PARAM_HIERARCHY])) {
            throw (new APIException())->setAPIError(APIError::getInstance(
                APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0003,[], true));
        }

        for ($i=0; $i<count($extraParams[ACON::API_EXTRA_PARAM_HIERARCHY]); $i++) {
            if (($extraParams[ACON::API_EXTRA_PARAM_HIERARCHY][$i]
                = trim($extraParams[ACON::API_EXTRA_PARAM_HIERARCHY][$i])) === '') {
                break;
            }
            $hierarchyParams[] = $extraParams[ACON::API_EXTRA_PARAM_HIERARCHY][$i];
        }

        if (! count($hierarchyParams)) {
            throw (new APIException())->setAPIError(APIError::getInstance(
                APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0003,[], true));
        }

        // check if GET and DELETE for /objects has keys as query strings
        if ( $extraParams[APIConstants::API_EXTRA_PARAM_REQ][APIConstants::API_EXTRA_PARAM_REQ_OBJECT] === 'objects'
             && ( $operation === ACON::API_OPERATION_READ || $operation === ACON::API_OPERATION_DELETE )
             && $extraParams[APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][APIConstants::API_URI_QUERY_PARAMS][APIConstants::API_QUERY_STRING_KEYS] ?? null) {
            // convert the query string to path params
            $hierarchyParams[] = $extraParams[APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][APIConstants::API_URI_QUERY_PARAMS][APIConstants::API_QUERY_STRING_KEYS];
            unset($extraParams[APIConstants::API_EXTRA_PARAM_CONTROL_PARAM][APIConstants::API_URI_QUERY_PARAMS][APIConstants::API_QUERY_STRING_KEYS]);
        }
        $extraParams[ACON::API_EXTRA_PARAM_HIERARCHY] = $hierarchyParams;
        return $hierarchyParams;
    }


    /**
     * Clean up the hierarchy parameters
     *
     * @param array $extraParams
     *
     * @return array
     */
    static function getHierarchyParams(&$extraParams)
    {
        if (!empty($extraParams[ACON::API_EXTRA_PARAM_HIERARCHY])) {
            return $extraParams[ACON::API_EXTRA_PARAM_HIERARCHY];
        }
        throw (new APIException())->setAPIError(APIError::getInstance(
            APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0003,[], true));
    }

    /**
     * return path to ancestor of the leaf object
     * works only for object service URI path format
     *
     * @param  array $extraParams
     * @return string
     */
    static function getAncestorPath(array $extraParams): string
    {
        $depth = count($extraParams[APIConstants::API_EXTRA_PARAM_HIERARCHY] ??[]);
        if (($numOfObject = intval(($depth+1)/2)) < 2) {
            return '';
        }
        return implode('/', array_slice($extraParams[APIConstants::API_EXTRA_PARAM_HIERARCHY], 0, ($numOfObject-1) * 2));
    }


    /**
     * @param string $object
     * @param string $operation
     * @param array  $extraParams
     * @return APILock|null
     * @throws APIOrchestratorException
     */
    static function getLock($object, $operation, $extraParams)
    {
        $isLockEnabled = self::isLockEnabled($extraParams);
        $sender_id = self::getControlSetting($extraParams, ACON::API_EXTRA_PARAM_CONTROL_PARAM_SENDER_ID);
        if (($isLockEnabled == "true" || $isLockEnabled == 't') && $sender_id != '') {
            $controlId = self::getRequestSetting($extraParams, ACON::API_EXTRA_PARAM_REQ_CONTROL_ID, null);
            if ($controlId == null) {
                throw (new APIException())->setAPIError(APIError::getInstance(
                    APIErrorMessages::BAD_REQUEST_INVALID_CONFIGURATION_0001,[], true));
            }
            return APIReqControlService::getLock($controlId, $sender_id, $object, $operation);
        }
        return null;
    }

    /**
     * @param  array $extraParams
     * @return bool
     * @throws APIOrchestratorException
     */
    static function isLockEnabled($extraParams)
    {
        $config = strtolower(self::getRequestSetting($extraParams, ACON::API_EXTRA_PARAM_REQ_UNIQUE_ID, 'false'));
        if ($config == 'true' || $config == 't') {
            return true;
        }
        return false;
    }

    /**
     * A generic method for getting header params
     *
     * @param array  $extraParams
     * @param string $header
     * @param mixed  $default
     * @return mixed
     */
    static function getRequestSetting($extraParams, $header, $default = null)
    {
        return ($extraParams[ACON::API_EXTRA_PARAM_REQ][$header]) ?? $default;
    }

    /**
     * A generic method for getting control params
     *
     * @param array $extraParams
     * @param string $key
     *
     * @return mixed|string
     */
    static function getControlSetting($extraParams, $key)
    {
        // todo - create extra param class
        return ($extraParams[ACON::API_EXTRA_PARAM_CONTROL_PARAM][$key]) ?? '';
    }


    /**
     * build outer wrapper for the inner most item object
     * todo - maybe should not inserting the inner most content yet, will revisit later if necessary
     *
     * @param string $operation
     * @param string $version
     * @param array  $innerContent
     * @param array  $extraParams
     *
     * @return array|null
     * @throws APIAdapterException
     * @throws APIException
     * @throws APIOrchestratorException
     * @noinspection OnlyWritesOnParameterInspection
     */
    static function getNestedOwnerWrapper($operation, $version, $innerContent, &$extraParams) {
        $hierarchyParams = self::getHierarchyParams($extraParams);
        if (count($hierarchyParams) < 3) {
            // no item layers specified in hierarchy parameters
            return $innerContent;
        } else {
            $handler = RegistryLoader::getInstance($version)->getSchemaHandler($hierarchyParams[0]);
        }

        $totalLayers = intdiv(count($hierarchyParams) + (count($hierarchyParams) % 2), 2);

        $current = [];
        $top = [&$current];
        for ($i=0; $i<$totalLayers-1; $i++) {
            $current[ACON::API_OBJECT_KEY_FIELD_NAME] = $hierarchyParams[$i*2 + 1];
            $itemEntityName = '';
            $itemWrapperName = self::getItemWrapperName($handler, $hierarchyParams[$i * 2 + 2], $itemEntityName);
            $itemWrapper = [];
            $current[$itemWrapperName] = &$itemWrapper;
            $current = &$itemWrapper;
        }

        if ($top !== null && ($topObject = self::getTopObjectName($extraParams))) {
            $registry = RegistryLoader::getInstance($version);
            $topHandler = $registry->getSchemaHandler($topObject);
            $topAdapter = $topHandler->getAdapter();
            $convertedWrappers = AU::convertApi2EntCollection(
                ObjectAPIAdapter::CALLER_API, $operation, $topObject, $version,
                $top, $topHandler, $topAdapter);
            $extraParams[ACON::API_EXTRA_PARAM_HIERARCHY_WRAPPER] = $convertedWrappers;
        }
        return $innerContent;
    }

    /**
     * build outer wrapper for the inner most item object
     * @param string $version
     * @param array  $innerContent
     * @param array  $extraParams
     *
     * @return array|null
     * @throws APIAdapterException
     * @throws APIException
     * @throws APIOrchestratorException
     * @noinspection OnlyWritesOnParameterInspection
     */
    static function addOwnerWrapper($version, $innerContent, &$extraParams) {
        // ========================================
        //  get exploded request URI to detect if a wrapper is needed
        $hierarchyParams = self::getHierarchyParams($extraParams);
        if (count($hierarchyParams) < 3) {
            // no item layers specified in hierarchy parameters
            return $innerContent;
        } else {
            $handler = RegistryLoader::getInstance($version)->getSchemaHandler($hierarchyParams[0]);
        }

        // ========================================
        // for CRUD services, assuming each object layer in URI will come with two elements
        //   - object name
        //   - object key (may or may not present for the leaf object
        $totalLayers = ceil( count($hierarchyParams) / 2 );

        // ========================================
        // recursively build the wrapper layers starting from the top object
        $outterWrapperPath = '';
        $current = [];
        $top = [&$current];
        for ($i=0, $parentCount=$totalLayers-1; $i<$parentCount; $i++) {
            $current[ACON::API_OBJECT_KEY_FIELD_NAME] = $hierarchyParams[$i*2 + 1];
            $itemObjectName = '';
            $itemWrapperName = self::getItemWrapperName($handler, $hierarchyParams[$i * 2 + 2], $itemObjectName);
            $itemWrapper = [];
            $current[$itemWrapperName] = &$itemWrapper;
            $outterWrapperPath = '/' . $itemWrapperName . $outterWrapperPath;
            $current = &$itemWrapper;
        }
        /** @noinspection PhpUnusedLocalVariableInspection */
        $itemWrapper = $innerContent;
        $extraParams[APIConstants::API_EXTRA_PARAM_HIERARCHY_WRAPPER_PATH] = $outterWrapperPath;
        return $top;
    }


    /**
     * @param SchemaHandler $currentHandler
     * @param string        $itemObjName
     * @param string        $itemEntityName
     *
     * @return string
     */
    static function getItemWrapperName(&$currentHandler, $itemObjName, &$itemEntityName) {

        $apiName = $currentHandler->getAPIListName($itemObjName);
        $entField = $currentHandler->getEntityField($apiName);
        if ( $entField instanceof ListFieldHandler ) {
            $currentHandler = $currentHandler->getRelatedHandler($entField->getObjectName());
            $itemEntityName = $currentHandler->getMappedToName();
        } else {
            throw (new APIException())->setAPIError(APIError::getInstance(
                APIErrorMessages::INTERNAL_SERVER_ERROR_API_SCHEMA_ISSUE_0311,
                ["OWNED_OBJECT_NAME" => $itemObjName], true));
        }
        return $apiName;
    }

    /**
     * @param array $items
     *
     * @return array
     */
    static function getItemKeyArray($items) {
        $keyArray = [];
        foreach ($items as $item) {
            if (key_exists(ACON::API_OBJECT_KEY_FIELD_NAME, $item)) {
                $keyArray[] = $item[ACON::API_OBJECT_KEY_FIELD_NAME];
            }
        }
        return $keyArray;
    }


    /**
     * @param string          $object
     * @param string          $version
     * @param APIHandler|null $handler
     *
     * @return mixed|ObjectAPIAdapter|null
     * @throws APIException
     * @throws APIOrchestratorException
     */
    static function getObjectAdapter($object, $version, &$handler)
    {

        $handler = ($handler)?? RegistryLoader::getInstance($version)->getSchemaHandler($object);
        // create adapter for the execution
        $adapter = $handler instanceof SchemaHandler ? ($handler->getAdapter() ?? $handler->getRequestHandler()->getAdapter()) : null;
        if ( empty($adapter)) {
            throw (new APIException())->setAPIError(APIError::getInstance(
                APIErrorMessages::NOT_FOUND_NOT_FOUND_0011,["VERSION" => $version, "OBJECT" => $object], true));
        }

        return $adapter;
    }


    /**
     * Validate keys existence
     * using named query ACON::API_EXTRA_PARAM_HIERARCHY_VALIDATE_QUERY
     * in $extraParams
     *
     * Caller should only invoke this method when necessary instead of
     * relying on this method to check the prerequisites
     *
     * @param array $request
     * @param array $excludeList
     *
     * @return array
     */
    static function excludeRecord($request, $excludeList)
    {
        return array_filter( $request, function ($v) use ($excludeList) {
            return !in_array($v[ACON::API_OBJECT_KEY_FIELD_NAME], $excludeList, true);
        });
    }

    /**
     * @param array         $request
     * @param SchemaHandler $leafHandler
     * @param array         $extraParam
     */
    static function buildAncestorValidationPayload($request, $leafHandler, &$extraParam, $operation)
    {
        if ( RegistryLoader::$doTimers) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $timer = new ScopeTimer(__CLASS__, false, 'RegistryLoader::logTiming', __METHOD__);
        }
        //validate
        if ($extraParam == null ||
            $leafHandler == null ||
            ($pathParam = $extraParam[ACON::API_EXTRA_PARAM_HIERARCHY] ?? null) == null ||
            ($pCount = count($pathParam)) < 1) {
            return; // no op
        }

        $filterArray = [];
        $currentHandler = $leafHandler;
        $i = ($pCount % 2) ? 2 : 3;
        while ($i<=$pCount && $currentHandler != null) {
            $ownerKey = (string)$pathParam[$pCount-$i];
            $i += 2;
            [$ownerFieldName, $currentHandler] =  self::getOwnerAPIRef($currentHandler);
            $fullFieldName = $ownerFieldName.'.'.ACON::API_OBJECT_KEY_FIELD_NAME;
            $filter = AQU::getEqFilter($fullFieldName, $ownerKey);
            $filterArray[] = $filter;
        }

        $keyList = array_column($request, ACON::API_OBJECT_KEY_FIELD_NAME);
        if ($operation === APIConstants::API_OPERATION_PATCH && empty($keyList)) { // if no keys are provided for patch, return error
            throw (new APIException())->setAPIError(APIError::getInstance(
                APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0019,['OPERATION' => APIConstants::getOperation($operation)], true));
        }
        $filterArray[] = AQU::getInFilter(ACON::API_OBJECT_KEY_FIELD_NAME, $keyList);

        $extraParam[ACON::API_EXTRA_PARAM_HIERARCHY_VALIDATE_QUERY] =
            AQU::buildSimpleQuery(
                $leafHandler->getName(),
                self::EXISTENCE_CHECK_FIELDS,
                $filterArray,
                ['usemst' => AU::usesCompanySchema($leafHandler),] /* perf enhancement for non-global schemas */);
    }

    /**
     * return true if request path is <{version}/objects/{itemObject}/key> and
     * where the manager of {itemObject} does not support direct CRUD operations
     *
     * @param SchemaHandler $handler
     * @param array         $extraParams
     * @return bool
     */
    static function canGroup($handler, $extraParams)
    {
        //TODO: Roy to verify this for nested Top owned objects
        //return self::isTopOwnedObject($handler->getRegistryVersion(), $extraParams)
        return $handler->isOwnedType()
            && self::isSingleLayerObjectHierarchy($extraParams)
            && !$handler->isDirectAccessItemObject();
    }

    /**
     * Get the struct path to parent key
     *
     * @param string $version
     * @param array $extraParam
     *
     * @return string // parent key field dot noted path
     * @throws APIOrchestratorException
     */
    static function getParentKeyPath($version, &$extraParam)
    {
        $ownerFieldName = self::getLeafHandler($version, $extraParam)->getOwnerFieldName();
        return $ownerFieldName.'.'.ACON::API_OBJECT_KEY_FIELD_NAME;
    }

    /**
     * Build query for getting parent keys for each item request in a bulk
     * return parent key field name
     * (while child key field name is "key" as NxG API convention)
     *
     * @param string $version
     * @param array  $request
     * @param string $parentKeyFieldName parent key field dot noted path
     * @param array  $query
     * @param array  $extraParam
     *
     * @throws APIOrchestratorException
     */
    static function buildParentChildKeyQuery(string $version, array $request, string &$parentKeyFieldName, array &$query, array $extraParam)
    {
        //validate
        $leafHandler = self::getLeafHandler($version, $extraParam);
        if ($extraParam == null ||
            $leafHandler == null ||
            ($pathParam = $extraParam[ACON::API_EXTRA_PARAM_HIERARCHY] ?? null) == null ||
            count($pathParam) < 1) {
            throw (new APIException())->setAPIError(APIError::getInstance(
                APIErrorMessages::INTERNAL_SERVER_ERROR_API_LAYER_ISSUE_0008,[], true));
        }

        $ownerFieldName = $leafHandler->getOwnerFieldName();
        $parentKeyFieldName = $ownerFieldName.'.'.ACON::API_OBJECT_KEY_FIELD_NAME;
        $fieldList[] = $parentKeyFieldName;
        $fieldList[] = ACON::API_OBJECT_KEY_FIELD_NAME;

        $keyList = [];
        foreach ($request as $record) {
            if (key_exists(ACON::API_OBJECT_KEY_FIELD_NAME, $record)) {
                $keyList[] = $record[ACON::API_OBJECT_KEY_FIELD_NAME];
            }
        }
        $filterArray[] = AQU::getInFilter(ACON::API_OBJECT_KEY_FIELD_NAME, $keyList);

        $query = AQU::buildSimpleQuery(
                $leafHandler->getName(),
                $fieldList,
                $filterArray,
                ['usemst' => AU::usesCompanySchema($leafHandler),] /* perf enhancement */);
    }

    /**
     * @param SchemaHandler $leafHandler
     * @param array         $extraParam
     *
     * @return array|false
     */
    static function buildAncestorConstrainPayload($leafHandler, $extraParam)
    {
        //validate
        if ($extraParam == null ||
            ($pathParam = $extraParam[ACON::API_EXTRA_PARAM_HIERARCHY] ?? null) == null ||
            ($pCount = count($pathParam)) < 1 ||
            $pCount %2 == 0) {
            return false; // no op
        }

        $filterArray = [];
        $currentHandler = $leafHandler;
        $i = 2;
        while ($i<=$pCount && $currentHandler != null) {
            $ownerKey = $pathParam[$pCount-$i++];
            [$ownerFieldName, $currentHandler] =  self::getOwnerAPIRef($currentHandler);
            if (empty($ownerFieldName)) {
                continue;
            }
            $fullFieldName = $ownerFieldName.'.'.ACON::API_OBJECT_KEY_FIELD_NAME;
            $filter = AQU::getEqFilter($fullFieldName, $ownerKey);
            $filterArray[] = $filter;
        }

        return AQU::buildSimpleQuery(
            $leafHandler->getName(),
            $leafHandler->buildFieldListForGetCollection(),
            $filterArray);
    }

    /**
     * @param  array $request
     * @param  string $fieldName
     * @return array
     */
    static function buildFieldToIndexMap($request, $fieldName = ACON::API_OBJECT_KEY_FIELD_NAME)
    {
        $map = [];
        foreach ($request as $index => $record) {
            $map[$record[$fieldName]] = $index;
        }
        return $map;
    }


    /**
     * @param  SchemaHandler $handler
     *
     * @return array [string, ObjectSchemaHandler]
     */
    private static function getOwnerAPIRef($handler)
    {
        $ownerName = $handler->getOwnerFieldName();
        $handler = empty($ownerName) ? null : $handler->getRelatedHandler($handler->getOwnerObjectName());
        return [$ownerName, $handler];
    }

    /**
     * assuming an "objects" type of request, checks if the URI contains leaf object key(s)
     * @param  array $extraParams
     * @return bool
     * @throws APIException
     */
    public static function hasLeafObjectKey($extraParams)
    {
        $hierarchy = self::getHierarchyParams($extraParams);
        $numOfNode = count($hierarchy);
        return $numOfNode % 2 === 0;
    }

    /**
     * @param string      $version
     * @param array       $extraParams
     * @param string|null $operation
     *
     * @return SchemaHandler
     * @throws APIOrchestratorException
     */
    public static function getLeafHandler(string $version, array $extraParams, ?string $operation = null)
    {
        $extraParamsHierarchy = self::getHierarchyParams($extraParams);
        $numOfNode = count($extraParamsHierarchy);
        $leafNodeIndex = $numOfNode - ( ( $numOfNode % 2 == 0 ) ? 2 : 1 );
        $leafNode = $extraParamsHierarchy[$leafNodeIndex] ?? null;
        $leafHandler = $leafNode === null ? null :
            ($operation === null
                ? RegistryLoader::getHandlerForVersion($leafNode, $version)
                : RegistryLoader::getHandlerForVersion($leafNode, $version, APIConstants::API_OPERATION_STANDARD_NAME_MAP[$operation])
            );
        if ( $leafHandler === null ) {
            throw (new APIException())->setAPIError(APIError::getInstance(
                APIErrorMessages::NOT_FOUND_NOT_FOUND_0010, [ "RESOURCE" => $extraParams[APIConstants::API_EXTRA_PARAM_REQ][APIConstants::API_EXTRA_PARAM_REQ_URL] ?? $leafNode], true));
        }

        return $leafHandler;
    }

    /**
     * @param array         $extraParams
     * @param SchemaHandler $handler
     *
     * @return mixed|string|null
     */
    static function getLeafObjectName($extraParams, $handler = null)
    {
        if ( $handler === null ) {
            $hierarchyParams = self::getHierarchyParams($extraParams);
            $numOfNode = count($hierarchyParams);
            $leafNodeIndex = $numOfNode - ( ( $numOfNode % 2 == 0 ) ? 2 : 1 );
            $leafNode = $hierarchyParams[$leafNodeIndex] ?? null;
        } else {
            $leafNode = $handler->getObjectName();
        }

        return $leafNode;
    }

    /**
     * Verifies if CRUD request has a determined parent object
     * This is used for mix-operation patch request which supports only request
     * of child objects that have single determined parent object per request, i.e.
     * the request format has to be objects/parent-object/parent-key/child-object
     * thus the element layer has to be at least 3
     *
     * @param array $extraParams
     *
     * @return bool
     */
    static function hasParentSpecified($extraParams)
    {
        return count(self::getHierarchyParams($extraParams)) >= 3;
    }

    /**
     * returns type if found, otherwise null when no type is found or $objectName is null
     *
     * @param string $objectName the object name extracted from URI
     *
     * @return string|null doc type if found, null otherwise
     */
    static function getDocTypeFromObjectName(string $objectName) : ?string
    {
        return explode("::",$objectName)[1] ?? null;
    }

    /**
     * Substitute top object in hierarchy params.
     * This method expects request URI is in the format of the following for target object
     * {object1}/{object1_key}/{object2}/{object2_key}/ ...
     *
     * @param array $extraParams
     * @param string $currentObject
     * @param string $newObject
     *
     * @return array
     */
    static function replaceOnlyHierarchyObject(&$extraParams, $currentObject, $newObject)
    {
        $originalHierarchy = $extraParams[ACON::API_EXTRA_PARAM_HIERARCHY];
        $newHierarchyParams = (new ArrayObject($originalHierarchy))->getArrayCopy();
        // if only one layer of object is specified in request URI, then count($hierarchyParams) == 1 or 2
        if (count($newHierarchyParams) < 3 && !empty($newHierarchyParams[0]) &&
                ($newHierarchyParams[0] === $currentObject || $newHierarchyParams[0] === RegistryLoader::getShortObjectName($currentObject))) {
            $newHierarchyParams[0] = RegistryLoader::getShortObjectName($newObject);
            if (count($newHierarchyParams) == 2) {
                array_pop($newHierarchyParams);
            }
        }
        $extraParams[ACON::API_EXTRA_PARAM_HIERARCHY] = $newHierarchyParams;
        return $originalHierarchy;
    }

    /**
     * wrap APIError in the exception thrown by internal invocation
     *
     * @param Throwable $t
     * @param array     $messageParameters
     * @param string    $messageCode
     *
     * @throws APIException
     * @throws APIInternalException
     * @throws Throwable
     */
    public static function throwWrappedPreprocessException(
        Throwable $t,
        array     $messageParameters,
        string    $messageCode = APIErrorMessages::UNPROCESSABLE_ENTITY_PREPROCESS_FAILED_0001)
    {
        if ($t instanceof APIException
            && $t->hasAPIError()
            && ($innerAPIError = $t->getAPIError()) !== null
            && ($innerStatus = $innerAPIError->getStatus()) !== 500) {
            // wrapping the inner error and throw the same exception again
            // given it was already handled by the inner class, no need to throw a new exception
            $wrappedError = APIError::getInstance($messageCode, $messageParameters, true)
                ->setOverrideStatus($innerStatus)
                ->addInnerError($innerAPIError);
            $t->setAPIError($wrappedError);
        }
        throw $t;
    }

    /**
     * @param  array $extraParams
     * @return bool
     */
    static function isSingleLayerObjectHierarchy($extraParams)
    {
        return (count($extraParams[ACON::API_EXTRA_PARAM_HIERARCHY]) < 3);
    }

    /**
     * Basic CRUD prerequisite checks.
     * Logic in this method should have been handled by schema check once we have well-defined schemas.
     *
     * @param string $operation request operation
     * @param array|null $request request body
     * @param SchemaHandler $handler has to be handler for leaf object found in request URI
     * @param array $extraParams extra parameters
     * @throws APIException validation error
     */
    static function validateRequestFormat(string $operation, ?array &$request, SchemaHandler $handler, array &$extraParams)
    {
        // advanced CRUD URI element validation
        if ($handler->isDocTypeResource()) {
            // only doc type objects need type validation
            self::validateDocumentObjectType($operation, $handler, $extraParams);
        }

        // checking payload
        if ($operation === ACON::API_OPERATION_READ && !empty($request)) {
            // No input payload is expected for READ operation
            throw (new APIException())->setAPIError(APIError::getInstance(
                APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0003,
                ['OPERATION' => APIConstants::API_OPERATION_STANDARD_NAME_MAP[$operation]], true));
        } else if ($operation === ACON::API_OPERATION_DELETE && !empty($request)) {
            self::allowKeysInDeletePayload($operation, $request, $extraParams);
        }else if (in_array($operation,[ACON::API_OPERATION_PATCH, ACON::API_OPERATION_CREATE], true)) {
            if (empty($request)) {
                // POST & PATCH - input payload is mandated
                throw (new APIException())->setAPIError(APIError::getInstance(
                    APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0004, ['OPERATION' => APIConstants::getOperation($operation)], true));
            } else if (is_array($request)) {
                $key = key($request);
                if (is_int($key) && !is_array($request[$key])) {
                    // found an number indexed non-object element
                    throw (new APIException())->setAPIError(APIError::getInstance(
                        APIErrorMessages::BAD_REQUEST_INVALID_BULK_FORMAT_0001, [], true));
                }
            }// else, having input payload is expected
        }

        // checking valid keys
        $layerCount = count(self::getHierarchyParams($extraParams));
        $hasKeyInURL = (($layerCount) % 2 == 0);
        if (in_array($operation,[ACON::API_OPERATION_READ, ACON::API_OPERATION_DELETE], true) && !$hasKeyInURL) {
            // GET & DELETE - expect keys specified in request URI
            throw (new APIException())->setAPIError(APIError::getInstance(
                APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0016, ["OPERATION" => APIConstants::getOperation($operation)], true));
        } else if ($operation === ACON::API_OPERATION_PATCH && $layerCount < 3) {
            // bulk with parent context (i.e. $layerCount > 3) support mixop, i.e. existence of keys implies the operations
            // otherwise, keys should exist if no parent context specified
            // PATCH - key should be specified
            if (!is_numeric(key($request))) { // body payload is not in bulk format
                // expect key in URI
                if (!$hasKeyInURL) {
                    throw (new APIException())->setAPIError(APIError::getInstance(
                        APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0016, ["OPERATION" => APIConstants::getOperation($operation)], true));
                }
            } else {
                // bulk patch does not expect keys in URL, fail only in atomic mode, let go if non-atomic
                // no keys should be in URI, instead CRUD expecting that every single record should come with a "key"
                if ($hasKeyInURL) {
                    // Bulk patch should not have any key specified in URI
                    throw (new APIException())->setAPIError(APIError::getInstance(
                        APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0018, ["OPERATION" => APIConstants::getOperation($operation)], true));
                }
                if (count($request) !== count(array_column($request, ACON::API_OBJECT_KEY_FIELD_NAME)) ||
                array_intersect([null],array_column($request, ACON::API_OBJECT_KEY_FIELD_NAME))) { //take care of both null & '' scenarios
                    // Bulk patch expect key field in every single record in the request body
                    if (isset($extraParams[APIConstants::API_EXTRA_PARAM_REQ][APIConstants::API_EXTRA_PARAM_REQ_TRANSACTION])) {
                        throw (new APIException())->setAPIError(APIError::getInstance(
                            APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0019, ["OPERATION" => APIConstants::getOperation($operation)], true));
                    }
                }
            }
        } else if ($operation === ACON::API_OPERATION_CREATE) {
            // POST - neither URI nor body should have keys
            if ($hasKeyInURL) {
                throw (new APIException())->setAPIError(APIError::getInstance(
                    APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0005, ['OPERATION' => APIConstants::getOperation($operation)], true));
            }
            if (!is_numeric(key($request))) { // body payload is not in bulk format
                if (key_exists(ACON::API_OBJECT_KEY_FIELD_NAME, $request)) {
                    throw (new APIException())->setAPIError(APIError::getInstance(
                        APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0005, ['OPERATION' => APIConstants::getOperation($operation)], true));
                }
            } else { // bulk patch does not expect keys in URL, fail only in atomic mode, let go if non-atomic
                if (self::getAtomicitySetting($extraParams) &&
                    count(array_column($request, ACON::API_OBJECT_KEY_FIELD_NAME)) > 0) {
                    throw (new APIException())->setAPIError(APIError::getInstance(
                        APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0005, ['OPERATION' => APIConstants::getOperation($operation)], true));
                }
            }
        }
    }

    /**
     * Allow keys in the payload for DELETE, but not GET.
     *
     * For GET operation input payload is not allowed.
     * For DELETE operations, tolerate payload, and extract key values from the request and append them to the URL.
     *
     * @param mixed $operation The operation being performed.
     * @param array $request The request payload.
     * @param array $extraParams Additional parameters for the request.
     *
     * @return array The modified request and extra parameters.
     *
     * @throws APIException If the keys are empty or invalid.
     */
    public static function allowKeysInDeletePayload(mixed $operation, array &$request, array &$extraParams): array
    {
        // GET does not allow input payload,
        // DELETE allows body in list of keys format per IA-200827
        try {
            $keys = self::extractKeyValues($request);
        } catch (InvalidArgumentException $e) {
            logToFileError($e->getMessage());
            throw (new APIException())->setAPIError(APIError::getInstance(
                APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0035, ['FIELD'=>'keys'], true));
        }
        if (empty($keys)) {
            throw (new APIException())->setAPIError(APIError::getInstance(
                APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0003,
                ['OPERATION' => APIConstants::API_OPERATION_STANDARD_NAME_MAP[$operation]], true));
        } else {
            $url = $extraParams[APIConstants::API_EXTRA_PARAM_REQ][APIConstants::API_EXTRA_PARAM_REQ_URL];
            if (!isset($extraParams[ACON::API_EXTRA_PARAM_HIERARCHY]) ||
                !is_array($extraParams[ACON::API_EXTRA_PARAM_HIERARCHY]) ||
                (count($extraParams[ACON::API_EXTRA_PARAM_HIERARCHY]) !== 1 && count($extraParams[ACON::API_EXTRA_PARAM_HIERARCHY]) !== 3)) {
                logToFileError('failed due to possibly both uri and payload have keys specified');
                throw (new APIException())->setAPIError(APIError::getInstance(
                    APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0035, ['FIELD'=>'key'], true));
            }
            $extraParams[APIConstants::API_EXTRA_PARAM_REQ][APIConstants::API_EXTRA_PARAM_REQ_URL] = rtrim($url, '/') . '/' . $keys;
            $extraParams[ACON::API_EXTRA_PARAM_HIERARCHY][] = $keys;
            array_splice($request, 0);
        }
        return array($request, $extraParams);
    }

    /**
     * Extracts key values from the input array of strings
     * Body payload should be an object with only one "keys" field in list of keys format
     * and each key should be in String type per IA-200827, e.g.,
     * {"keys": ["122", "123", "uuid-123", "125", "key-126"]}
     *
     * @param array|null $input Array of strings
     * @return string Updated path string with extracted values appended
     * @throws InvalidArgumentException If input is invalid
     */
    public static function extractKeyValues(?array $input): string {
        if (!is_array($input)) {
            throw new InvalidArgumentException('Input must be an object.');
        }

        if (!isset($input['keys'])) {
            throw new InvalidArgumentException('Input object must have a "keys" field.');
        }

        if (!is_array($input['keys'])) {
            throw new InvalidArgumentException('"keys" field must be an array.');
        }

        if (empty($input['keys'])) {
            throw new InvalidArgumentException('Input array cannot be null or empty.');
        }

        // Validate all values are strings
        if (array_filter($input['keys'], fn($v) => !is_string($v))) {
            throw new InvalidArgumentException('All values in "keys" array must be strings.');
        }

        // Handle path concatenation
        return implode(',', $input['keys']);
    }

    /**
     * advanced CRUD URI element validation
     * note:
     *  - only validats at most two layers of parent child for now
     *  - generic service pattern validations are done in APIDispatcher
     *
     *  - PPD allowed formats:
     *    - header::type/ (validated in CRUD Orchestrator already)
     *    - item::type/
     *    - header::type/key/item::type
     *
     *  - PPD wrong formats:
     *    - header (validated in CRUD Orchestrator already)
     *    - header/key/item
     *    - item/
     *    - header::type/key/item
     *    - header/key/item::type
     *
     *  - All 4 CRUD operations do not allow mismatched types between header and items
     *    - header::type1/key/item::type2
     *
     *
     * @param string        $operation request operation
     * @param SchemaHandler $handler has to be leaf object handler
     * @param array         $extraParams extra parameters
     *
     * @throws APIAdapterRecordKeyException
     * @throws APIException
     * @throws APIInternalException
     */
    public static function validateDocumentObjectType(string $operation, SchemaHandler $handler, array $extraParams) : void
    {
        $leafObjectName = $handler->getObjectName();
        if ( $handler->getType() !== APIConstants::API_OWNED_DOCTYPE_OBJECT_TYPE /* docuement header request */
             || ! self::hasParentSpecified($extraParams) /* direct document item request */) {
            // both scenarios has type validated done when getting leaf handler (which is the 2nd input parameter) in CRUD Orchestrator already
            // thus, no further advanced URI element checking for non-document objects
            return;
        }

        $topObjectName          = self::getTopObjectName($extraParams);
        $leafObjectDocType      = self::getDocTypeFromObjectName($leafObjectName); // note, leaf gurantees an owned document item in this logic block
        $topObjectDocType       = self::getDocTypeFromObjectName($topObjectName); // note, it does not worth the overhead of instaintiating a handler for just extracting doc type

        if ($operation === APIConstants::API_OPERATION_READ /* GET does allow missing doc type, but not mismatch */) {
            if ($topObjectDocType != null && $leafObjectDocType != null && $topObjectDocType !== $leafObjectDocType) {
                // err6: type inconsistency
                throw ( new APIException() )->setAPIError(APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0215, [ "OWNER_DOCTYPE" => $topObjectName, "OWNER_OBJECT_DOCTYPE" => $leafObjectName ], true));
            }
        } else { // PPD has much strict doc type validation rules
            if ( $topObjectDocType === null || $leafObjectDocType === null /* either one or both document header or item has missing type */ ) {
                if ( $leafObjectDocType xor $topObjectDocType ) {
                    // err4, err5: one with type and one w/o type; inconsistency not allowed
                    throw ( new APIException() )->setAPIError(APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0217, [ "OBJECT" => $topObjectDocType === null ? $topObjectName : $leafObjectName ], true));
                } else { // w/ or w/o type, consistency is allowed for GET regardless
                    // err2: both do not have type is not allowed for PPD
                    throw ( new APIException() )->setAPIError(APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0218, [ "OPERATION" => APIConstants::getOperation($operation) ], true));
                }
            } else if ( $topObjectDocType !== $leafObjectDocType ) {
                // err6: type inconsistency
                throw ( new APIException() )->setAPIError(APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0215, [ "OWNER_DOCTYPE" => $topObjectName, "OWNER_OBJECT_DOCTYPE" => $leafObjectName ], true));
            }
        }
    }
}
