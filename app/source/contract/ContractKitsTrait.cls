<?php

/**
 * Manager Trait for Contract Kits
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 Intacct Corporation All, Rights Reserved
 */

trait ContractKitsTrait
{
    // Excluded: RECOGNITION_METHOD_QUANTITYBASED && RECOGNITION_METHOD_REV_REC_ON_INVOICE
    static array $allowedRevenueTemplateMethods = [
        ContractRevenueTemplateManager::RECOGNITION_METHOD_STRAIGHTLINE,
        ContractRevenueTemplateManager::RECOGNITION_METHOD_DAILYRATE,
        ContractRevenueTemplateManager::RECOGNITION_METHOD_PREDEFINEDPCT,
    ];
    
    public static function getItemType(string $itemId): string
    {
        return self::getItemData($itemId)['ITEMTYPE'] ?? '';
    }
    
    public static function getItemName(string $itemId): string
    {
        return self::getItemData($itemId)['NAME'] ?? '';
    }
    
    public static function getItemData(string $itemId): ?array
    {
        static $items = [];
        
        if ($itemId && !array_key_exists($itemId, $items)) {
            $itemDetails = EntityManager::GetListQuick('item', ['ITEMTYPE', 'NAME'], ['ITEMID' => $itemId]);
            $items[$itemId] = $itemDetails[0] ?? [];
        }
        
        return $items[$itemId] ?? [];
    }
    
    /**
     * Retrieves the kit component item data based on the item kit definition
     */
    public static function getKitComponentItems(string $itemId): array
    {
        static $items = [];
        
        if (!array_key_exists($itemId, $items)) {
            $gManagerFactory = Globals::$g->gManagerFactory;
            $mgr = $gManagerFactory->getManager('item');
            
            $items[$itemId] = $mgr->GetKitComponentsValues($itemId) ?? [];
        }
        
        return $items[$itemId];
    }
    
    /**
     * Gets all the kit components lines for a kit line
     */
    private function getKitComponentLines(
        int $kitLineKey,
        array $selects = [],
        bool $includeMEAPct = false,
        bool $includePostedTypes = false
    ): array {
        $params = [
            'filters' => [[['PARENTKEY', '=', $kitLineKey]]],
            'orders' => [['LINENO', 'asc']],
        ];
        
        if (!empty($selects)) {
            if (!in_array('RECORDNO', $selects)) {
                $selects[] = 'RECORDNO';
            }
            $params['selects'] = $selects;
        }
        
        $lines = $this->GetList($params);
        
        // Retrieve the allocation percent
        if ($includeMEAPct && is_array($lines)) {
            $this->populateAllocationPercent($lines);
        }
        
        if ($includePostedTypes) {
            foreach ($lines as &$line) {
                $line['POSTED_TYPES'] = ContractScheduleManager::getPostedScheduleTypes($line['RECORDNO']);
            }
        }
    
        if (!empty($componentLines)) {
            foreach ($lines as &$line) {
                [$line['ITEMID']] = explode('--', $line['ITEMID']);
            }
        }
        
        return is_array($lines) ? $lines : [];
    }
    
    public function getKitComponentLineKeys(int $kitLineKey): array
    {
        $keys = [];
        $lines = $this->getKitComponentLines($kitLineKey, ['RECORDNO'], false);
        if (!empty($lines)) {
            $keys = array_column($lines, 'RECORDNO');
        }
        
        return $keys;
    }
    
    private function getKitComponentLinesForGet(array $kit): array
    {
        $componentLines = [];
        
        $kitKey = $kit['RECORDNO'];
        if (!empty($kitKey)) {
            $componentLines = $this->getKitComponentLines(
                $kitKey,
                [
                    'RECORDNO',
                    'LINENO',
                    'ITEMID',
                    'ITEMNAME',
                    'QUANTITY',
                    'DELIVERYSTATUS',
                    'REVENUESTARTDATE',
                    'REVENUEENDDATE',
                    'REVENUESCHEDULESTATUS',
                    'REVENUETEMPLATENAME',
                    'REVENUESCHEDULEKEY',
                    'REVENUE2STARTDATE',
                    'REVENUE2ENDDATE',
                    'REVENUE2SCHEDULESTATUS',
                    'REVENUE2TEMPLATENAME',
                    'REVENUE2SCHEDULEKEY',
                    'REVENUEHOLDDATE',
                    'EXPENSEHOLDDATE',
                    'BILLINGHOLDDATE',
                    'CANCELDATE',
                    'STATE',
                ],
                true,
                true
            );
        }
        
        return $componentLines;
    }
    
    /**
     * @param int $cnDetailId
     *
     * @return float|null
     */
    public function getAllocationPercent($cnDetailId)
    {
        $tempLines = [['RECORDNO' => $cnDetailId]];
        $this->populateAllocationPercent($tempLines);
        return $tempLines[0]['REVPERCENT'] ?? null;
    }
    
    /**
     * @param array $componentLines
     * @param bool  $isRenewal
     *
     * @return void
     */
    public function populateAllocationPercent(&$componentLines, $isRenewal = false)
    {
        if (!empty($componentLines)) {
            if ($isRenewal) {
                // For renewals, get the original allocation percents
                $key = ':recordNumber';
            } else {
                $key = 'RECORDNO';
            }
            $allocations = $this->getKitAllocationDetail(array_column($componentLines, $key), ['CONTRACTDETAILKEY', 'MEAPERCENT']);
            $allocationMap = array_combine(array_column($allocations, 'CONTRACTDETAILKEY'), array_column($allocations, 'MEAPERCENT'));
            
            foreach ($componentLines as &$componentLine) {
                $detailKey = $componentLine[$key];
                $componentLine['REVPERCENT'] = $allocationMap[$detailKey] ?? null;
            }
        }
    }
    
    /**
     * @param array $contractDetailKeys
     * @param array $selects
     *
     * @return array[]
     */
    protected function getKitAllocationDetail($contractDetailKeys, $selects = [])
    {
        $mgr = Globals::$g->gManagerFactory->getManager('contractallocationdetail');
        
        $params = [
            'selects' => $selects,
            'filters' => [
                [
                    ['CONTRACTDETAILKEY', 'IN', $contractDetailKeys],
                    ['STATUS', '=', 'T'],
                ],
            ],
        ];
        
        return $mgr->GetList($params);
    }
    
    protected function validateKitsComponents(array $componentLines): bool
    {
        $ok = true;
        
        if (!empty($componentLines)) {
            $totalAllocation = 0.0;
            
            foreach ($componentLines as $componentLine) {
                $totalAllocation = ibcadd($totalAllocation, $componentLine['REVPERCENT'], ContractUtil::KIT_ALLOCATION_PRECISION);
            }
            
            if (ibccomp($totalAllocation, 100) !== 0) {
                $msg = 'The total allocation percent must equal 100.';
                Globals::$g->gErr->addError(
                    'CN-0430',
                    __FILE__ . ':' . __LINE__,
                    $msg
                );
                $ok = false;
            }
        }
        
        return $ok;
    }
    
    /**
     * Saves kit component lines when a kit is added
     */
    private function createKitComponentLines(array $values): bool
    {
        $ok = true;
        
        if (ContractUtil::isKitsEnabled() && self::isKit($values)) {
            // For renewals, don't generate new component lines since they were already copied from the previous contract.
            // Instead, translate (which updates the PARENTKEY) and retrieve the original allocation percentages.
            if (!empty($values[':isInRenewingProcess']) || $values['STATE'] === ContractDetailState::STATE_RENEWAL_FORECAST) {
                $ok = $ok & $this->translateKit($values);
                $componentLines = $values['KITCOMPONENTLINES'];
                $this->populateAllocationPercent($componentLines, true);
                unset($values['KITCOMPONENTLINES']);
            } else {
                $componentLines = $this->generateKitComponentLines($values);
            }
            $ok = $ok && $this->updateSubmittedKitComponents($values, $componentLines);
            $ok = $ok && $this->validateKitsComponents($componentLines);
            
            foreach ($componentLines as $key => &$componentLine) {
                $componentLine[':overrideLineNo'] = $values['componentLineNoOverride'][$key][':overrideLineNo'] ?? null;
                $ok = $ok && $this->addLineNo($componentLine);
                $ok = $ok && $this->regularAdd($componentLine);
            }
            unset($componentLine);
            
            if ($ok && !empty($componentLines)) {
                /** @var ContractKitBundleManager $contractKitBundleManager */
                $contractKitBundleManager = Globals::$g->gManagerFactory->getManager('contractkitbundle');
                $ok = $contractKitBundleManager->createMEABundleForKit($values, $componentLines);
    
                // Update component lines after createMEABundleForKit, in case PRICECALCMEMO was updated
                foreach ($componentLines as $componentLine) {
                    // Good case for fast update
                    $ok = $ok && parent::regularSet($componentLine);
                }
                unset($componentLine);
            }
        }
        
        return $ok;
    }
    
    /**
     * Update kit components with submitted values
     *
     * @param array $kit
     * @param array $componentLines
     *
     * @return bool
     */
    private function updateSubmittedKitComponents(array $kit, array &$componentLines): bool
    {
        $ok = true;
        $isDraft = $kit['STATE'] === ContractDetailState::STATE_DRAFT;
        $isRenewalOnly = $kit['STATE'] === ContractDetailState::STATE_RENEWALONLY;
        $submittedComponentLines = $kit['KITCOMPONENTLINES'] ?? null;

        if (is_array($submittedComponentLines)) {
            // Create an indexed map based on RECORDNO
            $submittedComponentLinesRecordNoMap = array_combine(
                array_column($submittedComponentLines, 'RECORDNO'), $submittedComponentLines
            );
    
            // Create an indexed map based on ITEMID
            $submittedComponentLinesItemIdMap = array_combine(
                array_column($submittedComponentLines, 'ITEMID'), $submittedComponentLines
            );
    
            foreach ($componentLines as &$componentLine) {
                $recordNo = $componentLine['RECORDNO'] ?? null;
                $itemId = $componentLine['ITEMID'] ?? null;
                
                // If we have the RECORDNO then use that to match the submitted values with the saved values
                // Otherwise (such as with new Kit lines), use the ITEMID
                if ($recordNo) {
                    $submittedComponentLine = $submittedComponentLinesRecordNoMap[$recordNo] ?? null;
                } else {
                    $submittedComponentLine = $submittedComponentLinesItemIdMap[$itemId] ?? null;
                }
                
                if ($itemId && !empty($submittedComponentLine)) {
                    // Only allow updating certain fields
                    $allowedUpdateFields = [
                        'REVENUESTARTDATE',
                        'REVENUEENDDATE',
                        'REVENUESCHEDULESTATUS',
                        'REVENUETEMPLATENAME',
                        'REVENUESCHEDULEKEY',
                        'REVENUE2STARTDATE',
                        'REVENUE2ENDDATE',
                        'REVENUE2SCHEDULESTATUS',
                        'REVENUE2TEMPLATENAME',
                        'REVENUE2SCHEDULEKEY',
                        'REVENUEHOLDDATE',
                        'CANCELDATE',
                        'STATE',
                        'PRICECALCMEMO',
                        ':isCancelAction',
                        ':isUncancelAction',
                    ];
    
                    foreach ($allowedUpdateFields as $updateField) {
                        if (array_key_exists($updateField, $submittedComponentLine)) {
                            $componentLine[$updateField] = $submittedComponentLine[$updateField];
                        }
                    }
    
                    // If the Kit line is in Draft or Renewal only, all component lines should be the same too
                    if ($isDraft || $isRenewalOnly) {
                        $componentLine['STATE'] = $kit['STATE'];
                    }
    
                    // Update all the inherited values
                    $this->translateKitComponent($componentLine, $kit);
    
                    if (array_key_exists('REVPERCENT', $submittedComponentLine)) {
                        $componentLine['REVPERCENT'] = iround(
                            $submittedComponentLine['REVPERCENT'],
                            ContractUtil::KIT_ALLOCATION_PRECISION
                        );
                    }
                    
                    // Only allow updating delivery status on creation
                    if (empty($componentLine['LINENO'])) {
                        $componentLine['DELIVERYSTATUS'] = $submittedComponentLine['DELIVERYSTATUS'];
                    }
    
                    // Reset all dimensions back to the Kit values
                    $dimFields = $this->getDetailDimensions();
                    foreach ($dimFields as $dimField) {
                        $componentLine[$dimField] = $kit[$dimField] ?? null;
                    }
    
                    // Reset all custom fields back to the Kit values
                    $customFields = $this->GetCustomFields();
                    foreach ($customFields as $customField) {
                        $path = $customField->getFieldName();
                        $componentLine[$path] = $kit[$path] ?? null;
                    }
                }
            }
        }
        
        return $ok;
    }
    
    private function resetKitFields(array &$values): bool
    {
        $ok = true;
        $fields = ['ITEMTYPE', 'PARENTKEY', 'PARENTITEMTYPE'];
        
        foreach ($fields as $field) {
            $values[$field] = $this->oldValues[$field] ?? null;
        }
        
        return $ok;
    }
    
    /**
     * @param array $cnDetail
     *
     * @return bool
     */
    public static function isKit($cnDetail): bool
    {
        $itemId = $cnDetail['ITEMID'] ?? '';
        $itemType = $cnDetail['ITEMTYPE'] ?? self::getItemType($itemId);
        
        return $itemType === KIT;
    }
    
    /**
     * @param array $cnDetail
     *
     * @return bool
     */
    public static function isKitComponent($cnDetail)
    {
        return !empty($cnDetail['PARENTKEY']) && $cnDetail['PARENTITEMTYPE'] === KIT;
    }
    
    /**
     * @param array $cnDetail
     *
     * @return bool
     */
    public static function isKitOrComponent($cnDetail)
    {
        return self::isKit($cnDetail) || self::isKitComponent($cnDetail);
    }
    
    /**
     * Generates kit component lines for a kit item
     */
    public function generateKitComponentLines(array $values): array
    {
        $componentLines = [];
        
        if (ContractUtil::isKitsEnabled()) {
            $itemId = $values['ITEMID'];
            $itemType = $values['ITEMTYPE'] ?? self::getItemType($itemId);
            $values['ITEMNAME'] = $values['ITEMNAME'] ?? self::getItemName($itemId);
            
            if ($itemType === KIT) {
                $components = self::getKitComponentItems($itemId);
                $ok = !empty($components) && count($components) > 1;
                
                if ($ok) {
                    foreach ($components as $component) {
                        $revPercent = $component['REVPERCENT'];
                        
                        if (self::isEvergreenLine($values)) {
                            $revenueTemplate1 = ContractUtil::CONTRACT_REVREC_EVERGREEN_TEMPLATE;
                            $revenueTemplate2 = ContractUtil::CONTRACT_REVREC_EVERGREEN_TEMPLATE;
                        } else {
                            $revenueTemplate1 = self::getRevenueTemplateName($component['CNREVENUETEMPLKEY']);
                            $revenueTemplate2 = self::getRevenueTemplateName($component['CNREVENUE2TEMPLKEY']);
                        }
                        
                        // Set initial defaults
                        $componentValues = array_merge($values, [
                            'STATE' => $values['STATE'],
                            'RECORDNO' => null,
                            'LINENO' => null,
                            'PARENTKEY' => $values['RECORDNO'],
                            'PARENTITEMTYPE' => KIT,
                            'ITEMID' => $component['COMPONENT_ID'],
                            'ITEMNAME' => $component['COMPONENT_DESCRIPTION'],
                            'UOM' => $component['UOM'],
                            'QUANTITY' => $component['QUANTITY'],
                            'COMPONENTQUANTITY' => $component['QUANTITY'],
                            'REVENUETEMPLATENAME' => $revenueTemplate1,
                            'REVENUE2TEMPLATENAME' => $revenueTemplate2,
                            'DELIVERYSTATUS' => ($component['KCDLVRSTATUS'] == 'D') ?
                                ContractDetailDeliveryStatus::DELIVERED : ContractDetailDeliveryStatus::UNDELIVERED,
                            'DEFERRALSTATUS' => ($component['KCREVDEFSTATUS'] == 'I')
                                ? ContractDetailRevenueDeferralStatus::DEFER_UNTIL_ITEM
                                : ContractDetailRevenueDeferralStatus::DEFER_UNTIL_ALL,
                            'REVPERCENT' => $revPercent,
                        ]);
                        
                        $this->translateKitComponent($componentValues, $values, false);
                        
                        $componentLines[] = $componentValues;
                    }
                }
            }
        }
        
        return $componentLines;
    }
    
    private static function getRevenueTemplateName(?string $key): string
    {
        static $templates = [];
        
        if ($key && !array_key_exists($key, $templates)) {
            $revTemplateManager = Globals::$g->gManagerFactory->getManager('contractrevenuetemplate');
            $templates[$key] = $revTemplateManager->GetVidFromRecordNo($key);
        }
        
        return $templates[$key] ?? '';
    }
    
    /**
     * Retrieves the Kit (parent), updates the KITCOMPONENTLINES for the current component and triggers the set for the Kit
     */
    private function updateKitComponent(array $values): bool
    {
        $ok = true;

        if (ContractUtil::isKitsEnabled() && self::isKitComponent($values)) {
            $kitKey = (int) $values['PARENTKEY'];
            $kit = $this->Get($kitKey);
            $componentLines = $kit['KITCOMPONENTLINES'] ?? [];
            foreach ($componentLines as &$componentLine) {
                if ($componentLine['RECORDNO'] == $values['RECORDNO']) {
                    $componentLine = $values;
                    break;
                }
            }
            $kit['KITCOMPONENTLINES'] = $componentLines;
            
            // If we are performin a cancel or uncancel on a component line, copy to the Kit
            $kit[':isCancelAction'] = $values[':isCancelAction'];
            $kit[':isUncancelAction'] = $values[':isUncancelAction'];
            
            $ok = $ok && $this->regularSet($kit);
        }
        
        return $ok;
    }
    
    /**
     * Updates kit component lines
     * Removes and recreates MEA if certain fields are modified
     */
    private function updateKit(array $values): bool
    {
        $ok = true;
        
        if (ContractUtil::isKitsEnabled() && self::isKit($values)) {
            /** @var ContractKitBundleManager $contractKitBundleManager */
            $contractKitBundleManager = Globals::$g->gManagerFactory->getManager('contractkitbundle');
            $componentLines = $this->getKitComponentLines((int) $values['RECORDNO'], [], true);
            $redoMEA = $this->redoKitMEARequired($values, $componentLines);
            $ok = $ok && $this->updateSubmittedKitComponents($values, $componentLines);
            $ok = $ok && $this->validateKitsComponents($componentLines);
            
            if ($redoMEA) {
                $ok = $ok && $contractKitBundleManager->clearMEABundleForKit($values);
            }
            
            foreach ($componentLines as &$componentLine) {
                // If we are performin a cancel or uncancel on a kit line, copy to the component lines
                $componentLine[':isCancelAction'] = $values[':isCancelAction'];
                $componentLine[':isUncancelAction'] = $values[':isUncancelAction'];
                
                // When posting, set all Kit component lines to have the same state as the Kit line
                if ($values[':IS_POST_REQUEST']) {
                    $componentLine['STATE'] = $values['STATE'];
                }
    
                $componentLine[':updatingkit'] = true;
                $saveDetailOnly = $this->saveDetailOnly;
                $ok = $ok && $this->regularSet($componentLine);
                $this->saveDetailOnly = $saveDetailOnly;
            }
    
            if ($redoMEA) {
                $ok = $ok && $contractKitBundleManager->createMEABundleForKit($values, $componentLines);
            }
    
            // Update component lines after createMEABundleForKit, in case PRICECALCMEMO was updated
            foreach ($componentLines as $componentLine) {
                // Good case for fast update
                $ok = $ok && parent::regularSet($componentLine);
            }
        }
    
        return $ok;
    }
    
    private function redoKitMEARequired(array $kit, array $componentLines): bool
    {
        $kitRedoFields = [
            'FLATAMOUNT',
            'EXCHANGE_RATE',
            'GLPOSTINGDATE',
            'STARTDATE',
            'ENDDATE',
            'BILLINGOPTIONS',
            'BILLINGFREQUENCY',
        ];
        
        $kitRedoFields = array_merge($kitRedoFields, $this->getDetailDimensions(true));
        
        $kitComnponentRedoFields = [
            'REVENUESTARTDATE',
            'REVENUEENDDATE',
            'REVENUETEMPLATENAME',
            'REVENUESCHEDULEKEY',
            'REVENUE2STARTDATE',
            'REVENUE2ENDDATE',
            'REVENUE2TEMPLATENAME',
            'REVENUE2SCHEDULEKEY',
            'REVPERCENT',
        ];
        
        $redo = false;
        
        if (!$this->saveDetailOnly) {
            if (!empty($kit[':IS_POST_REQUEST'])) {
                $redo = true;
            }
    
            if (!$redo && !empty($kit['RECORDNO'])) {
                foreach ($kitRedoFields as $field) {
                    $oldValue = $this->oldValues[$field] ?? null;
                    $newValue = $kit[$field] ?? null;
                    if ($oldValue !== $newValue) {
                        $redo = true;
                        break;
                    }
                }
            }
    
            if (!$redo && !empty($kit['KITCOMPONENTLINES'])) {
                foreach ($componentLines as $index => $componentLine) {
                    foreach ($kitComnponentRedoFields as $field) {
                        $newLine = $kit['KITCOMPONENTLINES'][$index] ?? null;
                        $newValueSet = array_key_exists($field, $newLine);
                        if ($newValueSet) {
                            $oldValue = $componentLine[$field] ?? null;
                            $newValue = $newLine[$field] ?? null;
                            if (is_numeric($oldValue) || is_numeric($newValue)) {
                                if (ibccomp($oldValue, $newValue) !== 0) {
                                    $redo = true;
                                    break 2;
                                }
                            } elseif ((string) $oldValue !== (string) $newValue) {
                                $redo = true;
                                break 2;
                            }
                        }
                    }
                }
            }
        }
        
        return $redo;
    }
    
    private function translateKit(array &$values): bool
    {
        $ok = true;
        
        if (ContractDetailManager::isKit($values)) {
            // If the KitsEvergreen feature flag is turned off and a Kit is
            // being saved to an Evergreen contract, throw an error
            if (!ContractUtil::isKitsEvergreenEnabled() && $this->isEvergreenContract($values['CONTRACT'])) {
                Globals::$g->gErr->addIAError(
                    'CN-0431',
                    __FILE__ . ':' . __LINE__,
                    null,
                    ['ITEMID' => $values['ITEMID'], 'CONTRACTID' => $values['CONTRACTID']]
                );

                $ok = false;
            }
    
            // If the KitsEvergreen feature flag is turned off and a Kit is being saved to a contract
            // setup for termed to evergreen with the renewal flag on, throw an error
            $isRenewal = isArrayValueTrue($values, 'RENEWAL');
            if (
                !ContractUtil::isKitsEvergreenEnabled()
                && $isRenewal
                && RenewalContractHandler::isTermedToEvergreenRenewal($values['CONTRACT'])
            ) {
                Globals::$g->gErr->addIAError(
                    'CN-0432',
                    __FILE__ . ':' . __LINE__,
                    null,
                    ['CONTRACTID' => $values['CONTRACTID'], 'ITEMID' => $values['ITEMID']]
                );
    
                $ok = false;
            }
            
            $values['LINETYPE'] = self::LINETYPE_REGULAR;

            // For MVP we only support Fixed price, Include in every invoice
            $values['BILLINGMETHOD'] = $values['BILLINGMETHOD'] ?: ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_FIXED_PRICE;
            $values['BILLINGOPTIONS'] = $values['BILLINGOPTIONS'] ?: ContractBillingOptions::INCLUDE_WITH_EVERY_INVOICE;
            $values['BILLINGFREQUENCY'] = $values['BILLINGFREQUENCY'] ?: ContractUtil::BILLING_FREQUENCY_MONTHLY;
            $values['REVENUETEMPLATENAME'] = null;
            $values['REVENUETEMPLATEKEY'] = null;
            $values['REVENUESTARTDATE'] = null;
            $values['REVENUEENDDATE'] = null;
            $values['REVENUETOTALQUANTITY'] = null;
            $values['REVENUE2STARTDATE'] = null;
            $values['REVENUE2ENDDATE'] = null;
            $values['REVENUE2TEMPLATENAME'] = null;
            $values['REVENUE2TEMPLATEKEY'] = null;
            if (ContractDetailManager::isRevRecOnInvoice($values)) {
                Globals::$g->gErr->addIAError('CN-0683',__FILE__ . ':' . __LINE__, null, ['ITEMID' => $values['ITEMID']]);
                return false;
            }
            $values['REV_REC_ON_INVOICE'] = null;
            
            // Translate all the Kit component lines
            if (!empty($values['KITCOMPONENTLINES'])) {
                foreach ($values['KITCOMPONENTLINES'] as &$kitComponentLine) {
                    $kitComponentLine['PARENTKEY'] = $values['RECORDNO'];
                    $kitComponentLine['PARENTITEMTYPE'] = self::getItemType($values['ITEMID']);
                    $this->translateKitComponent($kitComponentLine, $values);
                }
            }
        }
        
        return $ok;
    }
    
    private function translateKitComponent(array &$values, array $kit, bool $validate = true): bool
    {
        $ok = true;
        
        if (!empty($kit)) {
            $inheritedValues = [
                'BEGINDATE',
                'ENDDATE',
                'EXCH_RATE_DATE',
                'GLPOSTINGDATE',
                'RENEWAL',
                'LINETYPE',
                'SHIPTOSOURCE',
                'SHIPTOKEY',
                'BILLTOSOURCE',
                'BILLTOKEY',
                'REVENUEJOURNALKEY',
                'REVENUE2JOURNALKEY',
                'ITEMDESC',
                'EXCHANGE_RATE',
            ];
            
            // Reset all inherited values back to the Kit values
            foreach ($inheritedValues as $inheritedValue) {
                $values[$inheritedValue] = $kit[$inheritedValue];
            }
            
            $kitQuantity = $kit['QUANTITY'] ?? 1;
            $values['QUANTITY'] = ibcmul($values['COMPONENTQUANTITY'], $kitQuantity, ContractUtil::QUANTITY_PRECISION);
            
            if ($validate) {
                $ok = $ok && $this->validateKitRevenueTemplates($kit);
            }
        }
        
        if (!empty($kit) || ContractDetailManager::isKitComponent($values)) {
            // Clear most fields for Kit component lines
            $values['SAASCHANGETYPEKEY'] = null;
            $values['PRICE'] = null;
            $values['MULTIPLIER'] = null;
            $values['FLATAMOUNT'] = null;
            $values['BASEFLATAMOUNT'] = null;
            $values['TOTALFLATAMOUNT'] = null;
            $values['TOTALBASEFLATAMOUNT'] = null;
            $values['DISCOUNTPERCENT'] = null;
            $values['FLATAMOUNT_MULTIPLIER'] = null;
            $values['USAGEQTYRSTPRD'] = null;
            $values['BILLINGSTARTDATE'] = null;
            $values['BILLINGENDDATE'] = null;
            $values['BILLINGFREQUENCY'] = null;
            $values['USAGEQTYRECUR'] = null;
            $values['BILLINGMETHOD'] = null;
            $values['BILLINGOPTIONS'] = null;
            $values['FLATAMOUNTBILLINGOPTIONS'] = null;
            $values['PRORATEBILLINGPERIOD'] = null;
            $values['USAGELINETYPE'] = null;
            $values['COMMITTEDUSAGEENDACTION'] = null;
            $values['COMMITTEDUSAGEEXCESS'] = null;
            $values['BILLINGTEMPLATEKEY'] = null;
            $values['LINETYPE'] = self::LINETYPE_REGULAR;
            [$values['REVENUETEMPLATENAME']] = explode('--', $values['REVENUETEMPLATENAME'] ?? '');
            [$values['REVENUE2TEMPLATENAME']] = explode('--', $values['REVENUE2TEMPLATENAME'] ?? '');
    
            if ($validate) {
                $ok = $ok && $this->validateKitComponentRevenueTemplates($values, 1);
                $ok = $ok && $this->validateKitComponentRevenueTemplates($values, 2);
            }
        }
        
        return $ok;
    }
    
    protected function validateKitRevenueTemplates(array $kit): bool
    {
        $ok = true;
        $components = $kit['KITCOMPONENTLINES'];
        
        if (!empty($components)) {
            $rev1Templates = array_filter(array_column($components, 'REVENUETEMPLATENAME'));
            $rev2Templates = array_filter(array_column($components, 'REVENUE2TEMPLATENAME'));
    
            foreach ($components as $component) {
                $revPercentZero = (ibccomp($component['REVPERCENT'] ?? 0, 0) === 0);
                $noRev1 = empty($component['REVENUETEMPLATENAME']);
                $noRev2 = empty($component['REVENUE2TEMPLATENAME']);
        
                // Revenue template journals must be consistent.  Either all J1, all J2 or all both.
                // The exception is 0 allocation component lines which do not require any revenue template
                if (!$revPercentZero || !$noRev1 || !$noRev2) {
                    if (($noRev1 && !empty($rev1Templates)) || ($noRev2 && !empty($rev2Templates))) {
                        Globals::$g->gErr->addIAError(
                            'CN-0433',
                            __FILE__ . ':' . __LINE__,
                        );
                
                        $ok = false;
                        break;
                    }
                }
            }
        }
        
        return $ok;
    }
    
    /** For MVP, Kits only support Straight line, Daily rate and Predefined percentage based Revenue templates */
    protected function validateKitComponentRevenueTemplates(array $values, int $journal): bool
    {
        $ok = true;
        
        if ($journal === 1) {
            $path = 'REVENUETEMPLATENAME';
            $errorCode = 'CN-0434';
        } else {
            $path = 'REVENUE2TEMPLATENAME';
            $errorCode = 'CN-0435';
        }
        
        $templateName = $values[$path] ?? null;
        if ($templateName) {
            $method = self::getTemplateMethod($templateName);
            if (!in_array($method, self::$allowedRevenueTemplateMethods)) {
                Globals::$g->gErr->addIAError(
                    $errorCode,
                    __FILE__ . ':' . __LINE__,
                    null,
                    ['ITEMID' => $values['ITEMID']]
                );
                
                $ok = false;
            }
        }
        
        return $ok;
    }
    
    protected static function getTemplateMethod(string $templateName): string
    {
        static $templateMethods;
        
        if (!isset($templateMethods[$templateName])) {
            $res = EntityManager::GetListQuick('contractrevenuetemplate', ['METHOD'], ['NAME' => $templateName]);
            if (!empty($res)) {
                $templateMethods[$templateName] = $res[0]['METHOD'];
            }
        }
        
        return $templateMethods[$templateName];
    }
    
    protected function getDetailDimensions(bool $glOnly = false): array
    {
        static $dimFields = null;
        
        if ($dimFields[$glOnly] ?? null === null) {
            $dimFields[$glOnly] = [
                'TASKID',
                'TASKNAME',
                'TASKDIM',
                'DEPARTMENTID',
                'LOCATIONID',
            ];
            
            if ($glOnly) {
                $dimInfo = $this->getGLDimensions();
            } else {
                $dimInfo = IADimensions::getAllDimensionObjectProperties(true);
            }
    
            $exclude = ['CONTRACTID', 'CUSTOMERID', 'ITEMID'];
            foreach ($dimInfo as $dim) {
                if (in_array($dim['path'], $exclude)) {
                    continue;
                }
                $dimFields[$glOnly][] = $dim['dimdbkey'];
                $dimFields[$glOnly][] = $dim['path'];
            }
            
            // Add $this->getDimFieldNames() to include custom dimensions for glOnly
            $dimFields[$glOnly] = array_unique(array_merge($dimFields[$glOnly], $this->getDimFieldNames()));
        }
        
        return $dimFields[$glOnly];
    }

    public static function getKitDetailFromKey(int $cnDetailKey): array
    {
        static $detailCache;

        if (!isset($detailCache[$cnDetailKey])) {
            $cnDetails = EntityManager::GetListQuick(
                'contractdetail',
                ['ITEMTYPE', 'PARENTKEY', 'PARENTITEMTYPE'],
                ['RECORDNO' => $cnDetailKey]
            );
            $detailCache[$cnDetailKey] = $cnDetails[0] ?? [];
        }
        
        return $detailCache[$cnDetailKey];
    }

    public static function isKitFromKey(int $cnDetailKey): bool
    {
        return self::isKit(self::getKitDetailFromKey($cnDetailKey));
    }
    
    public static function isKitComponentFromKey(int $cnDetailKey): bool
    {
        return self::isKitComponent(self::getKitDetailFromKey($cnDetailKey));
    }
    
    public static function isKitOrComponentFromKey(int $cnDetailKey): bool
    {
        return self::isKitOrComponent(self::getKitDetailFromKey($cnDetailKey));
    }
}
