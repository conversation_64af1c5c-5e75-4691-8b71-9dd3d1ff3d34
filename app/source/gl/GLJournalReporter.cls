<?php
/**
 * Reporter class for GL Journals report
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Reporter class for Journals report
 */
class GLJournalReporter extends GroupReporter
{
    /* @var bool $hasStatTxn*/
    private $hasStatTxn = false;
    /* @var bool $hasNrmTxn*/
    private $hasNrmTxn = false;
    /* @var int|string $batch_no*/
    private $batch_no;
    /* @var string $fromGLBatchEditor_Lister*/
    private $fromGLBatchEditor_Lister;
    /* @var string $multiJrlSeparator*/
    private $multiJrlSeparator = '#~#';
    /* @var array|bool $lines*/
    protected $lines;
    /* @var array|bool $journalTypeMap*/
    protected $journalTypeMap;

    /**
     * @var TextHelper $textHelper
     */
    private $textHelper;

    /**
     * @param array $_params Input params
     */
    public function __construct($_params)
    {
        $this->textHelper = TextHelper::getInstance(__CLASS__, true);
        $this->textHelper->AT('IA.ACCOUNT_NO');

        $_params = INTACCTarray_merge(
            $_params, array('report' => 'gl_jrnl',
            '2stage' => 'true'
            )
        );
        GroupReporter::__construct($_params);
        $this->_isgrouploc = $this->params['ISGROUPLOC'];
        $this->_isgroupdept = $this->params['ISGROUPDEPT'];
        $this->_isindividualloc = $this->_isgrouploc && $this->params['loc_IR'] == 'true';
        $this->_isindividualdept = $this->_isgroupdept && $this->params['dept_IR'] == 'true';


        //passing batch number
        $this->batch_no = &Request::$r->batchNo;
        $this->batch_no = ($this->batch_no == "" && $this->params['batchno'] != "") 
            ? $this->params['batchno'] : $this->batch_no;

        $_localBatchLister =  Request::$r->_fromGLBatchLister;
        $this->fromGLBatchEditor_Lister = (Request::$r->_fromGLBatchEditor) ?: ( $_localBatchLister ?? '' );

        $this->fromGLBatchEditor_Lister = (
            $this->fromGLBatchEditor_Lister == ""
            && $this->params['fromGLBatchEditor_Lister'] != ""
            ) ? $this->params['fromGLBatchEditor_Lister'] : $this->fromGLBatchEditor_Lister;

        // While printing from GLBatch lister/editor we need to skip location checks
        if ( $this->fromGLBatchEditor_Lister != "" ) {
            $this->params['NOLOCCHECKREQUIRED'] = true; // skip multivisibility check
            $this->params['NOREPORTLOCCHECK'] = true; // skip location check for ATLAS

            // While exporting to excel display all the dimension values and reference number cols
            if ( $this->params['type'] === kShowExcel ) {
                $dimensionObj = $this->GetReportDimensions();
                $showDimension = array();
                foreach ($dimensionObj as $key => $dimensionsParams) {
                    if ( isset($dimensionsParams['subElement']) && $dimensionsParams['subElement'] == true ) {
                        continue;
                    }

                    if ( $dimensionsParams['isplatform'] ) {
                        $lookup_field_names = $dimensionsParams['lookup_field_names'];
                        if ( is_array($lookup_field_names) && count($lookup_field_names) > 0 ) {
                            foreach ( $lookup_field_names as $extName => $schName) {
                                $showDimension[] = $key . "__$extName";
                                $this->params['UDDVALUES'][] = isl_strtoupper($key . "__$extName");
                            }
                        }
                    } else {
                        $showDimension[] = $key . '__name';
                        $showDimension[] = $key . '__id';
                    }
                }

                $this->params['SHOWDIMENSIONVALUES'] = implode($this->multiJrlSeparator, $showDimension);
                $this->params['showrefnum'] = 'true';
            }
        }

        //  Add information for the audit trail reporting of this user action.
        //   Get the key.  Sometimes it is batchid, sometimes we have to figure it from the batch.
        $key = Request::$r->_batchid;
        $entity = (Request::$r->_isstat == '1') ? 'statglbatch' : 'glbatch';
        if (!empty($this->batch_no)) {
            $batchNo = $this->batch_no;

            //  If not batchno/recordno, we need to get the record#.
            if (!empty($batchNo)) {
                $gManagerFactory = Globals::$g->gManagerFactory;
                $glMgr = $gManagerFactory->getManager('glbatch');
                $journal = Request::$r->_obj__jrnl;
                $qry = (Request::$r->_isstat == '1') ? 
                    'QRY_GLBATCH_SELECT_UID_BY_STATBATCHNO' : 'QRY_GLBATCH_SELECT_UID_BY_BATCHNO';
                $res = $glMgr->DoQuery($qry, array(GetMyCompany(), $journal, $batchNo));
                if ($res) {
                    $key = $res[0]['RECORD#'];
                }
            }
        }
        if (!empty($key)) {
            $this->params['auditTrail'] = [
                'entity' => $entity, 'key' => $key, 'action' => AuditTrail::AUDITTRAIL_ACTION_PRINT
            ];
        }
    }

    /**
     * Generates map
     *
     * @return array|false List of lines
     */
    public function DoMap()
    {
        $this->Translate();

        $gErr = Globals::$g->gErr;
        $_showdetail = &Request::$r->_showdetail;

        /** @noinspection PhpUnusedLocalVariableInspection */
        $_fo = $this->params['fo'];
        $_selPeriodIndex = $this->params['selperiodindex'];
        $_showdetail = $this->params['showdetail'];
        /** @noinspection PhpUnusedLocalVariableInspection */
        $_type = $this->params['oldtype'];
        /** @noinspection PhpUnusedLocalVariableInspection */
        $_repPeriodRecNo = ( $_selPeriodIndex > 0) ? $_selPeriodIndex : CurrentReportingPeriod();
        /** @noinspection PhpUnusedLocalVariableInspection */
        $_dept = $this->params['dept'];
        /** @noinspection PhpUnusedLocalVariableInspection */
        $_loc = $this->params['loc'];
        /** @noinspection PhpUnusedLocalVariableInspection */
        $_filterTransactions = $this->params['filterTransactions'];
        $this->title = $this->params['title'];

        /** @noinspection PhpUnusedLocalVariableInspection */
        $startDate = $this->params['startdate'];
        /** @noinspection PhpUnusedLocalVariableInspection */
        $endDate = $this->params['enddate'];
        $_inclRepBook = isset($this->params['INCLUDEREPBOOK']) && ($this->params['INCLUDEREPBOOK'] == 'false') ? false : true;
        
        SetReportingBookContext($this->params['reportingBook'], $this->params['ADJUSTMENTBOOK'], $_inclRepBook);
        $rptBookArr = GetGlobalReportingBookVal();
        $this->params['showrefnum'] = (in_array($this->params['showrefnum'], array("1", "true"))) ?
            'true' : 'false';

        // For backward compatibility 
        if ( !array_key_exists('gljrnltype', $this->params) ) {
            $this->params['gljrnltype'] = 'Single';
        } else if ( $this->params['gljrnltype'] == "" ) {
            $this->params['gljrnltype'] = 'All';
        }

        $jrnls = array();
        if ( $this->params['gljrnltype'] == 'Single' ) {
            [$sym] = explode("--", $this->params['jrnl']);
            $jrnls = array($sym);
        } else if ( $this->params['gljrnltype'] == 'Multi' ) {
            $this->params['multiJrnl'] = str_replace("\n", $this->multiJrlSeparator, $this->params['multiJrnl']);
            $jrnlRange = explode($this->multiJrlSeparator, $this->params['multiJrnl']);

            foreach ( $jrnlRange as $jr ) {
                [$frmjrl, $tojrl] = explode("->", $jr);
                if ( $frmjrl === $tojrl && !in_array($frmjrl, $jrnls, true) ) {
                    $jrnls[] = $frmjrl;
                } else {
                    // as we don't have support for upper function in filter section we can't use manager
                    $qry = array(
                        "select symbol from v_alljournalpick where cny# = :1 " .
                        "and upper(symbol) between :2 and :3", GetMyCompany(), 
                        isl_strtoupper($frmjrl), isl_strtoupper($tojrl)
                    );
                    if ( isset($this->params['reportingBook']) && $this->params['reportingBook'] != '' ) {
                        $qry = PrepINClauseStmt($qry, $rptBookArr, " and bookid", true);
                    }
                    $qry[0] .= " order by alljournalpick";

                    $res = QueryResult($qry);

                    foreach ( $res as $re ) {
                        if ( !in_array($re['SYMBOL'], $jrnls, true) ) {
                            $jrnls[] = $re['SYMBOL'];
                        }
                    }
                }
            }
        } else {
            $gManagerFactory = Globals::$g->gManagerFactory;
            $jrlMgr = $gManagerFactory->getManager('alljournalpick');

            $qry = array(
                'selects' => array('SYMBOL'),
                'orders' => array(array('PICKID')),
                'start' => 0,
                'max' => 1000,
            );

            if ( $this->params['incstatjrl'] === 'false' ) {
                $qry['filters'][0][] = array('STATISTICAL', '=', 'F');
            }
            if ( isset($this->params['reportingBook']) && $this->params['reportingBook'] != '' ) {
                $qry['filters'][0][] = array('BOOKID', 'IN', $rptBookArr);
            }

            $res = $jrlMgr->GetList($qry);

            foreach ( $res as $re ) {
                $jrnls[] = $re['SYMBOL'];
            }
        }

        foreach ( $jrnls as $jr ) {
            [$_jrnl] = explode("--", $jr);

            if ( $_jrnl === '' ) {
                $gErr->addError("GL-2819", __FILE__ . ":" . __LINE__,
                "Please select a valid journal(s).");
                return false;
            }
            $jrnlrpt = $this->SetJrnlReportParams($_jrnl);
            // If selected journal doesn't have any record then don't add it
            if ( !empty($jrnlrpt['entries']) ) {
                $jrnlReport[] = $jrnlrpt;
            }
        }

        $lines['jrnlreport'] = array();

        $lines = &$this->PrintJrnlReport($jrnlReport);

        if ( IsMCMESubscribed() ) {
            $this->DisplayBaseCurrHeader($lines);
        }

        //removing for all periods if the batch number is set.
        if ( isset($this->batch_no) || empty($lines) ) {
            $lines['jrnlreport']['0']['period'] = '';
        }

        $this->lines = &$lines; // remember this so csv can use it
        if ( $this->params['type'] === kShowExcel || $this->params['offreporttype'] === kShowExcel ) {
            $lines['jrnlreport']['0']['title'] = $this->title;
            $lines['jrnlreport']['0']['title2'] = $this->title2;
            $lines['jrnlreport']['0']['titlecomment'] = $this->titlecomment;
            $lines['jrnlreport']['0']['reportdate'] = GetCurrentDate(IADATE_USRFORMAT);
            $lines['jrnlreport']['0']['co'] = $this->GetReportTitle($this->params['ORIG_LOCATION'] );
        }
        $lines['jrnlreport']['0']['report_format'] = $this->params['type'];
        //Added for Drilldown used at .xls file 
        $lines['jrnlreport']['0']['REPORTINGACCOUNTSET'] = $this->params['REPORTINGACCOUNTSET'];
        $lines['jrnlreport']['0']['REPORTINGACCOUNTS'] = $this->params['REPORTINGACCOUNTS'];

        if ($this->params['REPORTINGACCOUNTSET']) {
            //TODO-i18n: does ucwords work in multi-lingual scenario??
            $lines['jrnlreport']['0']['reportJour_string'] = I18N::getSingleToken('IA.REPORT_JOUR_STRING');
        } else {
            $lines['jrnlreport']['0']['reportJour_string'] = $this->textHelper->GT('IA.ACCOUNT_NO');
        }

        return $lines;
    }

    /**
     * overriding the function due to lower/upper case different in this report
     *
     * @param array &$params Input params
     * 
     * @return bool True if success
     */
    function ProcessDateFilter(&$params)
    {

        $ok = true;
        if ( !$this->_showmemorized && !$this->params['OFFLINEREPORTS'] ) {
            $startdate = $params['startdate'];
            $enddate = $params['enddate'];
            $asofdate = $params['asofdate'];

            $gErr = Globals::$g->gErr;

            //To validate dates
            if ( !ValidateInputDate($asofdate) ) {
                $gErr->addError("GL-2820", __FILE__ . ":" . __LINE__, "Invalid As of Date format.");
                $ok = false;
            }
            if ( !ValidateInputDate($startdate) ) {
                $gErr->addError("GL-2821", __FILE__ . ":" . __LINE__, "Invalid start date format.");
                $ok = false;
            }
            if ( !ValidateInputDate($enddate) ) {
                $gErr->addError("GL-2822", __FILE__ . ":" . __LINE__, "Invalid end date format.");
                $ok = false;
            }
            $this->params['asofdate'] =  FormatDateForStorage($asofdate);
            if ( ! empty($startdate) && ! empty($enddate) &&
            DateCompare($startdate, $enddate, IADATE_USRFORMAT) == 1 ) {
                $gErr->addError("GL-0137", __FILE__ . ":" . __LINE__,
                "Start Date should be less than End Date.");
                $ok = false;
            }
        }

        return $ok;
    }

    /**
     * Overridden to skip validation for location while coming from GL Batch lister and editor 
     * and while using STANDARD book
     *
     * @return bool Returns true or false
     */
    public function ValidateParams()
    {
        if ( IsMCMESubscribed() ) {
            $bookName = ( isset($this->params['reportingBook']) && $this->params['reportingBook'] != '' ) ?
                $this->params['reportingBook'] : '';

            if ( GLBookManager::IsConsolidationBook($bookName, false) || $this->fromGLBatchEditor_Lister ) {
                $this->params['NOREPORTLOCCHECK'] = true;
            }
        }

        return parent::ValidateParams();
    }

    /**
     * Requried by base class, currently does nothing
     *
     * @return bool Always returns true
     */
    public function DoQuery()
    {
        // all the functionality is in domap right now.  It's not critical			
        return true;
    }

    /**
     * This function will build the query clauses depending on the dimensions field the user has choosen
     * to put inside the report
     *
     * @param string $alias Alias to be used in query
     * 
     * @return array Containing query parameters
     */
    private function BuildShowDimQuery($alias = 'glentry')
    {
        $select = '';
        $where = '';
        $from = '';

        // We will add dimensions select and join only if the user chooses to display the value.
        // This will optimize our query time
        $showdimensions = explode('#~#', $this->params['SHOWDIMENSIONVALUES']);
        $dimcount = count($showdimensions);

        for ( $i = 0; $i < $dimcount; $i++ ) {

            if ( $showdimensions[$i] == '' || $showdimensions[$i] == 'None' ) {
                continue;
            }

            /** @noinspection PhpUnusedLocalVariableInspection */
            $dimPath = '';
            [$dimentity, $dimvalue] = explode('__', $showdimensions[$i]);
            $params = $this->dimensions[$dimentity];
            $dimPath = $dimentity;

            if ( $params['isplatform'] ) {
                $dimPath = 'CUSTDIM_' . $params['objid'] . "__";
            }

            // build the header name
            // get the terminology of the dimension if exists
            // handle custom dimensions
            if ( $params['isplatform'] ) {
                // show field label if possible...
                $hdrname = $params['fullname'];
                /* @var Pt_StdDataObjectDef|Pt_DataObjectDef $fieldObj */
                $fieldObj = $params['indexedLookupTemplateFields'][$dimvalue];
                if ( is_object($fieldObj) ) {
                    $diaplayLabel = $fieldObj->__toString();
                    $hdrname .= ( $hdrname != $diaplayLabel ? " ".$diaplayLabel : "" );
                }
            } else {
                $hdrname = $params['fullname'];
                $hdrname .= ($dimvalue == 'id') ? '_ID' : '_' . strtoupper($dimvalue);
            }

            $dimensionHeader['dimension'][] = array('hdrtitle' => $hdrname);
            $dimensionDBCol[] = isl_strtoupper($dimPath . $dimvalue);
            
            // skip for now if it is a UDD, since we are handling that thru a function call
            if ( !$params['standard'] ) {
                continue;
            }

            // For employee we have to map with the contact table
            if ( $dimentity == 'employee' ) {

                if ( $dimvalue == 'id' ) {
                    $select .= ',employee.employeeid as employeeid';
                } else {
                    $select .= ',contact.name as employeename';
                    $where .= ' and contact.cny# (+) = employee.cny# and contact.record# (+) = employee.contactkey';
                    $from .= ',contact contact';
                }

                // we will add the from and where condition only if not already added
                // oracle will not like us to declare it 2 times
                $tmpfrom = ',employeemst employee';
                if ( isl_strpos($from, $tmpfrom) === false ) {
                    $from .= $tmpfrom;
                }

                $tmpwhere = ' and employee.cny# (+) = ' . $alias . '.cny# and employee.record# (+) = ' . $alias .
                    '.employeedimkey';
                if ( isl_strpos($where, $tmpwhere) === false ) {
                    $where .= $tmpwhere;
                }
            } else {  // For other dimensions we will select name and ID
                if ( $dimvalue == 'id' ) {
                    $select .= ',' . $dimentity . '.' . $params['dimfkid'] . ' as ' . $dimentity . 'id';
                } else {
                    if ( $dimentity == 'department' ) {
                        $select .= ',' . $dimentity . '.title as ' . $dimentity . 'name';
                    } else {
                        $select .= ',' . $dimentity . '.name as ' . $dimentity . 'name';
                    }
                }

                // department and location are already in the query. No need to join again.
                if ( $dimentity != 'department' && $dimentity != 'location' ) {

                    // we will add the from and where condition only if not already added
                    // oracle will not like us to declare it 2 times
                    $tmpfrom = ',' . $params['table'] . ' ' . $dimentity;
                    if ( isl_strpos($from, $tmpfrom) === false ) {
                        $from .= $tmpfrom;
                    }

                    $tmpwhere = ' and ' . $alias . '.cny# = ' . $dimentity . '.cny# (+) ' . ' and ' . $alias . '.' .
                        $params['dimdbkey'] . '=' . $dimentity . '.' . 'record# (+) ';
                    if ( isl_strpos($where, $tmpwhere) === false ) {
                        $where .= $tmpwhere;
                    }
                }
            }
        }

        /** @noinspection PhpUnusedLocalVariableInspection */
        $ok = parent::buildDynamicReportQuery($select, $from, $where, 'glentry');

        /** @noinspection PhpUndefinedVariableInspection */
        $qryParams = array( 'select' => $select,
                            'from'   => $from,
                            'where'  => $where,
                            'header' => $dimensionHeader,
                            'dbcol'  => $dimensionDBCol);

        return $qryParams;
    }

    /**
     * Translate all the old params such as dept, loc, individial and show and hide for dept and loc
     * Used by memorized edit and background reporter as well
     */
    function Translate()
    {
        // For existing memorized reports we must map them to new array
        // Assuming if showlocation or showdepartment param is set then it is created using old report filters
        if ( array_key_exists('showlocation', $this->params) || array_key_exists('showdepartment', $this->params) ) {

            $gluestr = "#~#";
            $this->params['dept_IR'] = $this->params['irdept'];
            $this->params['loc_IR'] = $this->params['irloc'];
            $this->params['loc_IR'] = $this->params['irloc'];
            $this->params['SHOWDIMENSIONVALUES'] = ($this->params['showdepartment']) ? "department__id" . $gluestr : "";
            $this->params['SHOWDIMENSIONVALUES'] .= ($this->params['showlocation']) ? "location__id" : "";
        }
        if ( array_key_exists('selPeriodIndex', $this->params) ) {
            $this->params['selperiodindex'] = $this->params['selPeriodIndex'];
        }

        // For existing memorized reports we must map them to new array with 2 underscores
        if ( isset($this->params['SHOWDIMENSIONVALUES']) && !empty($this->params['SHOWDIMENSIONVALUES']) ) {
            $existingParam = explode("#~#", $this->params['SHOWDIMENSIONVALUES']);

            //reset filters
            $this->params['SHOWDIMENSIONVALUES'] = "";
            foreach ($existingParam as $param) {
                if ($this->params['SHOWDIMENSIONVALUES'] !== "") {
                    $this->params['SHOWDIMENSIONVALUES'] .= "#~#";
                }

                // convert only if we don't have 2 underscores
                $this->params['SHOWDIMENSIONVALUES'] .= (isl_strpos($param, "__") !== false) ?
                    $param : str_replace("_", "__", $param);
            }
        }
    }

    /**
     * Regenerates the prompt on run params passed while running memorized report from lister
     *
     * @return array  List of prompt on run params
     */
    function GetCurrentLines_IncludeBlankFields()
    {
        $lines = parent::GetCurrentLines_IncludeBlankFields();

        if ( count($lines) > 0 ) {
            // For backward compatibility
            $reportingPeriods = GetPeriodMap();
            if ( array_key_exists('LOCATION', $lines) ) {
                $lines['loc'] = $lines['LOCATION'];
            }
            if ( array_key_exists('DEPARTMENT', $lines) ) {
                $lines['dept'] = $lines['DEPARTMENT'];
            }
            if ( array_key_exists('PERIOD', $lines) ) {
                $lines['selperiodindex'] = array_search($lines['PERIOD'], $reportingPeriods);
            }
        }
        return $lines;
    }

    /**
     * Gathers the transaction data and various properties of the report and stores these in a variable.
     * 
     * @param string $jrnlSymbol journal symbol
     * 
     * @return array|false Lines to be printed
     */
    private function SetJrnlReportParams($jrnlSymbol)
    {
        $style = $this->params['oldtype'];
        $_selPeriodIndex = $this->params['selperiodindex'];
        $repPeriodRecNo = ($_selPeriodIndex > 0) ? $_selPeriodIndex : CurrentReportingPeriod();
        $asofdate = $this->params['asofdate'];
        $deptids = $this->params['dept'];
        $locids = $this->params['loc'];
        $filterTransactions = $this->params['filterTransactions'];
        $title = $this->title;
        $title2 = $this->title2;
        $titlecomment = $this->titlecomment;
        $acct = &Request::$r->acct;
        $startDate = $this->params['startdate'];
        $endDate = $this->params['enddate'];
        $drillDownFrom = &Request::$r->drillDownFrom;
        
        // Get dimension filter clause
        $alias = 'glentry';
        $dimensionValues = array();

        $dimensionFilterClause = $this->GetDimensionFilterClause($alias, $dimensionValues);

        // if we have custom dimensions/UDD enabled then we need to make some tweaks
        // to the query so that CUSTDIM1 gets replaced as AIRCRAFT.RECORD#...
        // 1. CUSTDIM1 is for the balance formward (gltotals)
        // 2. AIRCRAFT.RECORD# is for the transactions list (glentry)
        if ( IADimensions::isCustomDimensionsEnabled() ) {
            foreach( $this->dimensions as $dimObj) {
                if ( $dimObj['isplatform'] && isl_stristr($dimensionFilterClause, $dimObj['dimdbkey']) ) {
                    $search = "and " . $dimObj['dimdbkey'];
                    $replace = "and CUSTDIM_" . $dimObj['objid'] . ".RECORD#";
                    $dimensionFilterClause = str_replace($search, $replace, $dimensionFilterClause);
                }
            }
        }

        $dimqryParams = $this->BuildShowDimQuery();
        $showmemorized = ( $this->_showmemorized || $this->params['OFFLINEREPORTS'] ) ;
        $depSub = $this->params['dept_SUBS'];


        if ( empty($this->journalTypeMap) ) {
            $this->journalTypeMap = GLBatchManager::getJournalsTypeMap();
        }
        $kAPid = Globals::$g->kAPid;
        $kARid = Globals::$g->kARid;

        $cny = GetMyCompany();

        $mcpEnabled = IsMCPSubscribed();

        $dimselect = (count($dimqryParams) > 0) ? $dimqryParams['select'] : '';
        $dimfrom = (count($dimqryParams) > 0) ? $dimqryParams['from'] : '';
        $dimwhere = (count($dimqryParams) > 0) ? $dimqryParams['where'] : '';

        // get period records
        //$entity		= "glbudgettype";
        //$selects	= "record#, start_date, end_date, header1, header2, datetype";
        //$wheres		= ( $batch_no != '' ? " datetype = $repPeriodRecNo " : " RECORD# = $repPeriodRecNo " );
        //$periodQResult = GetNObjects($entity, $selects, $wheres);

        if ( $drillDownFrom == '' && $startDate == '' && $endDate == '' ) {
            $filterField = ( $this->batch_no != '' ? "datetype" : "record#" );
            $args = array("", $cny, $repPeriodRecNo);
            $args[0] = "select record#, start_date, end_date, header1, header2, datetype, ".
                "name from glbudgettype where cny# = :1 and $filterField = :2";
            $periodQResult = QueryResult($args);

            $periodRecord = $periodQResult[0];
            GetReportingDateRange($periodRecord, $asofdate, $sDate, $eDate);
        }

        // If the drill down request is coming from Journal Activity Report
        if ( $startDate != '' && $endDate != '' ) {
            $sDate = ($showmemorized) ? $startDate : FormatDateForStorage($startDate);
            $eDate = ($showmemorized) ? $endDate : FormatDateForStorage($endDate);
        }

        // Set start and end dates, used in PDF output
        /** @noinspection PhpUndefinedVariableInspection */
        $periodRecord['START_DATE'] = $sDate;
        /** @noinspection PhpUndefinedVariableInspection */
        $periodRecord['END_DATE'] = $eDate;

        /** @noinspection PhpUnusedLocalVariableInspection */
        $journalName = "gljournal";
        /** @noinspection PhpUnusedLocalVariableInspection */
        $accountName = "glaccount";

        $journalName = "gljournal";
        $accountName = "glaccount";
        if ( !isl_strcasecmp($this->journalTypeMap[$jrnlSymbol]['STATISTICAL'], "T") ) {
            $journalName = "statjournal";
            $accountName = "stataccount";
        }

        // get transaction records
        $args = array("select record#, title from $journalName where cny# = :1 and symbol = :2", $cny, $jrnlSymbol);
        $glBatchQResult = QueryResult($args);

        $journalRec = $glBatchQResult[0]['RECORD#'];

        if ( $journalRec == '' ) {
            // raise error
            return false;
        }

        $stmt = array("", $cny, $journalRec, $sDate, $eDate);
        $numArgs = count($stmt);

        //filter for batch number for Print funnctionality in Journal entry screen / GLBatch Lister.
        $batchFilter = "";
        if ( $this->batch_no != '' ) {
            $batchFilter = " and glbatch.batch_no = :" . $numArgs;
            $batchFilterForView = " and glbatch_view.batch_no = :" . $numArgs++;
            $stmt[] = $this->batch_no;
        }

        // form the location clause
        $locationclause = " ";
        if (Util::countOrZero($locids)) {
            $locRecs = $locationRecs = array();
            foreach ( $locids as $locIdName ) {
                [$locid] = explode(PICK_RECVAL_SEP, isl_trim($locIdName));
                $locRecs[] = $locid;
            }

            if ( count($locRecs) > 0 ) {
                $args = array("", $cny);

                if ( $filterTransactions == 'SELF' ) {
                    $args[0] .= "select record# from locationmst where cny# = :1 ";
                    $args[0] .= " and location_no in ( " . VariablePlaceHolders(2, count($locRecs)) . ")";
                    $args = INTACCTarray_merge($args, $locRecs);
                } else if ( $filterTransactions == 'CHILDREN' ) {
                    $args[0] .= "select record# from locationmst start with cny# = :1 and parentkey in ( ";
                    $args[0] .= " select record# from locationmst where cny# = :1 and location_no in ( ";
                    $args[0] .= VariablePlaceHolders(2, count($locRecs)) . " ) ";
                    $args[0] .= " ) connect by prior record# = parentkey and cny# = :1 ";
                    $args = INTACCTarray_merge($args, $locRecs);
                } else {
                    $args[0] .= "select record# from locationmst start with cny# = :1 ";
                    $args[0] .= " and location_no in ( " . VariablePlaceHolders(2, count($locRecs)) . ")";
                    $args[0] .= " connect by prior record# = parentkey and cny# = :1 ";
                    $args = INTACCTarray_merge($args, $locRecs);
                }

                $resultSet = QueryResult($args);
                foreach ( $resultSet as $loc ) {
                    $locationRecs[] = $loc['RECORD#'];
                }
                $trx_flag = 0;
                if ( count($locationRecs) > 0 ) {
                    $locationRecs = array_unique($locationRecs);
                    $countLocs = count($locationRecs);

                    if ( $countLocs > 1 ) {
                        if ( $countLocs > kINClauseLimit ) {
                            //We are begining the transaction only when we have more than 1000 locations, and setting 
                            //the trx_flag = 1.At the End we are commiting the transaction based on the trx_flag status.
                            if ( !$trx_flag ) {
                                XACT_BEGIN("SetJrnlReportParams::DumpRecsToGlobalTempTable");
                                $trx_flag = 1;
                            }
                            $entitytype = 'location';
                            DumpRecsToGlobalTempTable($locationRecs, $tablename, $entitytype);
                            $inClause = "select ENTITYNO from $tablename where entitytype = '" . $entitytype . "'";
                            /** @noinspection PhpUnusedLocalVariableInspection */
                            $locationclause = " and glentry.location# in ( $inClause ) ";
                        } else {
                            $inClause = VariablePlaceHolders($numArgs, $countLocs);
                            $stmt = INTACCTarray_merge($stmt, $locationRecs);
                            $numArgs = count($stmt);
                        }

                        // In case of printing the GL entry having allocation after sliding into entity we need to 
                        // check for ALLOCATIONKEY, as DISPGLENTRY will not be populated with LOCATION# value
                        if ( $this->fromGLBatchEditor_Lister == '' && GetContextLocation() === false ) {
                            $locationclause = " and glentry.location# in ( $inClause ) ";
                        } else {
                            $locationclause = " and (glentry.location# in ( $inClause )".
                                " or glentry.allocationkey is not null)";
                        }
                    } else {
                        // In case of printing the GL entry having allocation after sliding into entity we need to 
                        // check for ALLOCATIONKEY, as DISPGLENTRY will not be populated with LOCATION# value
                        if ( $this->fromGLBatchEditor_Lister == '' && GetContextLocation() === false ) {
                            $locationclause = " and glentry.location# = :" . $numArgs;
                        } else {
                            $locationclause = " and (glentry.location# = :" . $numArgs . 
                                " or glentry.allocationkey is not null)";
                        }
                        $stmt[] = $locationRecs[0];
                        $numArgs = count($stmt);
                    }
                } else {
                    // if there is no location found for the given filter then we will make up
                    // a dummy location filter resolving to nothing
                    $locationclause = " and 1 = 2 ";
                }
            }
        }

        // form the department clause
        $departmentclause = " ";
        if ( !isEmptyArray($deptids) ) {
            $deptStmt[0] = "Select record# from department where cny#=:1 ";
            $deptStmt[1] = $cny;
            $argNo = 2;
            foreach ( $deptids as $dept ) {
                [$deptid] = explode(PICK_RECVAL_SEP, isl_trim($dept));
                $deptStmt[] = $deptid;
                $argParam[] = ":" . $argNo++;
            }
            $deptStmt[0] .= "and dept_no in (" . join(',', $argParam) . ")";
            $deptrecs = QueryResult($deptStmt);
            $depts = [];
            foreach ( $deptrecs as $deptrec ) {
                $depts[] = $deptrec['RECORD#'];
                $stmt[] = $deptrec['RECORD#'];
            }

            if ( $depSub == 'false' ) {
                $departmentclause = " AND GLENTRY.DEPT# IN (" .
                                    VariablePlaceHolders($numArgs, count($depts)) . ")";
            } else {
                $departmentclause = " AND GLENTRY.DEPT# IN (
                    SELECT  distinct(RECORD#) FROM DEPARTMENTMST
                    WHERE CNY#=:1
                    start with RECORD# in (" . VariablePlaceHolders($numArgs, count($depts)) . ") and CNY# = :1 
                    connect by prior RECORD# = PARENT# and CNY# = :1 )";
            }
        } else if ( $depSub == 'false' ) {
            $departmentclause = " and exists (select 1 from departmentmst d where d.cny# = :1".
                " and d.record# = glentry.dept# and d.parent# is null) ";
        }
        $numArgs = count($stmt);

        $acctFilter = "";
        if ( $drillDownFrom == 'journalactivity' && $acct != '' ) {
            if (!empty($acct) && strstr($acct, 'REPORTINGACKEY-') && !empty($this->params['REPORTINGACCOUNTSET'])) {
                $rptackey = explode("REPORTINGACKEY-", $acct);
                $gManagerFactory = Globals::$g->gManagerFactory;
                $reporToNativeMgr = $gManagerFactory->getManager('reportingtonativeacmap');
                $params = array(
                    'selects' => array('NATIVEACKEY'),
                    'filters' => array (array (array('reportingackey', '=', $rptackey[1])))
                );
                $mapRec = $reporToNativeMgr->GetList($params);
                $mappednatAccts = array();
                foreach ($mapRec as $eachVal){
                    $mappednatAccts[]  = $eachVal['NATIVEACKEY'];
                }
            } else {
                $acctFilter = " and glentry.account# = :" . $numArgs;
                $stmt[] = $acct;
            }

        }

        $orderby = " order by glentry.line_no ";
        $recordno = "";
        $glentry = " dispglentry glentry ";

        // When coming from the glbatch lister/editor, we dont want the glentry.amount != 0 filter 
        // since it is costly and affects performance.
        $amountfilter = "";

        if ( $this->fromGLBatchEditor_Lister == '' ) {
            $orderby = " order by glbatch_view.batch_no, glentry.tr_type desc, glaccount.acct_no, ".
                "glentry.location#, glentry.dept#, glentry.amount desc ";
            $recordno = " glentry.record#, ";
            $glentry = " glentry ";
            $amountfilter = " AND glentry.amount != 0 ";
        } else {
            if(!$this->doesEntryHasAllcoation($journalRec)) {
                $glentry = " glentry ";
                $orderby = " order by glentry.line_no, glentry.record# ";
            }
        }

        /** @noinspection PhpUndefinedVariableInspection */
        $stmt[0] = "WITH glbatch_view as (
                select glbatch.cny#, glbatch.record#, glbatch.batch_date,
                    glbatch.batch_no, glbatch.batch_title, glbatch.referenceno, glbatch.locationkey as ownerloc
                from glbatchmst glbatch
                where glbatch.cny# = :1 and glbatch.journal# = :2
                and glbatch.batch_date between to_date(:3, 'mm/dd/yyyy') and to_date(:4,'mm/dd/yyyy') " .
                   (($this->fromGLBatchEditor_Lister != '') ? '' : " and " .
            GLBatchManager::getPostStateQryFilter('glbatch')) . "
        )
        SELECT
                $recordno glentry.document, glentry.description as description,
                glaccount.acct_no, glaccount.title, dept#, department.dept_no, location#, location.location_no,
                glentry.amount, glentry.tr_type, glbatch_view.record# as glbatchkey, glbatch_view.batch_date,
                glbatch_view.batch_no, glbatch_view.batch_title, glentry.currency, glentry.trx_amount as transamount,
                glentry.basecurr, glbatch_view.referenceno $dimselect, glentry.allocationkey, glbatch_view.ownerloc
        from    
                $glentry, $accountName glaccount, locationmst location, departmentmst department, glbatch_view $dimfrom
        where
                glentry.cny# = glbatch_view.cny#
                and glentry.batch# = glbatch_view.record#
                $amountfilter
                and glaccount.cny# = glentry.cny#
                and glaccount.record# = glentry.account#
                and location.cny# (+) = glentry.cny#
                and location.record# (+)  = glentry.location#
                and department.cny# (+) = glentry.cny#
                and department.record# (+)  = glentry.dept# " .
                   (($this->fromGLBatchEditor_Lister != '') ? '' : " and " .
                GLEntryManager::getPostStateQryFilter('glentry')) . "
                $batchFilterForView $locationclause $acctFilter $departmentclause $dimensionFilterClause $dimwhere ";
        if (!empty($mappednatAccts)) {
            $stmt = PrepINClauseStmt($stmt, $mappednatAccts, " and glentry.account# ");
        }
        $stmt[0] = $stmt[0].$orderby;
        
        $glentryRecords = QueryResult($stmt) ?: [];

        // With recent changes in Number formatting, mcp check has been removed and
        // by default user preferences will be considered first and if it is not set
        // then company preferences will be used. Also user preferences should not be considered
        // for excel/csv file exports,hence commented below piece of code
        /*if ( $mcpEnabled && !empty($glentryRecords) ) {
            for ( $counter = 0; $counter < count($glentryRecords); $counter++ ) {
                $glentryRecords[$counter]['TRANSAMOUNT'] = glFormatCurrency($glentryRecords[$counter]['TRANSAMOUNT']);
            }
        }*/
        
        // for each entry, we have to know if the entry is associated with a bill/invoice
        if ( count($glentryRecords) > 0 ) {

            $stmt[0] = "select	glentry.record#, glbatch.prbatchkey, prb.recordtype
                            from		prbatchmst prb, glbatchmst glbatch,  glentry
                            where
                                glbatch.cny# = :1
                                and glbatch.journal# = :2
                                and glbatch.batch_date between :3 and :4
                                and glbatch.modulekey != '2.GL'
                                and prb.cny# = glbatch.cny#
                                and prb.record# = glbatch.prbatchkey
                                and prb.recordtype in ('pi', 'pa', 'ri', 'ra')
                                and glentry.cny# = glbatch.cny#
                                and glentry.batch# = glbatch.record# " .
                (($this->fromGLBatchEditor_Lister != '') ? '' : " and " .
                    GLBatchManager::getPostStateQryFilter('glbatch', true, 'glentry')) . "
                                and glentry.amount != 0 $batchFilter $locationclause  ";

            $haveInvs = QueryResult($stmt);

            //Commiting the transaction.So that it will remove the records from the temp table.
            /** @noinspection PhpUndefinedVariableInspection */
            if ( $trx_flag ) {
                XACT_COMMIT("SetJrnlReportParams::DumpRecsToGlobalTempTable");
            }

            $recnos = array();
            foreach ( $haveInvs as $hasInv ) {
                $recnos[] = $hasInv['RECORD#'];
                $prbatchMap[$hasInv['RECORD#']] = $hasInv['PRBATCHKEY'];
                $rectypeMap[$hasInv['RECORD#']] = $hasInv['RECORDTYPE'];
            }

            // unset user defined dimensions while we are exporting glbatch entry to excel having allocation
            $resetuddvalues = ($this->fromGLBatchEditor_Lister != "" && $this->params['type'] === kShowExcel
                && isset($this->params['UDDVALUES'])
                && count($this->params['UDDVALUES']) > 0) ? true : false;
            foreach ( $glentryRecords as $index => $entry ) {
                if($resetuddvalues && isset($entry['ALLOCATIONKEY']) && $entry['ALLOCATIONKEY'] !== "") {
                    foreach($this->params['UDDVALUES'] as $udd) {
                        unset($glentryRecords[$index][$udd]);
                    }
                }

                $glentryRecords[$index]['HASINV'] = in_array($entry['RECORD#'], $recnos);
                if ( $glentryRecords[$index]['HASINV'] ) {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $glentryRecords[$index]['PRBATCHKEY'] = $prbatchMap[$glentryRecords[$index]['RECORD#']];
                    /** @noinspection PhpUndefinedVariableInspection */
                    $glentryRecords[$index]['RECORDTYPE'] = $rectypeMap[$glentryRecords[$index]['RECORD#']];
                }
            }
        }

        // GET AP OFFSET ACCOUNT FOR BILLS/INVOICES
        GetModulePreferences($kAPid, $modprefs);
        GetModulePreferences($kARid, $modprefs);

        $apOffset = $modprefs['PI-ACCOUNT'];
        $apJrnl = $modprefs['PI-JOURNAL'];
        $arOffset = $modprefs['RI-ACCOUNT'];
        $arJrnl = $modprefs['RI-JOURNAL'];
        if ( $jrnlSymbol == $apJrnl ) {
            $offsetAcct = $apOffset;
        } else if ( $jrnlSymbol == $arJrnl ) {
            $offsetAcct = $arOffset;
        }

        $basecurr = ( IsMCPEnabled() ) ? GetBaseCurrency() : '';

        /** @noinspection PhpUndefinedVariableInspection */
        $jrnlReport = array(
            'TITLE' => $title,
            'TITLE2' => $title2,
            'TITLECOMMENT' => $titlecomment,
            'location' => $locids[0],
            'department' => $deptids[0],
            'symbol' => $jrnlSymbol,
            'name' => $glBatchQResult[0]['TITLE'],
            'style' => $style,
            'period' => $periodRecord,
            'entries' => $glentryRecords,
            'offset' => $offsetAcct,
            'basecurr' => $basecurr,
            'dimheaders' => $dimqryParams['header'],
            'dimdbcol' => $dimqryParams['dbcol'],
        );

        return $jrnlReport;
    }

    /**
     * Check whether the given GLBatch has allocation or not
     *
     * @param int|string $journalRec GLBatch Record#
     *
     * @return bool True if it has allocation else false
     */
    private function doesEntryHasAllcoation($journalRec)
    {
        $stmt[] = "select	glentry.allocationkey
                            from		glbatchmst glbatch,  glentry
                            where
                                glbatch.cny# = :1
                                and glbatch.journal# = :2
                                and glbatch.batch_no = :3
                                and glentry.cny# = glbatch.cny#
                                and glentry.batch# = glbatch.record#
                                and glentry.allocationkey is not null";

        $stmt[] = GetMyCompany();
        $stmt[] = $journalRec;
        $stmt[] = $this->batch_no;
        $haveAlloc = QueryResult($stmt);

        return (isset($haveAlloc[0]['ALLOCATIONKEY']) && $haveAlloc[0]['ALLOCATIONKEY'] !== "") ? true : false;
    }

    /**
     * Prints the journal report in any one of the three styles currently available. 
     * Builds an associative array which is either converted to an XML source tree for applying XSL formatting
     * 
     * @param array &$jrnlReport jrnlReport
     * 
     * @return array|false Lines to be printed
     */
    private function PrintJrnlReport(&$jrnlReport)
    {
        if ( empty($jrnlReport) ) {
            return false;
        }
        $_showrefnum = $this->params['showrefnum'];

        // Always headers remain same for all the journals
        $this->JrnlPrintReportHeader($lines, $jrnlReport[0]);

        $jrnlReportOp = GetOperationId('gl/reports/gljournal');
        $topPerm = CheckPermissionPriority(GetOperationId('gl/lists/journal'));

        $lines['jrnlreport']['0']['name'] = $jrnlReport[0]['name'];
        $lines['jrnlreport']['0']['myop'] = $jrnlReportOp;
        $lines['jrnlreport']['0']['jrnlperm'] = $topPerm;
        $lines['jrnlreport']['0']['location'] = $jrnlReport[0]['location'];
        $lines['jrnlreport']['0']['department'] = $jrnlReport[0]['department'];
        $lines['jrnlreport']['0']['showrefnum'] = ($_showrefnum === 'true') ? '1' : '';
        $lines['jrnlreport']['0']['fromeditorlister'] = ($this->fromGLBatchEditor_Lister !== '') ? 'Y' : 'F';
        $lines['jrnlreport']['0']['dimtransstyle'] = ( $this->params['type'] === kShowExcel ) ? "23" : "24";

        $dimensionHeader = $jrnlReport[0]['dimheaders'];
        $lines['jrnlreport']['0']['dimensionHeader'][] = $dimensionHeader;
        $lines['dimcount'] = countArray($dimensionHeader['dimension']);

        $mcpEnabled = IsMCPSubscribed();

        if ( $mcpEnabled ) {
            $lines['jrnlreport']['0']['multicurrency'] = 'true';
        }

        $terms = $this->GenerateRelabeledTerms();
        foreach ( $terms as $key => $val ) {
            $lines['jrnlreport']['0'][$key] = $val;
        }

        $grandCRTotal = $grandDBTotal = 0;
        $this->hasStatTxn = 'F';
        $this->hasNrmTxn = 'F';

        foreach ( $jrnlReport as $key => $jrlRpt ) {
            if ( !isl_strcasecmp($this->journalTypeMap[$jrlRpt['symbol']]['STATISTICAL'], "T") ) {
                $lines['jrnlreport']['0']['jrnl'][$key]['ISSTAT'] = "T";
                $isstat = true;
                $this->hasStatTxn = 'T';
            } else {
                $lines['jrnlreport']['0']['jrnl'][$key]['ISSTAT'] = "F";
                $isstat = false;
                $this->hasNrmTxn = 'T';
            }

            $lines['jrnlreport']['0']['jrnl'][$key]['name'] = $jrlRpt['name'];

            // if there is no entries then return false
            if ( !$jrlRpt['entries'] ) {
                continue;
            }

            $entryCnt = countArray($jrlRpt['entries']);
            $currEntryRow = 0;
            $currTransRow = 0;
            $jrnlCRTotal = 0;
            $jrnlDBTotal = 0;
            $crTotal = $dbTotal = 0;
            /** @noinspection PhpUnusedLocalVariableInspection */
            $didCnt = 0;
            $finished = false;
            $sublines = array();

            while ( $currEntryRow < $entryCnt && !$finished ) {
                $didCnt = $this->doTransactionPrint(
                    $jrlRpt, $sublines, $currTransRow, $currEntryRow, $crTotal, 
                    $dbTotal, $topPerm, $jrlRpt['dimdbcol']
                );
                $currEntryRow += $didCnt;
                if ( !($didCnt > 0) ) {
                    $finished = true;
                } else {
                    $currTransRow++;
                    $jrnlCRTotal += $crTotal;
                    $jrnlDBTotal += $dbTotal;
                    if ( !$isstat ) {
                        $grandCRTotal += $crTotal;
                        $grandDBTotal += $dbTotal;
                    }
                }
            }

            $lines['jrnlreport']['0']['jrnl'][$key]['db'] = $jrnlDBTotal;
            $lines['jrnlreport']['0']['jrnl'][$key]['cr'] = $jrnlCRTotal;

            $lines['jrnlreport']['0']['jrnl'][$key]['trans'] =
                INTACCTarray_merge($lines['jrnlreport']['0']['jrnl'][$key]['trans'], $sublines);
        }

        // Set to T only while displaying multiple journals
        if ( $this->params['gljrnltype'] !== 'Single' ) {
            $lines['jrnlreport']['0']['multiple'] = 'T';

            $lines['jrnlreport']['0']['granddb'] = $grandDBTotal;
            $lines['jrnlreport']['0']['grandcr'] = $grandCRTotal;
            $lines['jrnlreport']['0']['name'] = "Multiple Journals";
        } else {
            $lines['jrnlreport']['0']['multiple'] = 'F';
        }

        if ( $this->params['gljrnltype'] === 'All' ) {
            $lines['jrnlreport']['0']['name'] = "All Journals";
        }

        $lines['jrnlreport']['0']['hasStatTxn'] = $this->hasStatTxn;
        $lines['jrnlreport']['0']['hasNrmTxn'] = $this->hasNrmTxn;

        return $lines;
    }

    /**
     * Adds the header and other overall report information to the associative 
     * array data structure being built to display the journal report
     * 
     * @param array &$lines      the associative array data structure
     * @param array &$jrnlReport jrnlReport
     */
    private function JrnlPrintReportHeader(&$lines, &$jrnlReport)
    {
        global $gJrnlReport;
        $_done = Request::$r->_done;
        $_sess = Session::getKey();
        $gPeriodRecords = $jrnlReport['period'];

        if ( isset($gJrnlReport['TITLE']) && $gJrnlReport['TITLE'] != '' ) {
            $lines['jrnlreport']['0']['title'] = $gJrnlReport['TITLE'];
        } else {
            $lines['jrnlreport']['0']['title'] = $jrnlReport['TITLE'];
        }
        if ( isset($gJrnlReport['TITLE2']) && $gJrnlReport['TITLE2'] != '' ) {
            $lines['jrnlreport']['0']['title2'] = $gJrnlReport['TITLE2'];
        } else {
            $lines['jrnlreport']['0']['title2'] = $jrnlReport['TITLE2'];
        }
        $lines['jrnlreport']['0']['titlecomment'] = $gJrnlReport['TITLECOMMENT'];
        $lines['jrnlreport']['0']['sess'] = $_sess;
        $lines['jrnlreport']['0']['done'] = $_done;
        $lines['jrnlreport']['0']['co'] =  $this->GetReportTitle($this->params['ORIG_LOCATION'] );

        $lines['jrnlreport']['0']['period'] = I18N::getSingleToken('IA.JOURNAL_PERIOD', [
            ['name' => 'NAME', 'value' => $gPeriodRecords['NAME']],
            ['name' => 'START_DATE', 'value' => FormatDateForDisplay($gPeriodRecords['START_DATE'])],
            ['name' => 'END_DATE', 'value' => FormatDateForDisplay($gPeriodRecords['END_DATE'])]
        ]);
        $lines['jrnlreport']['0']['date'] = GetCurrentDate();
        $lines['jrnlreport']["0"]['reportdate'] = GetCurrentDate(IADATE_USRFORMAT);
        $lines['jrnlreport']["0"]['reporttime'] = GetCurrentTZTime();
        // for megl, we want the location context for drilldown into line items.
        $lines['jrnlreport']['0']['ismegl'] = (IsMultiEntityCompany()) ? 'Y' : 'N';
        $lines['jrnlreport']['0']['locationcontext'] = (GetContextLocation()) ?: '';
        $lines['jrnlreport']['0']['companyid'] = GetMyCompany();
        
        $jourAcct_string = $this->textHelper->GT('IA.ACCOUNT_NO');
        if ($this->params['REPORTINGACCOUNTSET']) {
            //TODO-i18n: does ucwords work in multi-lingual scenario??
            $lines['jrnlreport']['0']['reportJour_string'] = I18N::getSingleToken('IA.REPORT_JOUR_STRING');
        } else {
            $lines['jrnlreport']['0']['reportJour_string'] = $jourAcct_string;
        }
    }

    /**
     * Adds a transaction's header, entry data, and total information to the associative array
     * data structure being built to display a journal report
     *
     * @param array      $jrnlReport jrnlReport
     * @param array      $sublines   sublines
     * @param int        $transRow   the index at which the current transaction's data resides
     *                               within the $lines subarray of transactions
     * @param int        $startRow   the index of the first of this transaction's entries as found
     *                               in the subarray of entries of the report's global variable
     * @param int|float|string        $crTotal    the sum of the credit amounts of the transaction's entries
     * @param int|float|string        $dbTotal    the sum of the debit amounts of the transaction's entries
     * @param string $topPerm    topPerm
     * @param array      $dimDBCol   dimDBCol
     *
     * @return int
     */
    private function doTransactionPrint(
        &$jrnlReport, &$sublines, $transRow, $startRow, &$crTotal, &$dbTotal, $topPerm = '', $dimDBCol = array()
    ) {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $kAPid = Globals::$g->kAPid;
        /** @noinspection PhpUnusedLocalVariableInspection */
        $kARid = Globals::$g->kARid;

        $currEntry = $jrnlReport['entries'][$startRow];
        $currEntryNo = $currEntry['BATCH_NO'];
        $offsetAcct = $currEntry['OFFSET'];
        $crTotal = $dbTotal = 0;

        $sublines[$transRow]['date'] = $jrnlReport['entries'][$startRow]['BATCH_DATE'];
        $sublines[$transRow]['no'] = $jrnlReport['entries'][$startRow]['BATCH_NO'];
        $sublines[$transRow]['title'] = $jrnlReport['entries'][$startRow]['BATCH_TITLE'];
        $sublines[$transRow]['refnum'] =util_encode($jrnlReport['entries'][$startRow]['REFERENCENO']);
        $currRow = $startRow;
        $didCount = 0;

        $sublines[$transRow]['db'] = $dbTotal;
        $sublines[$transRow]['cr'] = $crTotal;

        while ( $currEntryNo != '' && $currEntry['BATCH_NO'] != ''
        && $currEntry['BATCH_NO'] == $currEntryNo ) {
            $dimTrans = array();

            $dbAmt = (intval($currEntry['TR_TYPE']) == 1) ? $currEntry['AMOUNT'] : 0;
            $crAmt = (intval($currEntry['TR_TYPE']) != 1) ? $currEntry['AMOUNT'] : 0;
            $currency = $currEntry['CURRENCY'] != '' ? $currEntry['CURRENCY'] : $jrnlReport['currency'];
            $basecurr = $currEntry['BASECURR'] != '' ? $currEntry['BASECURR'] : $jrnlReport['basecurr'];

            if ( $currEntry['ACCT_NO'] != $offsetAcct && $currEntry['HASINV'] ) {
                $drillStr = "invoice_lineitems";
            } else {
                $drillStr = "journal_entry";
            }

            // Build the drill down URL for the GL transaction
            $drillDownParams = array(
                'j' => $jrnlReport['symbol'],
            //            'j' => $journalMap[$currEntry['JOURNAL#']],
                'do' => $topPerm,
                'r' => $currEntry['GLBATCHKEY'],
                'fo' => 'gl',
            );

            foreach ( $dimDBCol as $dimcol ) {
                $dimTrans['DT'][] = array($currEntry[$dimcol]);
            }

            if ($this->nativeToRptAcs != ''
                && isset($this->nativeToRptAcs)
            ) {
                    $baseAcctNo = $currEntry['ACCT_NO'];
                    //Something is available for translation
                if ($this->nativeToRptAcs[$baseAcctNo] != '') {
                    //Translate
                    $currEntry['DRILL_TO_BILL_ACCT_NO'] = $currEntry['ACCT_NO'];
                    $currEntry['ACCT_NO'] = $this->nativeToRptAcs[$baseAcctNo]['REPORTINGACNO'];
                    $currEntry['TITLE'] = $this->nativeToRptAcs[$baseAcctNo]['REPORTINGACTITLE'];
                } else {
                    $this->hasUnMapeedAccounts = true;
                    $currEntry['DRILL_TO_BILL_ACCT_NO'] = $currEntry['ACCT_NO'];
                    $currEntry['ACCT_NO'] = "*" . $currEntry['ACCT_NO'];

                }
            } 
            $sublines[$transRow]['entry'][] =
                array(
                    'prbatchkey' => $currEntry['PRBATCHKEY'],
                    'rectype' => $currEntry['RECORDTYPE'],
                    'glentrykey' => $currEntry['RECORD#'],
                    'doc' => $currEntry['DOCUMENT'],
                    'memo' => $currEntry['DESCRIPTION'],
                    'drillToBill_acct_no' => $currEntry['DRILL_TO_BILL_ACCT_NO'],
                    'acct_no' => $currEntry['ACCT_NO'],
                    'acct_title' => util_encode($currEntry['TITLE']),
                    'deptrecno' => $currEntry['DEPT#'],
                    'dept' => $currEntry['DEPT_NO'],
                    'locrecno' => $currEntry['LOCATION#'],
                    'loc' => $currEntry['LOCATION_NO'],
                    'ownerloc' => $currEntry['OWNERLOC'],
                    'currency' => $currEntry['CURRENCY'] ?? $currency,
                    'basecurr' => $currEntry['BASECURR'] ?? $basecurr,
                    'transamount' => $currEntry['TRANSAMOUNT'],
                    'db' => $dbAmt,
                    'cr' => $crAmt,
                    'drill' => $drillStr,
                    'glbatchscript' => GLBatchEditor::buildDrillDownUrl($drillDownParams),
                    'tr_type' => $currEntry['TR_TYPE'],
                    'dimTrans' => array($dimTrans),
                );

            $crTotal = ibcadd($crTotal, $crAmt);
            $dbTotal = ibcadd($dbTotal, $dbAmt);

            $currRow++;
            $didCount++;
            $currEntry = $jrnlReport['entries'][$currRow];
        }
        $sublines[$transRow]['db'] = $dbTotal;
        $sublines[$transRow]['cr'] = $crTotal;
        //$sublines[$transRow]['entry'] = $trans;

        return $didCount;
    }
}
