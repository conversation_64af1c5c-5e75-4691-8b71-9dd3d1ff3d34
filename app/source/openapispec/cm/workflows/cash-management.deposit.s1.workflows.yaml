cash-management-deposit-actions-reverse-request:
  type: object
  x-mappedTo: deposit
  properties:
    key:
      type: string
      description: System-assigned key for the deposit.
      x-mappedTo: RECORDNO
      example: '49'
    reversedDate:
      type: string
      format: date
      description: Date this transaction is reversed.
      x-mappedTo: VOIDDATE
      example: '2024-04-15'
    notes:
      type: string
      description: Notes or comments about the reason for the reverse of deposit.
      x-mappedTo: DESCRIPTION
      example: Reversed the deposit  for duplicate entry
  required:
    - key
    - reversedDate
cash-management-deposit-actions-reverse-response:
  type: object
  x-mappedTo: deposit
  properties:
    key:
      type: string
      description: System-assigned unique record key for the new reverse deposit.
      x-mappedTo: RECORDNO
      readOnly: true
      example: '50'
    id:
      type: string
      x-mappedTo: RECORDNO
      description: Unique identifier for the new reverse deposit.
      readOnly: true
      example: '50'
    href:
      type: string
      description: URL endpoint for the reverse deposit.
      readOnly: true
      example: /objects/cash-management/deposit/50
    state:
      type: string
      description: Deposit state after reverse
      readOnly: true
      x-mappedTo: STATE
      example: reversed