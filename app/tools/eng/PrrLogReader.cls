<?php

class PrrLogReader
{
    const TMP = '/tmp';

    const TABLE_COLUMN_TIMESTAMP = 0;
    const TABLE_COLUMN_LOGINID = 1;
    const TABLE_COLUMN_CNY = 2;
    const TABLE_COLUMN_RELID = 3;
    const TABLE_COLUMN_MESSAGE = 4;

    /**
     * @var string $loginId
     */
    private $loginId = '';

    /**
     * @var string $fileName
     */
    private $fileName = '';

    /**
     * @var string[] $errorMessages
     */
    private $errorMessages = [];

    /**
     * @var bool $searchInMainFile
     */
    private $searchInMainFile = false;

    /**
     * @var bool $searchByCompany
     */
    private $searchByCompany = false;

    /**
     * @var bool $exportFileName
     */
    private $exportFileName = '';

    /**
     * @var bool $allowDelete
     */
    private $allowDelete = true;

    /**
     * @param string $loginId
     * @param int $companyId
     */
    public function __construct(string $loginId, int $companyId)
    {
        if ( $loginId ) {
            $this->loginId = $loginId;
            if ( $companyId ) {
                $this->fileName = "prr.log.$this->loginId.$companyId.csv";
                $this->exportFileName = $this->fileName;
            } else {
                $this->fileName = "prr.log.$this->loginId.main.csv";
                $this->exportFileName = "prr\.log\.$this->loginId.*\.csv";
                $this->searchInMainFile = true;
            }
        } else if ( $companyId ) {
            $this->fileName = "prr\.log.*\.$companyId\.csv";
            $this->exportFileName = $this->fileName;
            $this->searchByCompany = true;
        }
    }

    /**
     * @return string
     */
    private function getFilePath()
    {
        return self::TMP . "/$this->fileName";
    }

    /**
     * @return string[]
     */
    private function getColumnsNameMapping()
    {
        return $this->searchInMainFile
            ?
            [
                0 => 'timestamp',
                1 => 'user',
                2 => 'cny',
                3 => 'message',
            ]
            :
            [
                0 => 'timestamp',
                1 => 'user',
                2 => 'cny',
                3 => 'rel_id',
                4 => 'message',
            ];
    }

    /**
     * @return int[]
     */
    private function getTableColumnMapping()
    {
        if ( $this->searchInMainFile ) {
            $relIdIndex = -1;
            $messageIndex = 3;
        } else {
            $relIdIndex = 3;
            $messageIndex = 4;
        }

        return [
            self::TABLE_COLUMN_TIMESTAMP => 0,
            self::TABLE_COLUMN_LOGINID   => 1,
            self::TABLE_COLUMN_CNY       => 2,
            self::TABLE_COLUMN_RELID     => $relIdIndex,
            self::TABLE_COLUMN_MESSAGE   => $messageIndex,
        ];
    }

    /**
     * @param string $file
     * @param array  $header
     *
     * @return bool
     */
    private function testColumnsMatch(string $file, array $header)
    {
        $columnMapping = $this->getColumnsNameMapping();

        $ok = true;
        foreach ( $header as $key => $name ) {
            if ( !isset($header[$key]) || $columnMapping[$key] != $name ) {
                $this->errorMessages[] = "The columns in the file $file are changed";
                $ok = false;
                break;
            }
        }

        return $ok;
    }

    /**
     * @return string[]
     */
    public function getLog()
    {
        $log = [];
        if ( $this->fileName ) {
            $files = $this->searchByCompany ? glob($this->getFilePath()) : [ $this->getFilePath() ];
            if ( $files ) {
                $tableColumnMapping = $this->getTableColumnMapping();
                foreach ( $files as $file ) {
                    $content = file_get_contents($file);
                    if ( $content !== false ) {
                        $fileLog = explode(PHP_EOL, $content);
                        if ( count($fileLog) > 1 ) {
                            $headerColumns = explode(',', $fileLog[0]);
                            if ( !$this->testColumnsMatch($file, $headerColumns) ) {
                                continue;
                            }
                            unset($fileLog[0]);
                            $numberOfColumns = count($headerColumns);
                            $messageMatch = [];
                            foreach ( $fileLog as $line ) {
                                if ( $line ) {
                                    $line = explode(",", $line, $numberOfColumns);
                                    $newLog = [];
                                    foreach ( $tableColumnMapping as $key => $value ) {
                                        $columnValue = ( $value == -1 ) ? '' : $line[$value];
                                        if ( $key == self::TABLE_COLUMN_MESSAGE ) {
                                            preg_match('/(?<=").*(?=")/', $columnValue, $messageMatch);
                                            $columnValue = $messageMatch[0] ?? '';
                                        }
                                        $newLog[$key] = $columnValue;
                                    }
                                    $log = array_merge([ $newLog ], $log);
                                }
                            }
                        }
                    } else {
                        $this->errorMessages[] = "A log file with the name $this->fileName was not found";
                        $this->allowDelete = false;
                    }
                }
            }
        }

        return $log;
    }

    /**
     * @return array
     */
    private function getFilesToExport()
    {
        $exportContent = [];
        $files = glob(self::TMP . "/$this->exportFileName");
        foreach ( $files as $fileName ) {
            $content = file_get_contents($fileName);
            if ( $content ) {
                $exportContent[basename($fileName)] = file_get_contents($fileName);
            } else {
                $this->errorMessages[] = "Unable to read the content of the file $fileName";
            }
        }

        return $exportContent;
    }

    public function export()
    {
        $exportContent = $this->getFilesToExport();
        if ( $exportContent ) {
            $zip = new ZipArchive();
            $zip_name = "prr_log.zip";
            $res = $zip->open(self::TMP . "/$zip_name", ZipArchive::CREATE);
            if ( $res === true ) {
                foreach ( $exportContent as $fileName => $fileContent ) {
                    $zip->addFromString($fileName, $fileContent);
                }
                if ( $this->errorMessages ) {
                    $zip->addFromString("export_file_errors.csv", "Message\n"
                                                                  . implode("\n", $this->errorMessages));
                }
                $zip->close();

                header("Content-type: application/zip");
                header("Content-Disposition: attachment; filename=" . $zip_name);
                $archiveContent = file_get_contents(self::TMP . "/$zip_name");
                echo $archiveContent === false ? "Unable to open the archive content" : $archiveContent;
                unlink(self::TMP . "/$zip_name");
            } else {
                echo "Could not open archive";
            }
        } else {
            echo "Nothing to export";
        }

        exit();
    }

    /**
     * @return string[]
     */
    public function getLoginIds()
    {
        $loginIds = [];
        $files = glob(self::TMP . "/prr\.log.*\.csv");
        $regex = "(?<=prr\.log\.)(.*)\.([^.]*)\.[^.]*";
        foreach ( $files as $file ) {
            preg_match("/$regex/", $file, $matches);
            $loginIds[$matches[1]] = true;
        }

        return array_keys($loginIds);
    }

    /**
     * @return string[]
     */
    public function getErrorMessages()
    {
        return $this->errorMessages;
    }

    /**
     * @return bool
     */
    public function isAllowedToDelete()
    {
        return $this->allowDelete;
    }

    /**
     * @param bool $deleteCompanies
     */
    public function deleteFile(bool $deleteCompanies)
    {
        $files = $deleteCompanies ? glob(self::TMP . "/prr\.log\.$this->loginId.*\.csv") : [ $this->getFilePath() ];
        foreach ( $files as $fileName ) {
            if ( !unlink($fileName) ) {
                $this->errorMessages[] = "Unable to delete the file with the name $fileName";
            }
        }
    }

    /**
     * @param string $selectedHour
     */
    function showHours(string $selectedHour)
    {
        for ( $i = 0; $i < 24; $i++ ) {
            $hour = str_pad($i, 2, "0", STR_PAD_LEFT);
            echo "<option value='$hour' " . ( ( $i == $selectedHour ) ? "selected" : "" ) . ">$hour</option>";
        }
    }

    /**
     * @param string $selectedMinute
     */
    function showMinutes(string $selectedMinute)
    {
        for ( $i = 0; $i < 60; $i++ ) {
            $minute = str_pad($i, 2, "0", STR_PAD_LEFT);
            echo "<option value='$minute' " . ( ( $i == $selectedMinute ) ? "selected" : "" ) . ">$minute</option>";
        }
    }

    /**
     * @param string[] $loginIds
     * @param string   $selectedLoginId
     */
    function showLoginIds(array $loginIds, string $selectedLoginId)
    {
        foreach ( $loginIds as $loginId ) {
            echo "<option value='$loginId' " . ( ( $loginId == $selectedLoginId ) ? "selected" : "" )
                 . ">$loginId</option>";
        }
    }

    /**
     * @param int $selectedPageSize
     */
    function showPageSize(int $selectedPageSize)
    {
        $sizes = [ 10, 25, 50, 100, 200 ];
        foreach ( $sizes as $size ) {
            echo "<option value='$size' " . ( ( $size == $selectedPageSize ) ? "selected" : "" )
                 . ">$size</option>";
        }
    }

    /**
     * @param string $selectedLogLevel
     */
    function showLogLevel(string $selectedLogLevel)
    {
        $levels = [ 'All', 'Error', 'Success' ];
        foreach ( $levels as $level ) {
            echo "<option value='$level' " . ( ( $level == $selectedLogLevel ) ? "selected" : "" )
                 . ">$level</option>";
        }
    }
}
