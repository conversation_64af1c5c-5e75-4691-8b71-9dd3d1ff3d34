title: accounts-receivable-territory-group
x-mappedTo: territorygroup
type: object
description: Territory groups
properties:
  key:
    type: string
    description: System-assigned key for the territory-group.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: Territoty Group ID.
    x-mappedTo: ID
    x-mutable: false
    example: 'T1'
  href:
    type: string
    description: Endpoint for the territory-group.
    readOnly: true
    example: /objects/accounts-receivable/territory-group/23
  name:
    type: string
    description: Territory group name
    x-mappedTo: NAME
    example: 'Group A'
  description:
    type: string
    description: Description of the territory group
    x-mappedTo: DESCRIPTION
    maxLength: 500
    example: 'Group 1'
  members:
    type: array
    description: Territory group members.
    x-mappedTo: MEMBERS
    x-object: accounts-receivable/territory-group-member
    minItems: 1
    items:
      $ref: accounts-receivable.territory-group-member.s1.schema.yaml
  entity:
    $ref: ../../common/references/entity-ref.s1.schema.yaml
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml