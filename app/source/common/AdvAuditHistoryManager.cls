<?php
/**
 * Manager file for the Advanced Audit History object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2018 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class AdvAuditHistoryManager extends EntityManager
{
    const KEY_SEP = '^';

    const ADVAUDIT_TABLE_NONSTORAGE = 'ADVAUDITHISTORY'; // TODO vlad: unused for a while, remove it
    const ADVAUDIT_TABLE_STORAGE    = 'ADVAUDITSTORAGEHISTORY';
    const ADVAUDIT_TABLE_STORAGE_MST    = 'ADVAUDITSTORAGEHISTORYMST';

    /**
     * @var string[] $compressTypes Mapping to reduce size of saved keys in read query.
     */
    private static $compressTypes = [ 'CONTACT' => 'C', 'CUSTOMER' => 'X',
        'MAILADDRESS' => 'M', 'VENDOR' => 'V', /* 'EMPLOYEE' => 'E', 'USERINFO' => 'U' */ ];
    /**
     * @var string[] $uncompressTypes Mapping to expand compressed type.
     */
    private static $uncompressTypes = [ 'C' => 'contact', 'X' => 'customer',
        'M' => 'mailaddress', 'V' => 'vendor', /* 'E' => 'employee', 'U' => 'userinfo' */ ];
    /**
     * @var AdvAuditHistoryManager $postProcessObj Constructed object to use in post-processing.
     */
    private static $postProcessObj;
    /**
     * @var array $cachedMgrs Array of cached entity managers.
     */
    private $cachedMgrs;
    /**
     * @var string[][] $cachedObjectNames Translated ids to names, only get once so cache. Indexed by
     * type (e.g. 'reporttype') and key.
     */
    private $cachedObjectNames = [];
    /**
     * @var string $filterClauses Object type where clause (used for filtering).
     */
    private $filterClauses;
    /**
     * @var string[][] $matchOperands Array of object types / keys to match in filtering.
     */
    private $matchOperands;
    /**
     * @var string[][] $nomatchOperands Array of object types / keys to NOT match in filtering.
     */
    private $nomatchOperands;
    /**
     * @var string $viewToUse View to use (pre-audit storage or post-audit storage).
     */
    private $viewToUse;
    /**
     * @var int $maxReportRecs Maximum number of report recs to generate.
     */
    private $maxReportRecs = 200000;
    /**
     * @var array $fieldInfo Override of field info needed for field transformations.
     */
    private $fieldInfo = [];
    /**
     * @var bool $localRowsPending Have we processed local rows in getByRows?
     */
    private $localRowsPending;

    /**
     * @var bool $storageRowsPending Have we processed AWS storage rows in getByRows?
     */
    private $storageRowsPending;

    /**
     * @var array|null $rowsSoFar Cumulative rows gathers between local and AWS.
     */
    private $rowsSoFar = null;

    /**
     * @var array $opIdLabels
     */
    private $opIdLabels = [];
    
    /** @var bool $requiresRecordNo */
    private $requiresRecordNo = false;
    
    /** @var bool $useMstForSnowflake */
    private bool $useMstForSnowflake = false;

    /**
     * @param array $_params
     */
    public function __construct($_params = array())
    {
        parent::__construct($_params);

        if (GetMyAdminLevel() == 0 || !AdvAuditTracking::isAdvAuditHistoryReportEnabled()) {
            $this->_schemas[$this->_entity]['noadhocreport'] = true;
            $this->_schemas[$this->_entity]['postProcessObj'] = null;
        } else {
            $this->_schemas[$this->_entity]['postProcessObj'] = $this;
            self::$postProcessObj = $this;
        }
        $auditTrailSession = AuditTrailSession::getInstance();
        $this->useMstForSnowflake = AuditTrailUtils::useMstViewsForSnowflakeQueriesInReports();
        if ($auditTrailSession->isAuditStorageViewEnabled()) {
            $this->viewToUse =  $this->useMstForSnowflake ? self::ADVAUDIT_TABLE_STORAGE_MST : self::ADVAUDIT_TABLE_STORAGE;
            $this->_schemas[$this->_entity]['addReportSelects'] = [ 'FIELDNAME', 'NEWVAL', 'OPERATION' ];
        } else {
            $this->viewToUse = self::ADVAUDIT_TABLE_NONSTORAGE;
        }
    }

    /**
     * Process the report filters, if any.  We need to handle the object type clauses, for later use
     *  in the postProcessResults function.
     *
     * @param string[] &$touples  Parsed SQL touples of the query.
     * @param array    &$queryDef Associated query definition.
     *
     * @return bool True means no error, false means error.
     */
    public static function processFilter(&$touples, &$queryDef)
    {
        if (self::$postProcessObj) {
            self::$postProcessObj->matchOperands = [];
            self::$postProcessObj->nomatchOperands = [];
        }

        $auditTrailSession = AuditTrailSession::getInstance();
        $isStorageView = $auditTrailSession->isAuditStorageViewEnabled();

        $cnt = count($touples);
        for ($i=0; $i<$cnt; $i++) {
            $touple = $touples[$i];
            //  If there is filtering on the object type, we need to add in any advanced audit access
            //   records.  These will be filtered as they are 'exploded'
            //   in AdvAuditHistoryManager::postProcessResults.  NOTE: we don't do this for audit storage,
            //   which is already 'exploded'.
            if (!$isStorageView && stripos($touple, '.OBJECTTYPE') !== false) {
                if (self::$postProcessObj) {
                    self::$postProcessObj->filterClauses = $touple;
                }
                $touple = "((" . $touple . ") or ADVAUDITHISTORY.RAWMODE = 'S')";
            }

            if ((self::$postProcessObj) &&
             (stripos($touple, '.OBJECTKEY') !== false)) {
                self::$postProcessObj->parseATouple($touple, true);
                $touple = "((" . $touple . ") or ADVAUDITHISTORY.RAWMODE = 'S')";
            }

            if ((self::$postProcessObj) &&
             (stripos($touple, '.WORKFLOWACTION') !== false)) {
                self::$postProcessObj->parseATouple($touple, true);
            }
            $touples[$i] = $touple;
        }

        //  If we have an objecttype or objectkey, we can't allow the SQL to use a logical OR operator.
        //   This is because of the mixed database and dynamic nature of the query.
        $hasOFilter = false;
        $hasOR = false;
        if (self::checkLogicalFilter($queryDef['filters']['logicals'], $hasOFilter, $hasOR) == false) {
            Globals::$g->gErr->addError("CMN-0001", "",
             "advaudithistory queries with objecttype or objectkey cannot use OR operators");
            return false;
        }
        return true;
    }

    /**
     * Allows subclass to preset _ProcessParams params.
     *
     * @param array $params
     */
    function processParamsHook(&$params)
    {
        $auditTrailSession = AuditTrailSession::getInstance();
        if ($auditTrailSession->isAuditStorageViewEnabled()) {
            $params['SUBS_TABLE']['advaudithistory'] = $this->useMstForSnowflake ? self::ADVAUDIT_TABLE_STORAGE_MST : self::ADVAUDIT_TABLE_STORAGE;
        }
    }

    /**
     * This function gets the root table for a custom report query.  It allows the subclass to override
     *  the default table (e.g. for settings, like audit storage configuration).
     *
     * @param string     $root             Root object name.
     * @param string     $defaultRootTable Default root table already figured from schema - returned by default.
     * @param string[][] $fieldColumns     Fields being included.
     *
     * @return string Root table name, default is same as the given defaultRootTable, can be overridden.
     */
    public function getRootTable(/** @noinspection PhpUnusedParameterInspection */ $root, $defaultRootTable,
     /** @noinspection PhpUnusedParameterInspection */ $fieldColumns)
    {
        $auditTrailSession = AuditTrailSession::getInstance();
        if ($auditTrailSession->isAuditStorageViewEnabled()) {
            return $this->useMstForSnowflake ? self::ADVAUDIT_TABLE_STORAGE_MST : self::ADVAUDIT_TABLE_STORAGE;
        }
        return $defaultRootTable;
    }

    /**
     * getReportLimit - returns possible limit on number of rows in custom report of this type.
     *
     * @param CustomReport $customReportObj Custom report object.
     *
     * @return int Max number of rows to return in custom report.  0 means no limit (which is default).
     */
    function getReportLimit($customReportObj)
    {
        $cfg = GetValueForIACFGProperty('AUDIT_TRAIL');
        $maxFound = 0;
        if ($customReportObj->invokedFromUI()) {
            if (isset($cfg['AAT_MAX_REPORT_UI']) && is_numeric($cfg['AAT_MAX_REPORT_UI'])) {
                $maxFound = intval($cfg['AAT_MAX_REPORT_UI']);
            }
        } else {
            if (isset($cfg['AAT_MAX_REPORT_UI']) && is_numeric($cfg['AAT_MAX_REPORT_OFFLINE'])) {
                $maxFound = intval($cfg['AAT_MAX_REPORT_OFFLINE']);
            }
        }
        if ($maxFound > 0) {
            $this->maxReportRecs = $maxFound;
        }
        return $this->maxReportRecs;
    }

    /**
     * Check for illegal logical filter - a mixture of objecttype/key plus OR operator.  Recurses through
     *  querydef logical expression structure.
     *
     * @param array $logicals    Portion of querydef dealing with logical expressions.
     * @param bool  &$hasOFilter True means has objecttype or objectkey filter.
     * @param bool  &$hasOR      True means has OR operator.
     *
     * @return bool True means ok, false otherwise.
     */
    private static function checkLogicalFilter($logicals, &$hasOFilter, &$hasOR)
    {
        foreach ((array)$logicals as $log) {
            foreach ($log['expressions'] as $expression) {
                if ($expression['path'] == '.OBJECTTYPE' ||
                 $expression['path'] == '.OBJECTKEY') {
                    $hasOFilter = true;
                    break;
                }
            }
            if ($log['operator'] == 'OR') {
                $hasOR = true;
            }
            if ($log['logical']) {
                return self::checkLogicalFilter($log['logical'], $hasOFilter, $hasOR);
            }
        }
        if ($hasOR && $hasOFilter) {
            return false;
        }
        return true;
    }

    /**
     * Applies post processing to the report query results for Audit Storage results.
     *
     * @param CustomReport $custReportObj Custom report object.
     * @param string[][]   &$rawdata      Raw access data as read from DB, will be modified with dynamic content.
     */
    public function postProcessAuditStorage($custReportObj, &$rawdata)
    {
        $aliases = $custReportObj->nexusDB->GetAllFieldAlias();

        AuditTrail::getAATActions($opLabels, $opVals);
        $actionMap = array_combine($opVals, $opLabels);
        $wfAlias = null;
        foreach ($aliases as $aliasKey => $aliasValue) {
            if (strpos($aliasKey, "WORKFLOWACTION") !== false) {
                $wfAlias = $aliasValue;
                break;
            }
        }

        if ($wfAlias) {
            foreach ($rawdata as &$rec) {
                if (isset($actionMap[$rec[$wfAlias]])) {
                    $rec[$wfAlias] = $actionMap[$rec[$wfAlias]];
                } else {
                    $rec[$wfAlias] = I18N::getSingleToken('IA.UNKNOWN_ACTION_CODE_PLACEHOLDER', [
                        ['name' => 'ACTION_CODE', 'value' => $rec[$wfAlias] ]
                    ]);
                }
            }
        }
    }

    /**
     * Applies post processing to the report query results, resolving dynamic content (e.g. exploding access).
     *
     * @param CustomReport $custReportObj Custom report object.
     * @param string       $root          Name of root.
     * @param string[][]   &$rawdata      Raw access data as read from DB, will be modified with dynamic content.
     */
    public function postProcessResults($custReportObj, /** @noinspection PhpUnusedParameterInspection */ $root,
     &$rawdata)
    {
        
        $auditTrailSession = AuditTrailSession::getInstance();
        if ($auditTrailSession->isAuditStorageViewEnabled()) {
            $this->postProcessAuditStorage($custReportObj, $rawdata);
        } else {
            $aliases = $custReportObj->nexusDB->GetAllFieldAlias();
            $viewToUse = $this->viewToUse;
            $typeAlias = $aliases[$viewToUse.'.OBJECTTYPE'];
            $keyAlias = $aliases[$viewToUse.'.OBJECTKEY'];
            $actionAlias = $aliases[$viewToUse.'.WORKFLOWACTION'];
            $recordUrlAlias = $aliases[$viewToUse.'.RECORD_URL'];

            //  Make links only for HTML output, otherwise output the URL (not the label).
            $makeLinks = ($custReportObj->params['type'] == '_html') ? true : false;

            $this->explodeAccessRecs($rawdata, $typeAlias, $keyAlias, $actionAlias,
             $recordUrlAlias, $makeLinks);
        }

        //  Log run of history report.
        $auditSession = AuditTrailSession::getInstance();
        $auditSession->trackOperation(AuditTrailSession::OP_AAT_HISTORY_REPORT, $this->getAuditEntity(), '', null);
    }

    /**
     * Explodes advanced audit access records into individual access records.
     *
     * @param string[][] &$rawdata        Raw access data as read from DB.
     * @param string     $typeAlias       Name of key in rawData for the object type (e.g. C1).
     * @param string     $keyAlias        Name of key in rawData for the key name.
     * @param string     $actionAlias     Name of key in rawData for the action.
     * @param string     $recordUrlAlias  Name of key in rawData for the record url.
     * @param bool       $makeLinks       True means format record url as expected by formatter.
     */
    private function explodeAccessRecs(&$rawdata, $typeAlias, $keyAlias, $actionAlias,
     $recordUrlAlias, $makeLinks)
    {
        $source = "explodeAccessRecs";
        /** @noinspection PhpUnusedLocalVariableInspection */
        $restorer = startManagedTimer($source);
        $out = [];
        $allKeys = [];

        //  If there are filter expressions, then parse them to extract which records we want.
        $this->parseFilters($this->filterClauses);

        $filterByType = count($this->matchOperands['objecttype']) > 0 ? true : false;
        $filterByUnType = count($this->nomatchOperands['objecttype']) > 0 ? true : false;

        $keyCnt = count($this->matchOperands['objectkey']);
        $filterKey = null;
        $filterByKeyExactly = false;
        if ($keyCnt > 0) {
            if ($keyCnt == 1) {
                $filterKey = $this->matchOperands['objectkey'][0];
                $filterByKeyExactly = true;
            }
        }
        $cnt = 0;
        foreach ($rawdata as $rec) {

            //  Expand AAT records to one per referenced record by decomposing the blob data.
            if ($rec['RAWMODE'] == AuditTrail::AUDITTRAIL_EVENT_MULTI_ACCESS) {
                if ($actionAlias) {
                    $rec[$actionAlias] = AuditTrail::opIdToLabel($rec['WORKFLOWACTION']);
                }
                if ($rec['BLOB']) {
                    $blobData = unserialize(databaseStringUncompress($rec['BLOB']));
                    unset($rec['BLOB']);

                    if ($rec['OBJECTKEY'] == 'access') {
                        //  Generate a row for each reference (object type/key).
                        //StartTimer($source."loop");
                        foreach ($blobData as $objType => $keys) {

                            //  Since the types are encoded and we don't know them until now, this is where
                            //   we filter for matches and nomatches.
                            if ($filterByType) {
                                if (!in_array($objType, $this->matchOperands['objecttype'])) {
                                    continue;
                                }
                            }
                            if ($filterByUnType) {
                                if (in_array($objType, $this->nomatchOperands['objecttype'])) {
                                    continue;
                                }
                            }
                            $lowerType = strtolower($objType);
                            foreach ($keys as $key) {
                                if (!$key) {
                                    AdvAuditTracking::logMessage(
                                        "Found empty key for $objType, blob=" . ppS($blobData)
                                    );
                                    continue;
                                }
                                if ($filterByKeyExactly && $key != $filterKey) {
                                    continue;
                                }
                                $outRec = $rec;
                                if ($typeAlias) {
                                    $outRec[$typeAlias] = $lowerType;
                                }
                                if ($keyAlias) {
                                    $outRec[$keyAlias] = $key;
                                }
                                if ($recordUrlAlias) {
                                    $outRec[$recordUrlAlias] = $this->makeRecordUrl($objType, $key, $makeLinks);
                                }
                                $out[] = $outRec;
                                if (++$cnt >= $this->maxReportRecs) {
                                    break;
                                }
                                $allKeys[$objType][$key] = 1;
                            }
                            if ($cnt >= $this->maxReportRecs) {
                                break;
                            }
                        }
                        if ($cnt >= $this->maxReportRecs) {
                            break;
                        }
                        //StopTimer($source."loop");

                    //  There are single access records keys, like reportstore access.
                    } else {
                        $out[] = $this->prepareSingletonForReport($rec, $keyAlias);
                        if (++$cnt >= $this->maxReportRecs) {
                            break;
                        }
                    }
                }

            //  Other access types are copied as-is.
            } else {
                unset($rec['BLOB']);

                if ($actionAlias) {
                    //  Translate internal ACTION values from constants to user-presentable string.
                    if ($rec['RAWMODE'] == AuditTrail::AUDITTRAIL_EVENT_USERACTION) {
                        $rec[$actionAlias] = AuditTrail::actionIdToLabel($rec['WORKFLOWACTION']);
                    } else if ($rec['RAWMODE'] == AuditTrail::AUDITTRAIL_EVENT_TASK) {
                        $rec[$actionAlias] = AuditTrail::taskIdToLabel($rec['WORKFLOWACTION']);
                    } else {
                        $rec[$actionAlias] = AuditTrail::getLabel($rec['RAWMODE']);
                    }
                }
                if ($recordUrlAlias && $typeAlias && $keyAlias) {
                    $rec[$recordUrlAlias] = $this->makeRecordUrl($rec[$typeAlias], $rec[$keyAlias], $makeLinks);
                }
                $out[] = $rec;
                if (++$cnt >= $this->maxReportRecs) {
                    break;
                }
                $allKeys[strtoupper($rec[$typeAlias])][$rec[$keyAlias]] = 1;
            }
        }
        $rawdata = $out;

        //  We need to register all the 'seen' keys as being accessed via the report.
        $finalKeys = [];
        foreach ($allKeys as $objType => $keys) {
            $finalKeys[$objType] = array_keys($keys);
        }
        AdvAuditTracking::registerAccess($finalKeys, ($makeLinks?AuditTrail::OPID_AAT_REPORT:AuditTrail::OPID_EXPORT));
    }

    /**
     * Takes an AAT audit trail record for a singleton, does any necessary processing for output.
     *
     * @param string[] $rec      Data on the record.
     * @param string   $keyAlias Name of key in $rec for the 'key'.
     *
     * @return string[] Processed record.
     */
    private function prepareSingletonForReport($rec, $keyAlias)
    {
        if ($rec['OBJECTTYPE'] == 'reportstore') {
            $key = $rec['OBJECTKEY'];
            if (isset($this->cachedObjectNames['reportstore'][$key])) {
                $rec[$keyAlias] = $this->cachedObjectNames['reportstore'][$key];
            } else {
                $mgr = $this->getCachedMgr('reportstore');
                $res = $mgr->DoQuery('QRY_REPORTSTORE_RECNO_TO_NAME', array($rec['OBJECTKEY'], GetMyCompany()));
                if ($res[0]['NAME']) {
                    $rec[$keyAlias] = $res[0]['NAME'];
                    $this->cachedObjectNames['reportstore'][$key] = $res[0]['NAME'];
                } else {
                    $this->cachedObjectNames['reportstore'][$key] = $key;  // Couldn't get name, so just fill with key.
                }
            }
        }
        return $rec;
    }

    /**
     * Constructs the record url for an advanced audit trail record.
     *
     * @param string $objType   Object type.
     * @param string $key       Key of object.
     * @param bool   $makeLinks True means make value as expected by formatter containing name and url, else only url.
     *
     * @return string Record url value (if $makeLinks, then '$recUrl|*|*|*|$key')
     */
    private function makeRecordUrl($objType, $key, $makeLinks)
    {
        //  Don't track gathering of data for making record url (not necessary).
        /** @noinspection PhpUnusedLocalVariableInspection */
        $tracking = AdvAuditTracking::setManagedTracking(false);

        try {
            $mgr = $this->getCachedMgr($objType);
            if (!$mgr->canCreateShortUrl()) {
                return '';
            }
            $objectId = $mgr->GetRecordNoFromVid($key);
            $recordUrl = $mgr->createShortUrl($objectId);
            if ($makeLinks) {
                if (!$recordUrl) {
                    return '';
                }
                return $recordUrl . '|*|*|*|' . $key;
            } else {
                return $recordUrl;
            }
        } catch (Exception $e) {
            AdvAuditTracking::logMessage(
                "Could not create advanced audit history record url for type: $objType, key: $key"
            );
            return '';
        }
    }

    /**
     * getCachedMgr - returns the cached EntityManager for the given type (only created once per type).
     *
     * @param string $type Object type.
     *
     * @return EntityManager EntityManager object (or null if it can't be created).
     */
    private function getCachedMgr($type) {
        if (!array_key_exists($type, $this->cachedMgrs)) {
            try {
                $mgr = Globals::$g->gManagerFactory->getManager($type);
                $this->cachedMgrs[$type] = $mgr;
            } catch (Exception $e) {
                $this->cachedMgrs[$type] = null;
            }
        }
        return $this->cachedMgrs[$type];
    }

    /**
     * Process the filter clauses of a search.  We need to extract the contents and apply them
     *   dynamically, since the object type and key are encoded in the audit trail blob for advanced audit access recs.
     *
     * @param string &$query The query string to parse.
     *
     * @return bool True means parse was ok, false means error.
     */
    private function parseFilters(&$query)
    {
        if (!$query) {
            return true;
        }

        $touples = QueryManager::getSqlTouples($query);
        foreach ($touples as $touple) {
            $origTouple = $touple;
            $type = $this->parseATouple($touple, false);
            if ($type == 'objecttype' || $type == 'objectkey') {
                $query = (string)str_ireplace($origTouple,
                 "((" . $touple . ") or ADVAUDITHISTORY.RAWMODE = 'S')", $query);
            } else if ($type == 'accesstime' || $type == 'workflowaction') {
                $query = (string)str_ireplace($origTouple, $touple, $query);
            } else if ($type == 'error') {
                return false;
            }
        }

        return true;
    }

    /**
     * Convert actions in a workflow field touple.  The purpose of this function is to proper separate
     *  any 'action codes' into the right predicates.  Some 'actions' are workflow values, some are accessmodes.
     *  So, the SQL predicates have to be adjusted.
     *
     * @param bool   $fromReport  True if from report, false means from API.
     * @param string $toupleField Field name portion of touple.
     * @param string $toupleOp    Operator (e.g. 'in' or '=') portion of touple.
     * @param string $toupleValue Value portion of touple.
     *
     * @return string Converted SQL-ready clause.
     */
    private function convertActions($fromReport, $toupleField, $toupleOp, $toupleValue)
    {
        $labelExpr = trim($toupleValue, "()");
        $labels = explode(',', $labelExpr);
        $actionCodes = [];
        $opCodes = [];
        $taskCodes = [];
        $userActionCodes = [];
        foreach ($labels as $label) {
            $testValue = trim($label, "' ");

            //  Given value could be an event type, opid, user action, or task.
            //   From report are internal values, from API are external.
            if ($fromReport) {
                if (AuditTrail::isType($testValue)) {
                    //  Don't add S explicitly.  We only add it when there are no OP codes, later.
                    if ($testValue != AuditTrail::AUDITTRAIL_EVENT_MULTI_ACCESS) {
                        $actionCodes[] = $label;
                    }
                } else if (AuditTrail::isOpCode($testValue)) {
                    $opCodes[] = $label;
                } else if (AuditTrail::isActionCode($testValue)) {
                    $userActionCodes[] = $label;
                } else if (AuditTrail::isTaskCode($testValue)) {
                    $taskCodes[] = $label;
                } else {
                    Globals::$g->gErr->addIAError("CMN-0002", "", "Invalid action value " . $label, ['LABEL' => $label]);
                    return 'error';
                }
            } else {
                $actionCode = AuditTrail::getType($testValue);
                if ($actionCode) {
                    $actionCodes[] = "'" . $actionCode . "'";
                } else {
                    $code = AuditTrail::labelToOpId($testValue);
                    if ($code) {
                        $opCodes[] = "'" . $code . "'";
                    } else {
                        $code = AuditTrail::labelToActionId($label);
                        if (!$code) {
                            $code = AuditTrail::labelToTaskId($label);
                            if (!$code) {
                                Globals::$g->gErr->addIAError("CMN-0002", "", "Invalid action value " . $label, ['LABEL' => $label]);
                                return 'error';
                            }
                            $taskCodes[] = "'" . $code . "'";
                        } else {
                            $userActionCodes[] = "'" . $code . "'";
                        }
                    }
                }
            }
        }

        $startSep = '';
        $endSep = '';
        if ($toupleOp == 'in' || $toupleOp == 'not in') {
            $startSep = '(';
            $endSep = ')';
        }
        $touples = [];
        if (count($actionCodes)) {
            $touples[] = "(ADVAUDITHISTORY.RAWMODE $toupleOp $startSep" . implode(',', $actionCodes) . "$endSep)";
        }
        if (count($opCodes)) {
            $touples[] = "((" . $toupleField . " $toupleOp $startSep" . implode(',', $opCodes) .
             "$endSep) AND (ADVAUDITHISTORY.RAWMODE = '" . AuditTrail::AUDITTRAIL_EVENT_MULTI_ACCESS . "'))";
        }
        if (count($taskCodes)) {
            $touples[] = "((" . $toupleField . " $toupleOp $startSep" . implode(',', $taskCodes) .
             "$endSep) AND (ADVAUDITHISTORY.RAWMODE = '" . AuditTrail::AUDITTRAIL_EVENT_TASK . "'))";
        }
        if (count($userActionCodes)) {
            $touples[] = "((" . $toupleField . " $toupleOp $startSep" . implode(',', $userActionCodes) .
             "$endSep) AND (ADVAUDITHISTORY.RAWMODE = '" . AuditTrail::AUDITTRAIL_EVENT_USERACTION . "'))";
        }
        $touple = "(" . implode(' or ', $touples) . ")";
        return $touple;
    }

    /**
     * Process one query touple, processing any object type / key clauses.
     *
     * @param string &$touple    Query touple to process.
     * @param bool   $fromReport True if from report, false means from API.
     *
     * @return string Found field type, 'objecttype', 'objectkey', or empty.
     */
    private function parseATouple(&$touple, $fromReport)
    {
        static $filterFields = ['accesstime', 'workflowaction', 'objecttype', 'objectkey'];

        //  Rough guess as to whether touple is interesting.
        if (!(((stripos($touple, 'objecttype') !== false) || (stripos($touple, 'objectkey') !== false) ||
            (stripos($touple, 'accesstime') !== false) || (stripos($touple, 'workflowaction') !== false)))) {
            return '';
        }

        //  Substitute literals, parse touple, resubstitute literals.
        $parsedTouple = QueryManager::markLiterals($touple);
        $expr = isl_strtolower($parsedTouple['expression']);

        $pieces = QueryManager::getTouplePieces($expr);

        $f = -1;
        $v = -1;
        $type = '';
        $firstParts = explode('.', $pieces[0]);
        $firstPart = $firstParts[count($firstParts)-1];
        $secondParts = explode('.', $pieces[2]);
        $secondPart = $secondParts[count($secondParts)-1];
        foreach ($filterFields as $type) {
            if ($firstPart == $type) {
                $f = 0;
                $v = 2;
                break;
            } else if ($secondPart == $type) {
                $f = 2;
                $v = 0;
                break;
            }
        }
        $opToTest = $pieces[1];
        if ($f == -1) {
            return '';  //  Not one of the fields we're interested in.
        }

        $cnt = count($parsedTouple['literals']);
        if ($cnt > 0) {
            for ($i=$cnt-1; $i>=0; $i--) {
                $pieces[$v] = str_replace(QueryManager::QLITERAL_START.$i, $parsedTouple['literals'][$i], $pieces[$v]);
            }
        }

        //  Convert dates to 'query friendly' date format.
        if ($type == 'accesstime') {
            $touple = $pieces[$f] . " " . $pieces[1] . " '" .
             Util_DataTypeFormatter::xsdDateTimeToIntacct(trim($pieces[$v], "'")) . "'";

        //  Convert workflow labels to internal codes.
        } else if ($type == 'workflowaction') {
            $touple = $this->convertActions($fromReport, $pieces[$f], $opToTest, $pieces[$v]);

        //  Extract object type/key values for later matching.
        } else {

            //  For now, we don't support 'like', but the CRW generates it, so block only for API.
            if ($opToTest == 'like' && !$fromReport) {
                $opToTest = '';  // Generate default case but will report correct op type etc.
            }

            //  Extract the value(s) and match/nomatch based on the supported operators.
            switch ($opToTest) {
            case 'like':               // CRW generates like, treat as equal.
            case '=':
                if ($type == 'objecttype') {
                    $this->matchOperands[$type][] = strtoupper(trim($pieces[$v], " '"));
                } else {
                    $this->matchOperands[$type][] = trim($pieces[$v], " '");
                }
                break;
            case '<>':
            case '!=':
                if ($type == 'objecttype') {
                    $this->nomatchOperands[$type][] = strtoupper(trim($pieces[$v], " '"));
                } else {
                    $this->nomatchOperands[$type][] = trim($pieces[$v], " '");
                }
                break;
            case 'not in':
            case 'in':
                $types = explode(',', isl_trim($pieces[$v], ' ()'));
                if ($pieces[1] == 'in') {
                    foreach ($types as $t) {
                        if ($type == 'objecttype') {
                            $this->matchOperands[$type][] = strtoupper(trim($t, " '"));
                        } else {
                            $this->matchOperands[$type][] = trim($t, " '");
                        }
                    }
                } else {
                    foreach ($types as $t) {
                        if ($type == 'objecttype') {
                            $this->nomatchOperands[$type][] = strtoupper(trim($t, " '"));
                        } else {
                            $this->nomatchOperands[$type][] = trim($t, " '");
                        }
                    }
                }
               break;
            default:
               Globals::$g->gErr->addIAError("CMN-0003", "", "Unsupported operator on " . $type . ": " . $pieces[1], ['TYPE' => $type, 'PICES' => $pieces[1]]);
               return 'error';
            }
        }
        return $type;
    }

    /**
     * API access to the Get method - overridden to disallow option of passing in empty ids.
     *
     * @param int|string|int[]|string[] $ids    Array list of IDs (RECORDNO) for the record to get
     * @param string[]|null             $fields Array list of fields to return
     *
     * @return array|false Array of records to return or failure code
     */
    public function API_Get($ids, $fields = null)
    {
        //  For auditstorage, we don't allow the empty keys / first 100 entries functionality.
        if (AuditStorageManager::isAuditStorageViewingEnabled() && !$ids) {
            Globals::$g->gErr->addError("CMN-0004", "", "Read for advaudithistory must be supplied one or more audit keys");
            return false;
        }
        if (GetMyAdminLevel() != 2) {
            Globals::$g->gErr->addError("CMN-0006", "", "Need admin privileges to read advaudithistory");
            return false;
        }
        if (!IsOperationAllowed(GetOperationId('cerp/lists/customreport/view'))) {
            Globals::$g->gErr->addIAError(
                "CO-1079",
                __FILE__ . ":" . __LINE__, "Not authorized to access Advanced Audit History", [],
                "Access to Advanced Audit History requires additional permissions.",
            
            );
            return false;
        }
        $keys = explode(",", $ids);
        $res = parent::API_Get($ids, $fields);

        //  If we didn't find all the results we expected, look into the audit storage.
        if (count($res) < count($keys)) {
            $this->getStorageResultsByKeys($res, $keys, $fields, false);
        }
        return $res;
    }

    /**
     * Return a list of field descriptors.  Overridden to decompose "audithistory.field" in query strings.
     *  This allows use of query handling and external values for AWS queries.
     *
     * @param string $field
     *
     * @return array
     */
    function &GetFieldInfo($field)
    {
        $pos = strpos($field, '.');
        if ($pos !== false) {
            $field = substr($field, $pos+1);
        }
        $field = strtoupper($field);
        if (isset($this->fieldInfo[$field])) {
            return $this->fieldInfo[$field];
        }
        $map =& $this->GetEntityInfoMap();
        return $map[$field];
    }

    /**
     * Override for API readByQuery, allows capturing of query for dynamic filtering. 
     *
     * @param string   $query        String the where clause
     * @param int      $maxNumber
     * @param bool     &$outKeysOnly (output) returned true if maxNumber exceeded and keys only returned
     * @param string[] $fields
     *
     * @return array list of objects matching the query
     */
    public function getByQuery($query, $maxNumber, &$outKeysOnly, $fields = null)
    {
        if (!AdvAuditTracking::isCnyTracked()) {
            Globals::$g->gErr->addIAError("CMN-0005", "", "Invalid object type " . $this->_entity, ['ENTITY' => $this->_entity]);
            return null;
        }
        if (GetMyAdminLevel() != 2) {
            Globals::$g->gErr->addError("CMN-0006", "", "Need admin privileges to read advaudithistory");
            return null;
        }
        if (!IsOperationAllowed(GetOperationId('cerp/lists/customreport/view'))) {
            Globals::$g->gErr->addIAError(
                "CO-1079",
                __FILE__ . ":" . __LINE__, "Not authorized to access Advanced Audit History", [],
                "Access to Advanced Audit History requires additional permissions.",
            
            );
            return null;
        }

        $this->matchOperands = [];
        $this->nomatchOperands = [];
        if (!$this->parseFilters($query)) {
            return null;
        }

        $this->rowsSoFar = null;
        $this->localRowsPending = true;
        $res = parent::getByQuery($query, $maxNumber, $outKeysOnly, $fields);
        if (Globals::$g->gErr->GetErrorCount() > 0) {
            return [];
        }

        $awsRes = [];
        $auditTrailSession = AuditTrailSession::getInstance();
        if ($auditTrailSession->isAuditStorageViewEnabled()) {
            // Access mode mappings, only for AWS (cause local view does translation of external to internal).
            $modes = AuditTrail::getAccessModes();
            $aatLabel = I18N::getSingleToken(AuditTrail::AAT_LABEL);
            $modes[$aatLabel] = AuditTrail::AUDITTRAIL_EVENT_MULTI_ACCESS;
            $this->fieldInfo['ACCESSMODE'] = $this->GetFieldInfo('ACCESSMODE');
            $this->fieldInfo['ACCESSMODE']['type']['validlabels'] = array_keys($modes);
            $this->fieldInfo['ACCESSMODE']['type']['validivalues'] = array_values($modes);
            unset($this->fieldInfo['ACCESSMODE']['type']['_validivalues']);
            $this->clearFieldTransformCache();

            //  Get the ids from AWS.  For disamiguation, we need several fields.
            $this->getWhereString($query, $args, $argtypes);
            $query = str_ireplace("RAWMODE", "ACCESSMODE", $query);
            $awsFields = [ 'RECORDID', 'ACCESSTIME', 'ACCESSMODE', 'FIELDNAME', 'NEWSTRVAL' ];
            $this->storageRowsPending = true;
            $this->getStorageResultsByQuery($awsRes, $query, $args, $argtypes, $awsFields);
            if (Globals::$g->gErr->GetErrorCount() > 0) {
                return [];
            }
        }

        $outKeysOnly = ((count($res)+count($awsRes)) > $maxNumber);
        $keys = [];
        foreach ($res as $rec) {
            $keys[] = is_array($rec) ? $rec['ID'] : $rec;
        }
        foreach ($awsRes as $rec) {
            $keys[] = $rec['ID'] . ':' . $rec['FIELDNAME'] . ':' . $rec['NEWSTRVAL'];
        }
        if ($outKeysOnly) {
            return $keys;
        }
        $res = $this->getByKeys($keys, $fields);
        return $res;
    }

    /**
     *  Returns an array of the query keys used by getByQuery.
     *
     * @return string[] Array of key names
     */
    public function getQueryKeys()
    {
        $auditTrailSession = AuditTrailSession::getInstance();
        if ($auditTrailSession->isAuditStorageViewEnabled()) {
            return [ 'ID' ];
        }

        return [ 'ID', 'ACCESSMODE', 'BLOB' ];
    }

    /**
     *  Allows subclasses a chance to interpret the getByQuery results into the relevant key array.
     *
     * @param string[] $selectKeys Name of key (or keys).
     * @param array    $res        Raw results from the getByQuery select.
     * @param int      $maxNumber  Size of page (must be > 0).
     *
     * @return string[] Array of keys.
     */
    protected function extractGetByQueryKeys($selectKeys, $res, $maxNumber)
    {
        $auditTrailSession = AuditTrailSession::getInstance();
        if ($auditTrailSession->isAuditStorageViewEnabled()) {
            $res = parent::extractGetByQueryKeys($selectKeys, $res, $maxNumber);
        } else {
            $res = $this->extractGetByQueryKeysOld($selectKeys, $res, $maxNumber);
        }
        return $res;
    }

    /**
     *  Allows subclasses a chance to interpret the getByQuery results into the relevant key array.
     *
     * @param string[] $selectKeys Name of key (or keys).
     * @param array    $res        Raw results from the getByQuery select.
     * @param int      $maxNumber  Size of page (must be > 0).
     *
     * @return string[] Array of keys.
     */
    private function extractGetByQueryKeysOld(/** @noinspection PhpUnusedParameterInspection */ $selectKeys,
     $res, $maxNumber)
    {
        $filterByType = count($this->matchOperands['objecttype']) > 0 ? true : false;
        $filterByUnType = count($this->nomatchOperands['objecttype']) > 0 ? true : false;
        $filterByKey = count($this->matchOperands['objectkey']) > 0 ? true : false;
        $filterByUnKey = count($this->nomatchOperands['objectkey']) > 0 ? true : false;
        $keys = [];
        $i = 0;
        foreach ($res as $r) {
            if ($r['ACCESSMODE'] == AuditTrail::AAT_LABEL) {
                $blobData = unserialize(databaseStringUncompress($r['BLOB']));
                foreach ($blobData as $type => $akeys) {
                    if ($filterByType) {
                        if (!in_array($type, $this->matchOperands['objecttype'])) {
                            continue;
                        }
                    }
                    if ($filterByUnType) {
                        if (in_array($type, $this->nomatchOperands['objecttype'])) {
                            continue;
                        }
                    }

                    //  To save space in the keys, compress the type.
                    if (isset(self::$compressTypes[$type])) {
                        $type = self::$compressTypes[$type];
                    }

                    $id = $r['ID'];
                    foreach ($akeys as $bkey) {

                        if ($filterByKey) {
                            if (!in_array($bkey, $this->matchOperands['objectkey'])) {
                                continue;
                            }
                        }
                        if ($filterByUnKey) {
                            if (in_array($bkey, $this->nomatchOperands['objectkey'])) {
                                continue;
                            }
                        }
                        $keys[] = $id . self::KEY_SEP . $type . self::KEY_SEP . $bkey;

                        //  To save space, only write the ID if it is at the start of a page.
                        //   Note that it already gets written for when the key changes.
                        if ((++$i % $maxNumber) == 0) {
                            $id = $r['ID'];
                        } else {
                            $id = '';
                        }
                    }
                }
            } else {
                $keys[] = $r['ID'];
            }
        }
        return $keys;
    }

    /**
     *  Return the external fields used when user requests fields via '*'
     *
     * @return string[] Names of external fields.
     */
    private function getExternalFields()
    {
        $fields = $this->GetGetFields();
        foreach ($fields as $key => $field) {
            if ($field == 'BLOB' || $field == 'RECORD_URL') {
                unset($fields[$key]);
            }
        }
        return $fields;
    }

    /**
     *  Given an array of key field values (e.g. record#), queries and returns full objects (or,
     *   fields as given).
     *
     * @param array        $keys   An array of key values.
     * @param array|string $fields An optional list of fields to retrieve (null means get all fields).
     *
     * @return  array|false List of objects matching the query.
     */
    public function getByKeys($keys, $fields = null)
    {
        //  If no keys given, return an empty array.
        if (!$keys || !is_array($keys) || count($keys) == 0) {
            return [];
        }
        $auditTrailSession = AuditTrailSession::getInstance();
        if (!$auditTrailSession->isAuditStorageViewEnabled()) {
            return $this->getByKeysOld($keys, $fields);
        }

        if ($this->localRowsPending) {
            $this->rowsSoFar = $this->getByKeysNew($keys, $fields);
            $this->localRowsPending = false;
        }
        if ($this->storageRowsPending) {
            $this->getStorageResultsByKeys($res, $keys, $fields, true);
            $this->storageRowsPending = false;
        }
        return $this->rowsSoFar;
    }

    /**
     *  Given an array of key field values (e.g. record#), queries and returns full objects (or,
     *   fields as given). NEW VERSION for audit storage local data.
     *
     * @param array        $keys   An array of key values.
     * @param array|string $fields An optional list of fields to retrieve (null means get all fields).
     *
     * @return  array|false List of objects matching the query.
     */
    private function getByKeysNew($keys, $fields = null)
    {
        $resObjs = $this->getRecordDetails($keys, $fields, $getRecordUrl);

        return $this->parseResults($resObjs, $getRecordUrl);
    }

    /**
     *  Given an array of key field values (e.g. record#), queries and returns full objects (or,
     *   fields as given). OLD VERSION pre-audit storage.
     *
     * @param array        $keys   An array of key values.
     * @param array|string $fields An optional list of fields to retrieve (null means get all fields).
     *
     * @return  array|false List of objects matching the query.
     */
    private function getByKeysOld($keys, $fields = null)
    {
        $ids = [];
        $keyPieces = [];
        $i = 0;
        foreach ($keys as $key) {
            $keyPieces[$i] = explode(self::KEY_SEP, $key, 3);
            if ($keyPieces[$i][0] != '') {
                $ids[$keyPieces[$i][0]] = true;
            }
            $i++;
        }
        $ids = array_keys($ids);

        $resObjs = $this->getRecordDetails($ids, $fields, $getRecordUrl);

        //  Construct a map of the ID to results.  We can't rely on the order of the original keys.
        $idMap = [];
        foreach ($resObjs as $resObj) {
            $idMap[$resObj['ID']] = $resObj;
        }

        //  Get the data.  Note we do it in the key order, not in the data order.
        $objs = [];
        $obj = null;
        //$allKeys = [];
        foreach ($keyPieces as $keyPs) {
            //  If the id changes, get the base object with this id.
            if ($keyPs[0]) {
                $obj = $idMap[$keyPs[0]];   // First, get the base information.
            }
            if ($obj['RAWMODE'] == AuditTrail::AUDITTRAIL_EVENT_MULTI_ACCESS) {
                $type = self::$uncompressTypes[$keyPs[1]] ?? $keyPs[1];
                $obj['OBJECTTYPE'] = $type;
                $obj['OBJECTKEY'] = $keyPs[2];
                $obj['WORKFLOWACTION'] = AuditTrail::opIdToLabel($obj['WORKFLOWACTION']);
            } else {
                //  Translate internal ACTION values from constants to user-presentable string.
                if ($obj['RAWMODE'] == AuditTrail::AUDITTRAIL_EVENT_USERACTION) {
                    $obj['WORKFLOWACTION'] = AuditTrail::actionIdToLabel($obj['WORKFLOWACTION']);
                } else if ($obj['RAWMODE'] == AuditTrail::AUDITTRAIL_EVENT_TASK) {
                    $obj['WORKFLOWACTION'] = AuditTrail::taskIdToLabel($obj['WORKFLOWACTION']);
                } else {
                    $obj['WORKFLOWACTION'] = AuditTrail::getLabel($obj['RAWMODE']);
                }
            }
            //$allKeys[$obj['OBJECTTYPE']][$obj['OBJECTKEY']] = 1;
            if ($getRecordUrl) {
                $obj['RECORD_URL'] = $this->makeRecordUrl($obj['OBJECTTYPE'], $obj['OBJECTKEY'], false);
            }
            unset($obj['RAWMODE']);
            $objs[] = $obj;
        }

        return $objs;
    }

    /**
     *  Get details of the records from the local storage.
     *
     * @param array        $ids           An array of key values.
     * @param array|string $fields        An optional list of fields to retrieve (null means get all fields).
     * @param bool         &$getRecordUrl Are we also calculating the record url later?
     *
     * @return  array|false List of objects matching the query.
     */
    private function getRecordDetails($ids, $fields, &$getRecordUrl)
    {
        //  Figure which fields we want, and check them if listed individually.
        $getRecordUrl = false;
        if ((is_array($fields) && count($fields) == 0) ||
         (!is_array($fields) && ($fields == '*' || trim($fields) == false))) {
            $getFields = $this->getExternalFields();
        } else {
            $getFields = [];
            foreach ($fields as $field) {
                $field = strtoupper(trim($field));
                if ($field == 'BLOB') {
                    continue;
                }
                if ($field == 'RECORD_URL') {
                    $getRecordUrl = true;        // Remember, RECORD_URL must be explicitly requested.
                } else {
                    $fieldInfo = $this->GetFieldInfo($field);
                    if (!$fieldInfo) {
                        Globals::$g->gErr->addIAError("CMN-0007", "", "Invalid field " . $field, ['FIELD' => $field]);
                        return null;
                    }
                }
                $getFields[] = $field;
            }
            $getFields[] = 'ID';
        }
        $auditTrailSession = AuditTrailSession::getInstance();
        if ($auditTrailSession->isAuditStorageViewEnabled()) {
            $getFields = array_merge($getFields, [ 'FIELDNAME', 'NEWVAL' ]);
        }

        //  Form the field list.  If it includes accesstime, do the necessary SQL transformation.
        $getFields[] = 'RAWMODE';
        $fieldList = implode(',', $getFields);
        $viewToUse = $this->viewToUse;
        if (strpos($fieldList, 'ACCESSTIME') !== false) {
            $fieldList = str_replace("ACCESSTIME", 
             "to_char($viewToUse.ACCESSTIME, 'MM/DD/YYYY HH24:MI:SS') as ACCESSTIME", $fieldList);
        }

        //  Select the data.  Include the list of entry keys.
        $auditTrailSession = AuditTrailSession::getInstance();
        $view = $auditTrailSession->isAuditStorageViewEnabled() ? "advauditstoragehistory" : "advaudithistory";
        $stmt[0] = "select " . $fieldList . " from $view where cny# = :1";
        $stmt[1] = GetMyCompany();
        $stmt = PrepINClauseStmt($stmt, $ids, " and id ");
        $resObjs = QueryResult($stmt);
        return $resObjs;
    }

    /**
     * For audit storage, get additional results from AWS and add them in.
     *
     * @param array         &$data         Local data, combine AWS data into this array.
     * @param string[]      $keys          Keys of audit records to retrieve from AWS.
     * @param string[]|null $fields        Which fields to return (null means all).
     * @param bool          $exactKeyMatch Do we want an exact key match (for readByQuery) or not (read).
     */
    private function getStorageResultsByKeys(&$data, $keys, $fields, $exactKeyMatch)
    {
        $postRemoveFields = [];
        if (empty($fields) || $fields == '*') {
            $fields = [ 'OBJECTTYPE', 'OBJECTKEY', 'USERID', 'ACCESSTIME', 'ACCESSMODE', 'IPADDRESS',
             'SOURCE', 'WORKFLOWACTION', 'FIELDNAME', 'NEWSTRVAL' ];
        } else {
            //  Make sure that the fields required to dedupe are included.  If we force any, we need to
            //   unset them in the output.
            static $requiredFields = [ 'ID', 'FIELDNAME', 'NEWSTRVAL', 'ACCESSMODE' ];
            foreach ($requiredFields as $requiredField) {
                if (!in_array($requiredField, $fields)) {
                    $fields[] = $requiredField;
                    $postRemoveFields[] = $requiredField;
                }
            }
        }

        $readMgr = new AuditStorageReader();
        $origKeyMap = [];
        $objKeyMap = [];
        foreach ($keys as $key) {
            $pieces = explode(':', $key);
            $recordId = $pieces[0] . ':' . $pieces[1];
            $objKeyMap[$recordId] = $key;
            $origKeyMap[$key] = 1;
        }
        $objKeys = array_keys($objKeyMap);
        $res = $readMgr->readAWSByKeys(GetMyCompany(), $objKeys, $fields, true, true);

        if ($exactKeyMatch) {
            $outData = [];
            foreach ($res as $rec) {
                $ourKey = $rec['ID'] . ':' . $rec['FIELDNAME'] . ':' . $rec['NEWSTRVAL'];
                if (isset($origKeyMap[$ourKey])) {
                    $outData[] = $rec;
                } 
            }
        } else {
            $outData = $res;
        }

        $mapMode = array_flip(AuditTrail::getAccessModes());
        foreach ($outData as &$rec) {
            if ($rec['RAWMODE'] == AuditTrail::AUDITTRAIL_EVENT_MULTI_ACCESS) {
                $rec['OBJECTTYPE'] = $rec['FIELDNAME'];
                $rec['OBJECTKEY'] = $rec['NEWSTRVAL'];
                $rec['WORKFLOWACTION'] = AuditTrail::opIdToLabel($rec['WORKFLOWACTION']);
            } else {
                $rec['WORKFLOWACTION'] = $mapMode[$rec['RAWMODE']] ?? 'IA.UNKNOWN_ACTION';
            }
            unset($rec['FIELDNAME']);
            unset($rec['NEWSTRVAL']);
            unset($rec['RAWMODE']);
            foreach ($postRemoveFields as $removeField) {
                unset($rec[$removeField]);
            }
        }

        $data = INTACCTarray_merge($data, $outData);
    }

    /**
     * For audit storage, get additional results from AWS and add them in.
     *
     * @param array         &$data    Local data, combine AWS data into this array.
     * @param string        $query    Query to run (arguments already replaced with ?)
     * @param array         $args     Keys of audit records to retrieve from AWS.
     * @param string[]      $argtypes Argument types ('text' and others are all that matters).
     * @param string[]      $fields   Which fields to return (null means all).
     */
    private function getStorageResultsByQuery(&$data, $query, $args, $argtypes, $fields)
    {
        $readMgr = new AuditStorageReader();
        $res = $readMgr->readAWSByQuery(GetMyCompany(), $query, $args, $argtypes, $fields, true, true);
        $data = INTACCTarray_merge($data, $res);
    }

    /**
     * @param string $token
     *
     * @return string
     */
    protected function getOpIdLabels(string $token = ''): string
    {
        if ($this->opIdLabels === []) {
            $this->setOpIdLabels();
        }

        return $this->opIdLabels[$token] ?? "";
    }

    private function setOpIdLabels()
    {
        $this->opIdLabels = I18N::getTokensForArray(I18N::tokenArrayToObjectArray(AuditTrail::$opId2Label));
    }

    /**
     * @param array $resObjs
     * @param bool  $getRecordUrl
     *
     * @return array
     * @throws I18NException
     */
    private function parseResults(array $resObjs, bool $getRecordUrl = false)
    {
        $modesLabels = AuditTrail::getAccessModesLabels();

        foreach ($resObjs as &$obj) {
            if (isset($obj['RAWMODE'])) {
                if ($obj['RAWMODE'] == AuditTrail::AUDITTRAIL_EVENT_MULTI_ACCESS) {
                    $obj['OBJECTTYPE'] = strtolower($obj['FIELDNAME']);
                    $obj['OBJECTKEY'] = $obj['NEWVAL'];
                    $obj['WORKFLOWACTION'] = $this->getOpIdLabels(AuditTrail::opIdToLabel($obj['WORKFLOWACTION'] ?? ''));
                } else {
                    //  Translate internal ACTION values from constants to user-presentable string.
                    if ($obj['RAWMODE'] == AuditTrail::AUDITTRAIL_EVENT_USERACTION) {
                        $obj['WORKFLOWACTION'] = AuditTrail::actionIdToLabel($obj['WORKFLOWACTION']);
                    } elseif ($obj['RAWMODE'] == AuditTrail::AUDITTRAIL_EVENT_TASK) {
                        $obj['WORKFLOWACTION'] = AuditTrail::taskIdToLabel($obj['WORKFLOWACTION']);
                    } else {
                        $obj['WORKFLOWACTION'] = AuditTrail::getLabel($obj['RAWMODE']);
                    }
                }
            }
            if ($getRecordUrl) {
                $obj['RECORD_URL'] = $this->makeRecordUrl($obj['OBJECTTYPE'], $obj['OBJECTKEY'], false);
            }

            if (isset($obj['ACCESSMODE'])) {
                $obj['ACCESSMODE'] = $modesLabels[$obj['ACCESSMODE']];
            }
            if (isset($obj['SOURCE'])) {
                $obj['SOURCE'] = AuditTrailSession::getModeLabel($obj['SOURCE']);
            }

            unset($obj['FIELDNAME']);
            unset($obj['NEWVAL']);
            unset($obj['RAWMODE']);
        }

        return $resObjs;
    }

    /**
     * @param array $query
     * @param bool  $_crosscny
     * @param bool  $nocount
     *
     * @return array[]|null
     * @throws Exception
     */
    function strictGetList(array $query, $_crosscny = false, $nocount = true)
    {
        if (!AdvAuditTracking::isCnyTracked()) {
            Globals::$g->gErr->addIAError("CMN-0005", "", "Invalid object type " . $this->_entity, ['ENTITY' => $this->_entity]);
            return null;
        }
        if (GetMyAdminLevel() != 2) {
            Globals::$g->gErr->addError("CMN-0006", "", "Need admin privileges to read advaudithistory");
            return null;
        }
        if (!IsOperationAllowed(GetOperationId('cerp/lists/customreport/view'))) {
            Globals::$g->gErr->addIAError(
                "CO-1079",
                __FILE__ . ":" . __LINE__, "Not authorized to access Advanced Audit History", [],
                "Access to Advanced Audit History requires additional permissions.",
            
            );
            return null;
        }
        if (!in_array('OBJECTTYPE', $query['selects'])) {
            // We always need the OBJECTTYPE, because REST API will not be able to convert the FIELD_NAME into the REST API equivalent field without the object name.
            // The object name is used to build the REST Schema Handler. All these happen in the AuditHistoryAdapter.
            $query['selects'][] = 'OBJECTTYPE';
        }
        $results =  parent::strictGetList($query, $_crosscny, $nocount);

        if ($results === null) {
            $results = [];
        }
        return $this->parseResults($results);
    }
    
    /**
     * @param bool $value
     *
     * @return void
     */
    public function setRequiresRecordNo(bool $value): void
    {
        $this->requiresRecordNo = $value;
    }
    
    /**
     * @param bool $checkForCanCreate
     *
     * @return array
     */
    public function getRuntimeFields($checkForCanCreate = true): array
    {
        $fields = [];
        if ($this->requiresRecordNo) {
            // this is a specific field to be used in the REST API
            // We're not including it in the XML API response
            $fields['RECORDNO'] = [
                'path'                   => 'RECORDNO',
                // Should be HISTORY_KEY, but REST API framework does not support other field names as unique keys.
                'fullname'               => 'IA.HISTORY_KEY',
                'desc'                   => 'IA.HISTORY_KEY',
                'type'                   => array(
                    'type'      => 'text',
                    'ptype'     => 'text',
                    'maxlength' => 300,
                ),
                'readonly'               => true,
                'hidden'                 => true,
                'disableReportSelection' => true,   // Can't be used in report.
                'id'                     => 15,
            ];
        }
        return $fields;
    }
    
    /**
     * Returns array of default fields for the read API call, without the FIELD_* fields.
     *
     * @return array Array of select fields
     */
    public function GetGetFields()
    {
        if ($this->requiresRecordNo || defined('INTACCTCONTEXT')) {
            // for the audit history, the RECORDNO is not needed (it is the same as the ID field
            return parent::GetGetFields();
        }
        // for the other cases, keep the previous behavior.
        return array_diff(parent::GetGetFields(), ['RECORDNO']);
    }
}

