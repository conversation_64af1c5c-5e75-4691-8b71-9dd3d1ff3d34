<?php
/**
 * This is the employee rate manager.
 *   
 *  @description This is the EmployeeRate manager
 *
 *<AUTHOR> <<EMAIL>>
 *@copyright 2000-2012 Intacct Corporation All, Rights Reserved
 */

/**
 * The employee rate manager class.
 */
class EmployeeRateManager extends OwnedObjectManager
{
    /**
     * While updating a specific cost rate, do we need to update other cost rate records. Note that
     * EmployeeRate records are interrelated through startdate/enddate. In other words, change in 
     * startdate of one record requires change of enddate of other records.
     * 
     * @var bool $updateRelatedCostRates
     */
    private $updateRelatedCostRates = true;
    
    public function __construct()
    { 
        parent::__construct();
    }
    
    
    /**
     * Overridden function to check appropriate permission on read operation
     * 
     * @param int      $ID     RECORDNO of the record
     * @param string[] $fields List of fields to be fetched
     * 
     * @access public
     * <AUTHOR> <<EMAIL>>
     * @return array Employeerate record list
     */
    public function Get($ID, $fields = null)
    {
        if ( !self::isCostRateVisible() ) {
            $gErr = Globals::$g->gErr;
            $gErr->addError('PA-0164', __FILE__ . ':' . __LINE__, "Permission denied!");
            return array();
        } else {
            return parent::get($ID, $fields);
        }
    }

    /**
     * Overridden function to check appropriate permission on read operation
     * 
     * @param array $params    RECORDNOs to be fetched
     * @param bool  $_crosscny
     * @param bool  $nocount   Return count or not
     * 
     * @access public
     * <AUTHOR> Kumar <<EMAIL>>
     * @return array Employeerate record list
     */
    public function GetList($params = [], $_crosscny = false, $nocount = true)
    {
        if ( !self::isCostRateVisible() ) {
            $gErr = Globals::$g->gErr;
            $gErr->addError('PA-0165', __FILE__ . ':' . __LINE__, "Permission denied!");
            return array();
        } else {
            return parent::GetList($params, $_crosscny, $nocount);
        }
    }
    
    /**
     * GetList function which bypasses the permission checkings. This is required to fetch
     * the cost rates irrespective of user permissions.
     * 
     * @param array $params    RECORDNOs to be fetched
     * @param bool  $_crosscny
     * @param bool  $nocount   Return count or not
     * 
     * @access public
     * <AUTHOR> Kumar <<EMAIL>>
     * @return array Employeerate record list
     */
    public function systemGetList($params = [], $_crosscny = false, $nocount = true)
    {
        return parent::GetList($params, $_crosscny, $nocount);
    }
    
    /**
     * Retrieve the employee object for the specified employee.
     *  
     * @param array $employeeRate is the entity array of the employee rate being modified
     * 
     * @return array the employee
     */
    private static function getEmployee($employeeRate)
    {        
        $gManagerFactory = Globals::$g->gManagerFactory;
        /** @var EmployeeManager $employeeMgr */
        $employeeMgr = $gManagerFactory->getManager('employee');
        
        if (isset($employeeRate['EMPLOYEEID']) && $employeeRate['EMPLOYEEID'] != "") {
            return $employeeMgr->Get($employeeRate['EMPLOYEEID']);
        } else if (isset($employeeRate['EMPLOYEEKEY']) && $employeeRate['EMPLOYEEKEY'] != "") {
            return $employeeMgr->GetByRecordNo($employeeRate['EMPLOYEEKEY']);
        }   
        return null;
    }
    
    /**
     * Validation for business logic
     *
     * <AUTHOR> Kumar <<EMAIL>>

     * @param array $values Object values
     * 
     * @return bool  True on success and False on failure
     */
    protected function ValidateRecord(&$values)
    {
        $gErr = Globals::$g->gErr;
        $ok = true;
        $dict = Dictionary::getInstance();

        if ( !self::isCostRateVisible() && !self::hasPermissionEditEmployee()) {
            $gErr->addError('PA-0166', __FILE__ . ':' . __LINE__, "Permission denied!");
            $ok = false;
        } else if ( empty($values['RATESTARTDATE']) ) {
            $gErr->addError('PA-0167', __FILE__ . ':' . __LINE__, "Start date is missing.");
            $ok = false;
        } else {
            $employeeData = $this->getEmployee($values);
            if(empty($employeeData)) {
                if(isset($values['EMPLOYEEID'])) {
                    $gErr->addIAError(
                        "PA-0182",
                        __FILE__ . ':' . __LINE__,
                        "Invalid EMPLOYEEID - " . $values['EMPLOYEEID'],
                        ['EMPLOYEEID' => $values['EMPLOYEEID']]
                    );
                }elseif(isset($values['EMPLOYEEKEY'])) {
                    $gErr->addIAError(
                        "PA-0183",
                        __FILE__ . ':' . __LINE__,
                        "Invalid EMPLOYEEKEY - " . $values['EMPLOYEEKEY'],
                        ['EMPLOYEEKEY' => $values['EMPLOYEEKEY']]
                    );
                }else {
                    $gErr->addError(
                        "PA-0168",
                        __FILE__ . ':' . __LINE__,
                        "EMPLOYEEID can't be empty. Please provide the required information."
                    );
                }
                $ok = false;
            }
            // Set if EMPLOYEEKEY or EMPLOYEEID is missing
            if ( empty($values['EMPLOYEEKEY']) || empty($values['EMPLOYEEID']) ) {
                $values['EMPLOYEEKEY'] = empty($values['EMPLOYEEKEY']) 
                                            ? $employeeData['RECORDNO'] 
                                            : $values['EMPLOYEEKEY'];
                $values['EMPLOYEEID']  = empty($values['EMPLOYEEID']) 
                                            ? $employeeData['EMPLOYEEID'] 
                                            : $values['EMPLOYEEID'];
            }

            // ensure that EMPLOYEEKEY is matching with EMPLOYEEID incase API provides both
            // note that higher preference goes to EMPLOYEEID based on the function 'getEmployee'
            if ( $employeeData['RECORDNO'] != $values['EMPLOYEEKEY'] ) {
                $gErr->addIAError(
                    "PA-0184",
                    __FILE__ . ':' . __LINE__,
                    "Invalid EMPLOYEEKEY - " . $values['EMPLOYEEKEY'] . " for the EMPLOYEEID - " . $values['EMPLOYEEID'],
                    ['EMPLOYEEKEY' => $values['EMPLOYEEKEY'], 'EMPLOYEEID' => $values['EMPLOYEEID']]
                );
                $ok = false;
            }
            
            if ( $employeeData['STARTDATE'] ) {
                if ( DateCompare($values['RATESTARTDATE'], $employeeData['STARTDATE']) < 0 ) {
                    $gErr->addIAError(
                        "PA-0185",
                        __FILE__ . ':' . __LINE__,
                        sprintf('The cost rate start date, %1$s, has to be on or after the employee start date, %2$s.',$values['RATESTARTDATE'], $employeeData['STARTDATE']),
                        ['VALUES_RATESTARTDATE' => $values['RATESTARTDATE'], 'EMPLOYEE_DATA_STARTDATE' => $employeeData['STARTDATE']]
                    );
                    $ok = false;
                }
            }
        }

        if ( (!isset($values['BILLINGRATE']) || $values['BILLINGRATE'] == "")
            && (!isset($values['SALARYRATE']) || $values['SALARYRATE'] == "")
        ) {
            $gErr->addError(
                'PA-0169',
                __FILE__ . ':' . __LINE__,
               "Billing rate or salary rate must be specified on a cost rate."
            );
            $ok = false;
        }

        if ( isset($values['BILLINGRATE']) && $values['BILLINGRATE'] != ""
            && isset($values['SALARYRATE']) && $values['SALARYRATE']
        ) {
            $gErr->addError(
                'PA-0170',
                __FILE__ . ':' . __LINE__,
                "Billing rate and salary rate cannot both be specified for a cost rate."
            );
            $ok = false;
        }

        $ok = $ok && parent::ValidateRecord($values);
        return $ok;
    }
    
    
    /**
     * Translate the record as follows:
     * Set the RATEENDDATE as per the new start date
     *
     * @param array $values Object values
     * 
     * @return bool True on success and False on failure
     */
    protected function TranslateRecord(&$values)
    {
        $ok = true;
        if ( $this->updateRelatedCostRates ) {
            $ok = $this->setRateEndDateOnUpdate($values);
        }
        $ok = $ok && parent::TranslateRecord($values);
        return $ok;
    }
    
    /**
     * Delete a record
     * 
     * @param string $ID RECORDNO of the employeerate record
     * 
     * @access public
     * <AUTHOR> Kumar <<EMAIL>>
     * @return bool True or False based on the status of the call
     */
    public function Delete($ID)
    {
        $source = 'EmployeeRateManager::Delete()';
        $ok = true;
        $gErr = Globals::$g->gErr;
        $dict = Dictionary::getInstance();

        $ok = $ok && $this->beginTrx($source);
        $rateDetails = $this->Get($ID);
        if ( !self::isCostRateVisible() ) {
            $gErr->addError('PA-0171', __FILE__ . ':' . __LINE__, "Permission denied!");
            $ok = false;
        } else if ( empty($rateDetails) ) {
            $msg = "Cannot find " . $this->_entity . " with key '" . $ID . "' to delete.";
            $gErr->addIAError('PA-0186', __FILE__ . ':' . __LINE__, $msg, ['ENTITY' => $this->_entity, 'ID' => $ID]);
            $ok = false;
        } else {
            $ok = $ok && parent::Delete($ID);
            $ok = $ok && $this->setRateEndDateOnDelete($rateDetails);
        }

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( !$ok ) {
            if ( !HasWarnings() || HasErrors() ) {
                $msg = sprintf('Could not delete Employee Rate record with ID %1$s!', $ID);
                $gErr->addIAError('PA-0187', __FILE__ . ':' . __LINE__, $msg, ['ID' => $ID]);
                epp("$source: Error: $msg");
            }
            $this->_QM->rollbackTrx($source);
            return false;
        }
        
        return $ok;
    }

    /**
     * Change the RATEENDDATE of other EMPLOYEERATE records based on the RATESTARTDATE 
     * of the current EMPLOYEERATE record when updaing/adding records.
     * 
     * @param array $values Object details
     * 
     * @access private
     * <AUTHOR> Kumar <<EMAIL>>
     * @return bool False if any error else True
     */    
    private function setRateEndDateOnUpdate(&$values)
    {
        $ok = true;
        $sqlFilter = array(
            'selects' => array('RECORDNO', 'RATESTARTDATE', 'RATEENDDATE'),
            'filters' => array(
                array(
                    array('EMPLOYEEKEY', '=', $values['EMPLOYEEKEY']),
                    array('RATESTARTDATE', '>', $values['RATESTARTDATE']),
                )
            ),
            'orders' => array(
                array('RATESTARTDATE', 'asc')
            ),
            'max' => 1,
        );

        $rateRecords = $this->systemGetList($sqlFilter);
        if ( count($rateRecords) ) {
            // we need to set the rateenddate of current record
            $values['RATEENDDATE'] = AddDays($rateRecords[0]['RATESTARTDATE'], -1);
        }
        
        $sqlFilter = array(
            'selects' => array('RECORDNO', 'RATESTARTDATE', 'RATEENDDATE'),
            'filters' => array(
                array(
                    array('EMPLOYEEKEY', '=', $values['EMPLOYEEKEY']),
                    array('RATESTARTDATE', '<', $values['RATESTARTDATE']),
                )
            ),
            'orders' => array(
                array('RATESTARTDATE', 'desc')
            ),
            'max' => 1,
        );

        $rateRecords = $this->systemGetList($sqlFilter);
        if ( count($rateRecords) ) {
            $rateDetails = parent::get($rateRecords[0]['RECORDNO']);
            // we need to set the rateenddate of current record
            $rateDetails['RATEENDDATE'] = AddDays($values['RATESTARTDATE'], -1);
            $ok = $ok && $this->sysSet($rateDetails);
        }
        
        return $ok;
    }

    /**
     * Update the cost rates but bypassing update of related cost rates.
     * 
     * @param array &$values Values to be updated
     * 
     * @access private
     * @return bool True or False based on the status of the call
     */
    private function sysSet(&$values)
    {
        $ok = true;
        $this->updateRelatedCostRates = false;
        $ok = $ok && parent::regularSet($values);
        $this->updateRelatedCostRates = true;
        return $ok;
    }

    /**
     * Change the RATEENDDATE of other EMPLOYEERATE records based on the 
     * RATESTARTDATE of the current EMPLOYEERATE record when deleting records.
     * Assume that three records PREVIOUS, CURRENT and NEXT records are available in 
     * the database are in ascending order of RATESTARTDATE. 
     * Cases1: Trying to delete record PREVIOUS and no changes required for other records
     * Cases2: Trying to delete record CURRENT and changes required for record PREVIOUS
     * Cases3: Trying to delete record NEXT and changes required for record CURRENT
     * 
     * @param array $values Object details
     * 
     * @access private
     * <AUTHOR> Kumar <<EMAIL>>
     * @return bool False if any error else True
     */        
    private function setRateEndDateOnDelete(&$values)
    {
        $ok = true;
        $nextRecordSqlFilter = array(
            'selects' => array(),// fetch all fields
            'filters' => array(
                array(
                    array('EMPLOYEEKEY', '=', $values['EMPLOYEEKEY']),
                    array('RATESTARTDATE', '>', $values['RATESTARTDATE']),
                )
            ),
            'orders' => array(
                array('RATESTARTDATE', 'asc')
            ),
            'max' => 1,
        );

        $prevRecordSqlFilter = array(
            'selects' => array(), // fetch all fields
            'filters' => array(
                array(
                    array('EMPLOYEEKEY', '=', $values['EMPLOYEEKEY']),
                    array('RATESTARTDATE', '<', $values['RATESTARTDATE']),
                )
            ),
            'orders' => array(
                array('RATESTARTDATE', 'desc')
            ),
            'max' => 1,
        );

        $nextRateRecords = $this->GetList($nextRecordSqlFilter);
        $prevRateRecords = $this->GetList($prevRecordSqlFilter);
        $nextRecordAvailable = count($nextRateRecords) ? true : false;
        $prevRecordAvailable = count($prevRateRecords) ? true : false;
        
        if ( $nextRecordAvailable && $prevRecordAvailable ) {
            // both PREVIOUS and NEXT records are available and change the RATEENDDATE of PREVIOUS record
            $nextRateRecord = $nextRateRecords[0];
            $prevRateRecord = $prevRateRecords[0];
            $prevRateRecord['RATEENDDATE'] = AddDays($nextRateRecord['RATESTARTDATE'], -1);
            $ok = $ok && $this->sysSet($prevRateRecord);
            
        } else if ( $nextRecordAvailable && !$prevRecordAvailable ) {
            // PREVIOUS record is not available but NEXT record is available. No changes required.
        } else if ( !$nextRecordAvailable && $prevRecordAvailable ) {
            // PREVIOUS record is available but NEXT record is not available.
            // Since PREVIOUS record becomes the last record, set RATEENDDATE to null
            $prevRateRecord = $prevRateRecords[0];
            $prevRateRecord['RATEENDDATE'] = '';
            $ok = $ok && $this->sysSet($prevRateRecord);
        } else {
            // Both PREVIOUS and NEXT records are not available
            // No need to do anything since only one record available which is to be deleted
        }
        
        return $ok;        
    }
    
    /**
     * Check is the user can see or not the employee rate
     *
     * @access public
     * @return bool True|False If the user is allowed to see the employee rate else false
     */
    public static function isCostRateVisible()
    {
        $_mod = &Request::$r->_mod;
        $_mod = empty($_mod) ? 'co' : $_mod;
        $op   = $_mod . '/lists/employeerate/view';

        if ( !IsOperationAllowed(GetOperationId($op)) || GetMyUserType() != 'B' ) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * Check is the user has permission for employee edit
     *
     * @access private
     * @return bool True|False If the user is allowed edit employee
     */
    public static function hasPermissionEditEmployee()
    {
        $_mod = &Request::$r->_mod;
        $_mod = empty($_mod) ? 'co' : $_mod;
        $op   = $_mod . '/lists/employee/edit';

        if ( IsOperationAllowed(GetOperationId($op))) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Fetch cost rates for a specific employee. This is used to get the details when logged in user has no permission
     * for the costrates but necessary to calculate other details(like TIMESHEETENTRY).
     * 
     * @param string $employeeId Employee Id of the employee
     * 
     * @access public
     * <AUTHOR> Kumar <<EMAIL>>
     * @return array List of cost rates in terms of array
     */
    public function getCostRateByEmployeeId($employeeId)
    {
        $employeeRateDetails['EMPLOYEEID'] = $employeeId;
        $employeeData = self::getEmployee($employeeRateDetails);
        $sqlCostRateFilter = array(
            'selects' => array(), // fetch all fields
            'filters' => array(
                array(
                    array('EMPLOYEEKEY', '=', $employeeData['RECORDNO']),
                )
            ),
        );
        return parent::GetList($sqlCostRateFilter);
    }
    
    /**
     * Overridden to to API specific validation
     *
     * @param array $values Set of values to update
     *
     * @access public
     * @return bool signifying success or failuure
     */
    public function API_Set(&$values)
    {
        $ok         = true;
        $rateData   = ($values['RECORDNO'] != '') ? $this->API_Get($values['RECORDNO']) : false;
        if ( !self::isCostRateVisible() ) {
            Globals::$g->gErr->addError('PA-0172', __FILE__ . ':' . __LINE__, "Permission denied!");
            $ok = false;
        } else if ( $rateData !== false && is_array($rateData) ) {
            if ( DateCompare($rateData[0]['RATESTARTDATE'], $values['RATESTARTDATE']) !== 0 ) {
                Globals::$g->gErr->addIAError(
                    'PA-0188',
                    __FILE__ . ':' . __LINE__, 
                    "Invalid RATESTARTDATE for the RECORDNO " . $values['RECORDNO'],
                    ['RECORDNO' => $values['RECORDNO']]
                ); 
                $ok = false;
            }
        }

        return $ok && parent::API_Set($values);
    }  
    /**
     * Overridden to to API specific validation
     *
     * @param array $values Set of values to add
     *
     * @access public
     * @return bool signifying success or failuure
     */
    public function API_Add(&$values)
    {
        $ok = true;
        if ( !self::isCostRateVisible() ) {
            Globals::$g->gErr->addError('PA-0173', __FILE__ . ':' . __LINE__, "Permission denied!");
            $ok = false;
        }
        return $ok && parent::API_Add($values);
    }

    /**
     *  API_pruneFieldsOwnedToo
     *   The function API_pruneFields, above, prunes fields and 'LESS_GET_FIELDS' from an item.
     *   This function does that, and then recurses down the tree of 'owned' items and does pruning there as well.
     *   For example, an item record includes itemvendor, itemwarehouseinfo, and so on.  itemwarehouseinfo includes owned records too.
     *   This should be called when you know it won't have bad side effects for clients of existing APIs.
     *
     *   In addition to 'LESS_GET_FIELDS' you can ask for another list of fields to be pruned in the api section of the entity.
     *   This way you can have a list of fields to prune if, say, some feature is off.
     *
     * @param array           $values
     * @param string|string[] $fields
     * @param string          $additionalApiPrune
     *
     * @return array
     */
    public function API_pruneFieldsOwnedToo(&$values, $fields, $additionalApiPrune = '')
    {
        return $values;
    }
}
