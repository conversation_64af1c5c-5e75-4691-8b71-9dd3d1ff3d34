<?php

namespace tests\source\core;

use PHPUnit\Framework\TestCase;

class KafkaClientTest extends TestCase
{

    private static \AbstractKafkaClient $kafkaRestClient;

    /**
     * Setup data common to all invocations of test routines
     *
     */
    public static function setUpBeforeClass() : void
    {
        echo "\nKafkaClientTest::setUpBeforeClass()\n";
        self::$kafkaRestClient = \KafkaClientBuilder::getBuilder()
                                                    ->withClientType(\KafkaClientConstants::KAFKA_REST_PROXY)
                                                    ->build();
    }

    /**
     * ensure calling the KafkaClientBuilder multiple times with the same client Type
     * gives you the same instance and skips recrea
     *
     * @return void
     */
    public function testMakeSureInstancesAreSingleton()
    {
        $kafkaClientInstance = \KafkaClientBuilder::getBuilder()
                                                  ->withClientType(\KafkaClientConstants::KAFKA_REST_PROXY)
                                                  ->build();

        $newKafkaClientInstance = \KafkaClientBuilder::getBuilder()
                                                     ->withClientType(\KafkaClientConstants::KAFKA_REST_PROXY)
                                                     ->build();

        $this->assertSame($kafkaClientInstance, $newKafkaClientInstance);
    }

    /**
     * ensure kafkaClient instance is able to put a message on topic
     *
     * <b> WARNING </b>
     * this is an integration test and can take anywhere from 30 sec to minutes to run
     * this is skipped in CI run and when run as part of test suite
     *
     * <p>
     * Developers can run this as needed when run directly as a single test
     *
     * @return void
     */
    public function testPutSingleMessage()
    {
        if ( self::isInSuite($this->getCurrentClass() . '::' . __FUNCTION__) ) {
            // skip
            print("============skip ". __FUNCTION__ . "============\n");
            $this->assertTrue(true);
            return;
        }
        $response = self::$kafkaRestClient->putWithTopic("radu", [ [ "name" => "John Doe", "age" => 35 ] ]);

        $this->assertTrue($response);
    }

    /**
     * ensure kafkaClient instance is able to put multiple messages on topic
     *
     * <b> WARNING </b>
     * this is an integration test and can take anywhere from 30 sec to minutes to run
     * this is skipped in CI run and when run as part of test suite
     *
     * <p>
     * Developers can run this as needed when run directly as a single test
     *
     * @return void
     */
    public function testPutMultipleMessages()
    {
        if ( self::isInSuite($this->getCurrentClass() . '::' . __FUNCTION__) ) {
            // skip
            print("============skip ". __FUNCTION__ . "============\n");
            $this->assertTrue(true);
            return;
        }
        $response = self::$kafkaRestClient->putWithTopic("radu", [ [ "name" => "John Doe", "age" => 35 ],
                                                                    [ "name" => "tester", "age" => 33 ] ]);

        $this->assertTrue($response);
    }

    /**
     * ensure AbstractKafkaClient throws RetryException when Kafka operation fails.
     * one such case would be when  a bad topic name is passed in
     *
     * <b> WARNING </b>
     * this is an integration test and can sometimes be flaky if the host is not reachable
     *
     * <p>
     * Developers can run this as needed when run directly as a single test
     *
     * @return void
     */
    public function testFailure()
    {
        if ( self::isInSuite($this->getCurrentClass() . '::' . __FUNCTION__) ) {
            // skip
            print("============skip ". __FUNCTION__ . "============\n");
            $this->assertTrue(true);
            return;
        }
        $this->expectException(\RetryException::class);

        self::$kafkaRestClient->putWithTopic("bad/topic/name", [ "msg" => "hello world" ]);
    }

    /**
     * check if the test is running as part of suite
     * @param string $test
     *
     * @return bool true if running as part of suite; false otherwise
     */
    private static function isInSuite(string $test) : bool
    {
        global $argv;
        foreach ( $argv as $arg ) {
            if ( strpos($arg, $test) !== false ) {
                return false;
            }
        }

        return true;
    }

    /**
     * @return string
     */
    private function getCurrentClass(): string
    {
        $parts = explode('\\', get_class());
        return end($parts);
    }
}
