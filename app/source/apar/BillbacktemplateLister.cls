<?
/**
 *    FILE:        BillbacktemplateLister.cls
 *    AUTHOR:        MGohel
 *    DESCRIPTION: BillbacktemplateListerls class
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */


import('NLister');

class BillbacktemplateLister extends NLister
{
    function __construct()
    {
        parent::__construct(
            array(
                'entity'        =>  'billbacktemplate',
                'fields'        => array('TEMPLATEID','DESCRIPTION','STATUS'),
                'helpfile'        => 'ARP001',
            )
        );
        if(IsMultiEntityCompany()) {
            $this->privateButton = false;
        }
    }

    /**
     * @return bool|string
     */
    function calcAddUrl()
    {
        if(IsMultiEntityCompany() && GetContextLocation()) {
            return false;
        }
        return parent::calcAddUrl();
    }
}