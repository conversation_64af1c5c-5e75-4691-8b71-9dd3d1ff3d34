openapi: 3.0.0
info:
  title: contracts-compliance-task
  description: contracts.compliance-task API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Contract compliance tasks
    description: A task that can be included in the compliance checklist.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/contracts/compliance-task:
    get:
      summary: List Contract compliance tasks
      description: Returns a collection with a key, ID, and link for each Contract compliance task.
      tags:
        - Contract compliance tasks
      operationId: list-contract-compliance-task
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of contract-compliance-task objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of Contract compliance tasks:
                  value:
                    'ia::result':
                      - key: '1'
                        id: '1'
                        href: '/objects/contracts/compliance-task/1'
                    'ia::meta':
                      totalCount: 1
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a Contract compliance task
      description: Creates a new Contract compliance task.
      tags:
        - Contract compliance tasks
      operationId: create-contract-compliance-task
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/contracts-compliance-task'
                - $ref: '#/components/schemas/contracts-compliance-taskRequiredProperties'
            examples:
              Creates a Contract compliance task:
                value:
                  name: 'Review'
                  description: 'Review'
                  taskNumber: 12
                  status: 'active'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New contract-compliance-task
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                New Contract compliance task:
                  value:
                    'ia::result':
                      - key: '3'
                        id: '3'
                        href: '/objects/contracts/compliance-task/3'
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  '/objects/contracts/compliance-task/{key}':
    parameters:
      - name: key
        description: system-assigned unique key for the Contract compliance task.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a Contract compliance task
      description: Returns detailed information for a particular Contract compliance task.
      tags:
        - Contract compliance tasks
      operationId: get-contract-compliance-task-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the contract-compliance-task
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/contracts-compliance-task'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the Contract compliance task:
                  value:
                    'ia::result':
                      - key: '2'
                        id: '2'
                        name: 'Review'
                        description: 'Task to check after completion'
                        taskNumber: 1
                        status: 'active'
                        audit:
                          modifiedDateTime: '2024-02-15T08:09:35Z'
                          createdDateTime: '2024-02-15T08:09:35Z'
                          createdBy: '1'
                          modifiedBy: '1'
                        href: '/objects/contracts/compliance-task/2'
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a Contract compliance task
      description: Updates an existing Contract compliance task by setting field values. Any fields not provided remain unchanged.
      tags:
        - Contract compliance tasks
      operationId: update-contract-compliance-task-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/contracts-compliance-task'
                - type: object
                  properties:
                    id:
                      readOnly: true
            examples:
              Updates a Contract compliance task:
                value:
                  name: 'Review checklist to complete'
                  description: 'Review checklist'
                  taskNumber: 13
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated contract-compliance-task
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Updated Contract compliance task:
                  value:
                    'ia::result':
                      - key: '3'
                        id: '3'
                        href: '/objects/contracts/compliance-task/3'
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a Contract compliance task
      description: Deletes a Contract compliance task.
      tags:
        - Contract compliance tasks
      operationId: delete-contract-compliance-task-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    contracts-compliance-task:
      $ref: ../models/contracts.compliance-task.s1.schema.yaml
    contracts-compliance-taskRequiredProperties:
      type: object
      required:
        - id
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
