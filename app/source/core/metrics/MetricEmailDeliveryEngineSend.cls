<?php
/**
 * Metric class for EmailDeliveryEnginecomponent, send metric id
 *
 * <AUTHOR>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

/* --------------------------------------------------------------------------------------------------------------
 * DO NOT EDIT THIS FILE DIRECTLY. Update metrics_definition.json, then regenerate the file with metrics_gen.bin
 * --------------------------------------------------------------------------------------------------------------
 */
 
final class MetricEmailDeliveryEngineSend  extends IAMetrics
{

    /**
     * IAMetrics constructor.
     *
     * @param string $type
     * @param string $sender
     * @param string $recipients
     * @param string $subUser
     * @param bool $authenticatedSend
     * @param bool $success
     * @param string $error

     */
    public function __construct($type = null, $sender = null, $recipients = null, $subUser = null, $authenticatedSend = null, $success = null, $error = null)
    {
        parent::__construct(
            'EmailDeliveryEngine',
            'send',
            0,
            [
                'time'
            ],
            [
                'type',
                'sender',
                'recipients',
                'subUser',
                'authenticatedSend',
                'success',
                'error'
            ]
            , $type, $sender, $recipients, $subUser, $authenticatedSend, $success, $error
        );
    }

    public function startTime()
    {
        parent::startTimer('time');
    }
    
    public function stopTime()
    {
        parent::stopTimer('time');
    }

    /**
     * @return float|null
     */
    public function getTime(): ?float
    {
        return parent::getValue('time');
    }
    
    /**
     * @param float $time
     */
    public function setTime(float $time)
    {
        parent::setValue('time', $time);
    }

    /**
     * @return string|null
     */
    public function getType(): ?string
    {
        return parent::getAttribute('type');
    }
    
    /**
     * @param string $type
     */
    public function setType(string $type)
    {
        parent::setAttribute('type', $type);
    }

    /**
     * @return string|null
     */
    public function getSender(): ?string
    {
        return parent::getAttribute('sender');
    }
    
    /**
     * @param string $sender
     */
    public function setSender(string $sender)
    {
        parent::setAttribute('sender', $sender);
    }

    /**
     * @return string|null
     */
    public function getRecipients(): ?string
    {
        return parent::getAttribute('recipients');
    }
    
    /**
     * @param string $recipients
     */
    public function setRecipients(string $recipients)
    {
        parent::setAttribute('recipients', $recipients);
    }

    /**
     * @return string|null
     */
    public function getSubUser(): ?string
    {
        return parent::getAttribute('subUser');
    }
    
    /**
     * @param string $subUser
     */
    public function setSubUser(string $subUser)
    {
        parent::setAttribute('subUser', $subUser);
    }

    /**
     * @return bool|null
     */
    public function getAuthenticatedSend(): ?bool
    {
        return parent::getAttribute('authenticatedSend');
    }
    
    /**
     * @param bool $authenticatedSend
     */
    public function setAuthenticatedSend(bool $authenticatedSend)
    {
        parent::setAttribute('authenticatedSend', $authenticatedSend);
    }

    /**
     * @return bool|null
     */
    public function getSuccess(): ?bool
    {
        return parent::getAttribute('success');
    }
    
    /**
     * @param bool $success
     */
    public function setSuccess(bool $success)
    {
        parent::setAttribute('success', $success);
    }

    /**
     * @return string|null
     */
    public function getError(): ?string
    {
        return parent::getAttribute('error');
    }
    
    /**
     * @param string $error
     */
    public function setError(string $error)
    {
        parent::setAttribute('error', $error);
    }

}
