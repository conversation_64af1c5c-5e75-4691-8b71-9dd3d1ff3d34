<?php

require 'prentry.ent';

$kSchemas['eppaymentitem'] = $kSchemas['prentry'];

$kSchemas['eppaymentitem']['parententity'] = 'eppayment';
$kSchemas['eppaymentitem']['printas'] = 'IA.EMPLOYEE_PAYMENT_DETAIL';
$kSchemas['eppaymentitem']['pluralprintas'] = 'IA.EMPLOYEE_PAYMENT_DETAILS';
$kSchemas['eppaymentitem']['rpdFilters'] = [
    ['eppayment.recordtype', '=', PRRECORD_TYPE_REIMBURSEMENT],
];
$kSchemas['eppaymentitem']['publish'] = [
    'ENTRYDESCRIPTION',
    'GLACCOUNTNO',
    'GLACCOUNTTITLE',
    'AMOUNT',
    'LOCATIONID',
    'LOCATIONNAME',
    'DEPARTMENTID',
    'DEPARTMENTNAME',
    'LINE_NO',
    'ACCOUNTLABEL',
    'TOTALSELECTED',
    'TOTALPAID',
    'ALLOCATION',
];
$kSchemas['eppaymentitem']['module'] = 'ee';
$kSchemas['eppaymentitem']['nexus']['eppayment'] =
    [
        'object'   => 'eppayment',
        'relation' => MANY2ONE,
        'field'    => 'RECORDKEY',
    ];

$kSchemas['eppaymentitem']['children']['eppayment'] =
    [
        'fkey'    => 'recordkey',
        'invfkey' => 'record#',
        'table'   => 'prrecord',
    ];

$kSchemas['eppaymentitem']['api'] = [
    'PERMISSION_MODULES' => ['ee'],
    'PERMISSION_CREATE'  => 'NONE',
    'PERMISSION_UPDATE'  => 'NONE',
    'PERMISSION_DELETE'  => 'NONE',
    'PERMISSION_READ'    => 'lists/eppaymentrequest',
];

$kSchemas['eppaymentitem']['ignoredimensions'] = array('costtype');
$kSchemas['eppaymentitem']['allowDDS'] = false;
$kSchemas['eppaymentitem']['schema']['RECORDTYPE'] = 'eppayment.recordtype';
$kSchemas['eppaymentitem']['dbfilters'] = array (
    array ('eppayment.recordtype', '=', PRRECORD_TYPE_REIMBURSEMENT),
);
$kSchemas['eppaymentitem']['description'] = 'IA.DETAILED_INFORMATION_FOR_EMPLOY_EXPENSE_PAYMENT';
