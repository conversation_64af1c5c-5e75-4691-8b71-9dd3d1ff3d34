# Workflow to push committed informaton to Ticket Master

name: Update Ticket

# Controls when the action will run. 
on:
  push:
    branches:
      - main
      - 'Feb[2-9][0-9]'
      - 'May[2-9][0-9]'
      - 'Aug[2-9][0-9]'
      - 'Nov[2-9][0-9]'
      - 'Aug[2-9][0-9]-[0-9][0-9]'
      - 'Aug[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
      - 'Feb[2-9][0-9]-[0-9][0-9]'
      - 'Feb[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
      - 'May[2-9][0-9]-[0-9][0-9]'
      - 'May[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
      - 'Nov[2-9][0-9]-[0-9][0-9]'
      - 'Nov[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
      
    # Ignore all the changes specific to workflow
    paths-ignore:
      - '.github/**'
      - '.gitattributes'
      - '.gitignore'
      - '.phpstorm.meta.php/**'
      - '/app/dev/**'
      - '/app/tests/**'
      - '/app/unitTest/**'

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # Updating ticket information in ticket master application
  update_ticket:
    runs-on: self-hosted

    steps:
      # Steps to run ticket master integration script
      - name: Clone intacct/git-tm-integration repo
        uses: actions/checkout@v3
        with:
          repository: intacct/git-tm-integration
          path: git-tm-integration
          ssh-key: ${{ secrets.GIT_TM_INTEGRATION_DEPLOY_KEY }}

      - name: Clone intacct/ia-app repo
        uses: actions/checkout@v3
        with:
          path: ia-app

      - name: Push ticket information to TM app
        run: php ./git-tm-integration/gittm.php "-action=update" "-title=intacct" "-senderid=intacct_dev" "-senderpassword=${{ secrets.TM_SENDER_PASSWORD }}" "-userid=xml_gateway_SVN" "-userpassword=${{ secrets.TM_USER_PASSWORD }}" "-commitbranch=${{ github.ref }}" "-token=${{ github.token }}" "-gitcommiturl=$GITHUB_API_URL/repos/$GITHUB_REPOSITORY/commits/$GITHUB_SHA" "-gitrepourl=${{ github.repositoryUrl }}" "-forcecommandline=0"
