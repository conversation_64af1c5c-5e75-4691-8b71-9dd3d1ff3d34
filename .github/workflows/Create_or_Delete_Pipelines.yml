name: PR Job Creation and Deletion     
on:
   pull_request:
    types: [opened, closed, reopened, ready_for_review]
    branches:
      - 'main'
      - 'Feb[2-9][0-9]'
      - 'May[2-9][0-9]'
      - 'Aug[2-9][0-9]'
      - 'Nov[2-9][0-9]' 
      - 'Aug[2-9][0-9]-[0-9][0-9]'
      - 'Aug[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
      - 'Feb[2-9][0-9]-[0-9][0-9]'
      - 'Feb[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
      - 'May[2-9][0-9]-[0-9][0-9]'
      - 'May[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
      - 'Nov[2-9][0-9]-[0-9][0-9]'
      - 'Nov[2-9][0-9]-[0-9][0-9]-[0-9][0-9]'
   
jobs:
   Create_or_Delete_Pipelines:
    runs-on: self-hosted 
    steps:            
      - name: <PERSON><PERSON> for <PERSON><PERSON><PERSON> on PR opened or ready_for_review
        if: github.event.action == 'opened' || github.event.action == 'ready_for_review' || github.event.action == 'closed'
        id: Jenkinsjob
        run: |    
                  # Kill the previous scanning job.                   
                   curl -v -X GET https://ci-prod.intacct.com/crumbIssuer/api/json --user ci-automation:${{ secrets.PROD_JENKINS_TOKEN_P307 }} --insecure > crumb.log
                   echo "catch it ------------->"
                   new_crumb=$(cat crumb.log |grep crumb |awk -F ',"crumb":' '{print $2}' |awk -F ',' '{print $1}' | tr -d '"')
                   echo "caught it ------------->"
                   echo $new_crumb
                   curl -k  -s  -X  POST -L https://ci-prod.intacct.com/view/ia-app/job/Pull_Requests/indexing/stop --user ci-automation:${{ secrets.PROD_JENKINS_TOKEN_P307 }} -H '.crumb: $new_crumb' --insecure
                  # Start Scanning for PR jobs
                 
                   curl -k  -s -POST https://ci-prod.intacct.com/multibranch-webhook-trigger/invoke?token=ia-app --insecure
                   
                   sleep 360
                   
                  # Kill the previous scanning job.                   
                    
                   curl -v -X GET https://ci-prod.intacct.com/crumbIssuer/api/json --user ci-automation:${{ secrets.PROD_JENKINS_TOKEN_P307 }} --insecure > crumb.log
                   echo "catch it ------------->"
                   new_crumb=$(cat crumb.log |grep crumb |awk -F ',"crumb":' '{print $2}' |awk -F ',' '{print $1}' | tr -d '"')
                   echo "caught it ------------->"
                   echo $new_crumb
                   curl -k  -s -X  POST -L https://ci-prod.intacct.com/view/ia-app/job/Pull_Requests/indexing/stop --user ci-automation:${{ secrets.PROD_JENKINS_TOKEN_P307 }} -H '.crumb: $new_crumb' --insecure

      
      - name: Scan Repo for Jenkinsfiles on PR reopened
        if: github.event.action == 'reopened'
        id: Jenkinsjob-reopened
        run: |    
               curl -k  -s -POST https://ci-prod.intacct.com/multibranch-webhook-trigger/invoke?token=ia-app --insecure
                   
                  
