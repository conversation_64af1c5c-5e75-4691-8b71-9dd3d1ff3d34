<?php

/**
 * Event Handler for Update PODocument
 *
 * FILE:			 DNSetPODocumentEventHandler.cls
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 Intacct Corporation, All Rights Reserved
 */
class DNSetPODocumentEventHandler extends DNPODocumentCommonEventHandler
{

    /** @var string $orgId */
    protected $orgId;


    public function __construct()
    {
       parent::__construct();
    }

    /**
     * @return string
     */
    protected function getEventSubjectStr()
    {
        return '_PODOC_';
    }

    /**
     * @param array $job
     * @return bool
     */
    private function validateBulkAddQueue($job)
    {

        $ok = true;

        $dnQueueMgr = Globals::$g->gManagerFactory->getManager('podigitalnetworksyncqueue');
        // is there any DN sync job in queue, stop delete if true
        if(!isNullOrBlank($job['LOCATIONKEY'])) {
            $locationFilter = ['LOCATIONKEY', '=', $job['LOCATIONKEY']];
        }else {
            $locationFilter = ['LOCATIONKEY', 'is null'];
        }

        $dnJobQueue = [
            'selects'=>['TOPIC'],
            'filters'=>[
                [
                    ['STATE', '=', 'Q'],
                    ['TYPE', '=', 'P'],
                    ['TOPIC', '=', PODigitalNetworkSyncConsumer::BULKADD_PODOCUMENT],
                    ['RECORDNO', '<', $job['RECORDNO']],
                    $locationFilter,
                ]
            ]
        ];
        $dnJobQueueEntries = $dnQueueMgr->GetList($dnJobQueue);

        if(!empty($dnJobQueueEntries)) {
            $ok = false;
        }

        return $ok;
    }
    /**
     * @param array $job
     * @param array $jobDetails
     *
     * @return bool
     */
    public function processEvent(&$job, $jobDetails) : bool
    {
        
        $ok = $this->validateBulkAddQueue($job);
        $isFeedbackEvent = false;
        if(!$ok) {
            $errMsg = "Bulk Add PO Document not completed, please retry.";
            Globals::$g->gErr->addError('PO-0140', __FILE__ . ':' . __LINE__, $errMsg);
            return false;
        }

        $metricPOAutomationDocument = new MetricPOAutomationDocumentSync();
        $metricPOAutomationDocument->startTime();
        $metricPOAutomationDocument->setJobRecord($job['RECORDNO']);
        $metricPOAutomationDocument->setJobAction($job['TOPIC']);
        $metricPOAutomationDocument->setWhenCreated($job['WHENCREATED']);
        $metricPOAutomationDocument->setCUser($job['CREATEDBY']);

        XACT_BEGIN(__METHOD__);
        $startTime = time();
        // org/compmany is nont subscribe
        if (WPBSetupManager::isSubscribed()) {

            $jobDataMap = $job['OBJECT']['dataMap'];

            if (!empty($jobDataMap['DOCID'])) {
                $docMgr = Globals::$g->gManagerFactory->getManager('podocument');
                $includeHeaderFields = DNEventHandlerConstaint::$poIncludeFieldMap[DNEventHandlerConstaint::PODOCUMENT_TYPE];
                $objData = $docMgr->get($jobDataMap['DOCID'], $includeHeaderFields);
                if (Util::countOrZero( $objData ) > 0) {
                    $metricPOAutomationDocument->setDocumentAction('Set');
                    $metricPOAutomationDocument->setDocumentRecord($objData['RECORDNO']);
                    $metricPOAutomationDocument->setDocumentType($objData['DOCPARID']);
                    $metricPOAutomationDocument->setDocumentNo($objData['DOCNO']);
                    $metricPOAutomationDocument->setDocumentState($objData['STATE']);
                    $metricPOAutomationDocument->setDocumentSource($objData['DOCSOURCE']);

                    //prepareFeedback data, we are populating File UUID for standalone and target-sync
                    $this->prepareFeedbackPODocument($jobDataMap, $objData);

                    //This is specific logic added for SAIL team.
                    //So that they can differentiate the Source and Target Events
                    if(isset($jobDataMap['ISTRAGETDOCUMENT']) && $jobDataMap['ISTRAGETDOCUMENT'] == true){
                        $isFeedbackEvent = true;
                        $objData['TARGET_DOCUMENT'] = true;
                    }

                    //add standalone feedback flag for the document
                    $metricPOAutomationDocument->setIsStandAloneTarget(false);
                    if(isset($jobDataMap['ISSTANDALONEDOCUMENT']) && $jobDataMap['ISSTANDALONEDOCUMENT'] == true){
                        $isFeedbackEvent = true;
                        $objData['STAND_ALONE_FEEDBACK'] = true;
                        $metricPOAutomationDocument->setIsStandAloneTarget(true);
                    }
                    // add feedback flag for the document
                    $metricPOAutomationDocument->setIsFeedBack($isFeedbackEvent);
                    // add three-way match feedback flag for the document
                    $metricPOAutomationDocument->setIsThreeWaySource(false);
                    if(isset($jobDataMap['ISTHREEWAYMATCHDOC']) && $jobDataMap['ISTHREEWAYMATCHDOC'] == true){
                        $objData['THREE_WAY_MATCHING_SOURCE'] = true;
                        $metricPOAutomationDocument->setIsThreeWaySource(true);
                    }

                    $locationContext = $job['LOCATIONKEY'];

                    // Company GUID
                    $guid = Profile::getCompanyCacheProperty('COMPANYPREF', 'GUID');
                    $metricPOAutomationDocument->setLocation($locationContext ?? 0);
                    $metricPOAutomationDocument->setGuId($guid);

                    $externalID = $guid . '_0';
                    // Top Level or non Mega company
                    if (!isNullOrBlank($locationContext)) {
                        // Populate Entity ExternalID GUID_$locationContext
                        $externalID = $guid . '_'.$locationContext;

                    }
                    $this->setExternalIDMap($objData['RECORDNO'], $externalID);

                    $subjectStr = $this->getEventSubjectStr();

                    // Set Subject ID -- FORMAT - GUID_VEN_Record#
                    $subjectID = $guid . $subjectStr . $objData['RECORDNO'];

                    // set the idemPotencyKey
                    $idemPotencyKey = $guid . '_' . $job['RECORDNO'] . '_' . $job['TOPIC'];

                    // set subject
                    $this->setSubjectID($subjectID);

                    // set idempotencyKey
                    $this->setIdemPotencyKey($idemPotencyKey);

                    //Most of the headre Fields filtered here
                    $objData = $this->filterBlackListField($objData, DNEventHandlerConstaint::PODOCUMENT_TYPE);

                    // Prepare PO trasnaction header Data
                    $poMatchData = $this->preparePOTransactionHeaderDataMap($objData);

                    // Prepare PO trasnaction entry Data
                    $poMatchData['LINES'] = $this->preparePOTransactionEntriesDataMap($objData);

                    // 3 Reformat Date
                    $poMatchData = $this->reformatDateFields($poMatchData, DNEventHandlerConstaint::PODOCUMENT_TYPE);

                    $poMatchData = $this->changeToLowerCase($poMatchData);

                    $this->translateContactDetail($poMatchData, DNEventHandlerConstaint::PODOCUMENT_TYPE);

                    //4. Populate "companyids"
                    $poMatchData = $this->populateExternalIDMap($poMatchData);

                    //5. Prepare Final Sync Map
                    $syncData[] = $this->prepareEntitySyncDataMap($poMatchData);

                    $ok = $this->putEvents($syncData, $respHttpCode, $response);
                } else {
                    logToFileInfo("DigitalNetworkSync Processor :: Error in processing Set PO Document Event");
                    logToFileInfo("DigitalNetworkSync Processor :: Document data doesn't exist, it may be deleted");
                    $metricPOAutomationDocument->setError("Error in processing Set PO Document Event :: Document data doesn't exist, it may be deleted");
                }
            } else {
                logToFileCritical("DigitalNetworkSync Processor :: Error in processing Set PO Document Event");
                logToFileCritical("DigitalNetworkSync Processor :: DOCID is empty");
                $metricPOAutomationDocument->setError("Error in processing Set PO Document Event :: DOCID is empty ");
                $errMsg = "DigitalNetworkSync Processor :: Error processing Set PO Document ";
                Globals::$g->gErr->addError('PO-0138', __FILE__ . ':' . __LINE__,'', $errMsg);
                $ok = false;
            }
        } else {
            $errMsg = "DigitalNetworkSync Processor :: Organization/Company not subscribed ";
            $metricPOAutomationDocument->setError(" Organization/Company not subscribed ");
            Globals::$g->gErr->addError('PO-0129', __FILE__ . ':' . __LINE__, $errMsg);
            $ok = false;
        }

        if($ok){
            $this->setSyncStatus(self::STATUS_COMPLETE);
            $metricPOAutomationDocument->setStatus(self::STATUS_COMPLETE);
        }

        $ok = $ok && XACT_COMMIT(__METHOD__);

        if ( ! $ok ) {
            XACT_ABORT(__METHOD__);
            logToFileCritical("DigitalNetworkSync Processor :: Error in processing Set PO Document Event");
            $metricPOAutomationDocument->setError(" DigitalNetworkSync Processor :: Error in processing Set PO Document Event ");
            $errMsg = "DigitalNetworkSync Processor :: Error processing Set PO Document ";
            Globals::$g->gErr->addError('PO-0138', __FILE__ . ':' . __LINE__,'', $errMsg);
        }

        $timeProcess = time() - $startTime;
        $metricPOAutomationDocument->setTimeForProcess($timeProcess);
        $metricPOAutomationDocument->stopTime();
        $metricPOAutomationDocument->publish();

        return $ok;
    }

    /**
     * @return string
     */
    protected function getObjectSyncEventName()
    {
        return DNEventHandlerConstaint::PODOCUMENTUPDATED_EVENT;
    }

    /**
     * @return string
     */
    protected function getObjectIDField()
    {
        return 'recordno';
    }
}