<?php

/**
 * File AggregateTimesheetBillableLine.cls contains the class AggregateTimesheetBillableLine
 *
 * <AUTHOR> kumar <<EMAIL>>
 * @copyright 2019 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * AggregateTimesheetBillableLine billable line DTO
 *
 * Class AggregateTimesheetBillableLine
 */
class AggregateTimesheetBillableLine extends GenInvoiceBillableLine
{
    /* @var string[] $rawLine */
    private $rawLine;

    /**
     * @param int   $itemKey     item record no
     * @param int   $customerKey customer record no
     * @param float $trxAmount   transaction amount
     * @param int   $quantity    item quantity
     */
    public function __construct($itemKey, $customerKey, $trxAmount, $quantity)
    {
        parent::__construct($itemKey, $customerKey, $trxAmount, $quantity);
    }

    /**
     * Copy relevant attributes from raw lines in DB
     *
     * @param array $rawLine raw data from database query
     */
    public function innerFromRawLine($rawLine)
    {
        // save all raw data from database for this object
        $this->rawLine = $rawLine;
        $this->rawLine['TYPE'] = GenInvoiceUtil::SOURCE_PREFIX_AGGREGATE_TIMESHEET;
        $this->setContractKey($this->rawLine['PROJECT.CONTRACTKEY']);
        $this->setProjectKey($this->rawLine['PROJECTKEY']);
        $this->setDeptKey($this->rawLine['DEPTKEY']);
    }

    /**
     * Set entity-specific attribute values
     *
     * @param array $lineEntity incoming object being created
     */
    public function innerToGenInvoiceLineEntity(&$lineEntity)
    {


        $lineEntity['LOCATIONKEY'] = $this->rawLine['LOCATIONKEY'];
        $lineEntity['DEPTKEY'] = $this->rawLine['DEPTKEY'];
        $lineEntity['CLASSDIMKEY'] = $this->rawLine['CLASSDIMKEY'];
        $lineEntity['VENDORDIMKEY'] = $this->rawLine['VENDORDIMKEY'];
        $lineEntity['EMPLOYEEDIMKEY'] = $this->rawLine['EMPLOYEEDIMKEY'];
        $lineEntity['ASSETDIMKEY'] = $this->rawLine['ASSETKEY'];


//         set entity-specific fields needed
        $lineEntity['TYPE'] = GenInvoiceUtil::SOURCE_PREFIX_AGGREGATE_TIMESHEET;
        $lineEntity['TASKDIMKEY'] = $this->rawLine['TASKKEY'];
        $lineEntity['PRICE'] = $this->rawLine['PRICE'];
        $lineEntity['ITEMKEY'] = $this->rawLine['ITEMKEY'];
        $lineEntity['TRX_AMOUNT'] = $this->rawLine['TRX_VALUE'];
        $lineEntity['AggTimesheetEntryMapping'] = $this->rawLine['AggTimesheetEntryMapping'];
//        if (ContractUtil::isMCPEnabled()) {
//            $exchRate = $lineEntity['EXCHRATE'];
//            foreach($lineEntity['AggTimesheetEntryMapping'] as &$value){
//                $amount = ibcmul($value['TRX_AMOUNT'], $exchRate, ContractUtil::AMOUNT_PRECISION, true);
//                $value['AMOUNT'] = $amount;
//            }
//        }

    }

    /**
     * Gets the entries to be locked for the billable line
     *
     * @param int $contractRunEntryKey
     * @param int $genInvoiceHeaderKey
     * @param int $locationKey
     *
     * @return array
     */
    public function getLockingEntries($contractRunEntryKey, $genInvoiceHeaderKey, $locationKey)
    {
        $lockingEntries = [];
        $tsEntryKeys = $this->getTsEntryKeysFromAggregrate((array) $this->rawLine['AggTimesheetEntryMapping']);
        foreach ($tsEntryKeys as $tsEntryKey) {
            $lockingEntry = $this->createLockingEntry($contractRunEntryKey, $genInvoiceHeaderKey, $locationKey);
            $lockingEntry['TSENTRYKEY'] = $tsEntryKey;
            $lockingEntries[] = $lockingEntry;
        }
        return $lockingEntries;
    }

    /**
     * Gets the timesheet entry keys (i.e. TSENTRYKEYs) included in the timesheet aggregation
     *
     * @param array $aggTimesheetEntryMapping
     *
     * @return array
     */
    private function getTsEntryKeysFromAggregrate($aggTimesheetEntryMapping)
    {
        $tsEntryKeys = [];
        foreach ($aggTimesheetEntryMapping as $genInvoiceLineAndTsEntry) {
            $tsEntryKeys[] = $genInvoiceLineAndTsEntry['TSENTRYKEY'];
        }
        return $tsEntryKeys;
    }
}
