<?

/**
 * Order Entry SubTotalTemplate entity picker
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Intacct Corporation, All Rights Reserved
 */

class POSubTotalTemplatePicker extends SubTotalTemplatePicker
{

    public function __construct()
    {
        $params = array(
            'entity'        =>  'posubtotaltemplate',
            'title'         =>  'IA.PURCHASING_SUBTOTAL_TEMPLATES',
            'helpfile'      =>  'Adding_Editing_Viewing_SUBTOTAL_TEMPLATE',
            'fields'        =>  array('NAME'),
            'pickfield'     => 'NAME',
        );

        parent::__construct($params);

    }

}