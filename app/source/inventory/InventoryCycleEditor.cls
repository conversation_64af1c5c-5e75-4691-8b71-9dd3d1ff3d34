<?php
/**
 * =============================================================================
 *
 * FILE:        InventoryCycleEditor.cls
 * AUTHOR:        
 * DESCRIPTION:   Inventory Cycle Editor
 *
 * (C)2000,2009 Intacct Corporation, All Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 * =============================================================================
 */

/**
 * Editor class for InventoryCycleEditor object
 */
class InventoryCycleEditor extends FormEditor
{
    /**
     * @param array $_params Initial params
    */
    public function __construct($_params = array())
    { 
        parent::__construct($_params); 
    }

}

