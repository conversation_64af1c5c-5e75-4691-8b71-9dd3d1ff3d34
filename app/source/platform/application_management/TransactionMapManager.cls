<?php

/**
 * Class TransactionMapManager
 * TABLE: TRANSACTION_MAP (global schema)
 */

class TransactionMapManager extends OwnedObjectManager
{
    /**
     * @param $values
     *
     * @return bool
     */
    function regularAdd(&$values)
    {
        $partnerDoctype = $values['PARTNER_DOCTYPE'];
        $docparMgr = Globals::$g->gManagerFactory->getManager('documentparams');
        $values['WHENCREATED'] = GetCurrentTimestamp();
        $values['WHENMODIFIED'] = GetCurrentTimestamp();
        $values['MODIFIEDBY'] = $values['CREATEDBY'] = GetMyUserid();
        if ($docparMgr->doctypeExists($partnerDoctype, GetMyCompany())) {
            return parent::regularAdd($values);
        } else {
            Globals::$g->gErr->addIAError(
                'PAAS-0932', __FILE__ . ':' . __LINE__,
                "Transaction Definition for $partnerDoctype is not defined.", ['DOCTYPE' => $partnerDoctype]
            );

            return false;
        }
    }
    
    /**
     * @param $values
     *
     * @return bool
     */
    function regularSet(&$values)
    {
        $values['MODIFIEDBY'] = GetMyUserid();
        $partnerDoctype = $values['PARTNER_DOCTYPE'];
        $docparMgr = Globals::$g->gManagerFactory->getManager('documentparams');
        if ($docparMgr->doctypeExists($partnerDoctype, GetMyCompany())) {
            return parent::regularSet($values);
        } else {
            Globals::$g->gErr->addIAError(
                'PAAS-0932', __FILE__ . ':' . __LINE__,
                "Transaction Definition for $partnerDoctype is not defined.", ['DOCTYPE' => $partnerDoctype]
            );

            return false;
        }
    }
}
