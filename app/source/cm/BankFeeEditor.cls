<?php

/**
 * Editor class for the Bank Fee object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Intacct Corporation All, Rights Reserved
 */

/**
 * Editor class for the Bank Fee object
 */
class BankFeeEditor extends SubLedgerTxnEditor
{

    // Class variables
    /* @var null|string $fromBankReconScrn */
    private $fromBankReconScrn;
    /* @var array $transactionTypeForAcctLabel */
    private static $transactionTypeForAcctLabel = array(
        'Service charge' => 'SERVICECHARGEACCOUNTLABEL',
        'Interest earned' => 'INTERESTEARNEDACCOUNTLABEL'
    );

    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);

        // Do we come from bank reconciliation
        $this->fromBankReconScrn = Request::$r->_fromBankReconScrn;
    }

    /**
     * Map the ajax action to a PHP function
     *
     * @param string $cmd the Ajax action requested
     *
     * @return bool false if command not found
     */
    protected function runAjax($cmd)
    {
        $ok = true;

        switch ( $cmd ) {
            case 'getLocationRecordNo':
                if( Request::$r->equal == 'false') {
                    Request::$r->{'taxSolutionIds'} = $this->getEntityMgr()->getNonTaxImplicationTaxMethods();
                }
                SubLedgerTxnEditor::ajaxGetLocationRecordNo();
                break;
            default:
                $ok = parent::runAjax($cmd);
                break;
        }

        return $ok;
    }

    /**
     * Return an array of javascript files to include into the page
     *
     * @return array the list of javascript files to include
     */
    protected function getJavaScriptFileNames()
    {
        $jsFiles = parent::getJavaScriptFileNames();
        $jsFiles[] = '../resources/js/bankfee.js';
        return $jsFiles;
    }

    /**
     * Get the account label path
     *
     * @return string the account label path
     */
    protected function getAccountLabelPath()
    {
        // By default the service account label will be showned
        return 'SERVICECHARGEACCOUNTLABEL';
    }

    /**
     * Get the path of the entry memo field
     *
     * @return string[]|null the memo path
     */
    protected function getDefaultEntryMemoField()
    {
        return array(
            'path' => 'DESCRIPTION',
            'fullname' => 'IA.DESCRIPTION'
        );
    }

    /**
     * Transform the data before the view is built
     *
     * @param array $obj the timesheet data
     *
     * @return bool false if errors else true
     */
    protected function transformBizObjectToView(&$obj)
    {
       if (is_array($obj['ITEMS'])) {
           foreach ( $obj['ITEMS'] as &$item ) {
               $item[self::$transactionTypeForAcctLabel[$obj['TRANSACTIONTYPE']]] = $item['ACCOUNTLABEL'] ?? null;
           }
           unset($item);
       }
        return true;
    }

    /**
     * Transform the view data before is reaches the manager
     *
     * @param array $obj the view data
     *
     * @return bool false if errors else true
     */
    protected function transformViewObjectToBiz(&$obj)
    {
        $ok = true;

       if (is_array($obj['ITEMS'])) {
            foreach ( $obj['ITEMS'] as &$item ) {
                $item['ACCOUNTLABEL'] = $item[self::$transactionTypeForAcctLabel[$obj['TRANSACTIONTYPE']]] ?? null;
            }
            unset($item);
        }

        return $ok;
    }

    /**
     * This is a hook functions for subclases to adjust the metadata according to the current data & state
     *
     * @param array $obj the data
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        parent::mediateDataAndMetadata($obj);

        if(TaxSetupManager::isVATEnabled()) {
            if (IsRoot() && !TaxSetupManager::isSingleTaxJurisdictionConfigured()) {
                $obj['ISROOT'] = true;

            } else {
                $obj['ISROOT'] = false;
            }
        }

        $view = $this->getView();

        $view->findAndSetProperty(array('path' => 'DETAILID'), array('required' => true));

        // Default the bank account
        // $obj['fromBankReconScrn'] is set in innerProcessCreateAction to retain the gRequest in cases of error go back
        if ($this->state == $this->kShowNewState && ($this->fromBankReconScrn ||
                (isset($obj['fromBankReconScrn']) && $obj['fromBankReconScrn']))) {
            $bankID = Request::$r->_forBankAcct;
            $obj['FINANCIALENTITY'] = $bankID;
        } else if ( empty($obj['FINANCIALENTITY']) ) {
            $obj['FINANCIALENTITY'] = GetMyDefaultFinAcctID(Globals::$g->kCMid);
        }

        // Show hide the correct account label based on the transaction type
        if ( $this->getHasAcctLabel() && !empty($obj['TRANSACTIONTYPE']) ) {
            if ( $obj['TRANSACTIONTYPE'] == 'Service charge' ) {
                $hideColumn = 'INTERESTEARNEDACCOUNTLABEL';
                $showColumn = 'SERVICECHARGEACCOUNTLABEL';
            } else {
                $hideColumn = 'SERVICECHARGEACCOUNTLABEL';
                $showColumn = 'INTERESTEARNEDACCOUNTLABEL';
            }
            $view->findAndSetProperty(
                array('path' => $hideColumn), array('hidden' => true), EditorComponentFactory::TYPE_FIELD
            );
            $view->findAndSetProperty(
                array('path' => $showColumn), array('hidden' => false), EditorComponentFactory::TYPE_FIELD
            );
        }

        if( TaxSetupManager::isVATEnabled() ) {
            if ( $this->state == $this->kShowNewState && empty($obj['TRANSACTIONTYPE']) ) {
                $obj['TRANSACTIONTYPE'] = 'Service charge';
            }
            if ( $obj['TRANSACTIONTYPE'] == 'Service charge' ) {
                if ( $this->state == $this->kShowEditState ) {
                    if ( (!IsRoot() && $this->checkEntityFrstItemDetailId($obj['ITEMS'])) || (IsRoot() && TaxSolutionManager::isVatTaxMethod($obj['TAXMETHOD'])) ) {
                        $view->findAndSetProperty(array('path' => 'TRANSACTIONTYPE'), array('disabled' => 'true'));
                    }
                }
                if ( $this->state == $this->kShowNewState && (!IsRoot() || TaxSetupManager::isSingleTaxJurisdictionConfigured()) ) {
                    // ignore the case if go back when error on add
                    if ( isset($obj['ITEMS']) && is_array($obj['ITEMS']) && empty($obj['ITEMS'][0]['ACCOUNTLABEL']) ) {
                        $obj['INCLUSIVETAX'] = 'true';
                    }
                }
            } else if ( $obj['TRANSACTIONTYPE'] == 'Interest earned' ) {
                if (IsRoot() && TaxSetupManager::isMultiTaxJurisdictionConfigured()) {
                    $view->findAndSetProperty(array('path' => 'TAXIMPLICATIONS'), array('hidden' => 'true'));
                    $view->findAndSetProperty(array('path' => 'TAXSOLUTIONID'), array('hidden' => 'true'));
                } else {
                    $view->findAndSetProperty(array('path' => 'INCLUSIVETAX'), array('hidden' => 'true'));
                    $view->findAndSetProperty(array('id' => 'taxTotalsSection'), array('hidden' => false), EditorComponentFactory::TYPE_SECTION);
                    $view->findAndSetProperty(array('path' => 'TAXSUMMARY'), array('hidden' => true), EditorComponentFactory::TYPE_GRID);
                    $view->findAndSetProperty(array('path' => 'MULTIPLETAXES'), array('hidden' => true));

                    $itemGrid = array();
                    $view->findComponents(array('path' => 'ITEMS'), EditorComponentFactory::TYPE_GRID, $itemGrid);
                    if ($itemGrid[0]) {
                        $matches = [];
                        $itemGrid[0]->findComponents(array('path' => 'DETAILID'), EditorComponentFactory::TYPE_FIELD, $matches);
                        if ($matches) {
                            $matches[0]->setProperty('hidden', true);
                        }
                        $gridFieldsForVATCompany = $this->getIsMCPEnabled() ?
                            array('TAXRATE', 'TRX_TAX', 'TOTALTRXAMOUNT', 'TOTALBASEAMOUNT', 'BASETAXAMOUNT') :
                            array('TAXRATE', 'TRX_TAX', 'TOTALTRXAMOUNT');
                        foreach ($gridFieldsForVATCompany as $gridField) {
                            $matches = [];
                            $itemGrid[0]->findComponents(array('path' => $gridField), EditorComponentFactory::TYPE_FIELD, $matches);
                            if ($matches) {
                                $matches[0]->setProperty('hidden', true);
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * Prepare the transaction data / view before we show it
     *
     * @param array $obj the transaction data
     */
    protected function prepareTransaction(&$obj)
    {
        // Get the view
        $view = $this->getView();
        if ( $this->getIsMCMESubscribed() && !GetContextLocation() ) {
            $properties = [ 'readonly' => true ];
            $view->findAndSetProperty(array('path' => 'CURRENCY'), $properties);
        }
        parent::prepareTransaction($obj);
    }
    /**
     * Build the default line for the specified grid
     *
     * @param EditorGrid $grid the grid that needs the default line
     * @param array      $obj the data
     *
     * @return array the default line
     */
    public function getDefaultLine(EditorGrid $grid, &$obj)
    {
        //Calls base class to get the defaults and then we override below
        $defaultLine = parent::getDefaultLine($grid, $object);
        $forRecon = isset(Request::$r->_forRecon) && Request::$r->_forRecon == 'true';
        if ($forRecon && $this->state == $this->kShowNewState ) {
            // Default to transaction type for description
            $defaultLine['DESCRIPTION'] = $obj['TRANSACTIONTYPE'];
        }
        else
        {
            // Default to service charge for description
            $defaultLine['DESCRIPTION'] = I18N::getSingleToken('IA.SERVICE_CHARGE');
        }

        return $defaultLine;
    }

    /**
     * Figure out the number or rows in the grid
     *
     * @param array $_layout the metadata
     * @param array $obj    the data
     *
     * @return int the default number of rows in the grid
     */
    public function figureOutNumOfRows($_layout, &$obj = null)
    {
        $view = $this->getView();
        if ( $this->state == $this->kShowNewState && !($view->getProperty('copynew')) ) {
            return 1;
        }
        return parent::FigureOutNumOfRows($_layout, $obj);
    }

    /**
     * Returns the javascript call to execute while closing the popup window.
     *
     * @param string $key the record key
     * @param array $obj
     *
     * @return string the JS code
     */
    protected function getPopupCloseJS($key, $obj = [])
    {
        $jsCode = $this->fromBankReconScrn ? $this->getReconciliationJSReload() : '';
        $jsCode .= parent::getPopupCloseJS($key);
        return $jsCode;
    }

    /**
     * @param array $obj
     *
     * @return bool
     */
    protected function shouldCalcTaxSummaryForVat($obj)
    {
        $view = $this->getView();
        // adding duplcate action
        return ( parent :: shouldCalcTaxSummaryForVat($obj) || ($view->getProperty('copynew')) );
    }

    /**
     * If we come from Bank Reconciliation we will  autopopulate some details from bank transaction
     * This function will get all the parameters and modify the data
     *
     * @param array      &    $obj  the data
     * @param EditorGrid|null $grid the grid
     */
    protected function mediateDataAndMetadata_Reconciliation(&$obj, EditorGrid $grid=null)
    {
        // Get the values passed
        $docNo = Request::$r->_docno;
        $txnDate = Request::$r->_txnDate;
        $accountId = Request::$r->_accountId;
        $amount = Request::$r->_amount;
        $bankTxnDescription = Request::$r->_bankTxnDescription;
        $trType = Request::$r->_trType;
        $bankCurrency = Request::$r->_currency;
        $view = $this->getView();
        // useTotaltrxamount indentifies if transaction is vat enabled. if yes then transaction is defaulted to inclusive and in that case assign amount to
        // totaltrxamount instead of trx_amount
        $useTotaltrxamount = ((TaxSetupManager::isVATEnabled() && !IsRoot()) || TaxSetupManager::isSingleTaxJurisdictionConfigured()) && $trType == '-1';

        $obj['FINANCIALENTITY'] = $accountId;

        $obj['WHENCREATED'] = $txnDate;
        if ( $useTotaltrxamount ) {
            $obj['TOTALTRXAMOUNT'] = $amount;
        } else {
            $obj['TRX_AMOUNT'] = $amount;
        }
        $obj['RECORDID'] = $docNo;
        if ( $trType == '-1' ) {
            $obj['TRANSACTIONTYPE'] = 'Service charge';
        }
        else{
            $obj['TRANSACTIONTYPE'] = 'Interest earned';
        }
        //Restrict account. Marking it read-only will result in not triggering UI events that update currency, exchange rate etc.
        $accountField = array();
        $view->findComponents([ 'path' => 'FINANCIALENTITY' ], EditorComponentFactory::TYPE_FIELD, $accountField);
        if ( $accountField ) {
            $type = $accountField[0]->getProperty('type');
            $type['restrict'][] = array(
                'value' => $accountId,
                'pickField' => 'BANKACCOUNTID',
            );
            $accountField[0]->setProperty('type', $type);
        }
        $view->findAndSetProperty(array('path' => 'TRANSACTIONTYPE'), array('readonly' => true));

        $defaultDescription = '';
        $bankFeedType = Request::$r->_bankFeedType;
        if ( isset($bankTxnDescription) && ! empty($bankTxnDescription) ) {
            $defaultDescription = $bankTxnDescription;
        } else if ( isset($bankFeedType) && ! empty($bankFeedType) ) {
            if ( $bankFeedType == ReconciliationUtils::FEEDTYPE_ONLINE ) {
                $defaultDescription = $obj['TRANSACTIONTYPE'] .'  created for bank feed transaction';
            } else {
                $defaultDescription = $obj['TRANSACTIONTYPE'] .' created for uploaded transaction';
            }
            if ( isset($docNo) && $docNo !== '' ) {
                $defaultDescription = $defaultDescription . '--' . $docNo;
            };
        }

        $obj['DESCRIPTION'] = $defaultDescription;
        if ( ! empty($bankCurrency) ) {
            $obj['CURRENCY'] = $bankCurrency;
        }
        $obj['EXCH_RATE_DATE'] = $txnDate;

        $entry = $this->getDefaultLine($grid, $obj);
        $entry['LOCATIONID'] = Request::$r->_location;
        $entry['DEPARTMENTID'] = Request::$r->_department;
        if ( $useTotaltrxamount ) {
            $entry['TOTALTRXAMOUNT'] = $amount;
            $entry['TOTALBASEAMOUNT'] = $amount;
        } else {
            $entry['TRX_AMOUNT'] = $amount;
            $entry['AMOUNT'] = $amount;
        }
        $entry['DESCRIPTION'] = $obj['DESCRIPTION'] ?? $obj['TRANSACTIONTYPE'];

        $accountType = Request::$r->_accountType;
        // get GL account info. from the bank account
        $accountDetails = ReconciliationUtils::getAccountDetails($accountId, $accountType);
        if ( $obj['TRANSACTIONTYPE'] === 'Service charge' ) {
            if ( !empty($accountDetails['SERVICECHARGEACCOUNTLABEL'])) {
                $glAcctInfo = explode("--", $accountDetails['SERVICECHARGEACCOUNTLABEL']);
                $entry['SERVICECHARGEACCOUNTLABEL'] = $glAcctInfo[0];
            }
            else if ( !empty($accountDetails['SERVICECHARGEGLACCOUNT'])) {
                $glAcctInfo = explode("--", $accountDetails['SERVICECHARGEGLACCOUNT']);
                $entry['ACCOUNTNO'] = $glAcctInfo[0];
            }
        } else {
            if ( !empty($accountDetails['INTERESTEARNEDACCOUNTLABEL'])) {
                $glAcctInfo = explode("--", $accountDetails['INTERESTEARNEDACCOUNTLABEL']);
                $entry['INTERESTEARNEDACCOUNTLABEL'] = $glAcctInfo[0];
            }
            else if ( !empty($accountDetails['INTERESTEARNEDGLACCOUNT'])) {
                $glAcctInfo = explode("--", $accountDetails['INTERESTEARNEDGLACCOUNT']);
                $entry['ACCOUNTNO'] = $glAcctInfo[0];
            }
        }

        $obj['ITEMS'][0] = $entry;
    }
    /**
     * @return bool
     */
    protected function includeGLPostingTab()
    {
        return true;
    }

    /**
     * Figure out if the transaction has a UI header or not
     *
     * @return bool true if the transaction has a UI header else false
     */
    protected function hasTxnHeader()
    {
        return true;
    }


    /**
     * Prepare the transaction header with the right value before being view
     *
     * @param array $obj data
     */
    protected function prepareTxnHeader(&$obj)
    {
        parent::prepareTxnHeader($obj);
        //set Transaction type header
        if ($obj['TRANSACTIONTYPE'] == 'Interest earned') {
            $obj['TRANSACTIONTYPEHEADER'] = I18N::getSingleToken('IA.INTEREST_EARNED');
        } else {
            $obj['TRANSACTIONTYPEHEADER'] = I18N::getSingleToken('IA.SERVICE_CHARGE');
        }
    }

    /**
     * This function will return the list of fields to add in the Defaults popup for the grid
     *
     * @param array $_params the metadata
     * @param array $objRec   the ownedobject information
     *
     * @return array the list of fields to add into the popup
     */
    protected function getGridDefaultsFields(&$_params, $objRec)
    {
        $fields = parent::getGridDefaultsFields($_params, $objRec);
        // for single jurisdiction compnay we have can have non tax implication transaction so better remove DETAILID field
        if ( TaxSetupManager::isSingleTaxJurisdictionConfigured() ) {
            $this->removeTaxDetailFromDefaultFields($fields);
        }
        return $fields;
    }

    /**
     * Return the list of globals to have for the editor
     *
     * @return array the list of globals
     */
    protected function getEditorGlobals()
    {
        $vars = parent::getEditorGlobals();
        if ( $vars == null ) {
            $vars = array();
        }
        $vars['IS_TOP_LEVEL'] = !GetContextLocation();
        return $vars;
    }
}
