<?php
/**
 * Entity for the AP Adjustment object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2014 Intacct Corporation All, Rights Reserved
 */

global $gRecordNoFormat, $gDecimalFormat, $gDateFormat, $gContactNameFormat;
global $gBooleanType, $gDateType;
global $gModifiedByFieldInfo, $gCreatedByFieldInfo, $gAUWhenCreatedFieldInfo, $gWhenModifiedFieldInfo;

$kSchemas['apadjustment'] = array(
    'children' => array(
        'vendor' => array(
            'fkey' => 'vendorkey', 'invfkey' => 'record#', 'table' => 'vendormst',
            'children' => array(
                'vendortype' => array('fkey' => 'vendtypekey', 'table' => 'vendtype', 'join' => 'outer'),
            )
        ),
        'payto' => array(
            'fkey' => 'billtopaytokey', 'invfkey' => 'record#', 'table' => 'contactversion', 'join' => 'outer',
            'children' => array(
                'taxgroup' => array(
                    'fkey' => 'taxgroupkey', 'invfkey' => 'record#', 'table' => 'taxgrp', 'join' => 'outer',
                ),
            ),
        ),
        'returnto' => array(
            'fkey' => 'shiptoreturntokey', 'invfkey' => 'record#', 'table' => 'contactversion', 'join' => 'outer'
        ),
        'batch' => array(
            'fkey' => 'prbatchkey', 'invfkey' => 'record#', 'table' => 'prbatchmst', 'join' => 'outer'
        ),
        'user' => array(
            'fkey' => 'userkey', 'invfkey' => 'record#', 'table' => 'userinfo', 'join' => 'outer'
        ),
        'currencies' => array(
            'fkey' => 'currency', 'invfkey' => 'code', 'table' => 'companycurrencies', 'join' => 'outer'
        ),
        'exchangerateinfo' => array(
            'fkey' => 'record#', 'invfkey' => 'recordkey', 'table' => 'exchangerateinfo', 'join' => 'outer'
        ),
        'supdocmaps' => array(
            'fkey' => 'record#',
            'invfkey' => 'recordid',
            'fkeyconvto' => 'char',
            'join' => 'outer',
            'table' => 'supdocmaps',
            'filter' => " supdocmaps.transactiontype(+) = 'APADJUSTMENT' ",
            'children' => array(
                'supdoc' => array(
                    'fkey' => 'documentid',
                    'invfkey' => 'record#',
                    'table' => 'supdoc',
                    'join' => 'outer'
                )
            )
        ),
        'taxsolution' =>    array (
            'fkey' => 'taxsolutionkey',
            'invfkey'    => 'record#',
            'table' => 'taxsolution',
            'join' => 'outer',
        ),
    ),
    'nexus' => array(
        'vendor' => array(
            'object' => 'vendor',
            'relation' => MANY2ONE,
            'field' => 'vendorid',
        ),
        'payto' => array(
            'object' => 'contactversion',
            'relation' => MANY2ONE,
            'field' => 'billtopaytokey',
            'printas' => 'IA.PAY_TO_CONTACT',
        ),
        'returnto' => array(
            'object' => 'contactversion',
            'relation' => MANY2ONE,
            'field' => 'shiptoreturntokey',
            'printas' => 'IA.RETURN_TO_CONTACT',
        ),
        'batch' => array(
            'object' => 'prbatch',
            'relation' => ONE2MANY,
            'field' => 'prbatchkey',
        ),
        'userinfo' => array(
            'object' => 'userinfo',
            'relation' => ONE2MANY,
            'field' => 'userkey',
            'printas' => 'IA.LAST_MODIFIED_USER',
            'dbalias' => 'user',
        ),
        'trxcurrencies' => array(
            'object' => 'trxcurrencies',
            'relation' => ONE2MANY,
            'field' => 'currency',
            'printas' => 'IA.TRANSACTION_CURRENCIES',
            'dbalias' => 'currencies',
        ),
        'exchangerateinfo' => array(
            'object' => 'exchangerateinfo',
            'relation' => MANY2ONE,
            'field' => 'RECORDNO',
        ),
        'taxsolution' => array(
            'object' => 'taxsolution',
            'relation' => 'MANY2ONE',
            'field' => 'taxsolutionkey',
        ),
    ),
    'object' => array(
        'RECORDNO',
        'RECORDTYPE',
        'RECORDID',
        'CONTACTTAXGROUP',
        'STATE',
        'RAWSTATE',
        'ENTITY',
        'VENDORID',
        'VENDORNAME',
        'FORM1099TYPE',
        'FORM1099BOX',
        'VENDTYPE1099TYPE',
        'TRX_ENTITYDUE',
        'DOCNUMBER',
        'DESCRIPTION',
        'WHENCREATED',
        'WHENPOSTED',
        'WHENPAID',
        'BASECURR',
        'CURRENCY',
        'EXCH_RATE_DATE',
        'EXCH_RATE_TYPE_ID',
        'EXCHANGE_RATE',
        'TOTALENTERED',
        'TOTALSELECTED',
        'TOTALPAID',
        'TOTALDUE',
        'TRX_TOTALENTERED',
        'TRX_TOTALSELECTED',
        'TRX_TOTALPAID',
        'TRX_TOTALDUE',
        'BILLTOPAYTOCONTACTNAME',
        'SHIPTORETURNTOCONTACTNAME',
        'BILLTOPAYTOKEY',
        'SHIPTORETURNTOKEY',
        'PRBATCH',
        'PRBATCHKEY',
        'PRBATCH_OPEN',
        'PRBATCH_NOGL',
        'MODULEKEY',
        'AUWHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'INCLUSIVETAX',
        'TAXSOLUTIONKEY',
        'TAXSOLUTIONID',
        'SHOWMULTILINETAX',
        'TAXMETHOD',
        'SUPDOCID',
        'PAYTO.TAXGROUP.NAME',
        'PAYTO.TAXGROUP.RECORDNO',
        'PAYTO.TAXID',
        'VENDORKEY'
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'RECORDTYPE' => 'recordtype',
        'RECORDID' => 'recordid',
        'CONTACTTAXGROUP' => 'taxgroup.name',
        'STATE' => 'state',
        'RAWSTATE' => 'state',
        'ENTITY' => 'entity',
        'VENDORID' => 'vendor.vendorid',
        'VENDORNAME' => 'vendor.name',
        'FORM1099TYPE' => 'vendor.form1099type',
        'FORM1099BOX' => 'vendor.form1099box',
        'VENDTYPE1099TYPE' => 'vendortype.form1099type',
        'TRX_ENTITYDUE' => 'vendor.totaldue',
        'DOCNUMBER' => 'docnumber',
        'DESCRIPTION' => 'description',
        'WHENCREATED' => 'whencreated',
        'WHENPOSTED' => 'batch.created',
        'WHENPAID' => 'whenpaid',
        'BASECURR' => 'basecurr',
        'CURRENCY' => 'currency',
        'EXCH_RATE_DATE' => 'exchangerateinfo.exch_rate_date',
        'EXCH_RATE_TYPE_ID' => 'exchangerateinfo.exch_rate_type_id',
        'EXCHANGE_RATE' => 'exchangerateinfo.exchange_rate',
        'TOTALENTERED' => 'totalentered',
        'TOTALSELECTED' => 'totalselected',
        'TOTALPAID' => 'totalpaid',
        'TOTALDUE' => 'totaldue',
        'TRX_TOTALENTERED' => 'trx_totalentered',
        'TRX_TOTALSELECTED' => 'trx_totalselected',
        'TRX_TOTALPAID' => 'trx_totalpaid',
        'TRX_TOTALDUE' => 'trx_totaldue',
        'BILLTOPAYTOCONTACTNAME' => 'payto.name',
        'SHIPTORETURNTOCONTACTNAME' => 'returnto.name',
        'BILLTOPAYTOKEY' => 'billtopaytokey',
        'SHIPTORETURNTOKEY' => 'shiptoreturntokey',
        'PRBATCH' => 'batch.title',
        'PRBATCHKEY' => 'prbatchkey',
        'PRBATCH_OPEN' => 'batch.open',
        'PRBATCH_NOGL' => 'batch.nogl',
        'MODULEKEY' => 'modulekey',
        'LOCATIONKEY' => 'locationkey',
        'WHENMODIFIED' => 'whenmodified',
        'AUWHENCREATED' => 'auwhencreated',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
        'SUPDOCID' => 'supdoc.documentid',
        'PAYTO' => array(
            'contactversion.*' => 'payto.*',
            'TAXGROUP' => array(
                'taxgroup.*' => 'taxgroup.*',
            ),
        ),
        'INCLUSIVETAX'      => 'inclusivetax',
        'TAXSOLUTIONKEY' => 'taxsolutionkey',
        'TAXSOLUTIONID' => 'taxsolution.solutionid',
        'SHOWMULTILINETAX' => 'taxsolution.showmultilinetax',
        'TAXMETHOD' => 'taxsolution.taxmethod',
        'VENDORKEY' => 'vendorkey',
        'SI_UUID'   => 'si_uuid',
    ),
    'publish' => array(
        'RECORDNO',
        'RECORDID',
        'STATE',
        'VENDORID',
        'VENDORNAME',
        'DOCNUMBER',
        'DESCRIPTION',
        'WHENCREATED',
        'WHENPOSTED',
        'WHENPAID',
        'BASECURR',
        'CURRENCY',
        'EXCH_RATE_DATE',
        'EXCH_RATE_TYPE_ID',
        'EXCHANGE_RATE',
        'TOTALENTERED',
        'TOTALPAID',
        'TOTALSELECTED',
        'TOTALDUE',
        'TRX_TOTALENTERED',
        'TRX_TOTALSELECTED',
        'TRX_TOTALPAID',
        'TRX_TOTALDUE',
        'BILLTOPAYTOCONTACTNAME',
        'SHIPTORETURNTOCONTACTNAME',
        'PRBATCH',
        'AUWHENCREATED',
        'WHENMODIFIED',
        'INCLUSIVETAX',
        'TAXSOLUTIONID',
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'AUWHENCREATED',
        'WHENMODIFIED'
    ),
    'ownedobjects' => array(
        array(
            'fkey' => 'RECORDKEY', // the field that the owned object point to
            'invfkey' => 'RECORDNO',
            'minLinesRequired' => 1,
            'entity' => 'apadjustmentitem', // to the parent vid
            'path' => 'ITEMS'
        )
    ),
    'fieldinfo' => array(
        array(
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD_NUMBER',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'hidden' => true,
            'readonly' => true,
            'id' => 1
        ),
        array(
            'path' => 'RECORDTYPE',
            'fullname' => 'IA.RECORD_TYPE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 2
            ),
            'hidden' => true,
            'id' => 30
        ),
        array(
            'path' => 'RECORDID',
            'fullname' => 'IA.ADJUSTMENT_NUMBER',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 45
            ),
            'partialedit' => true,
            'id' => 102
        ),
        array(
            'path' => 'STATE',
            'fullname' => 'IA.STATE',
            'type' => array (
                'ptype' => 'enum',
                'type' => 'text',
                'validlabels' => array('IA.REVERSED', 'IA.REVERSAL', 'IA.DRAFT', 'IA.NO_VALUE', 'IA.POSTED', 'IA.PAID', 'IA.PARTIALLY_PAID', 'IA.SELECTED'),
                'validvalues' => array('Reversed', 'Reversal', 'Draft', 'No Value', 'Posted', 'Paid', 'Partially Paid', 'Selected'),
            ),
            'formula' => array(
                'typeOf' => 'DESCRIPTION',
                'fields' => array(
                    'RAWSTATE',
                    'TRX_TOTALENTERED',
                    'TRX_TOTALDUE',
                    'TRX_TOTALPAID',
                    'TRX_TOTALSELECTED',
                    'CNY#',
                    'RECORDNO'
                ),
                // do not change the sequence in below conditions
                'function' => "case when \${1} = 'V' then
                        case when (
                            select count(*) from prentrypymtrecs prepr
                            where prepr.cny# = \${6} and prepr.recordkey = \${7}
                        ) > 0 then'Reversed' else 'Reversal'
                        end
                        when \${1} = 'D' then 'Draft'
                        when \${2} = 0 then 'No Value'
                        when \${4} = 0 and \${5} = 0 then 'Posted'
                        when \${3} = 0 then 'Paid'
                        when \${4} != 0 then 'Partially Paid'
                        when \${5} != 0 then 'Selected'
                        else \${1} end",
            ),
            'rpdFormula' => [
                'fields' => [
                    'RAWSTATE',
                    'TRX_TOTALENTERED',
                    'TRX_TOTALDUE',
                    'TRX_TOTALPAID',
                    'TRX_TOTALSELECTED',
                ],
                // do not change the sequence in below conditions
                'function' => "case when \${1} = 'V' then
                        'IA.REVERSED_OR_REVERSAL'
                        when \${1} = 'D' then 'IA.DRAFT'
                        when \${2} = 0 then 'IA.NO_VALUE'
                        when \${4} = 0 and \${5} = 0 then 'IA.POSTED'
                        when \${3} = 0 then 'IA.PAID'
                        when \${4} != 0 then 'IA.PARTIALLY_PAID'
                        when \${5} != 0 then 'IA.SELECTED'
                        else \${1} end",
                'tokenMap' => [ // Do not change the tokanMap entries unless you clear this up with someone who knows the RPD build process
                                // These values are used in the current ICRW reports' results, filters and prompts
                    'IA.REVERSED_OR_REVERSAL' => 'Reversed or Reversal',
                    'IA.DRAFT' => 'Draft',
                    'IA.NO_VALUE' => 'No Value',
                    'IA.POSTED' => 'Posted',
                    'IA.PAID' => 'Paid',
                    'IA.PARTIALLY_PAID' => 'Partially Paid',
                    'IA.SELECTED' => 'Selected',
                ],
            ],
            'calculated' => true,
            'readonly' => true,
            'id' => 121
        ),
        array(
            'path' => 'RAWSTATE',
            'fullname' => 'IA.RAW_STATE',
            'hidden' => true,
            'readonly' => true,
            'partialedit' => true,
            "disableReportSelection" => true, // can't be used in reports
            'id' => 55
        ),
        array(
            'path' => 'ENTITY',
            'fullname' => 'IA.VENDOR_ENTITY',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 21
            ),
            'required' => true,
            'hidden' => true,
            'readonly' => true,
            'renameable' => true,
            'id' => 49
        ),
        array(
            'path' => 'VENDORID',
            'fullname' => 'IA.VENDOR_ID',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'vendor',
                'pickentity' => 'vendorpick',
                'pickfield' => array(
                    'PICKID', 'ACCOUNTLABEL', 'APACCOUNT', 'APACCOUNTTITLE', 'CURRENCY', 'DISPLAYCONTACT.CONTACTNAME',
                    'PAYTO.CONTACTNAME', 'RETURNTO.CONTACTNAME', 'ONHOLD', 'CREDITLIMIT', 'TOTALDUE', 'OFFSETGLACCOUNTNO',
                    'VENDTYPE1099TYPE', 'FORM1099TYPE', 'FORM1099BOX', 'DISPLAYCONTACT.TAXGROUP', 'DISPLAYCONTACT.TAXID', 'PAYTO.TAXGROUP', 'PAYTO.TAXID',
                )
            ),
            'required' => true,
            'isDimension' => true,
            'autofillrelated' => false,
            'renameable' => true,
            'id' => 101
        ),
        array(
            'path' => 'VENDORNAME',
            'fullname' => 'IA.VENDOR_NAME',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 100,
                'format' => '/^.{0,100}$/'
            ),
            'renameable' => true,
            'id' => 47
        ),
        array(
            'path' => 'TRX_ENTITYDUE',
            'fullname' => 'IA.VENDOR_DUE',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'renameable' => true,
            'readonly' => true,
            'id' => 174
        ),
        array(
            'path' => 'DOCNUMBER',
            'fullname' => 'IA.REFERENCE_BILL_NUMBER',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'text',
                'noSplit' => true,
                'entity' => 'apbill'
            ),
            'nonew'    => true,
            'noedit' => true,
            'noview' => true,
            'partialedit' => true,
            'id' => 103
        ),
        array(
            'path' => 'DESCRIPTION',
            'fullname' => 'IA.DESCRIPTION',
            'type' => array(
                'type' => 'text',
                'ptype' => 'textarea',
                'maxlength' => 1000
            ),
            'partialedit' => true,
            'id' => 7
        ),
        array(
            'path' => 'WHENCREATED',
            'fullname' => 'IA.DATE',
            'type' => $gDateType,
            'required' => true,
            'id' => 2
        ),
        array(
            'path' => 'WHENPOSTED',
            'fullname' => 'IA.GL_POSTING_DATE',
            'type' => $gDateType,
            'hidden' => true,
            'id' => 3
        ),
        array(
            'path' => 'WHENPAID',
            'fullname' => 'IA.DATE_FULLY_PAID',
            'type' => $gDateType,
            'id' => 16
        ),
        array(
            'path' => 'BASECURR',
            'fullname' => 'IA.BASE_CURRENCY',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'trxcurrencies'
            ),
            'required' => true,
            'hidden' => true,
            'noedit' => true,
            'nonew' => true,
            'noview' => true,
            'id' => 116,
        ),
        array(
            'path' => 'CURRENCY',
            'fullname' => 'IA.TRANSACTION_CURRENCY',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'trxcurrencies'
            ),
            'required' => true,
            'hidden' => true,
            'noedit' => true,
            'nonew' => true,
            'noview' => true,
            'id' => 117,
        ),
        array(
            'path' => 'EXCH_RATE_DATE',
            'fullname' => 'IA.EXCHANGE_RATE_DATE',
            'type' => array(
                'type' => 'date',
                'ptype' => 'date',
                'format' => $gDateFormat
            ),
            'hidden' => true,
            'id' => 118
        ),
        // THIS IS DEPRECATED AND ONLY USED TO FORMAT THE VALUE FOR 3.0 FORMER VALUE
        // -- START
        array(
            'path' => 'EXCHRATEDATE',
            'fullname' => 'IA.EXCHANGE_RATE_DATE',
            'type' => array(
                'type' => 'date',
                'ptype' => 'date',
                'format' => $gDateFormat
            ),
            'hidden' => true
        ),
        // -- END
        array(
            'path' => 'EXCH_RATE_TYPE_ID',
            'fullname' => 'IA.EXCHANGE_RATE_TYPE',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'ptr',
                'entity' => 'exchangeratetypesall'
            ),
            'hidden' => true,
            'id' => 119
        ),
        array(
            'path' => 'EXCHANGE_RATE',
            'fullname' => 'IA.EXCHANGE_RATE',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'maxlength' => 12
            ),
            'precision' => 12,
            'hidden' => true,
            'noformat' => true,
            'rpdMeasure' => false,
            'id' => 120
        ),
        array(
            'path' => 'TOTALENTERED',
            'fullname' => 'IA.TOTAL_AMOUNT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'readonly' => true,
            'id' => 105
        ),
        array(
            'path' => 'TOTALSELECTED',
            'fullname' => 'IA.TOTAL_SELECTED',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'readonly' => true,
            'id' => 31
        ),
        array(
            'path' => 'TOTALPAID',
            'fullname' => 'IA.TOTAL_PAID',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'readonly' => true,
            'id' => 13
        ),
        array(
            'path' => 'TOTALDUE',
            'fullname' => 'IA.TOTAL_DUE',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'decimal',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
            ),
            'readonly' => true,
            'id' => 12
        ),
        array(
            'path' => 'TRX_TOTALENTERED',
            'fullname' => 'IA.TOTAL_TRANSACTION_AMOUNT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'readonly' => true,
            'id' => 106
        ),
        array(
            'path' => 'TRX_TOTALSELECTED',
            'fullname' => 'IA.TOTAL_TRANSACTION_AMOUNT_SELECTED',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'readonly' => true,
            'id' => 40
        ),
        array(
            'path' => 'TRX_TOTALPAID',
            'fullname' => 'IA.TOTAL_TRANSACTION_AMOUNT_PAID',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'readonly' => true,
            'id' => 107
        ),
        array(
            'path' => 'TRX_TOTALDUE',
            'fullname' => 'IA.TOTAL_TRANSACTION_AMOUNT_DUE',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'readonly' => true,
            'id' => 108
        ),
        array(
            'path' => 'BILLTOPAYTOCONTACTNAME',
            'fullname' => 'IA.PAY_TO',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'noSplit' => true,
                'entity' => 'contact',
                'format' => $gContactNameFormat,
                'pickfield' => array(
                    'CONTACTNAME', 'MAILADDRESS.ADDRESS1', 'MAILADDRESS.ADDRESS2',
                    'MAILADDRESS.CITY', 'MAILADDRESS.STATE', 'MAILADDRESS.ZIP', 'EMAIL1', 'TAXID', 'TAXGROUP'
                )
            ),
            'id' => 109
        ),
        array(
            'path' => 'SHIPTORETURNTOCONTACTNAME',
            'fullname' => 'IA.RETURN_TO',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'text',
                'noSplit' => true,
                'entity' => 'contact',
                'format' => $gContactNameFormat,
                'pickfield' => array(
                    'CONTACTNAME', 'MAILADDRESS.ADDRESS1', 'MAILADDRESS.ADDRESS2',
                    'MAILADDRESS.CITY', 'MAILADDRESS.STATE', 'MAILADDRESS.ZIP', 'EMAIL1'
                )
            ),
            'id' => 110
        ),
        array(
            'path' => 'BILLTOPAYTOKEY',
            'fullname' => 'IA.PAYTO_CONTACT_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'readonly' => true,
            'hidden' => true,
            'id' => 112
        ),
        array(
            'path' => 'SHIPTORETURNTOKEY',
            'fullname' => 'IA.RETURNTO_CONTACT_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'readonly' => true,
            'hidden' => true,
            'id' => 114
        ),
        array(
            'path' => 'PRBATCH',
            'fullname' => 'IA.SUMMARY',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'apadjustmentbatch',
                'maxlength' => 100
            ),
            'id' => 104
        ),
        array(
            'path' => 'PRBATCHKEY',
            'fullname' => 'IA.SUMMARY_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'readonly' => true,
            'hidden' => true,
            'id' => 29
        ),
        array (
            'path' => 'PRBATCH_OPEN',
            'fullname' => 'IA.OPEN_CLOSE_SUMMARY',
            'type' => array (
                'ptype' => 'enum',
                'type' => 'text',
                'validvalues' => array ('Open', 'Closed'),
                '_validivalues' => array ('T', 'F'),
                'validlabels' => array('IA.OPEN', 'IA.CLOSED')
            ),
            'readonly' => true,
            'id' => 190
        ),
        array (
             'path' => 'PRBATCH_NOGL',
            'fullname' => 'IA.NOT_FOR_GL_POSTING',
            'type' => $gBooleanType,
            'readonly' => true,
            'id' => 191
        ),
        array(
            'path' => 'MODULEKEY',
            'fullname' => 'IA.MODULE_KEY',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20
            ),
            'readonly' => true,
            'hidden' => true,
            'id' => 19
        ),
        array(
            'path' => 'LOCATIONKEY',
            'fullname' => 'IA.LOCATION_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'derived' => true,
            'readonly' => true,
            'hidden' => true,
            'id' => 56
        ),
        // -- START UI OLY FIELDS
        array(
            'path' => 'ADJTYPE',
            'fullname' => 'IA.TYPE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'enum',
                'validlabels' => array('IA.DEBIT_MEMO', 'IA.CREDIT_MEMO'),
                'validvalues' => array('debit memo', 'credit memo')
            ),
            'default' => 'debit memo'
        ),
        array(
            'path' => 'SUPDOCID',
            'fullname' => 'IA.ATTACHMENT',
            'type' => array(
                'ptype' => 'supdocptr',
                'type' => 'supdocptr',
                'maxlength' => 20,
                'listAction' => 'pick'
            ),
            'noedit' => false,
            'partialedit' => true,
            'id' => 57
        ),
        array(
            'path' => 'WHENCREATEDHEADER',
            'fullname' => 'IA.TRANSACTION_DATE',
            'type' => $gDateType,
            'readonly' => true
        ),
        array(
            'path' => 'BILLTOPAYTOADDRESS',
            'type' => array(
                'type' => 'multitext',
                'ptype' => 'multitext'
            ),
            'readonly' => true
        ),
        array(
            'path' => 'SHIPTORETURNTOADDRESS',
            'type' => array(
                'ptype' => 'multitext',
                'type' => 'multitext'
            ),
            'readonly' => true
        ),
        // -- END OF UI FIELD
        $gAUWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
        array(
            'fullname' => 'IA.FORM_1099_TYPE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'path' => 'FORM1099TYPE',
            'id' => 58
        ),
        array(
            'fullname' => 'IA.FORM_1099_BOX',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'path' => 'FORM1099BOX',
            'id' => 59
        ),
        array(
            'fullname' => 'IA.VENDOR_TYPE_FORM_1099_TYPE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text'
            ),
            'path' => 'VENDTYPE1099TYPE',
            'id' => 60
        ),
        array(
            'path'      => 'CONTACTTAXGROUP',
            'fullname'  => 'IA.CONTACT_TAX_GROUP',
            'desc'      => 'IA.THE_VENDOR_PAYTO_CONTACT_TAX_GROUP',
            'type'      => array(
                'type'  => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'taxgroup',
            ),
            'readonly'  => true,
            'id'        => 193, // must have an unique ID to show up in the INSPECT API
        ),
        array(
            'path' => 'TAXID',
            'fullname' => 'IA.TAX_ID',
            'desc' => 'IA.TAX_IDENTIFICATION_NUMBER',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 20,
                'format' => '/^.{0,20}$/'
            ),
            'readonly'    => true,
            'showInGroup' => true,
            'id' => 195
        ),
        array(
            'path' => 'INCLUSIVETAX',
            'fullname' => 'IA.INCLUSIVE_TAXES',
            'type' => $gBooleanType,
            'default' => 'false',
            'id' => 196
        ),
        array(
            'fullname' => 'IA.TAX_SOLUTION',
            'desc' => 'IA.TAX_SOLUTION',
            'path' => 'TAXSOLUTIONID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'taxsolution',
                'pickfield' => array('SOLUTIONID', 'SHOWMULTILINETAX', 'TAXMETHOD', 'RECORDNO'),
                'restrict' => array(
                    array(
                        'pickField' => 'TAXMETHOD',
                        'operand' => 'IN',
                        'value' => APAdjustmentManager::getTaxImplicationTaxMethodsHelper(false),
                    ),
                )
            ),
            'nonew' => true,
            'id' => 196,
        ),
        array(
            'fullname' => 'IA.TRANSACTION_HAS_TAX_IMPLICATIONS',
            'desc' => 'IA.TRANSACTION_HAS_TAX_IMPLICATIONS',
            'path' => 'TAXIMPLICATIONS',
            'type' => $gBooleanType,
            'default' => 'false',
        ),
        $gSiUuidFieldInfo,
    ),
    'printas' => 'IA.AP_ADJUSTMENT',
    'pluralprintas' => 'IA.AP_ADJUSTMENTS',
    'sicollaboration' => true,
    'table' => 'prrecord',
    'updatetable' => 'prrecordmst',
    'lite_table' => 'prrecordlite',
    'module' => 'ap',
    'autoincrement' => 'RECORDNO',
    'vid' => 'RECORDNO',
    'supdocentity' => 'APADJUSTMENT',
    'auditcolumns' => true,
    'primaryDimensions' => array('VENDORID' => 'VENDORID'),
    'primaryDimensionKey' => ['vendor' => ['VENDORKEY' => 'VENDORID']],
    'pairedFields' => array(
        'VENDORID' => 'VENDORNAME'
    ),
    'upsertEntries' => true,
    'customerp' => array(
        'SLTypes' => array(
            CUSTOMERP_SMARTLINKCLICK,
            CUSTOMERP_SMARTLINKFETCH,
            CUSTOMERP_SMARTLINKVALIDATE,
            CUSTOMERP_SMARTLINKWORKFLOW
        ),
        'SLEvents' => array(
            CUSTOMERP_EVENT_ADD,
            CUSTOMERP_EVENT_SET,
            CUSTOMERP_EVENT_ADDSET,
            CUSTOMERP_EVENT_DELETE,
            CUSTOMERP_EVENT_CLICK
        ),
        'AllowCF' => true
    ),
    'api' => array(
        'GET_BY_GET' => true,
        'ITEMS_ALIAS' => ['APADJUSTMENTITEMS'],
        'ITEM_ALIAS' => ['APADJUSTMENTITEM'],
        'ITEMS_INTERNAL' => ['ITEMS'],
        'CANT_UPDATE_FIELDS' => ['TAXSOLUTIONID'],
        'LESS_GET_FIELDS' => array(
            'ENTITY',
            'PRBATCH_OPEN',
            'PRBATCH_NOGL'
        ),
        'DEPRECATED_FIELDS' => array(
            'EXCH_RATE_DATE' => 'EXCHRATEDATE',
            'EXCH_RATE_TYPE_ID' => 'EXCHRATETYPE',
            'EXCHANGE_RATE' => 'EXCHRATE',
            'BILLTOPAYTOCONTACTNAME' => 'PAYTOCONTACTNAME',
            'SHIPTORETURNTOCONTACTNAME' => 'RETURNTOCONTACTNAME'
        ),
    ),
    'dbfilters' => array(
        array(
            'apadjustment.recordtype', '=', SubLedgerTxnManager::APADJUSTMENT_RECTYPE
        )
    ),
    'standardWhere'                   => "recordtype = '" . SubLedgerTxnManager::APADJUSTMENT_RECTYPE . "'",
    'allowDDS'                        => false,
    SearchTable::GSALLOWED            => true,
    SearchTable::GSCOLUMNS            => array( 'VENDORID', 'VENDORNAME', 'RECORDID', 'WHENCREATED', 'TOTALENTERED', 'DESCRIPTION', 'STATE' ),
    SearchTable::GSANYFIELD           => 'RECORDID',
    SearchTable::GSDEFAULTFILTERFIELD => 'DESCRIPTION',
    SearchTable::GSMULTIENTITYCLAUSE  => PRRECORD_ME_CLAUSE,
    'description' => 'IA.HEADER_INFORMATION_FOR_AP_ADJUSTMENT_TRANSACTIONS',
    'nameFields' => [ 'RECORDID', 'VENDORID', 'ADJTYPE' ],
    'fastUpdate'  => true,
);
require 'taxsummary.ent';
$kSchemas['apadjustment'] = EntityManager::inheritEnts($kSchemas['taxsummary'], $kSchemas['apadjustment']);

