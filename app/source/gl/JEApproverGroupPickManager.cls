<?
/**
*   FILE: JEApproverGroupPickManager.cls
*   AUTHOR: <PERSON>
*   DESCRIPTION:
*
*   (C) 2000, Intacct Corporation, All Rights Reserved
*
*   This document contains trade secret data that belongs to Intacct
*   Corporation and is protected by the copyright laws.  Information
*   herein may not be used, copied or disclosed in whole or in part
*   without prior written consent from Intacct Corporation.
*/

import('ApprovalManager');

class JEApproverGroupPickManager extends ApproverPickManager
{
    /**
     * @param array $params
     */
    function __construct($params = array())
    {
        $params['multiEntityAware'] = true;
        parent::__construct($params);
    }

    /**
     * @return array
     */
    public static function GetSystemApprovers()
    {
        $approvers = array();
        $approvers[] = array ( 'PICKID' => APPTYPE_EMPLMNGR_LEVEL );

        return $approvers;
    }

    /**
     * @return string
     */
    protected function GetApproveOperationName()
    {
        return 'Approve Journal Entries';
    }

    /**
     * @return string
     */
    protected function GetApproveValueLevelOperationName()
    {
        return 'Expense Approval Levels';
    }

    /**
     * @return string
     */
    protected function GetApproveOperationModule()
    {
        return 'gl';
    }

    /**
     * @return string
     */
    protected function GetApproveOperationVerb()
    {
        return '%add%';
    }

    /**
     * @return string
     */
    protected function GetApproveOperationKey()
    {
        return 'gl/activities/approveglbatch';
    }

    /**
     * return a list of authorized approvers.  Overridden @see ApproverPickManager::getAuthorizedApprovalUsers so we
     * can handle je approval requirements of business/admin users
     * @param string $locationKey
     * @param array $userList
     *
     * @return array
     */
    function getAuthorizedApprovalUsers($locationKey = null, array $userList = [])
    {

        $users = parent::getAuthorizedApprovalUsers($locationKey, $userList);
        foreach ( $users as $key => $user ) {
            if ( $user['TYPE'] != 'B' ) {
                unset($users[$key]);
            }
        }

        return $users;

    }

    /**
     * Get JE Approval Picker with UserGroup
     *
     * @param array $params
     * @param bool $_crosscny
     * @param bool  $nocount
     *
     * @return array|array[]|mixed
     */
    function GetList($params = [], $_crosscny = false, $nocount = true)
    {
        $approvers = parent::GetList($params, $_crosscny, $nocount);

        //Collect User Groups to be listed in the picker
        $res = $this->getAuthorizedApprovalUsers();
        $groupApprovers = $this->getApproverGroup($res);

        foreach ( $groupApprovers as $groupApprover) {
            $pickid = $groupApprover['NAME'];
            $pickid .= "--UserGroup";
            $approvers[]= array ( 'PICKID' => $pickid );
        }

        return $approvers;
    }

    /**
     * Get List of User Groups
     *
     * @param array $approvers
     *
     * @return array|false|string[][]
     */
    public static function getApproverGroup($approvers)
    {
        $approversId = [];
        $approversGroup = [];
        if($approvers) {
            foreach ($approvers as $approver) {
                $approversId[] = $approver['RECORD#'];
            }
        }

        if($approversId) {
            $companyId = GetMyCompany();

            $query = "SELECT ugroup.record#, ugroup.name, LISTAGG(memberugroup.u_o_gkey, ',') WITHIN".
                " GROUP (ORDER BY memberugroup.u_o_gkey) AS groupmembers, ugroup.descr FROM ugroup".
                " LEFT JOIN memberugroup ON memberugroup.parentgroup = ugroup.record# AND memberugroup.cny# = ugroup.cny#".
                " WHERE ugroup.cny# = :1 AND memberugroup.U_O_GKEY IN(".implode(',',$approversId).")".
                " GROUP BY ugroup.record#, ugroup.name, ugroup.descr ORDER BY ugroup.name";

            $approversGroup = QueryResult([$query, $companyId]);
        }

        return $approversGroup;
    }

}

