key: contracts/mea-price-list-entry::systemContracts/mea-price-list-entryFW2
id: systemContracts/mea-price-list-entryFW2
object: contracts/mea-price-list-entry
name: IA.RECENTLY_MODIFIED
description: Specifies all active contracts/mea-price-list-entrys
query:
  object: contracts/mea-price-list-entry
  fields:
    - meaPriceList.id
    - item.name
    - currency.txnCurrency
    - priceType
    - usePriceRange
    - priceRangeVarianceType
    - priceRuleOutsideRange
    - audit.modifiedDateTime
    - audit.modifiedBy
  orderBy:
    - audit.modifiedDateTime: desc
  filters:
    - $eq:
        status: active



contexts:
  - __default