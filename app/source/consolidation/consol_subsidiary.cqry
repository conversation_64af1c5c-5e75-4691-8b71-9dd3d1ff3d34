<?
/**
 *    FILE:
 *    AUTHOR: <PERSON>
 *    DESCRIPTION:
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

global $kconsol_subsidiaryQueries;

$kconsol_subsidiaryQueries = array(
    'QRY_CNS_BDGTDATA' => array(
        'QUERY' => "Select glbudget.account#, baseaccount.acct_no, glbudget.location#, glbudget.dept#, 	glbudget.amount, baseaccount.statistical, baseaccount.account_type 
							From budgetheader budgetheader, glbudget glbudget, baseaccount baseaccount
							Where budgetheader.record# = glbudget.budgetkey and budgetheader.cny# = glbudget.cny# and budgetheader.default_budget = 'T' and glbudget.bud_type# =? and glbudget.cny# =? and baseaccount.cny# = glbudget.cny# and baseaccount.record# = glbudget.account#",
        'ARGTYPES' => array('integer' ,'integer'),
    ),
    
    'QRY_CNSLOCATION_SELECT_VID_FROM_KEY' => array(
        'QUERY' => 'select LOCATION_NO from LOCATION where CNY# =?  and RECORD# =?',
        'ARGTYPES' => array('integer' ,'integer' ),
    ),
    'QRY_SUBPERIOD_SELECT' => array(
        'QUERY' => 'select record#, start_date, end_date, header1, header2 from glbudgettype where record# =? and cny# =?',
        'ARGTYPES' => array('integer', 'integer'),
    ),
    'QRY_SUB_ACCOUNTS' => array(
        'QUERY' => 'Select record# as SUBACCOUNTKEY, acct_no as SUBACCOUNTNO, title as SUBTITLE, normal_balance, account_type
							From glaccount
							Where cny# =?',
        'ARGTYPES' => array('integer'),
    ),
    'QRY_SUB_DEPARTMENTS' => array(
        'QUERY' => 'Select c.record# as SUBDEPTKEY, c.dept_no as SUBDEPTNO, c.title as SUBTITLE, p.dept_no as SUBPARENTNO, p.title as SUBPARENTTITLE
							From department c, department p
							Where c.cny# =? and p.cny# = c.cny# (+) and p.record# = c.parent# (+) 
							UNION
							Select record# as SUBDEPTKEY, dept_no as SUBDEPTNO, title as SUBTITLE, NULL, NULL
							From department
							Where cny# =?',
        'ARGTYPES' => array('integer','integer'),
    ),
    'QRY_SUB_LOCATIONS' => array(
        'QUERY' => 'Select c.record# as SUBLOCATIONKEY, c.location_no as SUBLOCATIONNO, c.name as SUBNAME, p.location_no as SUBPARENTNO, p.name as SUBPARENTNAME
							From location c, location p
							Where c.cny# =? and p.cny# = c.cny# (+) and p.record# = c.parentkey (+) 
							UNION 
							Select record# as SUBLOCATIONKEY, location_no as SUBLOCATIONNO, name as SUBNAME, NULL, NULL
							From location
							Where cny# =? and parentkey is null',
        'ARGTYPES' => array('integer','integer'),
    ),
    'QRY_SUB_PERIODS' => array(
        'QUERY' => "Select record# as subperiodkey, name as subperiodname, start_date, end_date
							From glbudgettype
							Where cny# =? and budgeting = 'T'  and datetype = 99 and start_date >?",
        'ARGTYPES' => array('integer', 'date'),
    ),
    'QRY_SUB_COMPANYPREF' => array(
        'QUERY' => "Select property, value
							From companypref
							Where cny# =?", 
        'ARGTYPES' => array('integer'),
    ),
    'QRY_SUB_COMPANYINFO' => array(
        'QUERY' => "Select c.record#, c.name, a.accrual
							From company c, acctcompany a
							Where c.record# =? and a.cny# = c.record#", 
        'ARGTYPES' => array('integer'),
    ),
    'QRY_SUB_STATACCOUNTS' => array(
        'QUERY' => 'Select record# as SUBACCOUNTKEY, acct_no as SUBACCOUNTNO, title as SUBTITLE, normal_balance, account_type
							From stataccount
							Where cny# =?',
        'ARGTYPES' => array('integer'),
    ),
    'QRY_SUB_MODULEPREF' => array(
        'QUERY' => "select value from modulepref where cny# = ? and modulekey = ? and property = ? and locationkey is null",
        'ARGTYPES' => array('integer', 'text', 'text'),
    ),

    'QRY_SUB_GCBOOKS' => array(
        'QUERY' => "select bookid from glbook where cny# = ? and bookid not in ('ACCRUAL', 'CASH')",
        'ARGTYPES' => array('integer'),
    ),
    'QRY_SUB_MODULEPREF_GAAP' => array(
    'QUERY' => "select value from modulepref where cny# = ? and modulekey = '2.GL' and property = 'ENABLEGAAP' and locationkey is null", 
    'ARGTYPES' => array('integer'),
    ),
    'QRY_SUB_MODULEPREF_TAX' => array(
    'QUERY' => "select value from modulepref where cny# = ? and modulekey = '2.GL' and property = 'ENABLETAX' and locationkey is null", 
    'ARGTYPES' => array('integer'),
    ),
);

