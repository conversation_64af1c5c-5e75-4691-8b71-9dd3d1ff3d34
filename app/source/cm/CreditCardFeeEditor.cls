<?php

/**
 * Editor class for the Credit Card Fee object
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Intacct Corporation All, Rights Reserved
 */

/**
 * Editor class for the Credit Card Fee object
 */
class CreditCardFeeEditor extends CCTransactionEditor
{
    // Class variables
    /* @var string|null $fromCCReconScrn */
    private $fromCCReconScrn;

    /**
     * @param array $params
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
        
        // Do we come from credit card reconciliation ?
        $this->fromCCReconScrn = Request::$r->_fromCCReconScrn;
    }

    /**
     * Return an array of javascript files to include into the page
     *
     * @return array the list of javascript files to include
     */
    protected function getJavaScriptFileNames()
    {
        $jsFiles = parent::getJavaScriptFileNames();
        $jsFiles[] = '../resources/js/creditcardfee.js';
        return $jsFiles;
    }

    /**
     * Get the path of the entry memo field
     *
     * @return string[]|null
     */
    protected function getDefaultEntryMemoField()
    {
        return array(
            'path' => 'DESCRIPTION',
            'fullname' => 'IA.DESCRIPTION'
        );
    }

    /**
     * Map the ajax action to a PHP function
     *
     * @param string $cmd the Ajax action requested
     *
     * @return bool false if command not found
     */
    protected function runAjax($cmd)
    {
        $ok = true;

        switch ( $cmd ) {
            case 'getLocationRecordNo':
                if( Request::$r->equal == 'false') {
                    Request::$r->{'taxSolutionIds'} = $this->getEntityMgr()->getNonTaxImplicationTaxMethods();
                }
                SubLedgerTxnEditor::ajaxGetLocationRecordNo();
                break;
            default:
                $ok = parent::runAjax($cmd);
                break;
        }

        return $ok;
    }

    /**
     * This is a hook functions for subclases to adjust the metadata according to the current data & state
     *
     * @param array $obj
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        parent::mediateDataAndMetadata($obj);

        if(TaxSetupManager::isVATEnabled()) {
            if (IsRoot() && !TaxSetupManager::isSingleTaxJurisdictionConfigured()) {
                $obj['ISROOT'] = true;
            } else {
                $obj['ISROOT'] = false;
            }
        }

        $view = $this->getView();
        
        // Are we coming from credit card reconciliation ?
        if ( $this->state == $this->kShowNewState ) {

            // From credit card reconciliation ? Default the card
            if ( $this->fromCCReconScrn ) {
                $obj['FINANCIALENTITY'] = Request::$r->_forCreditCard;
            }
            
            // Hide the transaction type if not in create mode since we can not save it anywhere
            // This is just used to default the account on the grid.
            $view->findAndSetProperty(
                array('path' => 'TRANSACTIONTYPE'), array('hidden' => false), EditorComponentFactory::TYPE_FIELD
            );
        }

        if(TaxSetupManager::isVATEnabled()) {
            if ($this->state == $this->kShowNewState && empty($obj['TRANSACTIONTYPE'])) {
                $obj['TRANSACTIONTYPE'] = 'Other fees';
            }

            $chckTxVatEnabled = false;

            if ((!IsRoot() && $this->checkEntityFrstItemDetailId($obj['ITEMS'])) || (IsRoot() && TaxSolutionManager::isVatTaxMethod($obj['TAXMETHOD']))) {
                $chckTxVatEnabled = true;
            }
            if ((!$chckTxVatEnabled)) {
                if($this->state == $this->kShowNewState) {
                    $obj['TRANSACTIONTYPE'] = 'Finance charge';
                }
                if (IsRoot() && TaxSetupManager::isMultiTaxJurisdictionConfigured()) {
                    $view->findAndSetProperty(array('path' => 'TAXIMPLICATIONS'), array('hidden' => 'true'));
                    $view->findAndSetProperty(array('path' => 'TAXSOLUTIONID'), array('hidden' => 'true'));
                } else {
                    $view->findAndSetProperty(array('path' => 'INCLUSIVETAX'), array('hidden' => 'true'));
                    // CCTransactionEditor(parent) assigning INCLUSIVETAX as true, here it is non vat transaction so reset it to false
                    $obj['INCLUSIVETAX'] = 'false';
                    $view->findAndSetProperty(array('path' => 'TRX_AMOUNT'), array('readonly' => false));
                    $view->findAndSetProperty(array('id' => 'taxTotalsSection'), array('hidden' => false), EditorComponentFactory::TYPE_SECTION);
                    $view->findAndSetProperty(array('path' => 'TAXSUMMARY'), array('hidden' => true), EditorComponentFactory::TYPE_GRID);
                    $view->findAndSetProperty(array('path' => 'MULTIPLETAXES'), array('hidden' => true));
                    $itemGrid = array();
                    $view->findComponents(array('path' => 'ITEMS'), EditorComponentFactory::TYPE_GRID, $itemGrid);
                    if ($itemGrid[0]) {
                        $matches = [];
                        $itemGrid[0]->findComponents(array('path' => 'DETAILID'), EditorComponentFactory::TYPE_FIELD, $matches);
                        if ($matches) {
                            $matches[0]->setProperty('hidden', true);
                        }
                        $gridFieldsForVATCompany = $this->getIsMCPEnabled() ?
                            array('TAXRATE', 'TRX_TAX', 'TOTALTRXAMOUNT', 'TOTALBASEAMOUNT', 'BASETAXAMOUNT') :
                            array('TAXRATE', 'TRX_TAX', 'TOTALTRXAMOUNT');
                        foreach ($gridFieldsForVATCompany as $gridField) {
                            $matches = [];
                            $itemGrid[0]->findComponents(array('path' => $gridField), EditorComponentFactory::TYPE_FIELD, $matches);
                            if ($matches) {
                                $matches[0]->setProperty('hidden', true);
                            }
                        }
                    }
                }
            }
        }

        return true;
    }

    /**
     * Build the default line for the specified grid
     *
     * @param EditorGrid $grid the grid that needs the default line
     * @param array      $obj
     *
     * @return array the default line
     */
    public function getDefaultLine(EditorGrid $grid, &$obj)
    {
        //Calls base class to get the defaults and then we override below
        $defaultLine = parent::getDefaultLine($grid, $object);
        $forRecon = isset(Request::$r->_forRecon) && Request::$r->_forRecon == 'true';
        if ($forRecon && $this->state == $this->kShowNewState ) {
            // Default to transaction type for description
            $defaultLine['DESCRIPTION'] = $obj['TRANSACTIONTYPE'];
        }
        else {
            // Default to finance charge for description
            $defaultLine['DESCRIPTION'] =  I18N::getSingleToken('IA.FINANCE_CHARGE');
        }

        return $defaultLine;
    }

    /**
     * Pass over hidden values to the editor
     *
     * @return string[]|null Hidden values
     */
    protected function getEditorHiddenFields()
    {
        // Add the bank reconcialiation params
        $vars = parent::getEditorHiddenFields();
        $vars['_fromCCReconScrn'] = $this->fromCCReconScrn;
        $vars['_forCreditCard'] = Request::$r->_forCreditCard;
        return $vars;
    }

    /**
     * Figure out the number or rows in the grid
     *
     * @param array $_layout the metadata
     * @param array|null  $obj
     *
     * @return int the default number of rows in the grid
     */
    public function figureOutNumOfRows($_layout, &$obj = null)
    {
        $view = $this->getView();
        if ( $this->state == $this->kShowNewState && !($view->getProperty('copynew')) ) {
            return 1;
        }
        return parent::FigureOutNumOfRows($_layout, $obj);
    }
    
    /**
     * Get the list of values ot pass over in the URLs 
     *
     * @return array the url parameter to pass over
     */
    protected function getSpecificURLParameters()
    {
        $params = parent::getSpecificURLParameters();
        $params = array_merge($params, array('_fromCCReconScrn', '_forCreditCard'));
        return $params;
    }

    /**
     * Returns the javascript call to execute while closing the popup window.
     *
     * @param string $key
     * @param array  $obj
     *
     * @return string the JS code
     */
    protected function getPopupCloseJS($key, $obj = [])
    {
        $jsCode = $this->fromCCReconScrn ? $this->getReconciliationJSReload() : '';
        $jsCode .= parent::getPopupCloseJS($key);
        return $jsCode;
    }

    /**
     * If we come from Reconcile CCard we will  autopopulate the CC txn details from bank txn
     * This function will get all the parameters and modify the data
     *
     * @param array      &$obj  the data
     * @param EditorGrid $grid  the grid
     */
    protected function mediateDataAndMetadata_Reconciliation(&$obj, EditorGrid $grid)
    {
        parent::mediateDataAndMetadata_Reconciliation($obj, $grid);

        if ($this->state == $this->kShowNewState && empty($obj['TRANSACTIONTYPE'])) {
            $obj['TRANSACTIONTYPE'] = 'Other fees';
        }

        if (!empty($obj['ITEMS'])) {
            $accountId = Request::$r->_accountId;
            $accountType = Request::$r->_accountType;
            $entry = $obj['ITEMS'][0];
            // get GL account info. from the credit card account
            $accountDetails = ReconciliationUtils::getAccountDetails($accountId, $accountType);
            if ( $obj['TRANSACTIONTYPE'] == 'Finance charge' ) {
                if ( ! empty($accountDetails['FINCHRGACCTLABEL']) ) {
                    $glAcctInfo = explode("--", $accountDetails['FINCHRGACCTLABEL']);
                    $glAcctno = $glAcctInfo[0];
                    $entry['ACCOUNTLABEL'] = $glAcctno;
                }
                else if ( ! empty($accountDetails['FINCHRGACCT']) ) {
                    $glAcctInfo = explode("--", $accountDetails['FINCHRGACCT']);
                    $entry['ACCOUNTNO'] = $glAcctInfo[0];
                }
            } else {
                if ( ! empty($accountDetails['FEESACCTLABELKEY']) ) {
                    $glAcctInfo = explode("--", $accountDetails['FEESACCTLABELKEY']);
                    $entry['ACCOUNTLABEL']  = $glAcctInfo[0];
                }
                else if ( ! empty($accountDetails['FEESACCTKEY']) ) {
                    $glAcctInfo = explode("--", $accountDetails['FEESACCTKEY']);
                    $entry['ACCOUNTNO'] = $glAcctInfo[0];
                }
            }
            $entry['DESCRIPTION'] =  $obj['DESCRIPTION'] ?? $obj['TRANSACTIONTYPE'];
            $obj['ITEMS'][0] = $entry;
        }
    }

    /**
     * @return bool
     */
    protected function includeGLPostingTab()
    {
        return parent::includeGLPostingTab();
    }

    /**
     * This function will return the list of fields to add in the Defaults popup for the grid
     *
     * @param array $_params the metadata
     * @param array $objRec   the ownedobject information
     *
     * @return array the list of fields to add into the popup
     */
    protected function getGridDefaultsFields(&$_params, $objRec)
    {
        $fields = parent::getGridDefaultsFields($_params, $objRec);
        // for single jurisdiction compnay we have can have non tax implication transaction so better remove DETAILID field
        if ( TaxSetupManager::isSingleTaxJurisdictionConfigured() ) {
            $this->removeTaxDetailFromDefaultFields($fields);
        }
        return $fields;
    }


    /**
     * Figure out if we show the edit action button
     *
     * @return bool true if action is allowed else false
     */
    protected function canEdit()
    {
        if (!parent::canEdit()) {
            return false;
        }
        /* @var CreditCardFeeManager $mgr */
        $mgr=$this->getEntityMgr();
        $recordNo = Request::$r->GetCurrentObjectValueByPath('RECORDNO');
        if ($mgr->hasPayments($recordNo)) {
            return false;
        }

        return true;
    }
}
