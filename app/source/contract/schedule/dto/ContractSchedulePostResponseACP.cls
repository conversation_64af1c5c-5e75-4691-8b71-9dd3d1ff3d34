<?php
/**
 * File ContractSchedulePostResponseACP.cls contains the class ContractSchedulePostResponseACP
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2019 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation. */

class ContractSchedulePostResponseACP extends ContractSchedulePostResponse
{
    /* @var int[] $numberOfBillingSchedulesSkipped */
    private $numberOfBillingSchedulesSkipped = [];
    /* @var int[] $numberOfBillingSchedulesPosted */
    private $numberOfBillingSchedulesPosted = [];

    /* @var ContractSchedulePostErrorResponse[] $contractRevSchedulePostErrorResponse */
    private $contractBillingSchedulePostErrorResponse = [];

    /**
     * @return string[]
     */
    public function toBillingArray() {

        $billingErrorCount = count($this->getContractBillingSchedulePostErrorResponse());

        $response[] = [sprintf(_('Number of Billing entries posted: %s'), $this->getTotalNumberOfBillingSchedulesPosted())];

        if ($billingErrorCount > 0) {
            $response[] = [sprintf(_('Number of Billing errors: %s'), $billingErrorCount)];
        }

        if ($this->numberOfBillingSchedulesSkipped > 0) {
            $response[] = [sprintf(_('Number of Billing entries ignored: %s. These entries were posted earlier in a different process. This can occur when two or more users are posting Billing at the same time.'), $this->numberOfBillingSchedulesSkipped)];
        }

        if ($billingErrorCount > 0) {

            $response[] = [_('Billing errors: ')];

            $contractBillingSchedulePostErrorResponseArray = $this->getContractBillingSchedulePostErrorResponse();

            foreach ($contractBillingSchedulePostErrorResponseArray as $contractSchedulePostErrorResponse) {
                $response[] = [$contractSchedulePostErrorResponse->getContractId() . ' - Line no. ' . $contractSchedulePostErrorResponse->getContractLineNumber() . ': ' . $contractSchedulePostErrorResponse->getExceptionMessage()];
            }
        }

        if ($billingErrorCount > 0) {
            $response[] = [_('No entries were posted for the contracts listed in the Error sections. Correct the errors for the applicable contract(s) and then try posting the Billing and/or expense recognition again.')];
        }

        return $response;
    }

    /**
     * @return ContractSchedulePostErrorResponse[]
     */
    public function getContractBillingSchedulePostErrorResponse()
    {
        return $this->contractBillingSchedulePostErrorResponse;
    }

    /**
     * @param ContractSchedulePostErrorResponse $err
     */
    public function addContractBillingSchedulePostErrorResponse($err)
    {
        $this->contractBillingSchedulePostErrorResponse[] = $err;
    }

    /**
     * @return int
     */
    public function getTotalNumberOfBillingSchedulesPosted()
    {
        return $this->sumInts($this->numberOfBillingSchedulesPosted);
    }

    /**
     * @return int
     */
    public function getTotalNumberOfBillingSchedulesSkipped()
    {
        return $this->sumInts($this->numberOfBillingSchedulesSkipped);
    }

    /**
     * @param string $contractId
     *
     * @return int
     */
    public function getNumberOfBillingSchedulesPosted($contractId)
    {
        return $this->numberOfBillingSchedulesPosted[$contractId] ?? 0;
    }

    /**
     * @param string $contractId
     * @param int    $numberOfBillingSchedulesSkipped
     */
    public function addToNumberOfBillingSchedulesSkipped($contractId, $numberOfBillingSchedulesSkipped)
    {
        if (!isset($this->numberOfBillingSchedulesSkipped[$contractId])) {
            $this->numberOfBillingSchedulesSkipped[$contractId] = 0;
        }
        $this->numberOfBillingSchedulesSkipped[$contractId] += $numberOfBillingSchedulesSkipped;
    }

    /**
     * @param string $contractId
     * @param int    $numberOfBillingSchedulesPosted
     */
    public function addToNumberOfBillingSchedulesPosted($contractId, $numberOfBillingSchedulesPosted)
    {
        if (!isset($this->numberOfBillingSchedulesPosted[$contractId])) {
            $this->numberOfBillingSchedulesPosted[$contractId] = 0;
        }
        $this->numberOfBillingSchedulesPosted[$contractId] += $numberOfBillingSchedulesPosted;
    }
}