<?php
/**
 * Manager for billing template
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Manager class for the object
 */
class BillingTemplateManager extends EntityManager
{

    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }
    
    /**
     * Find a single record for an id
     *
     * @param string   $ID     vid
     * @param string[] $fields list of fields to query
     *
     * @return array query
     */
    public function get($ID, $fields=null)
    {
        $obj = false;
        if (is_numeric($ID)) {
            $obj = parent::get($ID, $fields);
        }
        if (!$obj) {
            $params = array('selects' => array('RECORDNO'));
            $params['filters'][0][] = array('TEMPLATEID', '=', $ID);
            $records = $this->GetList($params);
            if (!empty($records)) {
                $ID = $records[0]['RECORDNO'];
                $obj = parent::get($ID, $fields);
            }
        }
        return $obj;
    }

    /**
     * Build a query base on params
     *
     * @param array  $params        a structure used to build the custom query
     * @param string $type          type
     * @param array  $systemViewArr a structure used to build the custom query
     * @param bool   $nocount       if true do not query the count
     * @param array  $processed
     *
     * @return array query
     */
    function GetListQuery($params, $type = 'normal', $systemViewArr = [], $nocount = true, &$processed = null)
    {
        if (!$params['includehistorical']) {
            $params['filters'][0][] = array('HISTORICAL', '=', 'false');
        }
        return parent::GetListQuery($params, $type, $systemViewArr, $nocount);
    }

    /**
     * Return a list of entities
     *
     * @param array $params    a structure used to build the custom query
     * @param bool  $_crosscny if true do not add the var.cny# = ... code
     * @param bool  $nocount   if true do not query the count
     *
     * @return array[] $newResult  - result of query
     */
    public function GetList($params = [], $_crosscny = false, $nocount = true)
    {
        if (!$params['includehistorical']) {
            $params['filters'][0][] = array('HISTORICAL', '=', 'false');
        }
        return parent::GetList($params, $_crosscny, $nocount);
    }

    /**
     * Create
     *
     * @param array &$values record
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $source = "BillingTemplateManager::Add";
        $gErr = Globals::$g->gErr;

        $ok = $this->_QM->beginTrx($source);

        $ok = $ok && $this->validateValues($values);
        $ok = $ok && $this->translate($values);

        // enforce unique id
        $params = array(
            'includehistorical' => true,
        );
        $params['filters'][0][] = array('TEMPLATEID', '=', $values['TEMPLATEID']);
        $records = $this->GetList($params);
        if (!empty($records)) {
            $gErr->addIAError(
                "SO-0003", __FILE__.":".__LINE__,
                'A Billing Template with the same ID already exists.',[],
                sprintf("A Billing Template with the ID, {%s} already exists.", $values['TEMPLATEID']),['VALUES_TEMPLATEID' => $values['TEMPLATEID']],
                'Use a different Template ID.' 
            );
                return false;
        }

        $ok = $ok && parent::regularAdd($values);

        $ok = $ok && $this->_QM->commitTrx($source);
        if (! $ok) {
            $this->_QM->rollbackTrx($source);
        }
        return $ok;
    }

    /**
     * Update
     *
     * @param array &$values record
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        $source = "BillingTemplateManager::Set";
        $ok = $this->_QM->beginTrx($source);
        
        $ok = $ok && $this->validateValues($values);
        $ok = $ok && $this->translate($values);
        
        if ($this->needsVersioning($values['RECORDNO'])) {
                
            // mark last version as historical
            $ok = $ok && $this->DoQuery(
                'QRY_BILLINGTEMPLATE_SET_HISTORICAL',
                array($values['RECORDNO'], GetMyCompany())
            );
    
            // add new version
            unset($values['RECORDNO']);
            if (!empty($values['BILLINGTEMPLATEENTRY'])) {
                // unset RECORDNO in entries:
                foreach ($values['BILLINGTEMPLATEENTRY'] as &$entry) {
                    unset($entry['RECORDNO']);
                }
            }
            $ok = $ok && parent::regularAdd($values);
    
        } else {
            $ok = $ok && parent::regularSet($values);
        }
        
        $ok = $ok && $this->_QM->commitTrx($source);
        if ( ! $ok ) {
            $this->_QM->rollbackTrx($source);
        }
        return $ok;
    }

    /**
     * Check the previous template was used
     *
     * @param int $recordno record#
     *
     * @return bool
     */
    public function needsVersioning($recordno)
    {
        $result = $this->DoQuery('QRY_BILLINGTEMPLATE_TRANSACTIONS_EXIST', array ($recordno));
        return (count($result) > 0);
    }
    
    /**
     * Validate values before save
     *
     * @param array &$values record to save
     *
     * @return bool
     */
    protected function validateValues(&$values)
    {
        $gErr = Globals::$g->gErr;
        $ok = true;
        $lastCumulativePercent = 0;
        if (!empty($values['BILLINGTEMPLATEENTRY'])) {
            foreach ( $values['BILLINGTEMPLATEENTRY'] as $entry) {
                if ($entry['PERCENTCOMPLETED'] < 0) {
                    $gErr->addError(
                        "SO-0004", __FILE__.":".__LINE__,
                        "Incorrect billing template values",
                        "Negative line entries found in the Percent Completed field",
                        ''
                    );
                    return false;
                }
                $percent = ibcsub($entry['PERCENTCOMPLETED'], $lastCumulativePercent, 14);
                $lastCumulativePercent = $entry['PERCENTCOMPLETED'];
                if ($percent <= 0) {
                    $gErr->addError(
                        "SO-0005", __FILE__.":".__LINE__,
                        "Incorrect billing template values",
                        "Percent Completed is cumulative. Each percentage must be greater than the previous one.",
                        ''
                    );
                    return false;
                }
            }
            if ($lastCumulativePercent != 1) {
                $gErr->addError(
                    "SO-0006", __FILE__.":".__LINE__,
                    "Incorrect billing template values",
                    "Percent Completed is cumulative. The last percentage must equal 100.",
                    ''
                );
                return false;
            }
        }
        return $ok;
    }

    /**
     * Modify values before save
     *
     * @param array &$values record to save
     *
     * @return bool
     */
    protected function translate(&$values)
    {
        $ok = true;

        if (empty($values['BILLINGTEMPLATEENTRY'])) {
            if ($values['BILLINGMETHOD'] == 'Milestone') {
                $values['BILLINGTEMPLATEENTRY'] = [
                    [
                        'PERCENTCOMPLETED' => 1,
                        'PERCENTBILLED'    => 1,
                    ],
                ];
            }
        } else {
            // recalculate PERCENTBILLED - it doesn't come from API.
            // NOTE: The calculation is mimicking the one in JavaScript: billingtemplate.js, checkValues function.
            // It has been used since 2011/2013 and we don't want to change the calculation method because that could
            // cause unpredictable consequences in billing schedules and numbers used in actual billing/invoicing.
            $lastCumulativePercent = 0;
            foreach ( $values['BILLINGTEMPLATEENTRY'] as &$entry ) {
                $percentCompletedValue = round(floatval($entry['PERCENTCOMPLETED']) * 1000000)/1000000;
                $percentDiff = ibcsub($percentCompletedValue, $lastCumulativePercent, 5, true);
                $entry['PERCENTBILLED'] = $percentDiff;
                $lastCumulativePercent = $percentCompletedValue;
            }
        }

        return $ok;
    }

}