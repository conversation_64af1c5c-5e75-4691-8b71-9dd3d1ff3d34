/**
 * qxloading.styl
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Intacct Corporation -- All Rights Reserved
 */

/********************************************************************
 * loading animation overlay
 */
qx-spinner() {
  -webkit-animation rotation .6s infinite linear
  -moz-animation rotation .6s infinite linear
  -o-animation rotation .6s infinite linear
  animation rotation .6s infinite linear
  border-left 10px solid rgba(51,51,51,.3)
  border-right 10px solid rgba(51,51,51,.3)
  border-bottom 10px solid rgba(51,51,51,.3)
  border-top 10px solid rgba(51,51,51,.8)
  border-radius 100%
  margin-left -30px //half of width, to get exact middle for animation
}

.qx-loading-overlay {
  width calc(100% + 15px) //compensate negative left position
  height 100%
  position fixed
  top 0
  left -15px //to cover 15px padding from qx-forms body
  z-index 10000
  &.qx-lo-alphabg {
    background-color rgba(51, 51, 51, 0.5)
  }
  & .qx-lo-spinner {
    position absolute
    left 50%
    top 40%
    height 60px
    width 60px
    margin 0px auto
    qx-spinner()
  }
  & .qx-lo-text {
    position absolute
    top 40%
    margin 0px auto
    margin-top 70px
    width 100%
    text-align center
    color #333
    regular_font(14px)
  }
}

#SpecialWaitScrim {
  & div {
    &:last-child {
      & img {
        padding 20px
        qx-spinner()
        &[style] {
          width 0 !important
          height 0 !important
        }
      }
      & p {
        &[style] {
          padding-top 5px !important
          color #333 !important
          font-weight normal !important
          font-size 14px !important
          text-shadow none !important
          margin-left -30px
        }
      }
      &[style] {
        position absolute !important
        left 40% !important
        top 20% !important
        margin 0 auto !important
        padding-top 0 !important

      }
    }
  }
  & .wizardglassbackground {
    opacity 0.3
  }
}

@-webkit-keyframes rotation {
  from {-webkit-transform: rotate(0deg);}
  to {-webkit-transform: rotate(359deg);}
}
@-moz-keyframes rotation {
  from {-moz-transform: rotate(0deg);}
  to {-moz-transform: rotate(359deg);}
}
@-o-keyframes rotation {
  from {-o-transform: rotate(0deg);}
  to {-o-transform: rotate(359deg);}
}
@keyframes rotation {
  from {transform: rotate(0deg);}
  to {transform: rotate(359deg);}
}
