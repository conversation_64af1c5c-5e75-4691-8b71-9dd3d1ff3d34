#Makefile.in
#

ENTITY_XMLS=					\
	$(EMPTY)

ENTITY_ENTS=                           \
	expenseexternalqueue.ent \
	expenseexternalqueuehistory.ent	\
	expensefileuploadqueue.ent \
	expensefileuploadqueuehistory.ent \
	expensewebhookqueue.ent \
	expensewebhookqueuehistory.ent \
	$(EMPTY)

QUERY_OBJECTS=$(ENTITY_ENTS:.ent=.qry)

LAYOUT_PHPS=$(LAYOUT_XMLS:.xml=.php)
	
LAYOUT_XSLS=$(LAYOUT_XMLS:.xml=.xsl)

include ../Makefile.in
