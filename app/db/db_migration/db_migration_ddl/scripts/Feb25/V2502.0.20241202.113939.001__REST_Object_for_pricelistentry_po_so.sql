/*
== DELTA ONLY FOR DEV SCHEMAS ==

Description:

Repair action: IGNORE

*/

/*
    --OWNER_CONTACT:<EMAIL>
    --Transforming itemkey & productlinekey of “oeprclstitems” data which has id’s will be changed by record# of parent table icitem & icprodline.
*/

DECLARE
v_item_fk_exists number := 0;
v_item_index_exists number := 0;
v_prod_fk_exists number := 0;
v_prod_index_exists number := 0;
BEGIN

SELECT COUNT(1) INTO v_item_fk_exists
FROM all_constraints
WHERE owner = sys_context('userenv', 'current_schema')
  AND UPPER(table_name) = 'OEPRCLSTITEMS'
  AND UPPER(constraint_name) = 'FK_OEPRCLSTITEMS_ITMKEY';

if (v_item_fk_exists > 0) then
        EXECUTE IMMEDIATE 'ALTER TABLE OEPRCLSTITEMS DROP CONSTRAINT FK_OEPRCLSTITEMS_ITMKEY';
end if;

SELECT COUNT(1) INTO v_item_index_exists
FROM all_indexes
WHERE owner = sys_context('userenv', 'current_schema')
  AND index_name = 'IX_OEPRCLSTITEMS_ITMKEY'
  AND UPPER(table_name) = 'OEPRCLSTITEMS';

if (v_item_index_exists > 0) then
        EXECUTE IMMEDIATE 'DROP INDEX IX_OEPRCLSTITEMS_ITMKEY';
end if;

SELECT COUNT(1) INTO v_prod_fk_exists
FROM all_constraints
WHERE owner = sys_context('userenv','current_schema')
  AND UPPER(CONSTRAINT_NAME) = 'FK_OEPRCLSTITEMS_PRODLINEKEY'
  AND UPPER(table_name) = 'OEPRCLSTITEMS';

if (v_prod_fk_exists > 0) then
       EXECUTE IMMEDIATE 'ALTER TABLE OEPRCLSTITEMS DROP CONSTRAINT FK_OEPRCLSTITEMS_PRODLINEKEY';
end if;

SELECT COUNT(1) INTO v_prod_index_exists
FROM all_indexes
WHERE owner = sys_context('userenv', 'current_schema')
  AND index_name = 'IX_OEPRCLSTITEMS_PRODLINEKEY'
  AND UPPER(table_name) = 'OEPRCLSTITEMS';

if (v_prod_index_exists > 0) then
        EXECUTE IMMEDIATE 'DROP INDEX IX_OEPRCLSTITEMS_PRODLINEKEY';
end if;
end;
/

--Renaming the exisiting column's as temp columns
ALTER TABLE oeprclstitems RENAME COLUMN itemkey TO itemkey_temp
/

ALTER TABLE oeprclstitems RENAME COLUMN productlinekey TO productlinekey_temp
/

--create 2 columns for storing record#
ALTER TABLE oeprclstitems ADD (
  itemkey NUMBER(15,0),
  productlinekey NUMBER(15,0)
)
/

--creating constraint for newly created columns
ALTER TABLE oeprclstitems ADD (
  CONSTRAINT FK_OEPRCLSTITEMS_ITMKEY FOREIGN KEY (cny#, itemkey) REFERENCES ICITEM (cny#, record#) ON DELETE CASCADE DEFERRABLE ENABLE NOVALIDATE,
  CONSTRAINT FK_OEPRCLSTITEMS_PRODLINEKEY FOREIGN KEY (cny#, productlinekey) REFERENCES ICPRODLINE (cny#, record#) ON DELETE CASCADE DEFERRABLE ENABLE NOVALIDATE
)
/

--creating index for newly created columns
CREATE INDEX IX_OEPRCLSTITEMS_ITMKEY ON oeprclstitems (cny#, itemkey) TABLESPACE ACCTINDX ONLINE
/

CREATE INDEX IX_OEPRCLSTITEMS_PRODLINEKEY ON oeprclstitems (cny#, productlinekey) TABLESPACE ACCTINDX ONLINE
/
