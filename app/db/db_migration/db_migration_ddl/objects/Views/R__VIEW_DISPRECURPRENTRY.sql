--OWNER_CONTACT:<EMAIL>,<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_DISPRECURPRENTRY.sql

CREATE OR REPLACE FORCE VIEW disprecurprentry (cny#,record#,line_no,allocationkey,status,recordkey,description,
accountkey,accountlabelkey,glentrykey,"OFFSET",lineitem,gloffset,deferrevenue,revrectemplatekey,deferredrevacctkey,
revrecstartdate,currency,exch_rate_date,exch_rate_type_id,exchange_rate,revrecenddate,basecurr,billable,customerdimkey,
projectdimkey,vendordimkey,employeedimkey,itemdimkey,classdimkey,contractdimkey,taskdimkey,warehousedimkey,
costtypedimkey,class2dimkey,
assetdimkey,
affiliateentitydimkey,
workorderdimkey,
--NEWDIMTOOL-newstddimkey,
--*** DO NOT alter/remove the above line.
trx_amount,amount,location#,dept#,form1099,whencreated,whenmodified,createdby,modifiedby) AS
SELECT A.CNY# ,
    MIN(A.RECORD#),
    A.LINE_NO ,
    A.ALLOCATIONKEY ,
    A.STATUS ,
    A.RECORDKEY ,
    A.DESCRIPTION ,
    A.ACCOUNTKEY ,
    A.ACCOUNTLABELKEY ,
    A.GLENTRYKEY ,
    A.OFFSET ,
    A.LINEITEM ,
    A.GLOFFSET ,
    A.DEFERREVENUE ,
    A.REVRECTEMPLATEKEY ,
    A.DEFERREDREVACCTKEY ,
    A.REVRECSTARTDATE ,
    A.CURRENCY ,
    A.EXCH_RATE_DATE ,
    A.EXCH_RATE_TYPE_ID ,
    A.EXCHANGE_RATE ,
    A.REVRECENDDATE ,
    A.BASECURR ,
    A.BILLABLE,
    DECODE(A.ALLOCATIONKEY,NULL,A.CUSTOMERDIMKEY,NULL) AS CUSTOMERDIMKEY,
    DECODE(A.ALLOCATIONKEY,NULL,A.PROJECTDIMKEY,NULL)  AS PROJECTDIMKEY,
    DECODE(A.ALLOCATIONKEY,NULL,A.VENDORDIMKEY,NULL)   AS VENDORDIMKEY,
    DECODE(A.ALLOCATIONKEY,NULL,A.EMPLOYEEDIMKEY,NULL) AS EMPLOYEEDIMKEY,
    DECODE(A.ALLOCATIONKEY,NULL,A.ITEMDIMKEY,NULL)     AS ITEMDIMKEY,
    DECODE(A.ALLOCATIONKEY,NULL,A.CLASSDIMKEY,NULL)    AS CLASSDIMKEY,
    DECODE(A.ALLOCATIONKEY,NULL,A.CONTRACTDIMKEY,NULL)   AS CONTRACTDIMKEY,
    DECODE(A.ALLOCATIONKEY,NULL,A.TASKDIMKEY,NULL)	 AS TASKDIMKEY,
    DECODE(A.ALLOCATIONKEY,NULL,A.WAREHOUSEDIMKEY,NULL)  AS WAREHOUSEDIMKEY,
    DECODE(A.ALLOCATIONKEY,NULL,A.COSTTYPEDIMKEY,NULL)  AS COSTTYPEDIMKEY,
    DECODE(A.ALLOCATIONKEY,NULL,A.CLASS2DIMKEY,NULL)  AS CLASS2DIMKEY,
    DECODE(A.ALLOCATIONKEY,NULL,A.ASSETDIMKEY,NULL)  AS ASSETDIMKEY,
    DECODE(A.ALLOCATIONKEY,NULL,A.AFFILIATEENTITYDIMKEY,NULL) AS AFFILIATEENTITYDIMKEY,
    DECODE(A.ALLOCATIONKEY,NULL,A.WORKORDERDIMKEY,NULL) AS WORKORDERDIMKEY,
--NEWDIMTOOL-    DECODE(A.ALLOCATIONKEY,NULL,A.NEWSTDDIMKEY,NULL) AS NEWSTDDIMKEY,
--*** DO NOT alter/remove the above line.         
    SUM(A.TRX_AMOUNT)                                  AS TRX_AMOUNT ,
    SUM(A.AMOUNT)                                      AS AMOUNT ,
    DECODE(A.ALLOCATIONKEY,NULL,A.LOCATION#,NULL)      AS LOCATION# ,
    DECODE(A.ALLOCATIONKEY,NULL,A.DEPT#,NULL)          AS DEPT#,
    A.FORM1099,
    A.WHENCREATED,
    A.WHENMODIFIED,
    A.CREATEDBY,
    A.MODIFIEDBY
FROM RECURPRENTRY A
GROUP BY A.CNY# ,
    A.LINE_NO ,
    A.ALLOCATIONKEY ,
    A.STATUS ,
    A.RECORDKEY ,
    A.DESCRIPTION ,
    A.ACCOUNTKEY ,
    A.ACCOUNTLABELKEY ,
    A.GLENTRYKEY ,
    A.OFFSET ,
    A.LINEITEM ,
    A.GLOFFSET ,
    A.DEFERREVENUE ,
    A.REVRECTEMPLATEKEY ,
    A.DEFERREDREVACCTKEY ,
    A.REVRECSTARTDATE ,
    A.CURRENCY ,
    A.EXCH_RATE_DATE ,
    A.EXCH_RATE_TYPE_ID ,
    A.EXCHANGE_RATE ,
    A.REVRECENDDATE ,
    A.BASECURR ,
    A.BILLABLE,
    DECODE(A.ALLOCATIONKEY,NULL,A.CUSTOMERDIMKEY,NULL),
    DECODE(A.ALLOCATIONKEY,NULL,A.PROJECTDIMKEY,NULL),
    DECODE(A.ALLOCATIONKEY,NULL,A.VENDORDIMKEY,NULL),
    DECODE(A.ALLOCATIONKEY,NULL,A.EMPLOYEEDIMKEY,NULL),
    DECODE(A.ALLOCATIONKEY,NULL,A.ITEMDIMKEY,NULL),
    DECODE(A.ALLOCATIONKEY,NULL,A.CLASSDIMKEY,NULL),
    DECODE(A.ALLOCATIONKEY,NULL,A.CONTRACTDIMKEY,NULL),
    DECODE(A.ALLOCATIONKEY,NULL,A.TASKDIMKEY,NULL),
    DECODE(A.ALLOCATIONKEY,NULL,A.WAREHOUSEDIMKEY,NULL),
    DECODE(A.ALLOCATIONKEY,NULL,A.COSTTYPEDIMKEY,NULL),
    DECODE(A.ALLOCATIONKEY,NULL,A.CLASS2DIMKEY,NULL),
    DECODE(A.ALLOCATIONKEY,NULL,A.ASSETDIMKEY,NULL),
    DECODE(A.ALLOCATIONKEY,NULL,A.AFFILIATEENTITYDIMKEY,NULL),
    DECODE(A.ALLOCATIONKEY,NULL,A.WORKORDERDIMKEY,NULL),
--NEWDIMTOOL-    DECODE(A.ALLOCATIONKEY,NULL,A.NEWSTDDIMKEY,NULL),
--*** DO NOT alter/remove the above line.           
    DECODE(A.ALLOCATIONKEY,NULL,A.LOCATION#,NULL) ,
    DECODE(A.ALLOCATIONKEY,NULL,A.DEPT#,NULL),
    A.FORM1099,
    A.WHENCREATED,
    A.WHENMODIFIED,
    A.CREATEDBY,
    A.MODIFIEDBY;