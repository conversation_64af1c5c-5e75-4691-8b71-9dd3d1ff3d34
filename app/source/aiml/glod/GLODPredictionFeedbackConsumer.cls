<?php

/**
 *    FILE:          GLODPredictionFeedbackConsumer.cls
 *    AUTHOR:        <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 *    DESCRIPTION:   GLOD Consumer Class
 *
 *    (C) 2014, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

class GLODPredictionFeedbackConsumer extends Consumer
{

    use GLODPredictionFeedbackTrait;

    /**
     * @var bool $morePredictionWork
     */
    private $morePredictionWork = false;

    /**
     * @var bool $moreFeedbackWork
     */
    private $moreFeedbackWork = false;

    /**
     * Execute the job through the consumer
     *
     * @param int            $timeLimit
     * @param string         $jobId
     * @param ConsumerEngine $engine
     *
     * @return bool  True if the consumer has more work to do, false otherwise
     */
    public function run(int $timeLimit, string $jobId, ConsumerEngine $engine)
    {
        try {
            // Create company
            if (!GLODInteractions::isCompanyCreated()) {
                $this->doProcess(GLODInteractions::ACTION_CREATE_COMPANY, $timeLimit);
                $this->doProcess(GLODInteractions::ACTION_SET_DEFAULTS, $timeLimit);
            }
            // Company can be created or not, but if there is a job, then process it
            if ( GLODInteractions::hasJob()) {
                // Set company defaults
                if ( GLODInteractions::hasSetDefaultsJob() && GLODInteractions::isCompanyCreated() ) {
                    $this->doProcess(GLODInteractions::ACTION_SET_DEFAULTS, $timeLimit);
                }
                // Predict & Feedback, if the model is ready
                if ( GLODInteractions::isGlodReady() ) {
                    $this->doProcess(GLODInteractions::ACTION_PREDICT, $timeLimit);
                    $this->doProcess(GLODInteractions::ACTION_FEEDBACK, $timeLimit);
                }
                // if no more Job to do, then disable the job
                if ( ! $this->hasMoreWork() ) {
                    GLODInteractions::dequeueJob();
                }
            }
        } catch ( Exception $e ) {
            $this->morePredictionWork = $this->moreFeedbackWork = false;
            GLODInteractions::log($e->getMessage(), LogManager::ERROR, $this->getLogPrefix());
        }

        return $this->hasMoreWork();
    }

    /**
     * @return bool
     */
    public function hasMoreWork()
    {
        return $this->morePredictionWork || $this->moreFeedbackWork;
    }

    /**
     * @param string $action
     * @param int    $timeLimit
     *
     * @throws Exception
     */
    public function doProcess(string $action, int $timeLimit)
    {
        $runcount = 1;
        $maxCount = (int) GLODInteractions::getIAConfig('GLOD_PROCESS_MAX_LOOP_PER_CNY');
        $ok = true;
        $glod = new GLODInteractions($action);

        do {
            $process = $glod->getNewProcess();
            $process->setTimeLimit($timeLimit);
            $process->run();
            GLODInteractions::log("Total Run : {$runcount}/{$maxCount} | Last process run : {$process->getRunCount()}",
                                  LogManager::DEBUG, __CLASS__ . '::' . __FUNCTION__);
            $runcount += $process->getRunCount();
            $ok = $ok && $process->isSuccess() && $process->hasMore() && ! $process->isHTTPFailed();
            $hasMoreWork = ! $ok && ! $process->isCompanyNotFound() && ( $process->isHTTPFailed() || $process->hasMore() );

            if ( $action === GLODInteractions::ACTION_PREDICT ) {
                $this->morePredictionWork = $hasMoreWork;
            } else if ( $action === GLODInteractions::ACTION_FEEDBACK ) {
                $this->moreFeedbackWork = $hasMoreWork;
            } else if ( ! $process->hasMore() ) {
                break;
            }

        } while ( $ok && $runcount <= $maxCount );
    }

    /**
     * Initialize the company & DB context for the given job ID. This is intended as a quick function to set the
     * context. For example to call Backend_Init::SetEnvironment().
     *
     * @param string $jobId
     *
     * @return bool success or failure
     */
    public function initContext(string $jobId) : bool
    {
        //  Extract the company from the job id (created by the Dispatcher).
        $companyId = $this->parseCompanyFromJobID($jobId);

        // Use company id as an integer value, since a type is not specified in SetDBSchema
        if ( ! SetDBSchema($companyId, null) ) {
            logToFileError("Unable to set the context to company $companyId");;
            return false;
        }

        // Set system user as user context
        Backend_Init::SetEnvironment($companyId, -1);

        Request::$r->_oid = IASessionHandler::IMS_SESSION_KEY;
        // Setup a portal type session, this is to properly setup Company & Profile handlers
        // Required by the outlier assitant to pass validations when approve/decline glbatch
        // This is required only when approvals with outlier assistant is enabled
        if ( GLSetupManager::isApprovalEnabled() && GLODInteractions::hasAnyOutlierAssistEnabledJournals() ) {
            IASessionHandler::setupSession('PORTAL', Globals::$g->_userid);
        }

        return true;
    }
}