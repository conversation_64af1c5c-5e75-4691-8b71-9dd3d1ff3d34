<?php
/**
 * Metric class for AutomatedTransactionHttpRequestcomponent, httpCall metric id
 *
 * <AUTHOR>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

/* --------------------------------------------------------------------------------------------------------------
 * DO NOT EDIT THIS FILE DIRECTLY. Update metrics_definition.json, then regenerate the file with metrics_gen.bin
 * --------------------------------------------------------------------------------------------------------------
 */
 
final class MetricAutomatedTransactionHttpRequestHttpCall  extends IAMetrics
{

    /**
     * IAMetrics constructor.
     *
     * @param string $action
     * @param int $resCode
     * @param string $endPoint
     * @param string $guId

     */
    public function __construct($action = null, $resCode = null, $endPoint = null, $guId = null)
    {
        parent::__construct(
            'AutomatedTransactionHttpRequest',
            'httpCall',
            0,
            [
                'time'
            ],
            [
                'action',
                'resCode',
                'endPoint',
                'guId'
            ]
            , $action, $resCode, $endPoint, $guId
        );
    }

    public function startTime()
    {
        parent::startTimer('time');
    }
    
    public function stopTime()
    {
        parent::stopTimer('time');
    }

    /**
     * @return float|null
     */
    public function getTime(): ?float
    {
        return parent::getValue('time');
    }
    
    /**
     * @param float $time
     */
    public function setTime(float $time)
    {
        parent::setValue('time', $time);
    }

    /**
     * @return string|null
     */
    public function getAction(): ?string
    {
        return parent::getAttribute('action');
    }
    
    /**
     * @param string $action
     */
    public function setAction(string $action)
    {
        parent::setAttribute('action', $action);
    }

    /**
     * @return int|null
     */
    public function getResCode(): ?int
    {
        return parent::getAttribute('resCode');
    }
    
    /**
     * @param int $resCode
     */
    public function setResCode(int $resCode)
    {
        parent::setAttribute('resCode', $resCode);
    }

    /**
     * @return string|null
     */
    public function getEndPoint(): ?string
    {
        return parent::getAttribute('endPoint');
    }
    
    /**
     * @param string $endPoint
     */
    public function setEndPoint(string $endPoint)
    {
        parent::setAttribute('endPoint', $endPoint);
    }

    /**
     * @return string|null
     */
    public function getGuId(): ?string
    {
        return parent::getAttribute('guId');
    }
    
    /**
     * @param string $guId
     */
    public function setGuId(string $guId)
    {
        parent::setAttribute('guId', $guId);
    }

}
