<?php

/**
 * CoreHelpService
 *
 * <AUTHOR>
 * @copyright Copyright (C)2021 Sage Intacct Corporation, All Rights Reserved
 */

class CoreHelpService
{
    /** @var string $s2sClient */
    private static string $s2sClient;

    /** @var string $s2sSecret */
    private static string $s2sSecret;

    /** @var string $semanticSearchOauthUrl */
    private static string $semanticSearchOauthUrl;

    /** @var string $semanticSearchOauthAudience */
    private static string $semanticSearchOauthAudience;

    /** @var string $semanticSearchUrl */
    private static string $semanticSearchUrl;

    /** @var string $semanticSearchClientId */
    private static string $semanticSearchClientId;
    
    /** @var MetricCoreHelpServiceAuthentication $metricCoreHelpServiceAuthentication */
    private MetricCoreHelpServiceAuthentication $metricCoreHelpServiceAuthentication;
    
    /** @var MetricCoreHelpServiceSearch $metricCoreHelpServiceSearch */
    private MetricCoreHelpServiceSearch $metricCoreHelpServiceSearch;
    
    /** @var MetricCoreHelpServiceFeedback $metricCoreHelpServiceFeedback */
    private MetricCoreHelpServiceFeedback $metricCoreHelpServiceFeedback;
    
    const LOG_PREFIX = 'CoreHelpService';

    public function __construct()
    {
        $env = getCopilotConfig();

        self::$s2sClient = TwoWayDecryptWithKey($env['SAGE_ID_S2S_CLIENT'], 'IA_INIT');
        self::$s2sSecret = TwoWayDecryptWithKey($env['SAGE_ID_S2S_SECRET'], 'IA_INIT');
        self::$semanticSearchOauthUrl = $env['SEMANTIC_SEARCH_OAUTH_URL'];
        self::$semanticSearchOauthAudience = $env['SEMANTIC_SEARCH_OAUTH_AUDIENCE'];
        self::$semanticSearchUrl = $env['SEMANTIC_SEARCH_SERVICE_URL'];
        self::$semanticSearchClientId =  $env['SEMANTIC_SEARCH_CLIENT_ID'];
        
        $this->metricCoreHelpServiceFeedback = new MetricCoreHelpServiceFeedback();
        $this->metricCoreHelpServiceSearch = new MetricCoreHelpServiceSearch();
        $this->metricCoreHelpServiceAuthentication = new MetricCoreHelpServiceAuthentication();
    }

    /**
     * @param array $request
     * @param array $extraContext
     *
     * @return string[]
     */
    public function search(array $request, array $extraContext) : array
    {
        $this->logMessage('search', '', '', LogManager::DEBUG, 'Received request data:', $request);

        StartTimer('CoreHelpService::search');

        $version = $extraContext[APIConstants::API_VERSION_KEY];

        $ret = [];
        try {
            $sessionId = $request['sessionId'] ?? bin2hex(random_bytes(20));

            if (empty($request['query'])) {
                throw (new APIException())->setAPIError(
                    APIError::getInstance(APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0040, ['key' => "query", 'service' => 'search'])
                );
            }
            $query = $request['query'];

            $this->metricCoreHelpServiceSearch->setSessionId($sessionId);

            $accessToken = $this->getM2MSageIDToken($sessionId);

            $this->metricCoreHelpServiceSearch->setQuery($query);
            $ssResponse = $this->querySemanticSearch($query, $accessToken, $sessionId);
            $this->logMessage('search', $sessionId, $version, LogManager::DEBUG, 'Semantic search result: ', $ssResponse);

            $ret['replyId'] = $ssResponse['id'] ?? bin2hex(random_bytes(20));
            if (isset($ssResponse['answer'])) {
                $ret['reply'] = $ssResponse['answer'];
            } else {
                throw (new APIException())->setAPIError(
                    APIError::getInstance(APIErrorMessages::INTERNAL_SERVER_ERROR_INTERNAL_ERROR_0002, [])
                );
            }
            $ret['sources'] = $ssResponse['sources'] ?? [];
            $ret['sessionId'] = $ssResponse['session_id'] ?? $sessionId;
            if ($ssResponse['session_id'] !== $sessionId) {
                $this->logMessage(
                    'search',
                    $sessionId,
                    $version,
                    LogManager::WARN,
                    " (SemanticSearch) discrepancy between the session IDs: started with '$sessionId' vs. responded with '". $ssResponse['sessionId'] . "'"
                );
            }
            $this->metricCoreHelpServiceSearch->setId($ret['replyId'] ?? '');
        } catch (\Throwable $throwable) {
            $this->logMessage('search', $sessionId, $version, LogManager::ERROR, 'Search query failed - Exception found: ' . $throwable->getMessage());
            $totalResponseTime = StopTimer('CoreHelpService::search');
            $this->metricCoreHelpServiceSearch->setResponseTime($totalResponseTime);
            $this->metricCoreHelpServiceSearch->publish();
            if ($throwable instanceof APIException) {
                $this->logMessage('search', $sessionId, $version, LogManager::DEBUG, 'Exception occurred with message: ' . $throwable->getMessage());
                throw $throwable;
            }
            throw (new APIException())->setAPIError(
                APIError::getInstance(APIErrorMessages::INTERNAL_SERVER_ERROR_INTERNAL_ERROR_0002, [])
            );
        }
        $this->logMessage('search', $sessionId, $version, LogManager::INFO, 'Search query done');
        $totalResponseTime = StopTimer('CoreHelpService::search');
        $this->metricCoreHelpServiceSearch->setResponseTime($totalResponseTime);
        $this->metricCoreHelpServiceSearch->publish();
        $this->logMessage('search', $sessionId, $version, LogManager::DEBUG, 'Returned data: ', $ret);
        return $ret;
    }

    /**
     * @param array $request
     * @param array $extraContext
     *
     * @return string[]
     */
    public function feedback(array $request, array $extraContext) : array
    {
        StartTimer('CoreHelpService::feedback');

        $version = $extraContext[APIConstants::API_VERSION_KEY];

        $ret = [];
        try {
            $id = $request['replyId'];
            $sessionId = $request['sessionId'];
            $rating = $request['rating'];
            $responseOption = $request['responseOption'] ?? [];
            $comment = $request['comment'] ?? '';
            $helpData = [
                "id" => $id,
                'sessionId' => $sessionId,
                "rating" => $rating,
                "responseOption" => $responseOption,
                "comment" => $comment
            ];
            $this->logMessage('feedback', $sessionId, $version, LogManager::INFO, 'Begin sending feedback');
            $accessToken = $this->getM2MSageIDToken($sessionId);
            $httpResponseCode = 0;

            $this->metricCoreHelpServiceFeedback->setId($id ?? '');
            $this->metricCoreHelpServiceFeedback->setSessionId($sessionId ?? '');
            $this->metricCoreHelpServiceFeedback->setAction($rating ?? '');
            if (!empty($comment)) {
                $this->metricCoreHelpServiceFeedback->setDescription($comment);
            }
            $ssResponse = $this->feedbackSemanticSearch($helpData, $accessToken, $httpResponseCode);

            if ($httpResponseCode === 204 || $httpResponseCode === 202 || $httpResponseCode === 200) {
                $ret['replyId'] = $id;
                $ret['sessionId'] = $sessionId;
            } else {
                $errorDetail = $ssResponse['message'] ?? $ssResponse['detail'] ?? '';
                $this->logMessage('feedback', $sessionId, $version, LogManager::ERROR, "Feedback failed with http $httpResponseCode, detail: $errorDetail");

                /* we do not throw an error for now because the semantic search will not record feedback changes on the same IDs
                throw (new APIException())->setAPIError(
                    APIError::getInstance(APIErrorMessages::INTERNAL_SERVER_ERROR_INTERNAL_ERROR_0002, [])
                );*/
                /* instead we reply with success because we were able to store the feedback in Kibana */
                $ret['replyId'] = $id;
                $ret['sessionId'] = $sessionId;
            }
        } catch (\Throwable $throwable) {
            $this->logMessage('feedback', $sessionId, $version, LogManager::ERROR, 'Feedback failed - Exception found: ' . $throwable->getMessage());
            $totalResponseTime = StopTimer('CoreHelpService::feedback');
            $this->metricCoreHelpServiceFeedback->setResponseTime($totalResponseTime);
            $this->metricCoreHelpServiceFeedback->publish();
            
            throw (new APIException())->setAPIError(
                APIError::getInstance(APIErrorMessages::INTERNAL_SERVER_ERROR_INTERNAL_ERROR_0002, [])
            );
        }
        $this->logMessage('feedback', $sessionId, $version, LogManager::INFO, 'Feedback sent');
        $totalResponseTime = StopTimer('CoreHelpService::feedback');
        $this->metricCoreHelpServiceFeedback->setResponseTime($totalResponseTime);
        $this->metricCoreHelpServiceFeedback->publish();
        return $ret;
    }

    private function generateOauthToken(): array
    {
        $headers = [
            'Accept: application/json',
            'Content-Type: application/json',
        ];

        $data = [
            'audience' => self::$semanticSearchOauthAudience,
            'grant_type' => "client_credentials",
            'client_id' => self::$s2sClient,
            'client_secret' => self::$s2sSecret
        ];
        $httpResponseCode = 0;
        $this->metricCoreHelpServiceAuthentication->startTime();
        $jsonResult = $this->sendPost(self::$semanticSearchOauthUrl, $data, $headers, $httpResponseCode);
        $this->metricCoreHelpServiceAuthentication->stopTime();
        if (!empty($httpResponseCode)) {
            $this->metricCoreHelpServiceAuthentication->setHTTPResponseCode($httpResponseCode);
        } else {
            logToFileWarning(__FILE__ . ':' . __LINE__ . ' - generateOauthToken() - HTTP Response Code is empty');
        }
        
        return json_decode($jsonResult, true);
    }

    /**
     * query semantic search service
     *
     * @param string $query
     * @param string $accessToken
     * @param string $sessionId
     *
     * @return array
     */
    private function querySemanticSearch(string $query, string $accessToken, string $sessionId): array
    {
        $headers = [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $accessToken,
        ];
        $userId = Globals::$g->_userid;
        $userLocale = explode('-', I18N::getLocale());
        $language = $userLocale[0];
        $country = $userLocale[1];
        $data = [
            "product_code" => "sage.intacct",
            "query" => $query,
            "include_metadata" => false,
            "top_k" => 5,                                   // sweet-spot as tested by D&D team
            "filter_relevance" => true,
            "top_r" => 5,                                   // sweet-spot as tested by D&D team
            "client_id" => self::$semanticSearchClientId,    // SS analytics are partitioned by this
            "user_id" => !empty($userId) ? $userId : "intact-dev-user",  // this is pointing to the current logged-in user
            "session_id" => $sessionId,
            "language" => $language,
            "country" => $country,
        ];

        $chatUrl = self::$semanticSearchUrl . '/api/rag';
        
        $httpResponseCode = 0;
        $this->metricCoreHelpServiceSearch->startTime();
        $jsonResult = $this->sendPost($chatUrl, $data, $headers, $httpResponseCode);
        $this->metricCoreHelpServiceSearch->stopTime();
        if (!empty($httpResponseCode)) {
            $this->metricCoreHelpServiceSearch->setHTTPResponseCode($httpResponseCode);
        } else {
            logToFileWarning(__FILE__ . ':' . __LINE__ . ' - querySemanticSearch() - HTTP Response Code is empty');
        }

        return json_decode($jsonResult, true);
    }

    /**
     * feedback call to semantic search service
     *
     * @param array $helpData
     * @param string $accessToken
     * @param int|null $responseHttpCode
     * @return array
     */
    private function feedbackSemanticSearch(array $helpData, string $accessToken, ?int &$responseHttpCode = null): array
    {
        $headers = [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $accessToken,
        ];

        $comment = join(',', $helpData['responseOption']) . ' | ' . $helpData['comment'];
        $data = [
            'id' => $helpData['id'],
            'session_id' => $helpData['sessionId'],
            'rating' => $helpData['rating'] ? 'helpful' : 'unhelpful',
            'comment' => $comment,
        ];

        $feedbackUrl = self::$semanticSearchUrl . '/api/feedback';

        $this->metricCoreHelpServiceFeedback->startTime();
        $jsonResult = $this->sendPost($feedbackUrl, $data, $headers, $responseHttpCode);
        $this->metricCoreHelpServiceFeedback->stopTime();
        if (!empty($responseHttpCode)) {
            $this->metricCoreHelpServiceFeedback->setHTTPResponseCode($responseHttpCode);
        } else {
            logToFileWarning(__FILE__ . ':' . __LINE__ . ' - feedbackSemanticSearch() - HTTP Response Code is empty');
        }

        return empty($jsonResult) ? [] : json_decode($jsonResult, true);
    }
    
    /**
     * @param string   $url
     * @param array    $data
     * @param array    $headers
     * @param int|null $responseHttpCode
     *
     * @return string
     */
    private function sendPost(string $url, array $data = [], array $headers = [], ?int &$responseHttpCode = null): string
    {
        $this->logMessage('sendPost', '', '', LogManager::DEBUG,  'url: '.$url);
        $this->logMessage('sendPost', '', '', LogManager::DEBUG,  'data:',$data);
        $this->logMessage('sendPost', '', '', LogManager::DEBUG,  'headers:', $headers);
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, true);

        if (in_array("Content-Type: application/json", $headers)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        } else {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Timeout in seconds
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        //Set the proxy for this component and URL
        $adaptProxy = new AdaptProxy();
        $adaptProxy->setProxy("CoreHelpService", $url);

        $response = curl_exec($ch);
        $responseHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        //Restore the default value of the Proxy
        $adaptProxy->resetProxy();
        
        curl_close($ch);
        
        return $response;
    }

    /**
     * @return string Sage ID token for the semantic search service
     */
    private function getM2MSageIDToken(string $sessionId) : string
    {
        StartTimer('CoreHelpService::getM2MSageIDToken');
        $response = [];
        $this->logMessage('getM2MSageIDToken', $sessionId, '', LogManager::INFO, 'Begin fetching token');
        try {
            $dao = AdtDaoFactory::getAdtDao(AbstractDataType::VOLATILE_DATA, StorageType::MONGO_DB);
            $documentId = sprintf("Core:Help:SageID:%s", Session::getKey() ?? 'generic');
            $responseStored =  $dao->readDocument($documentId) ?? [];
            $this->metricCoreHelpServiceAuthentication->setId($documentId);
            $this->metricCoreHelpServiceAuthentication->setSessionId($sessionId);
            if (is_string($responseStored)) {
                $response = json_decode(TwoWayDecryptWithKey($responseStored, 'IA_INIT'), true);
            } else {
                $response = $this->generateOauthToken();
                $responseToStore = TwoWayEncryptWithKey(json_encode($response), 'IA_INIT');
                $expiration = (((int) $response['expires_in'] ?? 28800)) - 100;
                $dao->setDocumentExpirationTime($expiration > 0 ? $expiration : 28700);
                $dao->storeDocument($documentId, $responseToStore);
            }
        } catch (\Throwable $throwable) {
            $this->logMessage('getM2MSageIDToken', $sessionId, '', LogManager::ERROR, 'Exception found: ' . $throwable->getMessage());
        }
        $totalTime = StopTimer('CoreHelpService::getM2MSageIDToken');
        $this->metricCoreHelpServiceAuthentication->setResponseTime($totalTime);
        $this->metricCoreHelpServiceAuthentication->publish();
        if (empty($response['access_token'])) {
            $this->logMessage('getM2MSageIDToken', $sessionId, '', LogManager::CRITICAL, 'Token not found');
            return '';
        } else {
            $this->logMessage('getM2MSageIDToken', $sessionId, '', LogManager::INFO, 'Token fetched');
            return $response['access_token'];
        }
    }
    
    /**
     * @param string $function
     * @param string $sessionId
     * @param string $apiVersion
     * @param string $logLevel
     * @param string $message
     *
     * @return void
     */
    private function logMessage(string $function, string $sessionId, string $apiVersion, string $logLevel, string $message, mixed $extraParams = null): void
    {
        $message = self::LOG_PREFIX . " | func: $function | v: $apiVersion | sid: $sessionId | $message";
        if($logLevel == LogManager::DEBUG && LogManager::getIsSlideEnabled() && LocalToGMT(date("m/d/Y H:i:s")) <= LogManager::getElevatedVerbosityActiveTime()) {
            if(!empty($extraParams) && is_array($extraParams)) {
                $message .= ' '.json_encode($extraParams);
            }
            logToFileDebug($message);
        } else {
            LogToFile($message, LOG_FILE, false, $logLevel);
        }
    }
}

