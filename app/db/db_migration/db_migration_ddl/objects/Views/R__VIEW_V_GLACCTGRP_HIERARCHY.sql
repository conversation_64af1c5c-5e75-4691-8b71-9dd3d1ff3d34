--OWNER_CONTACT:<EMAIL>,<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_V_GLACCTGRP_HIERARCHY.sql

CREATE OR REPLACE VIEW "V_GLACCTGRP_HIERARCHY" ("<PERSON>N<PERSON>#", "RECOR<PERSON>#", "GLA<PERSON><PERSON><PERSON><PERSON>KEY", "GLACCT<PERSON><PERSON><PERSON>ME", "G<PERSON><PERSON><PERSON><PERSON><PERSON>TITL<PERSON>", "GLACCTGRP<PERSON>ORMALBALANCE", "G<PERSON><PERSON>TGRPMEMBERTYPE", "G<PERSON><PERSON><PERSON><PERSON>PHOWCREATED", "<PERSON><PERSON><PERSON><PERSON>GRPLOCATIONKEY", "<PERSON>OUNTKE<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCOUNT<PERSON>ORMA<PERSON>BALANCE", "<PERSON>OUNTTYP<PERSON>", "ACCOUNTLOCATIONKEY") AS
with glgrp as (
    -- collect group of groups here
    SELECT distinct grpmem.child# leafchild
            , connect_by_root grp.CNY# CNY#
            , connect_by_root grp.RECORD# RECORD#
            , connect_by_root grp.name name
            , connect_by_root grp.title title
            , connect_by_root grp.normal_balance normal_balance
            , connect_by_root grp.membertype membertype
            , connect_by_root grp.howcreated howcreated
            , connect_by_root grp.melocationkey melocationkey
    FROM GLACCTGRPMEMBERSMST grpmem,
        GLACCTGRP grp
    WHERE grp.RECORD# = grpmem.PARENT#
            AND grp.CNY# = grpmem.CNY#
            AND grp.CNY# = sys_context('TMCtx', 'CNYNO')
            AND CONNECT_BY_ISLEAF = 1 -- get only leaf nodes
    START WITH grpmem.PARENT# = grp.RECORD#
            AND grpmem.CNY# = sys_context('TMCtx', 'CNYNO')
            AND grp.CNY# = sys_context('TMCtx', 'CNYNO')
    CONNECT BY PRIOR grpmem.CHILD# = grpmem.PARENT#
            AND grpmem.CNY# = sys_context('TMCtx', 'CNYNO')
    UNION
    -- collect group ranges here
    SELECT grp.record# leafchild
            , grp.CNY# CNY#
            , grp.RECORD# RECORD#
            , grp.name name
            , grp.title title
            , grp.normal_balance normal_balance
            , grp.membertype membertype
            , grp.howcreated howcreated
            , grp.melocationkey melocationkey
    FROM GLACCTGRP grp
    WHERE grp.CNY# = sys_context('TMCtx', 'CNYNO')
            AND EXISTS(SELECT 1 from GLACCTGRPRANGESMST grpmem where grp.RECORD# = grpmem.PARENTKEY
            AND grp.CNY# = grpmem.CNY#)
    UNION
    -- collect category groups here
    SELECT grp.record# leafchild
            , grp.CNY# CNY#
            , grp.RECORD# RECORD#
            , grp.name name
            , grp.title title
            , grp.normal_balance normal_balance
            , grp.membertype membertype
            , grp.howcreated howcreated
            , grp.melocationkey melocationkey
    FROM GLACCTGRP grp
    WHERE grp.CNY# = sys_context('TMCtx', 'CNYNO')
            AND EXISTS(SELECT 1 from COACATMEMBERSMST grpmem where grp.RECORD# = grpmem.PARENT#
            AND grp.CNY# = grpmem.CNY#)
)
SELECT
    grp.cny#,
    lower(
                grp.record#
                ||'-'
                ||ba.record#
        ) record#,
    grp.record#,
    grp.name,
    grp.title,
    grp.normal_balance,
    grp.membertype,
    grp.howcreated,
    grp.melocationkey,
    ba.record#,
    ba.acct_no,
    ba.title,
    ba.normal_balance,
    ba.account_type,
    ba.locationkey
FROM
    BASEACCOUNT ba,
    GLACCTGRPRANGES ga,
    glgrp grp
WHERE grp.leafchild = ga.PARENTKEY
        AND ga.cny# = grp.cny#
        AND ba.CNY# = ga.CNY#
        AND ba.ACCT_NO BETWEEN ga.RANGEFROM AND ga.RANGETO
UNION
SELECT
    grp2.cny#,
    lower(
                grp2.record#
                ||'-'
                ||ba2.record#
        ) record#,
    grp2.record#,
    grp2.name,
    grp2.title,
    grp2.normal_balance,
    grp2.membertype,
    grp2.howcreated,
    grp2.melocationkey,
    ba2.record#,
    ba2.acct_no,
    ba2.title,
    ba2.normal_balance,
    ba2.account_type,
    ba2.locationkey
FROM
    BASEACCOUNT ba2,
    COACATMEMBERS ga2,
    glgrp grp2
WHERE ba2.CNY# = ga2.CNY#
        AND ga2.CNY# = grp2.cny#
        AND ba2.CATEGORYKEY = ga2.CATEGORYKEY
        AND ga2.PARENT# = grp2.leafchild
;