<?php

/**
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 *
 * <AUTHOR> <<EMAIL>>
 * @desc      warehouseaisle.ent
 * @copyright 2000-2009 Intacct Corporation
 */
$kSchemas['warehouse'] = array(

    'children' => array(
        'parent' => array(
            'fkey' => 'parentkey', 'invfkey' => 'record#', 'table' => 'icwarehouse', 'join' => 'outer'
        ),
        'ccontact' => array('fkey' => 'contactkey', 'invfkey' => 'record#',
            'table' => 'contact', 'join' => 'outer',
            'children' => array(
                'caddress' => array('fkey' => 'mailaddrkey', 'table' => 'mailaddress', 'join' => 'outer',)
            ),
        ),
        'manageremp' => array('fkey' => 'employeekey', 'invfkey' => 'employeeid',
            'table' => 'employee', 'join' => 'outer',
            'children' => array(
                'mcontact' => array('fkey' => 'contactkey', 'invfkey' => 'record#',
                    'table' => 'contact', 'join' => 'outer',
                    'children' => array(
                        'maddress' => array('fkey' => 'mailaddrkey', 'table' => 'mailaddress')
                    ),
                ),
            ),
        ),
        'loc' => array(
            'fkey' => 'locationrefkey',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'location'
        ),
        'shipto' => array(
            'fkey' => 'shiptokey',
            'invfkey' => 'record#',
            'join' => 'outer',
            'table' => 'contactversion'
        ),
    ),


    'object' => array(
        'RECORDNO',
        'LOCATIONID',
        'NAME',
        'PARENTKEY',
        'PARENTID',
        'PARENTNAME',
        'WAREHOUSEID',
        'LOC.LOCATIONID',
        'LOC.RECORDNO',
        'MANAGER.NAME',
        'MANAGERID',
        'MANAGER.RECORDNO',
        'CONTACTINFO.CONTACTNAME',
        'CONTACTINFO.RECORDNO',
        'SHIPTO.CONTACTNAME',
        'SHIPTO.RECORDNO',
        'USEDINGL',
        'STATUS',
        'ENABLE_REPLENISHMENT',
        'ENABLENEGATIVEINV',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ),
    'publish' => array(
        'RECORDNO',
        'LOCATIONID',
        'NAME',
        'WAREHOUSEID',
        'LOC.LOCATIONID',
        'LOC.RECORDNO',
        'MANAGER.NAME',
        'MANAGERID',
        'MANAGER.RECORDNO',
        'CONTACTINFO.CONTACTNAME',
        'CONTACTINFO.RECORDNO',
        'SHIPTO.CONTACTNAME',
        'SHIPTO.RECORDNO',
        'USEDINGL',
        'STATUS',
        'ENABLE_REPLENISHMENT',
        'ENABLENEGATIVEINV',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ),

    'nexus' => array('LOC' => array('object' => 'location'
            , 'relation' => MANY2ONE
            , 'field' => 'LOC.locationID'
            , 'printas' => 'IA.WAREHOUSE_LOCATION'
        )
        , 'contact' => array('object' => 'contact'
            , 'relation' => MANY2ONE
            , 'field' => 'contactinfo.contactname'
            , 'dbalias' => 'ccontact'
        )
        , 'manager' => array('object' => 'employee'
            , 'relation' => MANY2ONE
            , 'field' => 'managerid'
            , 'dbalias' => 'manageremp'
        )
        , 'shipto' => array('object' => 'contact'
            , 'relation' => MANY2ONE
            , 'field' => 'shipto.contactname'
            , 'printas' => 'IA.SHIP_TO_CONTACT'
        ),
        'parent' => array(
            'object' => 'warehouse',
            'relation' => MANY2ONE,
            'field' => 'warehouseid',
            'printas' => 'IA.PARENT_WAREHOUSE'
        )
    ),

    'schema' => array(
        'LOCATIONID' => 'location_no',
        'NAME' => 'name',
        'PARENTKEY' => 'parentkey',
        'PARENTID' => 'parent.warehouseid',
        'PARENTNAME' => 'parent.name',
        'WAREHOUSEID' => 'warehouseid',
        'USEDINGL' => 'use_gl',
        'MANAGER' => array(
            'NAME' => 'mcontact.name',
            'employee.*' => 'manageremp.*',
        ),
        'MANAGERID' => 'manageremp.employeeid',
        'CONTACTINFO' => array(
            'CONTACTNAME' => 'ccontact.name',
            'contact.*' => 'ccontact.*',
            'MAILADDRESS' => array(
                'mailaddress.*' => 'caddress.*',
            )
        ),
        'LOC' => array(
            'LOCATIONID' => 'loc.location_no',
            'location.*' => 'loc.*',
        ),
        'SHIPTO' => array(
            'CONTACTNAME' => 'shipto.name',
            'contact.*' => 'shipto.*',
        ),
        'STATUS' => 'status',
        'ENABLE_REPLENISHMENT' => 'enable_replenishment',
        'ENABLENEGATIVEINV' => 'enablenegativeinv',
        'RECORDNO' => 'record#',
        'WHENMODIFIED' => 'whenmodified',
        'WHENCREATED' => 'whencreated',
        'MODIFIEDBY' => 'modifiedby',
        'CREATEDBY' => 'createdby',
    ),


    'ownedobjects' => array(
        array(
            'fkey' => 'LOCATIONID', // the field with which the owned object points to the parent
            'invfkey' => 'WAREHOUSEID',
            'entity' => 'warehouseaisle',
            'path' => 'AISLE_INFO'
        ),
        array(
            'fkey' => 'LOCATIONID', // the field with which the owned object points to the parent
            'invfkey' => 'WAREHOUSEID',
            'entity' => 'warehouserow',
            'path' => 'ROW_INFO'
        ),
        array(
            'fkey' => 'WAREHOUSEID', // the field with which the owned object points to the parent
            'invfkey' => 'WAREHOUSEID',
            'entity' => 'warehousebin',
            'path' => 'BIN_INFO'
        ),
        array(
            'fkey' => 'WAREHOUSEID', // the field with which the owned object points to the parent
            'invfkey' => 'WAREHOUSEID',
            'entity' => 'warehousebin',
            'path' => 'BIN_INFO_CLASSIC'    // we only use one of (BIN_INFO and BIN_INFO_CLASSIC); see warehouse manager.
        ),
    ),

    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED',
    ),

    'fieldinfo' => array(
        $gRecordNoFieldInfo,
        array(
            'fullname' => 'IA.WAREHOUSE_ID',
            'required' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 40,
                'format' => $gWarehouseIDFormat
            ),
            'showInGroup' => true,
            'desc' => 'IA.UNIQUE_IDENTIFIER',
            'path' => 'WAREHOUSEID',
            'renameable' => true,
            'id' => 1
        ),
        $gStatusFieldInfo,
        array(
            'fullname' => 'IA.NAME',
            'required' => true,
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 40,
                'format' => $gLocationNameFormat
            ),
            'desc' => 'IA.FREE_FORM_NAME_FOR_THIS_WAREHOUSE_LOCATION',
            'path' => 'NAME',
            'renameable' => true,
            'fastUpdate' => true,
            'id' => 2
        ),
        array(
            'fullname' => 'IA.LOCATION',
            'required' => false,
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'location',
                'pickentity' => 'locationpick',
                'maxlength' => 62,
                'format' => '/^.{1,62}$/'
            ),
            'showInGroup' => true,
            'desc' => 'IA.LOCATION',
            'path' => 'LOC.LOCATIONID',
            'renameable' => true,
            'id' => 3
        ),
        array(
            'fullname' => 'IA.MANAGER_ID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'employee',
                'pickentity' => 'employeepick',
            ),
            'showInGroup' => true,
            'hidden' => true,
            'desc' => 'IA.MANAGER_IN_CHARGE',
            'path' => 'MANAGER.NAME',
            'id' => 4
        ),
        array(
            'fullname' => 'IA.MANAGER_ID',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'employee',
                'pickentity' => 'employeepick',
                'maxlength' => 20,
            ),
            'desc' => 'IA.MANAGER_IN_CHARGE',
            'path' => 'MANAGERID',
            'id' => 5
        ),
        array(
            'fullname' => 'IA.CONTACT',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'contact',
                'maxlength' => 200,
                'format' => $gContactNameFormat
            ),
            'required' => false,
            'desc' => 'IA.CONTACT_EMPLOYEE',
            'path' => 'CONTACTINFO.CONTACTNAME',
            'renameable' => true,
            'id' => 6
        ),
        array(
            'fullname' => 'IA.SHIPTO_CONTACT',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'contact',
                'maxlength' => 200,
                'format' => $gContactNameFormat
            ),
            'required' => false,
            'desc' => 'IA.SHIP_TO_CONTACT',
            'path' => 'SHIPTO.CONTACTNAME',
            'id' => 7
        ),
        array(
            'fullname' => 'IA.USED_IN_GL',
            'required' => false,
            'type' => array(
                'ptype' => 'enum',
                'type' => 'boolean',
                'validvalues' => array('true', 'false'),
                '_validivalues' => array('T', 'F'),
                'validlabels' => array('IA.TRUE', 'IA.FALSE'),
            ),
            'default' => 'true',
            'desc' => 'IA.USED_IN_GL',
            'path' => 'USEDINGL',
            'id' => 8
        ),
        array(
            'fullname' => 'IA.PARENT_WAREHOUSE',
            'required' => false,
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'pickentity' => 'warehouselocationpick',
                'entity' => 'warehouse',
                'maxlength' => 50,
            ),
            'showInGroup' => true,
            'desc' => 'IA.PARENT_WAREHOUSE',
            'path' => 'PARENTID',
            'idw' => false,
            'id' => 9
        ),
        array(
            'fullname' => 'IA.WAREHOUSE_ID',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 40,
                'format' => $gWarehouseIDFormat
            ),
            'hidden' => true,
            'desc' => 'IA.UNIQUE_IDENTIFIER',
            'path' => 'LOCATIONID',
            'renameable' => true,
            'id' => 10
        ),
        array(
            'fullname' => 'IA.ENABLE_REPLENISHMENT_FOR_THIS_WAREHOUSE',
            'desc' => 'IA.ENABLE_REPLENISHMENT_FOR_THIS_WAREHOUSE',
            'required' => false,
            'type' => array(
                'ptype' => 'enum',
                'type' => 'boolean',
                'validvalues' => array('true', 'false'),
                '_validivalues' => array('T', 'F'),
                'validlabels' => array('IA.TRUE', 'IA.FALSE'),
            ),
            'default' => 'true',
            'path' => 'ENABLE_REPLENISHMENT',
            'id' => 11
        ),
        array(
            'fullname' => 'IA.ALLOW_NEGATIVE_INVENTORY_FOR_THIS_WAREHOUSE',
            'desc' => 'IA.ALLOW_NEGATIVE_INVENTORY_FOR_THIS_WAREHOUSE',
            'type' => $gBooleanType,
            'path' => 'ENABLENEGATIVEINV',
            'id' => 12
        ),
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ),

    'table' => 'icwarehouse',
    'vid' => 'WAREHOUSEID',
    'autoincrement' => 'RECORDNO',
    'showhierarchy' => true,
    'auditcolumns' => true,
    'module' => 'inv',
    'module_list' => ['inv', 'so', 'po'],
    'renameable' => true,
    'nochatter' => true,
    'printas' => 'IA.WAREHOUSE',
    'pluralprintas' => 'IA.WAREHOUSES',
    'customerp' => array(
        'SLTypes' => array(CUSTOMERP_SMARTLINKCLICK,
            CUSTOMERP_SMARTLINKVALIDATE,
            CUSTOMERP_SMARTLINKFETCH,
            CUSTOMERP_SMARTLINKWORKFLOW),
        'SLEvents' => array(CUSTOMERP_EVENT_ADD,
            CUSTOMERP_EVENT_SET,
            CUSTOMERP_EVENT_ADDSET,
            CUSTOMERP_EVENT_DELETE,
            CUSTOMERP_EVENT_CLICK),
        'AllowCF' => true),
    'api' => array(
        'PERMISSION_MODULES' => array('inv', 'so', 'po'),
        'PERMISSION_CREATE' => 'ALL',
        'PERMISSION_UPDATE' => 'ALL',
        'PERMISSION_DELETE' => 'ALL',
        'GETNAME_BY_GET' => true, // Use individual Get instead of GetList
        'GET_BY_GET' => true, // Use individual Get instead of GetList
        'ITEMS_ALIAS' => array('AISLEINFO', 'ROWINFO', 'BININFO'), // Items, as seen externally
        'ITEM_ALIAS' => array('WAREHOUSEAISLE', 'WAREHOUSEROW', 'WAREHOUSEBIN'), // Single item, as seen externally
        'ITEMS_INTERNAL' => array('AISLE_INFO', 'ROW_INFO', 'BIN_INFO'), // Items, as used by internal API
        'LESS_GET_FIELDS_NO_REPLENISHMENT' => [ 'ENABLE_REPLENISHMENT' ],
        'LESS_GET_FIELDS' => [ 'LOC.RECORDNO','MANAGER.RECORDNO','CONTACTINFO.RECORDNO', 'SHIPTO.RECORDNO'],
    ),
    'allowDDS' => true,
    'platformProperties' => array(
        FIELD_IS_GL_DIMENSION => true,
        FIELD_LOOKUP_TEMPLATE => '{!WAREHOUSEID!}--{!NAME!}'
    ),
    'description' => 'IA.INFORMATION_ABOUT_EACH_WAREHOUSE_DESC',
    'nameFields' => [ 'NAME', 'WAREHOUSEID' ],
    'upsertEntries' => true,
    'fastUpdate'  => true,
);



