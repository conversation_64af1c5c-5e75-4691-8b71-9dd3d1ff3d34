Global_run_event_hook_cmb2 = true;
Global_combo_array = new Array();
Global_combo_open = null;
Global_combo_changed = false;
Global_combo_debug = false;
Global_dirty_text_box = false;
Global_inside_add_handler = false;    //This is used to make sure that the we dont show the add confirmation popup more than once
Global_inside_find_handler = false;    //This is used as a check to before showing the add confirmation popup.
//If the user has clicked on the find or add then we will no show the popup in the
//change handler
Global_previous_value = '';

ComboBox2.LOADING_MESSAGE = '-- ' + GT('IA.LOADING_DATA') + ' --';
ComboBox2.PICKER_MESSAGE = '-- ' + GT('IA.SEARCH_IN_PICKER') + ' --';
ComboBox2.SHOW_MORE_MESSAGE = '-- ' + GT('IA.SHOW_MORE_PICKER') + ' --';
ComboBox2.ERROR_MESSAGE = '-- ' + GT('IA.PICKER_ERROR_FETCH') + ' --';
ComboBox2.LOAD_MORE_MESSAGE = '-- ' + GT('IA.LOADING_MORE') + ' --';
ComboBox2.OPTION_COMMAND = "COMMAND";
ComboBox2.PICKER_LINK_LABEL = GT('IA.FIND');
ComboBox2.ADD_LABEL = GT('IA.ADD');
ComboBox2.VIEW_LABEL = GT('IA.VIEW');
ComboBox2.SELECT_MOVING = 1;
ComboBox2.OPTGROUP_CLASS = "listselect_optgroup";

ComboBox2.prototype.make = function()
{
    // create the table for input text and button
    var ul = this.view;
    //var tbody = document.createElement('tbody');
    //var tr = document.createElement('tr');
    var td1;
    var td2;
    if (!this.allowStretchingInGridViews()) {
        td1 = document.createElement('li');
        td2 = document.createElement('li');
        ul.appendChild(td1);
        ul.appendChild(td2);
    } else {
        td1 = document.createElement('td');
        td1.className = 'combo-box-td';
        td1.style.backgroundColor = 'inherit';
        td2 = document.createElement('td');
        ul.appendChild(td1);
        ul.appendChild(td2);
        //td2.style.position ='relative';
    }

    td2.className = 'combo-image'; // we need a set a class here because IE doesn't support last-child in CSS

    this.listTouchTarget = null;

    //  Create the combobox text field.
    this.txtview = null;
    var addEventCall = false;
    if (this.eventcall) {
        try {
            this.txtview = document.createElement('<input '+this.eventcall+  '/>');
        } catch(e) { addEventCall=true; };
    }
    if (!this.txtview)
        this.txtview = document.createElement('input');


    // JS validation properties
    if(this.displayName) {
        this.txtview.setAttribute('displayName', this.displayName);
    }
    if(this.iaclass){
        this.txtview.setAttribute('iaclass', this.iaclass);
    }
    if(this.layerID){
        this.txtview.setAttribute('layerID', this.layerID);
    }
    if(this.layerTitle){
        this.txtview.setAttribute('layerTitle', this.layerTitle);
    }

    this.txtview.type = 'text';
    if(this.textSize != null) {
        this.txtview.size = this.textSize;
    }
    this.txtview.name = this.textName;
    this.txtview.id = this.textName;
    this.txtview.oname = this.name;
    this.txtview.className = "combo-input";
    this.txtview.setAttribute('autocomplete', 'off');
    this.txtview.setAttribute('data-index', this.i);

    this.txtview.isInSpecialDiv = this.isInSpecialDiv;
    this.txtview.fromComboBox = this;
    //this.txtview.onblur = ComboBox_txtview_blur;
    td1.appendChild(this.txtview);

    //  Add any 'extra' html - could be event handlers, disabled flag, etc.
    if (this.eventcall && addEventCall) {
        this.txtview.innerHTML = this.txtview.innerHTML + " " + this.eventcall;
    }

    jq(this.txtview).change(ComboBox2_txtview_onchange);
    jq(this.txtview).focus(ComboBox2_txtview_focus);
    jq(this.txtview).blur(ComboBox2_txtview_blur);

    // Create the ComboBox button.
    var tmp = document.createElement("span");
    tmp.id = 'span_'+this.textName;
    tmp.className = "buttons dropdownCB";
    tmp.tabIndex = -1;
    tmp.fromComboBox = this;
    var sp1;
    if ( !this.allowStretchingInGridViews() ) {
        // create container to hide full dropdown but still have events goto 'combo-image'.
        sp1 = document.createElement("span");
        addClassName(sp1, "combo-image-span");
        td2.appendChild(sp1);
    } else {
        sp1 = td2;
    }

    sp1.appendChild(tmp);
    /*
     if( this.assists )
     {
     var pickAssist = null;
     for( var ix = 0; ix < this.assists.length; ix++ )
     {
     var linkType = this.assists[ix].text;
     if( linkType == ComboBox2.PICKER_LINK_LABEL )
     {
     pickAssist = this.assists[ix];
     break;
     }
     }

     if( pickAssist )
     {
     var link = this.innerBuildAssists( [pickAssist], pickAssist.position, true);
     if( link && link.length > 0 )
     {
     link = link[0];
     while( link.childNodes.length > 0 )
     {
     link.removeChild(link.childNodes[0]);
     }
     sp1.appendChild(link);
     }
     }
     }
     */
    tmp.onclick = ComboButton_click;
};

ComboBox2.prototype.allowStretchingInGridViews = function() {
    return this.isInGridColumn;
};

ComboBox2.prototype.setDisabled = function(disabled)
{
    this.disabled = disabled;
    if( disabled ) {
        addClassName(this.view, 'disabled');
    }
    else {
        removeClassName(this.view, 'disabled');
    }
};

function ComboBox2_txtview_focus(event)
{
    if( this.fromComboBox.onfocus )
    {
        eval(this.fromComboBox.onfocus);
    }
    else
    {
        this.select();
    }
};

ComboBox2.prototype.remove = function()
{
    this.listclose();
    this.view = null;
    this.txtview = null;
    this.opslist = null;
};

ComboBox2.prototype.setCommandOption = function(option, specialOptionDescription)
{
    option.commandCallback = specialOptionDescription.callback;
    if (specialOptionDescription.css) {
        jq(option).css(eval(specialOptionDescription.css))
    }
    if (specialOptionDescription.displayAsHTML) {
        jq(option).html(jq(option).text());
    }
    if (specialOptionDescription.disabled) {
        jq(option).attr('disabled', true);
    }
};

ComboBox2.prototype.isCommandOption = function(option)
{
    return option.commandCallback !== undefined;
};

ComboBox2.prototype.processCommandOption = function(option) {
    if (option.commandCallback) {
        eval(option.commandCallback);
    }
}


ComboBox2.prototype.setIgnoredOption = function(option)
{
    this.ignoredOption = option;
};

ComboBox2.prototype.clearIgnoredOption = function()
{
    this.ignoredOption = null;
};

ComboBox2.prototype.ignoreOption = function(value, action)
{
    var ignoreOption = value == ComboBox2.LOADING_MESSAGE
        || value == ComboBox2.PICKER_MESSAGE
        || value == ComboBox2.SHOW_MORE_MESSAGE
        || value == ComboBox2.LOAD_MORE_MESSAGE
        || value == ComboBox2.ERROR_MESSAGE
        || (this.ignoredOption && value == this.ignoredOption);
    if ( ignoreOption && action == 'click' ) {
        if ( value == ComboBox2.SHOW_MORE_MESSAGE ) {
            this.showMore();
        }
    }

    return ignoreOption;
};

// Useful function to debug on the touch devices -- Need to move this to a more centralized place
function myPrintStackTrace(msg) {
    var callstack = [];
    var isCallstackPopulated = false;
    try {
        i.dont.exist+=0; //doesn't exist- that's the point
    } catch(e) {
        if (e.stack) { //Firefox
            var lines = e.stack.split('\n');
            for (var i=0, len=lines.length; i<len; i++) {
                if (lines[i].match(/^\s*[A-Za-z0-9\-_\$]+\(/)) {
                    callstack.push(lines[i]);
                }
            }
            //Remove call to printStackTrace()
            callstack.shift();
            isCallstackPopulated = true;
        }
        else if (window.opera && e.message) { //Opera
            var lines = e.message.split('\n');
            for (var i=0, len=lines.length; i<len; i++) {
                if (lines[i].match(/^\s*[A-Za-z0-9\-_\$]+\(/)) {
                    var entry = lines[i];
                    //Append next line also since it has the file info
                    if (lines[i+1]) {
                        entry += ' at ' + lines[i+1];
                        i++;
                    }
                    callstack.push(entry);
                }
            }
            //Remove call to printStackTrace()
            callstack.shift();
            isCallstackPopulated = true;
        }
    }
    if (!isCallstackPopulated) { //IE and Safari
        var currentFunction = arguments.callee.caller;
        while (currentFunction) {
            var fn = currentFunction.toString();
            var fname = fn;//.substring(fn.indexOf("function") + 8, fn.indexOf('')) || 'anonymous';
            callstack.push(fname);
            currentFunction = currentFunction.caller;
        }
    }
    output(msg, callstack);
}

function output(msg, arr) {
    //Optput however you want
    alert(msg + '\n====================\n' + arr.join('\n***************\n'));
}


ComboBox2.prototype.listclose = function ()
{
    if (this.opslist) {
        if ( document.activeElement && document.activeElement == this.opslist.selObj ) {
            this.txtview.focus();
        }
        this.opslist.style.display='none';

        /*
        DE11441 - 87309
        Remove the accesskey attribute for elements that are in the combobox header
        This is done because:
        - on some pages there are multiple comboboxes with the Find action ex: AR -> + Invoice
        - the find action has an accesskey="P" property
        - accesskey is handled by the browser and in Chrome/Firefox when the shortcut is used it is fired for the first matching DOM element
        - multiple elements in DOM with the same accesskey = bad behaviour when the find is fired for a different element than intended
        - when the list is opened a build() is called which removes and sets up everything again so the accesskey functionality will not be lost
     */
        if (this.name) {
            try {
                var elementsWitAccessKey = jq('#' + this.name.replace(/[!"#$%&'()*+,.\/:;<=>?@[\\\]^`{|}~]/g, "\\\$&") + 'lst > ul li *[accesskey]');
                if (elementsWitAccessKey) {
                    elementsWitAccessKey.removeAttr('accesskey');
                }
            }catch (e) {
                console.error(e);
            }
        }
    }
    Global_inside_find_handler = false;
    Global_combo_open = null;
};

function ComboBox2_txtview_onchange(evt) {
    //if(window.console) {
    //    window.console.log('change event called');
    //}
    evt = (evt || window.event);
    var targ = (evt.srcElement || evt.target);
    var fromComboBox = targ.fromComboBox;

    if(fromComboBox == null) {
        fromComboBox = window[targ.id];
        if(fromComboBox == null) {
            return;
        }
    }
    // BugId#41574 DWilks
    // On FF & Webkit if you type in the text field and then click in the list you first get
    // an onchange for the textbox, then an onchange for the list.  We want to ignore the first
    // onchange in favor of the second or some fields only use the partially entered data in the
    // first event rather than the actual element from the second.
    //
    // If we just (within the last few ms) received a mouse down event in the select list then
    // ignore this onchange event expecting the mouse to generate a select list change/click that
    // will explicitly trigger an onchange for this text view.
    //if ( false ) {
    if ( globalIs.ie == null && fromComboBox.txtview.meta !== undefined && fromComboBox.mouseDownInSelectList !== undefined ) {
        //This is ONLY if we are in the form editor
        delete fromComboBox.mouseDownInSelectList;
        if ( evt.stopImmediatePropagation ) {
            evt.stopImmediatePropagation();
        } else if ( evt.stopPropagation ) {
            evt.stopPropagation();
        } else {
            evt.cancelBubble = true;
        }
        if ( evt.preventDefault ) {
            evt.preventDefault();
        } else {
            evt.returnValue = false;
        }
    } else {
        fromComboBox.typedValue = null;
        var continuteWithCheck = true;
        var selectorDiv = document.getElementById('_c'+targ.id+'lst');
        if(selectorDiv == null) {
            selectorDiv = document.getElementById(targ.id+'lst');
        }
        if(selectorDiv) {
            if(selectorDiv.style.display == 'block') {
                continuteWithCheck = false;
            }
        }
        var checkForViewOrFind = false;
        var eventObject = evt.originalEvent != null ? evt.originalEvent : evt;
        if(eventObject) {
            if(eventObject.explicitOriginalTarget) {
                var lookupdata = eventObject.explicitOriginalTarget.data;
                if(lookupdata != null) {
                    if(lookupdata.indexOf('Find') != -1) {
                        checkForViewOrFind = true;
                    }
                }
            } else if(document.activeElement) {
                var lookupdata = document.activeElement.innerHTML;
                if(lookupdata) {
                    if(lookupdata.indexOf('Find') == 0) {
                        checkForViewOrFind = true;
                    }
                }
            }
        }
        if(!Global_inside_add_handler && !checkForViewOrFind && continuteWithCheck) {
            //window.console.log('change handler default condition');
            var elementId = fromComboBox.txtview.id;
            var textElement = document.getElementById(elementId);

            var isUDDPicker = false;

            // ptCode based platform picker will not have meta attribute on text field
            if ( fromComboBox.txtview.meta) {
                isUDDPicker = fromComboBox.txtview.meta.platform && fromComboBox.txtview.meta.isDimension;
            }

            var nameIdPair = false;
            if ( isUDDPicker ) {

                // New UDD picker
                nameIdPair = true;
            } else {

                // Normal platform picker i.e. ptCode based
                var divElement = document.getElementById('placeholder_'+elementId);
                var idElementName = null;
                if ( divElement ) {
                    var idElementName = divElement['idElementName'];
                    var idElement = document.getElementById(idElementName);
                    if ( idElement ) {
                        nameIdPair = true;
                    }
                }
            }

            if(nameIdPair && !isUDDPicker && textElement && fromComboBox && textElement.value != '') {
                //This section applies to picker with name id pair
                //this section will try to find the best match for the value

                var currentTextValue = textElement.value;
                var currentTextValueLowerCase = currentTextValue.toLowerCase();
                var currentOptions = fromComboBox.options;
                var isOptGroup = false;
                if (currentOptions.length > 0 && currentOptions[0]['<>'] != null) {
                    isOptGroup = true;
                }
                var found = false;
                var foundExactMatch = false;
                var looseMatch;
                if ( isOptGroup == false) {
                    for(var i=0;i<currentOptions.length;i++) {
                        var innerItem = currentOptions[i];
                        var specialOption = innerItem[2];
                        if (specialOption && specialOption.optionType == ComboBox2.OPTION_COMMAND) {
                            continue;
                        }
                        var innerItemValue = innerItem[0];
                        var innerItemValueLowerCase = innerItemValue.toLowerCase();

                        if(innerItemValue.indexOf(currentTextValue) == 0
                          || (!fromComboBox.prioritizeCaseSensitiveSearch && innerItemValueLowerCase.indexOf(currentTextValueLowerCase) == 0)) {
                            setIdElementValue(fromComboBox, innerItem);

                            textElement.value = innerItem[0];

                            found = true;
                            foundExactMatch = true;
                            break;
                        }
                        if(fromComboBox.prioritizeCaseSensitiveSearch  && looseMatch == null
                          && innerItemValueLowerCase.indexOf(currentTextValueLowerCase) == 0) {
                            looseMatch = innerItem;
                            found = true;
                        }
                    }
                    if (fromComboBox.prioritizeCaseSensitiveSearch && found && !foundExactMatch) {
                        setIdElementValue(fromComboBox, looseMatch);
                        textElement.value = looseMatch[0];
                    }
                } else {
                    //this search is inefficient but there is no other way to search through all
                    //option groups for the data as the data in this case is not sorted.
                    //the code will stop searching one it has found both first match and exact match entires
                    //the code will still go through all the entries if it cannot find an exact match
                    //we need to do that as the list is not sorted and exact match can be any where in the list
                    var filteredOptions = currentOptions;
                    var firstMatch = null;
                    var exactMatch = null;
                    for ( var i=0; i<filteredOptions.length; i++ ) {
                        var innerItem = filteredOptions[i];
                        var specialOption = innerItem[2];
                        if (specialOption && specialOption.optionType == ComboBox2.OPTION_COMMAND) {
                            continue;
                        }

                        var innerItemValue = innerItem[0];
                        innerItemValue = innerItemValue.toLowerCase();
                        if ( innerItemValue == currentTextValueLowerCase ) {
                            exactMatch = innerItem;
                        } else if (innerItemValue.indexOf(currentTextValueLowerCase) == 0) {
                            firstMatch = innerItem;
                        }

                        if ( exactMatch != null && firstMatch != null) {
                            break;
                        }
                    }

                    if ( exactMatch != null ) {
                        setIdElementValue(fromComboBox, exactMatch);
                        textElement.value = exactMatch[0];
                        found = true;
                    }

                    if ( firstMatch != null  && found == false ) {
                        setIdElementValue(fromComboBox, firstMatch);
                        textElement.value = firstMatch[0];
                        found = true;
                    }
                }
                if(!found) {
                    var assists = fromComboBox.assists;
                    var addAssist = null;
                    if(assists) {
                        for(var i=0;i<assists.length;i++){
                            var innerAssist = assists[i];
                            if(innerAssist.text == ComboBox2.ADD_LABEL) {
                                addAssist = innerAssist;
                                break;
                            }
                        }
                    }
                    if(!Global_inside_find_handler) {
                        if(addAssist != null) {
                            var result = ComboBox2_call_add_function(evt, textElement, addAssist);
                            if(result != true) {
                                textElement.value = '';
                                textElement.focus();
                                YAHOO.util.Event.preventDefault(evt);
                                sendTxtviewChangeEvent(fromComboBox);
                                return;
                            }
                        } else {
                            var valueForAlert = textElement.value;
                            textElement.value = '';
                            var localizedText = GT({ id: 'IA.PICKER_VALUE_NOT_PRESENT_LIST',
                                                       placeHolders: [{ name: 'VALUE_FOR_ALERT',
                                                                        value: valueForAlert }] });
                            alert(localizedText);
                            //this will clear all the values
                            setIdElementValue(fromComboBox,null);
                            YAHOO.util.Event.preventDefault(evt);
                            Global_inside_find_handler = false;
                            return;
                        }
                    }
                }
            } else if(fromComboBox.txtview && fromComboBox.txtview.meta && fromComboBox.txtview.value != '') {
                //clear error highlight if any
                fromComboBox.txtview.meta.clearErrorHighlight(true);
                //this section is for the form editor
                //window.console.log('change handler default');
                var currentTextValue =  fromComboBox.txtview.value;
                //currentTextValue = currentTextValue.toLowerCase();
                //first try to get using the complete value
                var returnValue = fromComboBox.txtview.meta.findPickerObject(currentTextValue);
                //if there is none then try to get the matching value

                if(returnValue == null) {
                    returnValue = fromComboBox.txtview.meta.findMatchingPickerObject(currentTextValue);
                }

                var pickerFieldId = fromComboBox.txtview.id;
                var pickerMeta = fromComboBox.txtview.meta;
                var gridPath = fromComboBox.txtview.meta.gridPath;
                var lineNo = fromComboBox.txtview.meta.getLineNo();

                //console.log(fromComboBox.txtview.meta.currentPickValues);
                if(returnValue == null) {
                    if(fromComboBox.options) {
                        var isLoading = false;
                        if((fromComboBox.txtview.meta.currentPickValues && fromComboBox.txtview.meta.currentPickValues.length > 0 && fromComboBox.txtview.meta.currentPickValues[0][0] == ComboBox2.LOADING_MESSAGE)) {
                            isLoading = true;
                        }
                        if((! isLoading && fromComboBox.options && fromComboBox.options.length == 1 && fromComboBox.options[0] == ComboBox2.LOADING_MESSAGE)) {
                            isLoading = true;
                        }
                        if( isLoading || fromComboBox.options.length < fromComboBox.serverCount || fromComboBox.serverCount < 0) {
                            fromComboBox.txtview.meta.fetchMorePickList(currentTextValue, function() {
                                //look at the arguments here and set the value
                                returnValue = pickerMeta.findPickerObject(currentTextValue);
                                if(returnValue == null) {
                                    returnValue = pickerMeta.findMatchingPickerObject(currentTextValue);
                                }
                                if(returnValue != null) {

                                    // fromComboBox may not exist if user has focused out of the current line on the grid
                                    if(fromComboBox && fromComboBox.txtview) {

                                        fromComboBox.txtview.value = returnValue[0];
                                        //this can be removed as the change bubble will handle it
                                        //sendTxtviewChangeEvent(fromComboBox);

                                    }

                                    if ( isUDDPicker ) {
                                        pt_InputControlPlatformPTR_ComboBox2_setValue( pickerFieldId, lineNo, gridPath,
                                            pickerMeta.pathPrefix, pickerMeta.autofillrelated, returnValue );
                                    }
                                } else if ( fromComboBox ) {
                                    //window.console.log('standard call here');
                                    var assists = fromComboBox.assists;
                                    var addAssist = null;
                                    if(assists) {
                                        for(var i=0;i<assists.length;i++){
                                            var innerAssist = assists[i];
                                            if(innerAssist.text == ComboBox2.ADD_LABEL) {
                                                addAssist = innerAssist;
                                                break;
                                            }
                                        }
                                    }
                                    if(fromComboBox.txtview && Global_previous_value != fromComboBox.txtview.value) {
                                        Global_previous_value = fromComboBox.txtview.value;
                                        if(!Global_inside_find_handler) {
                                            if(addAssist != null) {
                                                var result = ComboBox2_call_add_function(evt, textElement, addAssist);
                                                if(result != true) {
                                                    textElement.value = '';
                                                    Global_previous_value = '';
                                                    textElement.focus();
                                                    YAHOO.util.Event.preventDefault(evt);
                                                    sendTxtviewChangeEvent(fromComboBox);
                                                    return;
                                                }
                                            } else {
                                                var localizedText = GT({ id: 'IA.PICKER_VALUE_NOT_PRESENT_LIST',
                                                                           placeHolders: [{ name: 'VALUE_FOR_ALERT',
                                                                               value: textElement.value }] });
                                                alert(localizedText);
                                                YAHOO.util.Event.preventDefault(evt);
                                                textElement.value = '';
                                                return;
                                            }
                                        }
                                    }
                                }
                            });
                        } else if(!isLoading && fromComboBox && fromComboBox.options.length == fromComboBox.serverCount) {
                            //window.console.log('standard call here');
                            var assists = fromComboBox.assists;
                            var addAssist = null;
                            if(assists) {
                                for(var i=0;i<assists.length;i++){
                                    var innerAssist = assists[i];
                                    if(innerAssist.text == ComboBox2.ADD_LABEL) {
                                        addAssist = innerAssist;
                                        break;
                                    }
                                }
                            }
                            if(fromComboBox.txtview && Global_previous_value != fromComboBox.txtview.value) {
                                Global_previous_value = fromComboBox.txtview.value;
                                if(!Global_inside_find_handler) {
                                    if(addAssist != null) {
                                        var result = ComboBox2_call_add_function(evt, textElement, addAssist);
                                        if(result != true) {
                                            textElement.value = '';
                                            Global_previous_value = '';
                                            textElement.focus();
                                            YAHOO.util.Event.preventDefault(evt);
                                            sendTxtviewChangeEvent(fromComboBox);
                                            return;
                                        }
                                    } else {
                                        var localizedText2 = GT({ id: 'IA.PICKER_VALUE_NOT_PRESENT_LIST',
                                                                   placeHolders: [{ name: 'VALUE_FOR_ALERT',
                                                                       value: textElement.value }] });
                                        alert(localizedText2);
                                        textElement.value = '';
                                        YAHOO.util.Event.preventDefault(evt);
                                        textElement.value = '';
                                        return;
                                    }
                                }
                            }
                        }
                    }
                } else {
                    fromComboBox.txtview.value = returnValue[0];
                    if ( isUDDPicker ) {
                        var triggerAutofillNow = pickerMeta.autofillrelated;
                        var selControl = document.getElementById('_c' + fromComboBox.txtview.id + 'sel');

                        // TODO: selControl may not be available, investigate further
                        if ( selControl ) {
                            jq(selControl).val(returnValue[1]);

                            // If selControl is on the UI wait for fromComboBox.onchange to do autofill
                            triggerAutofillNow = false;
                        }

                        pt_InputControlPlatformPTR_ComboBox2_setValue( pickerFieldId, lineNo, gridPath,
                            pickerMeta.pathPrefix, triggerAutofillNow, returnValue );
                    }

                    Global_dirty_text_box = false;
                }
            }
        }

        //call the onchange if its set up
        if (fromComboBox.onchange) {
            eval(fromComboBox.onchange);
        }
    }

    Global_combo_changed = false;    // If the browser triggered the onchange event, make sure that we don't trigger another one in onblur. See comment on handleKey TAB.
};

function pt_InputControlPlatformPTR_ComboBox2_setValue( fieldId, lineNo, gridPath, pathPrefix, triggerAutofillNow,
                                                        returnValue )
{
    // Do not directly set in meta, go through getDataFromHTML
    InputControlPlatformPTR_setValue( fieldId, returnValue[1], returnValue[0] );

    var srcFieldName = fieldId;
    var n = fieldId.lastIndexOf("__");
    if(n != -1) {
        var prefix = fieldId.substr(0, n + 2);
        srcFieldName = fieldId.substr(n + 2, fieldId.length);
    }

    if ( lineNo != -1 ) {
        var grid = window.editor.view.findComponents(gridPath, 'Grid');
        if (!grid || !grid[0]) {
            return;
        }
        grid = grid[0];

        grid.value[ lineNo ][ srcFieldName ]  = returnValue[1];
        grid.value[ lineNo ][ srcFieldName  + '_disp' ] = returnValue[0];

    } else {
        // Outside grid

        var pickerMeta = window.editor.view.findComponents(srcFieldName, 'Field');
        if (!pickerMeta || !pickerMeta[0]) {
            return;
        }
        pickerMeta = pickerMeta[0];

        pickerMeta.parentValue[ srcFieldName ]  = returnValue[1];
        pickerMeta.parentValue[ srcFieldName  + '_disp' ] = returnValue[0];
        pickerMeta.value = returnValue[1];
    }

    if ( triggerAutofillNow ) {
        var isHeaderDefault = false;
        if(srcFieldName.indexOf('HEADER_') == 0) {
            srcFieldName = srcFieldName.substr(7);
            isHeaderDefault = true;
        }

        pt_InputControlPlatformPTR_defaultRelated(prefix, pathPrefix, isHeaderDefault, srcFieldName, gridPath, lineNo,
            returnValue[1], new Array());
    }
}

function ComboBox2_call_add_function (evt, textElement, addAssist) {
    Global_inside_add_handler = true;
    //confirm with user to call the add function
    var msg = GT({
                     "id": "IA.PICKER_VALUE_NOT_PRESENT_LIST_ADD",
                     "placeHolders": [
                         {"name": "VALUE_FOR_ALERT", "value": textElement.value}
                        ]
                 });
    var result = confirm(msg);
    //look up add assist and call the function
    if(result == true) {
        textElement.value = '';
        if(addAssist.func) {
            window.Global_std_add_called = true;
            if(typeof addAssist.func == 'function') {
                (addAssist.func)(evt, evt.target);
            } else {
                window[addAssist.func] (addAssist.script, addAssist.action, addAssist.module, addAssist.assistEntity, addAssist.pickEntity, addAssist.varname, addAssist.form, addAssist.layer,
                    addAssist.popupWidth, addAssist.popupHeight, addAssist.afterFunction, addAssist.context, addAssist.value);
            }
            Global_inside_add_handler = false;
            return true;
        }
    }
    Global_inside_add_handler = false;
    return false;
}

function ComboBox2_txtview_blur(evt) {
    //window.console.log('blur called');
    //window.console.log(Global_inside_find_handler);
    evt = (evt || window.event);
    var targ = (evt.srcElement || evt.target);
    var fromComboBox = targ.fromComboBox;
    if(fromComboBox == null) {
        fromComboBox = window[targ.id];
        if(fromComboBox == null) {
            return;
        }
    }

    if(fromComboBox) {
        if (!fromComboBox.opslist) {
            Global_inside_find_handler = false;
            return;        // Combobox list is not posted; e.g. layer tabbed out of.
        }
        if (fromComboBox.opslist && fromComboBox.opslist.ismoused) {
            evt.stopPropagation ? evt.stopPropagation() : evt.cancelButton = 1;
            if (evt.preventDefault) {
                evt.preventDefault();
            } else {
                Global_inside_find_handler = false;
                return false;
            }
        } else {
            debugMsg("Sending change event on blur close: " + Global_combo_changed);
            if (Global_combo_changed || Global_dirty_text_box) {
                Global_dirty_text_box = false;
                cmbMousedOut(fromComboBox.opslist);
                fromComboBox.listclose();
                sendTxtviewChangeEvent(fromComboBox);
            }
            Global_inside_find_handler = false;
            //cmbCloseAll(fromComboBox);
        }
        Global_inside_find_handler = false;
    }
};

function ComboButton_click(evt) {
    this.fromComboBox.typedValue = null;
    if ( ! this.fromComboBox.disabled ) {
        this.fromComboBox.toggle(evt);
        if (evt && evt.preventDefault) evt.preventDefault();
    }
    return false;
}

function ComboButton_click_outside(evt, combobox) {
    this.fromComboBox = combobox;
    this.fromComboBox.typedValue = null;
    if ( ! this.fromComboBox.disabled ) {
        //set the value from the text box to txtview.value
        var comboTextElement = document.getElementById(combobox.name);
        if(comboTextElement) {
            combobox.txtview.value = comboTextElement.value;
            combobox.typedValue = null;
        }
        this.fromComboBox.toggle(evt);
        if (evt && evt.preventDefault) evt.preventDefault();
    }
    return false;
}

ComboBox2.prototype.choose = function(txtval, textId) {
    if (this.txtview.value != txtval || textId !=null) {
        var textElement = document.getElementById(this.txtview.id);
        if(textElement) {
            textElement.value = txtval;
            this.txtview.value = txtval;
        } else {
            this.txtview.value = txtval;
        }
        this.typedValue = null;
        setIdElementValue(this);

        debugMsg("Sending change event on choose");
        sendTxtviewChangeEvent(this);
    }
};

function ComboBox2_mouseDown(e) {
    var len,el,i;
    el = e.target ? e.target : e.srcElement;
    while (el.nodeType != 1) el = el.parentNode;
    var elcl = el.className;
    if(elcl.indexOf("combo-")!=0) {
        len=Global_combo_array.length;
        for(i=0;i<len;i++) {
            var curobj = Global_combo_array[i];
            if(curobj.opslist) {
                curobj.opslist.style.display='none';
                Global_combo_open = null;
            }
        }
    }
};

function ComboBox2_handleKey(e) {
    //window.console.log(e.type + '   HANDLE KEY ' +  (new Date()).getTime());
    var key,obj,eobj,el,strname;
    eobj = e;
    key  = eobj.keyCode;

    var modKeyBits = eventModKeyBits(eobj);
    // skip over CTRL+* events - second part of the condition is for keyUp when one releases CTRL key
    if( modKeyBits == 1
        || (eobj.type == "keyup" && key == 17 && ! eobj.charCode ) ) return;

    el = e.target ? e.target : e.srcElement;
    while (el.nodeType != 1) el = el.parentNode;

    var comboBox = el.fromComboBox ? el.fromComboBox :
        ( el.comboBox ? el.comboBox: null );
    //try to get the combo box object from the placehoder div
    if(comboBox == null) {
        comboBox = window[el.id];
    }
    if((comboBox instanceof ComboBox2) == false ) {
        return;
    }

    var isCombo = comboBox != null ? true : false;

    if( isCombo && comboBox.disabled ) {
        return;
    }

    if (isCombo && eobj.type == "keydown" && key == 13) {
        comboBox.listclose();    // When enter key is pressed close the list
        if (e.preventDefault)
            e.preventDefault();
        return;
    }
    if (eobj.type == "keydown" && !(key == 9 || key == 40 || key == 38)) { return; }
    if (key == 16 || key == 18 || key == 35 || key == 36 || key == 37 ||  key == 39 ) { return; }

    if(isCombo == true) {
        strname = comboBox.name;
        obj = window[strname];
        if( ! obj ) return;

        switch (key) {
            case 9:        //TAB
                if (obj.opslist && (obj.opslist.style.display == 'block' || Global_combo_changed) && myCMBTrim(obj.txtview.value) != '') {
                    // Firefox generates onchange event inconsistently when hitting the TAB key. It sometimes generates the onchange event
                    // automatically sometimes it doesn't.  If it doesn't fire, then the _blur will fire the onchange event.  If it does
                    // fire, then the _onchange callback will get called and clear the Global_combo_changed flag so that _blur doesn't
                    // generate a second onchange event.
                    // The following simulates a TAB out by making sure that the mouse is out and the list is closed. Otherwise _blur thinks
                    // we are still active in the combo box.
                    cmbMousedOut(obj.opslist);
                    obj.listclose();

                    if (globalIs.ie) {
                        debugMsg("Sending change event on tab");
                        sendTxtviewChangeEvent(obj);
                    }

                } else if(obj.opslist && (obj.opslist.style.display == 'block')) {
                    cmbMousedOut(obj.opslist);
                    obj.listclose();
                }
                break;
            case 40:    // Down Arrow
                if (eobj.type == "keydown")    mvCMBDown(obj, e);
                break;
            case 38:    // Up Arrow
                if (eobj.type == "keydown")    mvCMBUp(obj, e);
                break;
            case 34:    //Pg Down
                pgCMBDn(obj);
                break;
            case 33:    //Pg Up
                pgCMBUp(obj);
                break;
            case 27:    //ESC
                obj.toggle(e);
                break;
            default:
                if (key == 13 && ((obj.opslist && obj.opslist.style.display=='none') || !obj.opslist)) { break;};
                if(myCMBTrim(el.value) != '') {
                    obj.txtview.value = myCMBTrim(el.value);
                } else {
                    obj.txtview.value = '';
                }
                Global_combo_changed = true;
                Global_dirty_text_box = true;

                if (key == 13 && obj.txtview.value == '') break;

                obj.expops.length=0;
                obj.selectedIndex=-1;
                //this section will re-popuplate the expops array
                var isLoading = obj.update(true);
                //use the current expops array to draw the list
                obj.build(e, obj.expops);

                var len = obj.expops.length;

                //this is the condition when user enter in the field while the data is being loaded
                //till the batch job finishes we want to show the loading data message
                if(obj.options.length == 1 && obj.options[0][0] == ComboBox2.LOADING_MESSAGE){
                    obj.expops = new Array();
                    obj.expops[obj.expops.length] = obj.options[0];
                    obj.build(e, obj.expops);
                    obj.opslist.style.display='block';
                    setIdElementValue(obj,null);
                    obj.resize();
                } else if ( ! len && ! isLoading )    {
                    if (document.getElementById('_changed')) {
                        SetFormChangedFlag();
                    }
                    obj.listclose();
                } else if( len == 1 && key != 8 && key != 46 && obj.expops[0][0] != ComboBox2.LOAD_MORE_MESSAGE) {
                    if(el.value == null) return;
                    var tval = (obj.ignoreCase)?(el.value).toLowerCase():el.value;
                    var tt = obj.expops[0][0];

                    if ( ! obj.typedValue ) {
                        obj.typedValue = obj.txtview.value;
                        var tx = tt.toLowerCase();
                        var ts = tx.split('--');
                        var tv = obj.typedValue.toLowerCase();
                        var tvl = tv.length;
                        var idx = -1;
                        if ( ts[0].substr(0, tvl) == tv ) {
                            idx = 0;
                        } else if ( ts.length > 1 && ts[1].substr(0, tvl) == tv ) {
                            idx = ts[0].length + 2; // id length + length of '--'
                        }
                        if ( idx >= 0 ) {
                            obj.typedPrefix = tt.substr(0, idx);
                            obj.typedSuffix = tt.substr(idx + obj.typedValue.length);
                        }
                    }

                    if(obj.ignoreOption(tt, '')) {
                        el.value = '';
                        return;
                    }

                    el.value=tt;
                    setIdElementValue(obj,obj.expops[0]);
                    alen = tval.length;
                    idpart = (obj.ignoreCase) ? obj.expops[0][0].toLowerCase().substring(0,alen) : obj.expops[0][0].substring(0,alen);
                    sIndex = obj.expops[0][0].indexOf('--');
                    oopart='';
                    if (sIndex>0) {
                        sIndex += 2;
                        oopart=(obj.ignoreCase) ? obj.expops[0][0].toLowerCase().substring(sIndex,sIndex+alen) : obj.expops[0][0].substring(sIndex,sIndex+alen);
                    }
                    var rbg, ren;
                    rbg = (tval==idpart)?tval.length:((tval==oopart) ? sIndex+tval.length : tval.length);
                    ren = (el.value).length;
                    setCmbSelectionRange(el,rbg,ren);
                    obj.listclose();
                    //sendTxtviewChangeEvent(obj);
                } else if (len == 0 && !isLoading) {
                    var currentValue = el.value;
                    var idIndex = currentValue.indexOf('--');
                    if(idIndex > 0){
                        var newValue = currentValue.substring(idIndex + 2);
                        el.value = newValue;
                        setIdElementValue(obj,null);
                    }
                } else {
                    obj.opslist.style.display='block';
                    setIdElementValue(obj,null);
                    obj.resize();
                    Global_combo_open = obj;
                }
                break;
        }
    }
};

function sendTxtviewChangeEvent(fromComboBox)
{
    Global_combo_changed = false;
    Global_dirty_text_box = false;
    baseSendUIEvent(fromComboBox.txtview, 'change');
}

function ComboBox2_handleFocusIn(event)
{
    var target = event.target ? event.target : event.srcElement;

    var skipObj = null;

    if ( target.fromComboBox ) {
        skipObj = target.fromComboBox;
    }
    if ( target.comboBox ) {
        skipObj = target.comboBox;
    }
    if ( target.parent && target.parent.comboBox ) {
        skipObj = target.parent.comboBox;
    }
    if ( target.parentNode && target.parentNode.fromComboBox ) {
        skipObj = target.parentNode.fromComboBox;
    }

    if ( Global_combo_open && ( ! skipObj || Global_combo_open != skipObj) ) {
        var curobj = Global_combo_open;
        if(curobj.opslist) {
            if (curobj.listTouchTarget == null) {
                try{
                    if(curobj.listclose() == 'undefined') {
                        curobj.listclose();
                        jq(curobj.txtview).blur();
                    }
                }catch(ex){}
            } else {
                curobj.listTouchTarget = null;
                Global_combo_open = null;
            }
        }
    }
    Global_combo_changed = false;
}

ComboBox2.prototype.appendMoreValues = function(allValues, serverCount, newValues, partialServerCount)
{
    if ( this.loadingMore > 0 ) {
        this.loadingMore--;
    }
    var scrollTop = this.opslist && this.opslist.style.display == 'block' ? this.opslist.selObj.scrollTop : -1;

    this.reset();
    this.populate(allValues, serverCount);
    this.refreshList(! this.showAll);

    if ( scrollTop > 0 ) {
        this.opslist.selObj.scrollTop = scrollTop;
    }
};


function ComboBox2_handleFocusIn2(event)
{
    var target = event.target ? event.target : event.srcElement;
    try{jq(target).closest('ul.combo-box').addClass('hasfocus');} catch(e){};
    try{jq(target).closest('tr.combo-box').addClass('hasfocus');} catch(e){};
};

function ComboBox2_handleFocusOut2(event)
{
    var target = event.target ? event.target : event.srcElement;
    try{jq(target).closest('ul.combo-box').removeClass('hasfocus');} catch(e){};
    try{jq(target).closest('tr.combo-box').removeClass('hasfocus');} catch(e){};
};

function ComboBox2_handleMouseDown(event)
{
    var target = event.target ? event.target : event.srcElement;
    try{jq(target).closest('ul.combo-box').find("INPUT.combo-input").focus();} catch(e){};
    try{jq(target).closest('tr.combo-box').find("INPUT.combo-input").focus();} catch(e){};
}

ComboBox2.prototype.getSelectedItem = function () {
    var selectedItem = null;
    var selectedIndex = this.selectedIndex;
    if(selectedIndex != -1) {
        selectedItem = this.expops[selectedIndex];
    } else {
        var textElement = document.getElementById(this.txtview.id);
        if(textElement.value != '') {
            for(var i=0;i<this.options.length;i++) {
                var cmItem = this.options[i];
                if(cmItem[0] == textElement.value) {
                    selectedItem = cmItem;
                    break;
                }
            }
        }
    }
    return selectedItem;
};

ComboBox2.prototype.update = function(canLoadMore)
{
    var opart,astr,alen,opln,boo;
    boo=false;
    opln = this.options.length;

    astr = (this.ignoreCase) ? this.txtview.value.toLowerCase() : this.txtview.value;

    if ( this.typedValue ) {
        // if the prefix is in the text box, cut it out
        var b = astr;
        if ( this.typedPrefix ) {
            var idx = b.indexOf(this.typedPrefix.toLowerCase());
            if ( idx == 0 ) {
                astr = this.txtview.value.substr(this.typedPrefix.length);
                this.txtview.value = astr;
                astr = this.ignoreCase ? astr.toLowerCase() : astr;
                this.typedValue = this.typedPrefix = null;
            }
        }
    }


    alen = astr.length;
    var displaylen = Math.min(this.options.length, this.listSize);
    var isLoading = false;
    if(alen == 0) {
        //window.console.log('UPDATE 1-1 ' + (new Date()).getTime());
        //window.console.log(opln);
        this.expops = new Array();
        for(var i = 0; i < opln; i++) {
            this.expops[this.expops.length] = this.options[i];
            boo = true;
        }
        boo = true;
        this.selectedIndex = -1;
        this.oselectedIndex = -1;
        //window.console.log('END UPDATE 1-1 ' + (new Date()).getTime());
    } else {
        //window.console.log('UPDATE 1 ' + (new Date()).getTime());
        //window.console.log(opln);
        var continueFromThisIndex = -1;
        for(var i = 0; i < opln; i++) {
            var specialOption = this.options[i][2];
            if (specialOption && specialOption.optionType == ComboBox2.OPTION_COMMAND) {
                continue;
            }

            var txtOption = this.options[i][0];
            if(this.ignoreCase) {
                txtOption = txtOption.toLowerCase();
            }

            opart= txtOption.substring(0,alen);
            oopart='';

            // Note: Separator '--' is valid for UDDs as well, does not matter if it is part of the text and is not
            // actually a separator

            sIndex = txtOption.indexOf('--');
            if (sIndex > 0) {
                sIndex += 2;
                oopart = txtOption.substring(sIndex,sIndex+alen);
            }

            if(astr == opart || astr == oopart) {
                boo=true;
                if(this.expops.length == displaylen + 1) {
                    continueFromThisIndex = i;
                    break;
                }
                if(this.serverCount > this.expops.length || this.serverCount == -1) {
                    this.expops[this.expops.length] = this.options[i];
                }
            }
        }
        //load rest of values with timeout
        if( continueFromThisIndex >= 0
            && this.listSize <= this.expops.length
            && (this.serverCount > this.expops.length || this.serverCount == -1)
            ) {
            this.currentAStr = astr;
            var context = { t: this, opln: opln, idx: continueFromThisIndex, astr: astr };
            window.setTimeout(function() {
                var currentInstance = context.t;
                addRemainingToExpops(
                    context.opln,
                    context.idx,
                    context.astr,
                    currentInstance
                );
                context = null;
            }, 0);
        }

        this.oselectedIndex = this.selectedIndex;

        // if we have at least 2 characters typed in the string, let's see if we need to fetch more data from server
        // this needs to be triggered if the length of options is less than the server count and if the best matching
        // pattern didn't fetch all the data from server
        callRetrieveValues = false;
        if ( canLoadMore && alen >= 2 && this.txtview.meta && this.expops.length < this.listSize ) {
            if ( this.options.length < this.serverCount ) {
                var matchingPattern = this.txtview.meta.findBestPickListMatch(astr);
                if ( ! matchingPattern
                    || matchingPattern.valuesCount < matchingPattern.serverCount
                    || matchingPattern.serverCount < 0 ) {
                    this.loadingMore++;
                    isLoading = true;
                    this.txtview.meta.fetchMorePickList(astr);
                } else {
                    callRetrieveValues = true;
                }
            } else {
                callRetrieveValues = true;
            }

            if(callRetrieveValues){
                target = this;
                isLoading = true;
                setTimeout( function() {
                    t = target;
                    if(target != null && target.txtview != null) {
                        target.txtview.meta.retrieveValues(function(values, serverCount) {
                            if (t != null && values.length > t.options.length ) {
                                t.loadingMore++;
                                t.appendMoreValues(values, serverCount, [], 0);
                            }
                            t = null;
                        });
                    }
                    target = null;
                }, 0);
            }
        }

        //window.console.log('END UPDATE 1 ' +  (new Date()).getTime());
    }
    if (!boo && this.validateInsert) {
        this.nop();
    }

    return isLoading;
};

function addRemainingToExpops(opln, displaylen, astr, obj)
{
    // currentAStr
    if(obj.expops.length == obj.options.length || obj.currentAStr != astr) {
        //this means that expops is completely populated
        return;
    }
    var alen = astr.length;
    var cnt = 0;
    for(var i=displaylen; i<opln; i++) {
        var txtOption = obj.options[i][0];
        if(obj.ignoreCase) {
            txtOption = txtOption.toLowerCase();
        }

        var opart= txtOption.substring(0,alen);
        var sIndex = txtOption.indexOf('--');
        var oopart='';
        if (sIndex > 0) {
            sIndex += 2;
            oopart = txtOption.substring(sIndex,sIndex+alen);
        }
        if(astr == opart || astr == oopart) {
            if ( obj.currentAStr == astr && obj.expops ) {
                obj.expops.push( obj.options[i] );
                cnt++;
                if ( cnt >= 500 ) {
                    var context = { len: opln, dl: i+1, astr: astr, obj: obj };
                    setTimeout( function() {
                        if ( context ) {
                            addRemainingToExpops(context.len, context.dl, context.astr, context.obj);
                            context = null;
                        }
                    }, 0);
                    return;
                }
            } else {
                return;
            }
        }
    }
}

ComboBox2.prototype.add = function() {
    var i,arglen;
    arglen=arguments.length;
    for(i=0;i<arglen;i++) {
        this.options[this.options.length]=arguments[i];
    }
};

ComboBox2.prototype.getOptions = function()
{
    return this.options;
};

ComboBox2.prototype.populate = function(values, serverCount)
{
    var i,arglen;
    arglen=values.length;
    if ( serverCount === undefined ) {
        serverCount = arglen;
    }
    for(i=0;i<arglen;i++) {
        obj = values[i];
        this.options[this.options.length]=obj;
    }
    this.serverCount = parseInt(serverCount);
};

ComboBox2.prototype.reset = function() {
    this.options = [];
    this.expops = [];
};

function cmbpopupclick(popup) {
    if (popup.options.selectedIndex < 0) {
        //can happen if you select in the white are below the --Loading data-- text.
        return;
    }
    var val = popup.options[popup.options.selectedIndex].rawText;
    if (val === undefined) {
        val = popup.options[popup.options.selectedIndex].text;
    }
    var id = popup.options[popup.options.selectedIndex].value;
    if( popup.fromComboBox.ignoreOption(val, 'click') ) {
        return;
    }
    if (popup.fromComboBox.isCommandOption(popup.options[popup.options.selectedIndex])) {
        popup.fromComboBox.processCommandOption(popup.options[popup.options.selectedIndex]);
        return;
    }
    popup.fromComboBox.selectedIndex = popup.options.selectedIndex;
    popup.fromComboBox.listclose();
    popup.fromComboBox.choose(val, id);
}

function cmbMousedIn(elem) {
    elem.ismoused = true;
}

function cmbMousedOut(elem) {
    elem.ismoused = false;
}

function createSpace()
{
    return document.createTextNode('\u00A0');
}

ComboBox2.prototype.activatePicker = function()
{
    if ( this.assists ) {
        for ( var ix = 0; ix < this.assists.length; ix++ ) {
            if ( ! this.assists[ix].action == 'list' ) {
                continue;
            }
            var a = this.assists[ix];

            this.txtview.focus();
            this.listclose();

            a.object.launchPicker({assist: a});
        }
    }
};

ComboBox2.prototype.showMore = function()
{
    if ( ! this.currentOptions ) {
        return;
    }

    var optionsCount = this.currentOptions.length;
    var displayCount = this.getListSize() - 1;
    var expectedDisplayCount = displayCount + window.comboPickSize;

    var localDisplayCount = Math.min(optionsCount, expectedDisplayCount);

    var scrollTop = this.opslist.selObj.scrollTop;

    var elementToSelect = null;

    // Remove show more
    this.removeLastListElement();

    if ( localDisplayCount >= 1 && displayCount < optionsCount ) {

        var ix = displayCount;

        // Remove the "forced" option and decrement the start index.
        if ( this.forcedOption ) {
            // Remove the forced option
            this.removeLastListElement();
            if ( this.forcedCategory ) {
                this.removeLastOptGroup();
            }
            ix--;
        }

        // Add the next group of options
        var lastOptGroup = this.currentOptions[ix-1]['<>'];
        for( ; ix < localDisplayCount; ix++ ) {
            var thisOptGroup = this.currentOptions[ix]['<>'];
            if ( lastOptGroup != thisOptGroup ) {
                this.addOptGroup(thisOptGroup);
                if ( thisOptGroup == this.forcedCategory ) {
                    this.forcedCategory = null;
                }
                lastOptGroup = thisOptGroup;
            }
            var nextOpt = this.addOption(this.currentOptions[ix][0], this.currentOptions[ix][1]);
            if ( this.forcedOption && this.forcedOption == this.currentOptions[ix][0] ) {
                // if we fond the forced option in the main list, it's not a forced option anymore :)
                this.forcedOption = null;
                this.forcedOptionId = null;
                this.forcedCategory = null;
                this.selectedIndex = ix;
                this.oselectedIndex = ix;
                elementToSelect = nextOpt;
            }
        }

        // Add the forced option back if we need to
        if ( this.forcedOption ) {
            if ( this.forcedCategory && this.forcedCategory != lastOptGroup ) {
                this.addOptGroup(this.forcedCategory);
            }
            elementToSelect = this.addOption(this.forcedOption, this.forcedOptionId);
            this.selectedIndex = this.oselectedIndex = ix;
        }

    }

    var astr = null;
    if ( ! this.showAll ) {
        astr = (this.ignoreCase) ? this.txtview.value.toLowerCase() : this.txtview.value;
    }
    // if we couldn't display as many more options as expected, let's see if there are more on the server
    if ( localDisplayCount < expectedDisplayCount ) {
        if ( this.options.length < this.serverCount ) {
            // we need to load more options from the server
            // probably load one more round of X records and filter them by the value
            // that means that i should have saved the filter!
            // we should also display the loading data message until we get more data from server
            this.addOption(ComboBox2.LOAD_MORE_MESSAGE, '');
            this.loadingMore++;
            if(this.txtview.meta) {
                this.txtview.meta.fetchMorePickList(astr);
            }
            this.listSize += window.comboPickSize;
        } else {
            this.listSize = this.getListSize();
        }
    } else {
        this.listSize = this.getListSize();
        var showMoreMsg = optionsCount > expectedDisplayCount;
        if ( ! showMoreMsg && this.options.length < this.serverCount ) {
            if ( ! this.showAll ) {
                if(this.txtview.meta) {
                    var matchingPattern = this.txtview.meta.findBestPickListMatch(astr);
                    if ( ! matchingPattern
                        || matchingPattern.valuesCount < matchingPattern.serverCount
                        || matchingPattern.serverCount < 0 ) {
                        showMoreMsg = true;
                    }
                } else {
                    showMoreMsg = true;
                }
            } else {
                showMoreMsg = true;
            }
        }

        if ( showMoreMsg ) {
            this.addOption(ComboBox2.SHOW_MORE_MESSAGE, '');
        }

        if ( ! elementToSelect ) {
            if ( this.selectedIndex >= 0 && this.selectedIndex < this.getListSize() ) {
                elementToSelect = this.getListElement(this.selectedIndex);
            }
        }
        if ( elementToSelect ) {
            this.setListElementSelected(elementToSelect);
        }
    }

    this.preventDropDownClose = true;
    //window.console.log('call to resize');
    this.resize();
    this.opslist.selObj.scrollTop = scrollTop;
};

ComboBox2.prototype.buildAssists = function(parentElement, assists, position)
{
    var links = this.innerBuildAssists(assists, position);
    if( !links ) return 0;

    var li = document.createElement('li');
    parentElement.appendChild(li);
    parentElement = li;

    for( var ix = 0; ix < links.length; ix++ )
    {
        parentElement.appendChild( createSpace() );
        parentElement.appendChild(links[ix]);
    }

    return links.length;
};

ComboBox2.prototype.innerBuildAssists = function(assists, position, noQuickViews)
{
    var textImageMap = ["redist", "date", "new", "view", "calc", "attach", "graph", "pick","edit", "view_attachment"];
    var assistFields = ['script', 'action', 'module', 'assistEntity', 'entity', 'popupWidth', 'popupHeight', 'context', 'onmouseover', 'onmouseout',  'filters'];

    var links = [];

    // these assist links are added at the end after all other
    var quickViews = [];

    if( assists && assists.length > 0 )
    {
        for( var ax = 0; ax < assists.length; ax++ )
        {
            var assist = assists[ax];

            if( assist.position != position ) continue;

            // let's fix the "undefined" assist values so we don't build funky stuf
            for( var tx = 0; tx < assistFields.length; tx++ )
            {
                if( assist[ assistFields[tx] ] == undefined ) assist[ assistFields[tx] ] = '';
            }

            assist.module = assist.module ? assist.module : editor.getModule();
            if( ! assist.layer ) assist.layer = '';
            assist.comboBox = this;

            var hrefContent;
            var hrefText = null;
            var addVertAlignBot = false;
            if ( ! assist.image ) {
                for( var ix = 0; ix < textImageMap.length; ix++ )
                {
                    if( textImageMap[ix] === assist.text ) {
                        assist.image = assist.text;
                        break;
                    }
                }
            }

            if( assist.image )
            {
                hrefContent = document.createElement('img');
                hrefContent.src = gCSSButtonPath + assist.image + '.png';
                hrefContent.alt = assist.text;
                hrefText = document.createTextNode(assist.text);
                // tihs should be handled in the CSS: hrefContent.border = 0;
            }
            else
            {
                hrefContent = document.createTextNode(assist.text);
                addVertAlignBot = true;
            }

            //add the quick view event triggers
            var onmouseover = null;
            var onmouseout = null;
            if( window.isQuickView && assist.text == ComboBox2.PICKER_LINK_LABEL && this.viewAllowed )
            {
                onmouseover = function(event, target)
                {
                    assist = target.assist;
                    DispQuickViewObject(assist.entity, assist.varname, assist.form, '', assist.value);
                };
                onmouseout = function(event, target)
                {
                    HideQuickViewObject();
                };
            }

            var a = document.createElement('a');
            a.title = assist.text;
            a.href = '#none';
            a.assist = assist;
            var onClickFunction = null;

            onClickFunction = function(event, target)
            {
                var a = target.assist;
                // this line is commented because:
                // open a dropdown that has an item with an access key in it, ex: AR -> Add Invoice -> Customer
                // press Alt + P => if this line is not commented then the list is hidden and shown again while the modal loads
                // a.comboBox.txtview.focus();
                a.comboBox.listclose();
                if(a.text == ComboBox2.ADD_LABEL) {
                    window.Global_std_add_called = true;
                    Global_inside_find_handler = true;
                    a.comboBox.txtview.value = '';
                    a.comboBox.typedValue = null;
                }

                if(a.action == 'list') {
                    Global_inside_find_handler = true;
                }

                if ( a.customlink )
                {
                    eval(a.customlink);
                }
                else if( typeof a.func == 'function' )
                {
                    (a.func)(event, target);
                }
                else
                {
                    window[a.func] ( a.script, a.action, a.module, a.assistEntity, a.pickEntity, a.varname, a.form, a.layer,
							a.popupWidth, a.popupHeight, a.afterFunction, a.context, a.value, a.filters );
                }
                //Global_inside_find_handler = false;
                return false;
            };

            //baseAddEventHandler(a, 'click' , onClickFunction);
            YAHOO.util.Event.addListener(a, "click", onClickFunction, a);
            if( onmouseover ) YAHOO.util.Event.addListener(this.txtview, "mouseover", onmouseover, a); //baseAddEventHandler(a, 'mouseover', onmouseover);
            if( onmouseout ) YAHOO.util.Event.addListener(this.txtview, "mouseout", onmouseout, a); //baseAddEventHandler(a, 'mouseout', onmouseout);

            this.setLinkAttributes( assist, a);
            if(addVertAlignBot) {
                //a.style.verticalAlign = 'bottom';
            }

            a.appendChild(hrefContent);
            if(hrefText != null) {
                a.appendChild(hrefText);
            }
            links.push(a);
            a = null;
            assist = null;
        }

        for( var qx = 0 ;qx < quickViews.length; qx++ )
        {
            links.push( quickViews[qx] );
        }
    }

    return links;
};

ComboBox2.prototype.setLinkAttributes = function(assist, element)
{
    var linkType = assist.text;
    var varname = assist.varname;
    // Skip pick/new/edit/calculator/calendar out of TAB  sequence
    // Assign hotkeys Alt+P only for pick/calc/date
    element.tabIndex = -1;
    var isPickType = linkType == ComboBox2.PICKER_LINK_LABEL;

    if( isPickType )
    {
        element.accessKey = 'P';
    }

    if ( varname )
    {
        if( isPickType )
        {
            var onFocusInFunction = function(event, target)
            {
                var el = typeof event.fromElement == "undefined" ? event.relatedTarget : event.fromElement;
                if( event.altKey && el )
                {
                    if ( target.assist.varname == el.name )
                    {
                        this.click();
                        this.blur();
                    }
                    else
                    {
                        el.focus();
                    }
                }
            };
            YAHOO.util.Event.addListener(element, "focusin", onFocusInFunction, element);
        }
    }
    else {
        if( isPickType )
        {
            var onFocusFunction = function(event, target)
            {
                if( ! event )
                {
                    alert('The event is not defined in focusin');
                    return;
                }

                if (event.altKey)
                {
                    this.click();
                    this.blur();
                };
            };
            YAHOO.util.Event.addListener(element, "focus", onFocusFunction, element);
        }
    }

    element.className = 'Pick';
    //element.style.verticalAlign = 'bottom';
    element.id = "fld" + varname;

    element = null;
    assist = null;
};

var cacheOption = null;
ComboBox2.prototype.addOption = function(text, value)
{
    var nextOpt;
    if (globalIs.touch) {
        nextOpt = document.createElement('li');
        nextOpt.textContent = text;
        nextOpt.iaValue = value;
        nextOpt.rawText = text;
        this.opslist.selObj.appendChild(nextOpt);
    } else {
        nextOpt = document.createElement('option');
        nextOpt.appendChild(document.createTextNode(text));
        nextOpt.setAttribute("value", value);
        nextOpt.rawText = text;
        this.opslist.selObj.appendChild(nextOpt);
    }

    return nextOpt;
};

ComboBox2.prototype.addOptGroup = function(label)
{
    // We're not adding options to the groups because 1) we don't like the indented look and
    // 2) it makes the showMore code simpler.
    var optGroup;
    if (globalIs.touch) {
        optGroup = document.createElement('li');
        addClassName(optGroup, ComboBox2.OPTGROUP_CLASS);
        optGroup.textContent = label;
        this.opslist.selObj.appendChild(optGroup);
    } else {
        optGroup = document.createElement('optgroup');
        optGroup.label = label;
        this.opslist.selObj.appendChild(optGroup);
    }

    return optGroup;
};

ComboBox2.prototype.build = function(evt, arr)
{
    var touch = globalIs.touch;
    var arrlen;
    arrlen = arr.length;
    var displayLen = Math.min(arrlen, this.listSize);
    var lstsize = displayLen + 1; // +1 because we might add one extra for the selected value
    lstsize = lstsize >= 20 ? 20 :  lstsize <= 1 ? 2 : lstsize;
    var cmboht = 0;
    var optgroups = 0;

    this.showAll = this.options.length == arr.length;
    this.forcedOption = null;
    this.forcedOptionId = null;
    this.forcedCategory = null;

    if(this.opslist){
        try{
            document.body.removeChild(this.opslist);
        } catch(ex){}

    }
    var oldList = document.getElementById(this.name+'lst');
    if( oldList ) document.body.removeChild(oldList);

    this.opslist = document.createElement("div");
    this.opslist.id = this.name+'lst';
    this.opslist.className = 'list';

    this.opslist.top = document.createElement("ul");

    var count = this.buildAssists(this.opslist.top, this.assists, 'top');
    if( count > 0 ) {
        cmboht += 20;
        this.opslist.appendChild(this.opslist.top);
    }


    var boo=false;
    if (touch) {
        this.opslist.selObj = document.createElement('ul');
    } else {
        this.opslist.selObj = document.createElement('select');
    }
    this.opslist.selObj.id = this.name+'sel';
    this.opslist.selObj.name = this.name+'sel';
    //window.console.log('START 1 '+ ' ' + displayLen + ' **** ' +  (new Date()).getTime());
    var txtValue = this.txtview.value;

    var elementId = this.txtview.id;
    var divElement = document.getElementById('placeholder_'+elementId);
    var idElementName = null;
    if(divElement) {
        idElementName = divElement['idElementName'];
    }
    //this is needed as platform adds two hidden elements to the editor
    //with the same id and call to getElementById returns the first element in
    //the list which didnt have the id
    var idElementValue = null;
    var idElementCollection = document.getElementsByName(idElementName);
    if(idElementCollection.length > 0) {
        for(var i=0;i<idElementCollection.length;i++) {
            var innerElement = idElementCollection[i];
            if(innerElement.id == idElementName) {
                idElementValue = innerElement.value;
                break;
            }
        }
    }

    var lastOptGroup = undefined;
    for (var i=0; i < displayLen; i++) {
        var optGroup = arr[i]['<>'];
        if ( optGroup != lastOptGroup ) {
            this.addOptGroup(optGroup);
            lastOptGroup = optGroup;
            optgroups++;
        }
        var idValue = arr[i][1] ? arr[i][1] : '';
        var textValue = arr[i][0];
        var specialOption = arr[i][2];
        var nextOpt = this.addOption(textValue, idValue);

        if (specialOption && specialOption.optionType == ComboBox2.OPTION_COMMAND) {
            this.setCommandOption(nextOpt, specialOption);
        }

        if(idElementValue != null) {
            if (!this.isIdBased) idValue = textValue;
            //this is needed for the name - id pair element, such as platform objects
            //elements needs to be selected by name and id combinition
            if (!boo && txtValue == textValue && idValue == idElementValue) {
                this.selectedIndex = this.oselectedIndex = i;
                this.setListElementSelected(nextOpt);
                boo=true;
            }
        } else {
            //this section is needed for the standard object which is not defined with id
            //the elements needs to be selected by name onli
            //this.selectedIndex = i;
            if (!boo && txtValue == textValue) {
                this.selectedIndex = this.oselectedIndex = i;
                this.setListElementSelected(nextOpt);
                boo=true;
            }
        }
    }

    //window.console.log('START 2 with arrlen = '+ arrlen + ' and time is ' + (new Date()).getTime());
    // we add the selected value here, if we haven't added it above
    if ( ! boo && this.txtview.value && displayLen < arrlen) {
        for (var i=displayLen; i < arrlen; i++) {
            if ( txtValue == arr[i][0]) {
                var thisOptGroup = arr[i]['<>'];
                if ( thisOptGroup != lastOptGroup) {
                    this.forcedCategory = thisOptGroup;
                    this.addOptGroup(thisOptGroup);
                    optgroups++;
                }
                var nextOpt = this.addOption(arr[i][0], arr[i][1]);
                this.setListElementSelected(nextOpt);
                displayLen++;
                this.forcedOption = txtValue;
                this.forcedOptionId = arr[i][1];
                this.selectedIndex = this.oselectedIndex = displayLen - 1;
                break;
            }
        }
    } else {
        this.forcedOption = null;
    }

    //window.console.log('END 1 '+  (new Date()).getTime());
    // if we haven't displayed all the data or if we haven't fetched all the data from
    // the server, we add the "show more" option
    // FIXME: in here we need to consider the case when we haven't loaded all the data
    // from the server but we have loaded all the data for the current typed text!
    var showMoreMsg = false;
    if ( ! this.loadingMore ) {
        showMoreMsg = displayLen < arrlen;
        if ( ! showMoreMsg && this.options.length < this.serverCount ) {
            if ( ! this.showAll ) {
                astr = (this.ignoreCase) ? this.txtview.value.toLowerCase() : this.txtview.value;
                if(this.txtview.meta) {
                    var matchingPattern = this.txtview.meta.findBestPickListMatch(astr);
                    if ( ! matchingPattern
                        || matchingPattern.valuesCount < matchingPattern.serverCount
                        || matchingPattern.serverCount < 0 ) {
                        showMoreMsg = true;
                    }
                } else {
                    showMoreMsg = true;
                }
            } else {
                showMoreMsg = true;
            }
        }
    } else {
        this.addOption(ComboBox2.LOAD_MORE_MESSAGE, '');
        lstsize++;
    }

    if( showMoreMsg ) {
        this.addOption(ComboBox2.SHOW_MORE_MESSAGE, '');
        this.currentOptions = arr;
        lstsize++;
    } else {
        this.currentOptions = null;
    }

    if (touch) {
        this.opslist.sel = document.createElement('div');
        this.opslist.sel.id = this.name+'seldiv';
        this.opslist.sel.className = 'listselect_dropdown';
        this.opslist.sel.appendChild(this.opslist.selObj);
        this.opslist.appendChild(this.opslist.sel);
    } else {
        this.opslist.appendChild(this.opslist.selObj);
    }

    if ( ! touch ) {
        // IE select.appendChild initializes selectedIndex to 0.
        // Chrome opslist.appendChild initializes selected index to 0.
        // Reset to -1 to ensure selection of first element works
        this.opslist.selObj.selectedIndex = this.selectedIndex;
    }

    lstsize += optgroups;
    lstsize = lstsize >= 20 ? 20 :  lstsize <= 1 ? 2 : lstsize;
    cmboht += lstsize*15 + 2;
    this.opslist.selObj.size = lstsize;

    if( this.assists && count < this.assists.length )
    {
        this.opslist.bottom = document.createElement("ul");
//        this.opslist.appendChild( document.createElement('br') );
        count = this.buildAssists(this.opslist.bottom, this.assists, 'bottom');
        if( count > 0 ) {
            cmboht += 20;
            this.opslist.appendChild(this.opslist.bottom);
        }
    }

    var left = 1;
    var top = 1;

    this.opslist.style.position='absolute';
    var defaultZIndex = (this.zIndex === undefined) ? 6 : this.zIndex;
    var trialXIndex = findEffectiveZIndex(document.getElementById(this.name), defaultZIndex);
    this.opslist.style.zIndex = (trialXIndex == defaultZIndex) ? trialXIndex : trialXIndex + 1;

    this.opslist.comboBox = this;

    left = cmbfindLeftOffset(this.txtview);

    var ctrlTop = cmbfindTopOffset(this.txtview);
    var ctrlpos = ctrlTop + this.txtview.clientHeight + 2;
    var cond = ctrlpos + cmboht > YAHOO.util.Dom.getViewportHeight() ? 1 : 0;

    if (cond)
    {
        top = ctrlTop - cmboht - 2;
        if( top < 2 ) top = 2;
    }
    else {
        top = ctrlpos;
    }

    this.opslist.style.left = left + 'px';
    this.opslist.style.top = top + 'px';

    this.opslist.style.display='none';
    document.body.appendChild(this.opslist);

    if (this.opslist.selObj == null)
        this.opslist.selObj = document.getElementById(this.name+'sel');


    if (touch) {
        YAHOO.util.Event.on(this.opslist.selObj, 'touchstart', ComboBox2OpsListSelObjTouchStart, this);
        YAHOO.util.Event.on(this.opslist.selObj, 'touchmove', ComboBox2OpsListSelObjTouchMove, this);
        YAHOO.util.Event.on(this.opslist.selObj, 'touchend', ComboBox2OpsListSelObjTouchEnd, this);
    } else {
        jq(this.opslist.selObj).change(ComboBox2OpsListSelObjChange);
        YAHOO.util.Event.on(this.opslist, 'click', ComboBox2OpsListClick, this);
        YAHOO.util.Event.on(this.opslist.selObj, 'click', ComboBox2OpsListSelObjClick, this);
    }
    this.opslist.selObj.onmousedown = ComboBox2OpsListSelObjMouseDown;
    this.opslist.selObj.onmouseup = function() {
        var comboBox = this.fromComboBox;
        if (comboBox.mouseDownInSelectList !== undefined ) {
            comboBox.mouseDownInSelectList = undefined;
        }
    };
    this.opslist.onmouseover = ComboBox2OpsListMouseOver;
    this.opslist.onmouseout = ComboBox2OpsListMouseOut;
    this.opslist.ismoused = false;
    this.opslist.selObj.fromComboBox = this;


    document.getElementById(this.name+'sel').tabIndex = -1;
    //  IE6 workaround.  Set value to empty so that onchange will be properly triggered.
    if (typeof this.opslist.selObj.value == 'undefined') {
        this.opslist.selObj.value = "";
    }

    //window.console.log('FINISH BUILD ' +  (new Date()).getTime());
};

function ComboBox2OpsListBlur(event, comboBox)
{
    closePopup(comboBox, true);
}

function ComboBox2OpsListSelObjClick(event, comboBox)
{
    if ( ! comboBox.preventDropDownClose ) {
        closePopup(comboBox, false);
    } else {
        comboBox.preventDropDownClose = false;
    }
}

function ComboBox2OpsListSelObjTouchStart(event, comboBox)
{
    comboBox.listTouchTarget = event.target;
    comboBox.listTouchTimeout = window.setTimeout ( function()
    {
        if (comboBox.listTouchTarget != ComboBox2.SELECT_MOVING) {
            if ( ! jq(event.target).is('.' + ComboBox2.OPTGROUP_CLASS) ) {
                addClassName(comboBox.listTouchTarget, 'listselect_selecting');
            }
        }
    } , 100 );
}

function ComboBox2OpsListSelObjTouchMove(event, comboBox)
{
    if (comboBox.listTouchTarget && comboBox.listTouchTarget != ComboBox2.SELECT_MOVING) {
        if (comboBox.listTouchTimeout) {
            window.clearTimeout(comboBox.listTouchTimeout);
        }
        removeClassName(comboBox.listTouchTarget, 'listselect_selecting');
        comboBox.listTouchTarget = ComboBox2.SELECT_MOVING;    // dummy object assignment, as long it is not null or event.target
    }
}

function ComboBox2OpsListSelObjTouchEnd(event, comboBox)
{
    // Don't send mouse click since that would be seen by a control beneath the combo box
    event.preventDefault();
    if (comboBox.listTouchTarget == event.target) {
        if (comboBox.listTouchTimeout) {
            window.clearTimeout(comboBox.listTouchTimeout);
        }
        comboBox.listTouchTarget = null;
        var val = comboBox.getListElementText(event.target);
        var id = comboBox.getListElementValue(event.target);
        var isCommand = comboBox.isCommandOption(event.target);
        if (isCommand
            || comboBox.ignoreOption(val, 'click')
            || jq(event.target).is('.' + ComboBox2.OPTGROUP_CLASS) ) {
            return;
        }
        addClassName(event.target, 'listselect_selecting');
        comboBox.choose(val, id);
        comboBox.listclose();
    }
}

function ComboBox2OpsListSelObjChange()
{
    cmbpopupclick(this);
}

function ComboBox2OpsListSelObjMouseDown()
{
    var comboBox = this.fromComboBox;
    comboBox.mouseDownInSelectList = true;
    setTimeout(function() {
        if ( comboBox.mouseDownInSelectList !== undefined ) {
            delete comboBox.mouseDownInSelectList;
        }
    }, 25);
}

function ComboBox2OpsListMouseOver()
{
    cmbMousedIn(this);
}

function ComboBox2OpsListMouseOut()
{
    cmbMousedOut(this);
}

function ComboBox2OpsListClick(event, combo)
{
    var targ = event.srcElement? event.srcElement : event.target;
    if( targ == combo.opslist )
    {
        if( combo.opslist.selObj ) combo.opslist.selObj.focus();
        else combo.txtview.focus();
    }
};

ComboBox2.prototype.refreshList = function(showFiltered)
{
    if ( this.opslist && this.opslist.style.display != "none" )
    {
        this.showList(null, showFiltered);
    }
};

ComboBox2.prototype.showList = function(evt, showFiltered)
{
    if ( Global_combo_open && Global_combo_open != this ) {
        Global_combo_open.listclose();
    }
    this.update();
    this.build(evt, showFiltered ? this.expops : this.options);
    //jq.browser.msie  && parseInt(jq.browser.version, 10) === 8
    if (jq.browser.msie  && parseInt(jq.browser.version, 10) === 8 ) {
        var that = this;
        window.setTimeout (function () {
            that.opslist.style.display="block";
            that.resize();
            Global_combo_open = that;
        }, 0);
    } else {
        this.opslist.style.display="block";
        this.resize();
        Global_combo_open = this;
    }
};

ComboBox2.prototype.toggle = function (evt)
{
    evt = (evt || window.event);

    // If text item is disabled, button click will not toggle control
    if (this.txtview.disabled)
        return;

    var focusObj = this.txtview;

    if (! this.opslist || this.opslist.style.display == "none" )
    {
        this.showList(evt);
        if (globalIs.touch) {
            focusObj = this.opslist.selObj;
        }
    }
    else
    {
        this.listclose();
    }

    focusObj.focus();

    this.expops.length=0;
    this.expops = new Array();
    if (this.expops.length != this.options.length) {
        var i;
        for (i=0;i<this.options.length ;i++ ) {
            this.expops[i] = this.options[i];
        }
    }
};

function ComboBox2()
{
    this.typedValue = null;

    if(arguments.length==0)
    {
        self.status="ComboBox invalid - no name arg";
    }

    this.name        = arguments[0];
    this.par        = arguments.length > 1 ? arguments[1] : null;
    this.ignoreCase = (arguments[2][0]["ignorecase"])?arguments[2][0]["ignorecase"]:1;
    this.validateInsert = (arguments[2][0]["validateinsert"])?arguments[2][0]["validateinsert"]:0;
    this.adjustWidth = (arguments[2][0]["adjustwidth"])?arguments[2][0]["adjustwidth"]:1;
    this.textName    = arguments[2][0]["textname"];
    this.elementNamePrefix = arguments[2][0]["elementNamePrefix"];
    this.entityname = (arguments[2][0]["entityname"])?arguments[2][0]["entityname"]:'entity';
    this.uicontrol    = arguments[2][0]["uicontrol"];
    this.onchange    = arguments[2][0]["onchange"];
    this.onfocus    = arguments[2][0]["onfocus"];
    this.eventcall    = arguments[2][0]["eventcall"];
    this.isInSpecialDiv = arguments[2][0]["isInSpecialDiv"];
    this.viewAllowed = arguments[2][0]["viewAllowed"];
    this.assists = arguments[2][0]["assists"];
    this.isInGridColumn = arguments[2][0]['isInGridColumn'];
    this.zIndex = arguments[2][0]['zIndex'];
    this.textSize = arguments[2][0]['textSize'];
    this.isIdBased = arguments[2][0]['isIdBased'];
    var forceListSize = arguments[2][0]['forceListSize'];
    this.addReturnOverride = arguments[2][0]['addReturnOverride'];
    this.i = arguments[2][0]['i'];
    this.prioritizeCaseSensitiveSearch = arguments[2][0]['prioritizeCaseSensitiveSearch'];

    if (!this.allowStretchingInGridViews()) {
        this.view        = document.createElement("ul");
    } else {
        this.view = document.createElement("tr");

        this.viewParentTable = document.createElement("table");
        this.viewParentTable.appendChild(this.view);
        this.viewParentTable.className  = "combo-box-table";

        //this.viewParentTableUL = document.createElement("ul");
        //this.viewParentTableUL.className ="combo-box-table-ul";
        //this.viewParentTableUL.appendChild(this.viewParentTable);

    }
    this.view.className  = "combo-box";
    this.disabled = false;

    if(arguments[2][0]["required"]) {
        this.view.setAttribute('displayName',arguments[2][0]["displayName"]);
        this.view.setAttribute('className','required');
    }

    // JS validation properties
    this.displayName = arguments[2][0]["displayName"];
    this.iaclass = arguments[2][0]["iaclass"];
    this.layerID = arguments[2][0]["layerID"];
    this.layerTitle = arguments[2][0]["layerTitle"];

    this.options    = new Array();
    this.expops        = new Array();
    this.value        = "";
    this.make();
    //this.txtview    = this.view.childNodes[0];
    this.txtview.size =    arguments[2][0]["size"];
    this.txtview.maxlength = arguments[2][0]["maxlength"];
    if (arguments[2][0]["textvalue"]) {
        this.txtview.value = arguments[2][0]["textvalue"];
        this.typedValue = null;
    }
    if( this.par )
    {

        if (!this.allowStretchingInGridViews()) {
            this.par.appendChild(this.view);
            this.fixParentWrap(this.par);
        } else {
            //this.par.appendChild(this.viewParentTableUL);
            this.par.appendChild(this.viewParentTable);
            this.fixParentWrap(this.par);
        }
    }

    this.selectedIndex = -1;
    this.oselectedIndex = -1;
    Global_combo_array[Global_combo_array.length]=this;
    if(Global_run_event_hook_cmb2){
        ComboBox2_init();
    }
    this.loadingMore = 0;
    if(window.comboPickSize) {
        if(forceListSize) {
            this.listSize = 10;
        } else {
            this.listSize = window.comboPickSize;
        }
    } else {
        window.comboPickSize = 10;
        this.listSize = window.comboPickSize;
    }

}

ComboBox2.prototype.fixParentWrap = function(par)
{
    if( ! par ) par = this.par;
    // If this combobox is in a TD (like most are), then set that TD to not allow wrapping.
    //  Otherwise, when the width is decreased, the combobox input and button can become wrapped.
    if (this.par && this.par.parentNode && this.par.parentNode.nodeName == 'TD') {
        this.par.parentNode.style.whiteSpace = "nowrap";
    }
};

ComboBox2.prototype.getView = function()
{
    if (!this.allowStretchingInGridViews()) {
        return this.view;
    } else {
        return this.viewParentTable;
    }
};

ComboBox2_init = function()
{
    YAHOO.util.Event.on( document, 'keyup', ComboBox2_handleKey );
    YAHOO.util.Event.on( document, 'keydown', ComboBox2_handleKey );
    YAHOO.util.Event.addListener(document.body, 'click', ComboBox2_handleFocusIn);
    YAHOO.util.Event.addListener(document.body, 'focusin', ComboBox2_handleFocusIn2);
    YAHOO.util.Event.addListener(document.body, 'focusout', ComboBox2_handleFocusOut2);
    YAHOO.util.Event.addListener(document.body, 'mousedown', ComboBox2_handleMouseDown);
    Global_run_event_hook_cmb2 = false;
};

function setCmbSelectionRange(input, selectionStart, selectionEnd)
{
    if (input.setSelectionRange)
    {
        input.focus();
        input.setSelectionRange(selectionStart, selectionEnd);
    }
    else if (input.createTextRange)
    {
        var range = input.createTextRange();
        range.collapse(true);
        range.moveEnd('character', selectionEnd);
        range.moveStart('character', selectionStart);
        range.select();
    }
}

function mvCMBDown(obj, e)
{
    if (e.preventDefault) e.preventDefault();
    e.returnValue = false;

    obj.typedValue = null;

    var textElement = document.getElementById(obj.name);
    if (!obj.opslist || obj.opslist.style.display=="none")
    {
        if(textElement) {
            obj.txtview.value = textElement.value;
            obj.typedValue = null;
            setIdElementValue(obj);
        }

        obj.toggle(e);
        return;
    }
    var cond = (obj.options.length!=obj.expops.length);
    //window.console.log(obj.selectedIndex);
    if(obj.opslist && obj.opslist.selObj && obj.opslist.selObj.options) {
        //this condition is needed if there is a show more message
        if(obj.opslist.selObj.options[obj.selectedIndex + 1]) {
            if(obj.opslist.selObj.options[obj.selectedIndex + 1].text == ComboBox2.SHOW_MORE_MESSAGE) return;
        } else {
            //this condition when the last element of the list is reached and there is no more move down possible
            if(obj.opslist.selObj.options.length == obj.selectedIndex + 1) return;
        }

    }
    if ( (!cond && obj.selectedIndex >= obj.options.length-1) || (cond && obj.oselectedIndex >= obj.expops.length-1)) return;
    obj.selectedIndex++;
    obj.oselectedIndex++;
    var value = obj.getNthListElementText(obj.selectedIndex);
    if( obj.ignoreOption(value, 'move')) {
        return;
    }
    while (obj.selectedIndex < obj.options.length && obj.isCommandOption(obj.getListElement(obj.selectedIndex))) {
        obj.selectedIndex++;
        obj.oselectedIndex++;
    }
    if(obj.selectedIndex >= obj.options.length) {
        return;
    }
    value = obj.getNthListElementText(obj.selectedIndex);

    if(e) {
        if(textElement) {
            textElement.value = value;
        }

        obj.txtview.value = value;
    } else {
        obj.txtview.value = value;
    }
    obj.typedValue = null;
    setIdElementValue(obj);

    Global_combo_changed = true;
    if (obj.opslist)
    {
        document.getElementById(obj.name+'sel').options.selectedIndex = (!cond)?obj.selectedIndex:obj.oselectedIndex;
    }
    obj.txtview.select();
}

function mvCMBUp(obj, e)
{
    if (e.preventDefault) e.preventDefault();
    e.returnValue = false;

    var textElement = document.getElementById(obj.name);
    var cond = (obj.options.length!=obj.expops.length);
    if (obj.selectedIndex <= 0 || (cond && obj.oselectedIndex <= 0))    return;
    obj.selectedIndex--;
    obj.oselectedIndex--;
    var value = (!cond)?obj.options[obj.selectedIndex][0] : obj.expops[obj.oselectedIndex][0];
    if( obj.ignoreOption(value, 'move')) {
        return;
    }

    while (obj.selectedIndex > 0 && obj.isCommandOption(obj.getListElement(obj.selectedIndex))) {
        obj.selectedIndex--;
        obj.oselectedIndex--;
    }
    if(obj.isCommandOption(obj.getListElement(obj.selectedIndex))) {
        return;
    }

    value = obj.getNthListElementText(obj.selectedIndex);

    if(e) {
        if(textElement) {
            textElement.value = value;
        }
        obj.txtview.value = value;

    } else {
        obj.txtview.value = value;

    }
    obj.typedValue = null;
    setIdElementValue(obj);

    Global_combo_changed = true;
    if (obj.opslist) {
        document.getElementById(obj.name+'sel').options.selectedIndex = (!cond)?obj.selectedIndex:obj.oselectedIndex;
    }
    obj.txtview.select();
}

function pgCMBUp(obj)
{
    var cond = (obj.options.length!=obj.expops.length);
    if (cond) return;
    if (obj.selectedIndex - 20 < 0)    {
        obj.selectedIndex = 0;
    } else {
        obj.selectedIndex -= 20;
    }
    var value = obj.options[obj.selectedIndex][0];
    if( obj.ignoreOption(value, 'move') ) {
        return;
    }

    while (obj.selectedIndex < obj.options.length && obj.isCommandOption(obj.getListElement(obj.selectedIndex))) {
        obj.selectedIndex++
    }
    if(obj.selectedIndex >= obj.options.length) {
        return;
    }
    value = obj.options[obj.selectedIndex][0];

    obj.txtview.value = value;
    obj.typedValue = null;
    setIdElementValue(obj);

    Global_combo_changed = true;
    if (obj.opslist) {
        document.getElementById(obj.name+'sel').options.selectedIndex = obj.selectedIndex;
    }
    obj.txtview.select();
}

function pgCMBDn(obj)
{
    var cond = (obj.options.length!=obj.expops.length);
    if (cond) return;
    if (obj.selectedIndex + 20 > obj.options.length) {
        obj.selectedIndex = obj.options.length - 1;
    } else {
        obj.selectedIndex += 20;
    }

    var value = obj.options[obj.selectedIndex][0];
    if( obj.ignoreOption(value, 'move') ) {
        return;
    }
    while (obj.selectedIndex > 0 && obj.isCommandOption(obj.getListElement(obj.selectedIndex))) {
        obj.selectedIndex--
    }
    if(obj.isCommandOption(obj.getListElement(obj.selectedIndex))) {
        return;
    }
    value = obj.options[obj.selectedIndex][0];

    obj.txtview.value = value;
    obj.typedValue = null;
    setIdElementValue(obj);

    Global_combo_changed = true;
    if (obj.opslist) {
        document.getElementById(obj.name+'sel').options.selectedIndex = obj.selectedIndex;
    }
    obj.txtview.select();
}

ComboBox2.prototype.nop = function()
{
    alert('" '+this.txtview.value + ' " - no such '+this.entityname+' exist !');
    this.txtview.value='';
    this.selectedIndex=-1;
};

ComboBox2.prototype.resize = function()
{
    var comboElement = document.getElementById(this.name);
    var elementWidth = jq(comboElement).width();
    var appendToSides = 5;
    if(comboElement) {
        var parentsCollection = jq(comboElement).parents();
        if(parentsCollection.length >0) {
            var firstParent = parentsCollection[0];
            if(!jq(firstParent).hasClass('combo-box-td')) {
                appendToSides = 22;
            }
        }
    }
    if(elementWidth == null) {
        elementWidth = this.txtview.clientWidth;
        appendToSides = 22;
    }

    var selectWidth = jq(document.getElementById(this.name+'sel')).width();
    if(selectWidth < elementWidth) {
        w = elementWidth + appendToSides;
        var list = document.getElementById(this.name+'lst');
        if (list) {
            w = Math.max(w, list.clientWidth);
        }
        document.getElementById(this.name+'sel').style.width = (w) + 'px';
        selectWidth = (w);
    }

    if(selectWidth < 155) {
        document.getElementById(this.name+'sel').style.width = '155px';
    }

    var offset = jq(comboElement).offset();
    //this applies only to platform element or elements used outside form editor
    if(offset == null) {
        offset = jq(this.txtview).offset();
    }

    if(offset != null) {
        var elementTop = offset.top;
        var elementLeft = offset.left;
        this.opslist.style.left =(elementLeft)+'px';
        this.opslist.style.top = (elementTop + 20) + 'px';
        var clientHeight = document.documentElement.clientHeight;
        var scrollTop = document.documentElement.scrollTop;
        var isFormEditor = false;
        var isOldEditor = false;
        var isNonEditor = false;
        try{
            //this is a check for the new editor
            if(editor != null && editor instanceof FormEditor) {
                isFormEditor = true;
            }
        }catch(ex) {
            try {
                //this is a check for the old editor
                var internalCheck = gEditState;
                isOldEditor = true;
            }catch(exp) {
                for(var t=0; t<parentsCollection.length;t++) {
                    var currentParent = parentsCollection[t];
                    if(jq(currentParent).hasClass('wizardModal')) {
                        isNonEditor = true;
                        break;
                    }
                }
            }
        }
        var selectorHeight = jq(document.getElementById(this.name+'sel')).height();//this.opslist.scrollHeight;
        if(selectorHeight < 100) {
            //var lineCount = this.expops.length;
            var lineCount = jq(document.getElementById(this.name+'sel'))[0].children.length; //get opttioons and otion groups
            if(lineCount) {
                if(lineCount < 10 && scrollTop == 0 && jq.browser.msie && isOldEditor) {
                    lineCount = 10;
                }
                var newHeight = (lineCount + 1) * 15;
                if(newHeight > 200) {
                    newHeight = 300;
                }

                jq(document.getElementById(this.name+'sel')).height(newHeight);
                selectorHeight = newHeight;
            }
        }
        var addToBottom = scrollTop;
        var topAdjustment = 0;
        if(isFormEditor) {
            topAdjustment = 25;
        } else {
            if(isNonEditor) {
                topAdjustment = 26;
            } else if(isOldEditor) {
                if(jq.browser.msie) {
                    topAdjustment = 30;
                } else {
                    topAdjustment = 35;
                }
            }
            if(topAdjustment == 0) {
                topAdjustment = 20;
            }
        }
        //this section hold true for old editors only
        if(scrollTop == 0 && isOldEditor) {
            scrollTop = document.body.scrollTop;
            if(clientHeight == 0) {
                clientHeight = jq(document).height();
            }
            addToBottom = 0;
        }
        if(scrollTop == 0 && jq.browser.webkit) {
            var innerScroll = document.body.scrollTop;
            clientHeight = clientHeight + innerScroll;
        }
        var availableTop = elementTop - scrollTop - topAdjustment;
        var availableBottom = clientHeight - elementTop + addToBottom + topAdjustment;
        var finalTop = elementTop;
        var showOnBottom = true;
        if(availableTop > availableBottom && (availableBottom - 65) < selectorHeight && !isOldEditor) {
            showOnBottom = false;
        }
        var adjustedSelectorHeight = Math.max(availableTop, availableBottom);

        if(adjustedSelectorHeight - 65 < selectorHeight) {
            selectorHeight = adjustedSelectorHeight - 65;
            jq(document.getElementById(this.name+'sel')).height(selectorHeight);

        }

        if(!showOnBottom) {
            finalTop = elementTop - selectorHeight - topAdjustment;
            this.opslist.style.top = (finalTop) + 'px';
        }
    }
};

ComboBox2.prototype.getComboValue = function(item,idOrValue) {
    var selectedIndx = document.getElementById(this.name+'sel').options.selectedIndex;
    var comboBoxItem = null;
    if(this.expops.length >= selectedIndx) {
        comboBoxItem = this.expops[selectedIndx];
        if(comboBoxItem) {
            if(!item) {
                if(idOrValue) {
                    return comboBoxItem[1];
                } else {
                    return comboBoxItem[0];
                }
            } else {
                return comboBoxItem;
            }
        }
    }
};

ComboBox2.prototype.getListElement = function(index)
{
    if ( globalIs.touch ) {
        var $options = jq('li', this.opslist.selObj).not('.'+ComboBox2.OPTGROUP_CLASS);
        return $options[index];
    } else {
        return this.opslist.selObj.options[index];
    }
};

ComboBox2.prototype.getNthListElementText = function(index)
{
    if ( globalIs.touch ) {
        return this.opslist.selObj.children[index].textContent;
    } else {
        var val = this.opslist.selObj.options[index].rawText;
        if ( val == undefined ) {
            val = this.opslist.selObj.options[index].text;
        }
        return val;
    }
};

ComboBox2.prototype.getListElementText = function(el)
{
    var result = el.rawText;
    if (result === undefined) {
        if ( globalIs.touch ) {
            result =  el.textContent;
        } else {
            result =  el.text;
        }
    }
    return result;
};

ComboBox2.prototype.getListElementValue = function(el)
{
    if ( globalIs.touch ) {
        return el.iaValue;
    } else {
        return el.value;
    }
};

ComboBox2.prototype.getListSize = function()
{
    if ( globalIs.touch ) {
        // Emulate select list optgroups
        var $optGroups = jq('.'+ComboBox2.OPTGROUP_CLASS, this.opslist.selObj);
        return this.opslist.selObj.children.length - $optGroups.length;
    } else {
        return this.opslist.selObj.length;
    }
};

ComboBox2.prototype.setListElementSelected = function(el)
{
    if ( globalIs.touch ) {
        addClassName(el, 'listselect_selected');
    } else {
        el.selected = "1";
    }
};

ComboBox2.prototype.removeLastListElement = function()
{
    if ( globalIs.touch ) {
        var selObj = this.opslist.selObj;
        selObj.removeChild(selObj.children[selObj.children.length-1]);
    } else {
        this.opslist.selObj.remove(this.opslist.selObj.length-1);
    }
};


ComboBox2.prototype.removeLastOptGroup = function(index)
{
    if ( globalIs.touch ) {
        var $optGroups = jq('li.listselect_optgroup', this.opslist.selObj);
        if ( $optGroups.length > 0 ) {
            this.opslist.selObj.removeChild($optGroups[$optGroups.length-1]);
        }
    } else {
        var optGroups = this.opslist.selObj.getElementsByTagName('optgroup');
        if ( optGroups.length > 0 ) {
            this.opslist.selObj.removeChild(optGroups[optGroups.length-1]);
        }
    }
};



//ComboBox2.prototype.returnFunctionAdd() {

//}

function cmbfindLeftOffset(elem)
{
    return jq(elem).offset().left;
}

function cmbfindTopOffset(elem)
{
    var top = 0;
    while (elem) {
        top += elem.offsetTop;
        elem = elem.offsetParent;
    }
    return top;
}

function findTopOffsetinDiv(elem) {
    var top = 0;
    while (elem.tagName != "DIV" && elem.tagName != 'BODY' && elem.tagName != 'HTML') {
        top += elem.offsetTop;
        elem = elem.offsetParent;
    }
    return top;
}

function isInDiv(elem) {
    while (elem && elem.tagName != "BODY" && elem.tagName != "HTML") {
        elem = elem.offsetParent;
        if (elem.tagName == "DIV") return true;
    }
    return false;
}

function cmbCloseAll(fromCtrl)
{
    if (fromCtrl && document.activeElement && document.activeElement.id == fromCtrl+'sel')
    {
        return;
    }
    len=Global_combo_array.length;
    var i;
    for(i=0;i<len;i++) {
        var curobj = Global_combo_array[i];
        if(curobj.opslist) {
            try{
                if(curobj.listclose !== undefined) {
                    curobj.listclose();
                }
            } catch(ex){}
        }
    }
}

function myCMBTrim(value) {
    var temp = value;
    if(temp == null) {
        temp = '';
    }
    var obj = /^(\s*)([\W\w]*)(\b\s*$)/;
    if (obj.test(temp)) { temp = temp.replace(obj, '$2$3'); }
    if (temp == " ") { temp = ""; }
    return temp;
}

function closePopup(comboBox, isBlur)
{
    cmbCloseAll();
}

function debugMsg(s)
{
    if (Global_combo_debug) {
        if(window.console) {
            window.console.log(s);
        }
    }
}

//This section adds a combo box to the element.
function attachComboBoxToElement(elementName, options) {
    var assists = [];
    var assist1 = getBaseAssits();
    var textValue = '';
    if(options['dataValue']) {
        textValue = options['dataValue'];
    }

    // If is UDD and is outside window shade, show value as soon as platform picker is loaded
    if ( !textValue ) {
        uddHidden = document.getElementById(elementName);
        if ( uddHidden && uddHidden.meta ) {
            if ( uddHidden.meta.isDimension && uddHidden.meta.isInGridColumn ) {
                uddDisp = document.getElementById(elementName+'_disp');
                if ( uddDisp && uddDisp.value ) {
                    textValue = uddDisp.value;
                }
            }
        }
    }

    if(options['elementNamePrefix'] == undefined) {
        options['elementNamePrefix'] = 'txt';
        elementNamePrefix = 'txt';
    } else {
        elementNamePrefix = options['elementNamePrefix'];
    }

    var addFunction = options ['addFunction'];
    if(addFunction) {
        var assist3 = getBaseAssits();
        if ( assist3 )
        {
            assist3.position = 'top';
            assist3.text = ComboBox2.ADD_LABEL;
            assist3.image = 'add_new';
            assist3.func = function (event, target) {
                if(addFunction) {
                    Global_inside_find_handler = true;
                    var textElement = '';
                    if(this.comboBox) {
                        textElement = document.getElementById(this.comboBox.name);
                        if(textElement) {
                            textElement.value = '';
                        }
                    }
                    event.returnValue = false;
                    addFunction.call();
                }
            },
                assists.push(assist3);
        }
    }

    if(options['viewURL'] || options['viewFunction']) {
        var assist2 = getBaseAssits();
        if ( assist2 )
        {
            assist2.position = 'top';
            assist2.text = ComboBox2.VIEW_LABEL;
            assist2.image = 'view_new';
            assist2.func = function (event, target) {
                var comboBox = target.assist.comboBox;
                var selectedId = -1;
                var isIdBased = options['isIdBased'] != null ? options['isIdBased'] : true;
                if(comboBox) {
                    selectedId = comboBox.getComboValue(false,isIdBased);
                    if(comboBox.getSelectedItem() == null) {
                        var divElement = document.getElementById('placeholder_'+comboBox.name);
                        var idElementName = null;
                        if(divElement) {
                            idElementName = divElement['idElementName'];
                        }

                        var textElement = document.getElementById(comboBox.name);
                        var idElement = document.getElementById(idElementName);
                        var clearElementValue = true;
                        if(idElement) {
                            if(idElement.value != '') {
                                selectedId = idElement.value;
                                clearElementValue = false;
                            }
                        }
                        if(textElement && clearElementValue) {
                            textElement.value = '';
                        }
                        comboBox.typedValue = null;
                    }
                }
                var viewURL = '';
                var viewFunction = options['viewFunction'];
                if(viewFunction != null) {
                    if(!isIdBased && selectedId != null) {
                        viewFunction.call();
                    } else if(isIdBased && selectedId > 0){
                        viewFunction.call();
                    } else {
                        alert(GT('IA.NOTHING_TO_VIEW'));
                        var textElement = document.getElementById(comboBox.name);
                        if(textElement) {
                            textElement.value = '';
                        }
                    }
                } else {
                    if(options['viewURL'] && selectedId > 0) {
                        viewURL = options['viewURL'];
                        viewURL = viewURL + selectedId;
                        window.open(viewURL);
                    } else {
                        alert(GT('IA.NOTHING_TO_VIEW'));
                        var textElement = document.getElementById(comboBox.name);
                        if(textElement) {
                            textElement.value = '';
                        }
                    }
                }
            };
            assists.push(assist2);
        }

    }

    var findFunction = options['findFunction'];
    if ( findFunction && assist1 ) {
        assist1.position = 'top';
        assist1.text = ComboBox2.PICKER_LINK_LABEL;
        assist1.func = function (event, target) {
            if(findFunction) {
                Global_inside_find_handler = true;
                //window.console.log('before call to find ');
                findFunction.call();
            }
        };
        assists.push(assist1);
    }

    if(options['defaultlistsize']) {
        window.comboPickSize = options['defaultlistsize'];
    }

    var userHasPermission = options['userHasPermission'];
    var userHasViewPermission = options['userHasViewPermission'];
    if(userHasPermission == null) {
        //default condition is to show the list
        userHasPermission = 'true';
    }

    if(userHasViewPermission == null) {
        //default condition is to show the list
        userHasViewPermission = 'true';
    }

    var opts = [];
    opts[0] = [];  // plug the list data
    opts[0]['textname']= elementNamePrefix+elementName;
    opts[0]['elementNamePrefix']= elementNamePrefix;
    opts[0]['isInSpecialDiv'] = '';
    opts[0]['allowed'] = 1;
    opts[0]['uicontrol']= '1';
    opts[0]['onchange']= options['onchange'];
    opts[0]['size'] = 50;
    opts[0]['textvalue'] = textValue;
    opts[0]['maxlength']= 1000;
    opts[0]['viewAllowed'] = 1;
    opts[0]['isInGridColumn'] = (window.editor && !options['isInGridColumn']) ?  0 : 1;
    opts[0]['assists'] = assists;
    opts[0]['forceListSize'] = false;
    opts[0]['textSize'] = options['textSize'];
    opts[0]['userHasViewPermission'] = userHasViewPermission == 'true';
    opts[0]['zIndex'] = (options['zIndex'] === undefined) ? 6 : options['zIndex'];
    opts[0]['isIdBased'] = (options['isIdBased'] === undefined) ? true : options['isIdBased'];
    opts[0]['i'] = (options['index'] === undefined) ? '' : options['index'];
    opts[0]['prioritizeCaseSensitiveSearch'] = !!options['prioritizeCaseSensitiveSearch'];

    if(options['addReturnOverride'] != null) {
        opts[0]['addReturnOverride'] = options['addReturnOverride'];
    }

    var comboBox = new ComboBox2(elementNamePrefix+elementName, null, opts);
    var divElement = document.getElementById('placeholder_'+elementNamePrefix+elementName);

    var hiddenDisplay = document.getElementById(elementNamePrefix+elementName + '_disp');
    var idElement = document.getElementById(elementNamePrefix+elementName);
    if(textValue == '') {
        if(hiddenDisplay) {
            if(idElement) {
                if(idElement.value != '0') {
                    opts[0]['textvalue'] = hiddenDisplay.value;
                    textValue = opts[0]['textvalue'];
                } else if(idElement.value == '0') {
                    hiddenDisplay.value = '';
                }
            }
        }
    }

    var url = options['ajaxURL'];
    var csrfToken = options['ajaxCsrfToken'];
    var defaultCollection = options['defaultCollection'];
    var nonAjaxMode = false;
    if(url == null) {
        nonAjaxMode = true;
    }
    var isOutsideEditors = false;
    if(options['nonEditor'] != null) {
        isOutsideEditors = options['nonEditor'];
    }
    divElement['nonAjaxMode'] = nonAjaxMode;
    divElement['ajaxURL'] = url;
    divElement['ajaxCsrfToken'] = csrfToken;
    divElement['ajaxCallback'] = elementName;
    divElement['elementName'] = elementNamePrefix+elementName;
    divElement['idElementName'] = elementName;
    divElement['tabIndx'] = options['tabIndex'];
    var initialCollection = new Array();


    if(nonAjaxMode) {
        if(defaultCollection != null) {
            for(var i=0; i<defaultCollection.length;i++) {
                var innerElement = defaultCollection[i];
                if (innerElement instanceof Object) {
                    var nameValuePair = innerElement;
                } else {
                    var nameValuePair = new Array();
                    if (!(innerElement instanceof Array)) {
                        nameValuePair[0] = innerElement;
                    } else if(innerElement.length == 2) {
                        nameValuePair[0] = innerElement[0];
                        nameValuePair[1] = innerElement[1];
                    } else if(innerElement.length == 1) {
                        nameValuePair[0] = innerElement[0];
                    }
                }
                initialCollection[i] = nameValuePair;
            }
        }
    } else {
        var nameValuePair = new Array();
        //we need loading message when list is loaded using ajax
        nameValuePair[0] = ComboBox2.LOADING_MESSAGE;
        nameValuePair[1] = -1;
        initialCollection[0] = nameValuePair;
    }
    if(userHasPermission == 'true') {
        comboBox.populate(initialCollection,-1);
    }

    if ( options['isInGridColumn'] ) {
        comboBox.txtview.style.minWidth = '110px';
    }

    comboBox.update(true);
    comboBox.refreshList();
    window[elementNamePrefix+elementName] = comboBox;
    var inHTML;
    if (!options['isInGridColumn']) {
        inHTML = '<ul class=\'assistedField\'>' +
            '<li class=\'assistedField-input-li\' style=\'list-style:none;\'>' +
            '<ul class=\'combo-box\'>' +
            comboBox.getView().innerHTML +
            '</ul>' +
            '</li>' +
            '</ul>';
    } else {
        inHTML = '<table class=\'combo-box-table\'>' +
            '<tr class=\'combo-box\'>' +
            '<td class=\'combo-box-td\' style=\'background-color: inherit;\'>' +
            comboBox.getView().innerHTML +
            '</td>' +
            '</tr>' +
            '</table>';
    }
    if (isOutsideEditors) {
        //this is needed for borders in application layout. We do not allow td based comboboxes in application layout
        if (jq.browser.msie  && parseInt(jq.browser.version, 10) === 8 ) {
            //this is IE8 specific code where it is not able to filter html
            //like newer browsers
            var innerView = comboBox.getView();
            var filteredView = null;
            var innerTR = innerView.childNodes[0];
            if (innerTR != null) {
                var tdCollection = innerTR.childNodes;
                for (var i=0; i < tdCollection.length; i++ ) {
                    var innerElement = tdCollection[i].childNodes[0];
                    if (innerElement != null ) {
                        if (filteredView == null ) {
                            filteredView = innerElement.outerHTML;
                        } else {
                            filteredView = filteredView + innerElement.outerHTML;
                        }
                    }
                }
            }

            if (filteredView == null ) {
                filteredView = innerView.innerHTML;
            }

            inHTML =
                '<ul class=\'assistedField\'>' +
                    '<li class=\'assistedField-input-li\' style=\'list-style:none;\'>' +
                    '<ul class=\'combo-box combo-box-border\'>' +
                    '<div style=\'white-space: nowrap\'>' +
                    filteredView +
                    '</div>' +
                    '</ul>' +
                    '</li>' +
                    '</ul>';
        } else {
            inHTML = '<ul class=\'assistedField\'>' +
                '<li class=\'assistedField-input-li\' style=\'list-style:none;\'>' +
                '<ul class=\'combo-box combo-box-border\'>' +
                //'<table class=\'combo-box-table\' style =\'margin-right: 0px;\'>' +
                comboBox.getView().innerHTML +
                //'</table>' +
                '</ul>' +
                '</li>' +
                '</ul>';
        }
    }

    divElement.innerHTML = inHTML;
    var spanElement = document.getElementById('span_'+elementNamePrefix+elementName);
    var textElement = document.getElementById(elementNamePrefix+elementName);
    if(textElement) {
        if(textElement.value == '') {
            if(textValue != '') {
                textElement.value = textValue;
            }
        }
    }
    //attach event handler as the combo box is added later
    if(spanElement) {
        YAHOO.util.Event.addListener(spanElement, 'click', ComboButton_click_outside, comboBox);
    }
    if(textElement) {
        if(options['textSize'] != null) {
            textElement.size = options['textSize'];
        }
        YAHOO.util.Event.addListener(textElement, 'blur', ComboBox2_txtview_blur);
        YAHOO.util.Event.addListener(textElement, 'change', ComboBox2_txtview_onchange);
    }
    //do the ajax call if requested
    if(userHasPermission == 'true' && !nonAjaxMode) {
        //ajax call here
        remoteAjaxCall(url, csrfToken, divElement, textValue, options['objDefId']);
    } else if (nonAjaxMode) {
        document.getElementById(elementNamePrefix+elementName).value = textValue;

    }
}

function getBaseAssits() {
    assist = {
        'func' : function (event, target) {
            var comboBox = target.assist.comboBox;
            if(comboBox) {
                comboBox.getComboValue(true,true);
            }
        },
        'script' : 'test',
        'module':'platform',
        'action' : 'list',
        'assistEntity' : 'test',
        'entity' : 'test',
        'text' : 'pick',
        'afterFunction' : '',
        'varname' : 'txt',
        'form' : 'test',
        'value' : 'value',
        'object' : this,
        'popupWidth': 100,
        'popupHeight': 10
    };
    return assist;
}

window.ptComboCache = {};
window.ptComboWaiting = {};

function remoteAjaxCall(ajaxURL, csrfToken, divElement, textValue, objDefId)
{
    var elementName = divElement['elementName'];
    var tabIndex = divElement['tabIndx'];
    if(elementName) {
        var textElement = document.getElementById(elementName);
        if(textElement) {
            if(textValue) {
                textElement.value = textValue;
            }
            try{
                //check for form editor and set tab index = 0. Tab order is handled
                //by form editor
                if(editor != null && editor instanceof FormEditor) {
                    tabIndex = 0;
                }
            }catch(ex){}

            if(tabIndex) {
                textElement.tabIndex = tabIndex;
            }
            textElement.disabled = true;
        }
    }

    if ( window.ptComboCache[objDefId] ) {
        ptAjaxCallBack(window.ptComboCache[objDefId], divElement, textValue);
        return;
    }

    if ( window.ptComboWaiting[objDefId] ) {
        window.ptComboWaiting[objDefId].push( { d: divElement, t: textValue} );
        return;
    }

    window.ptComboWaiting[objDefId] = [];
    window.ptComboWaiting[objDefId].push( { d: divElement, t: textValue} );

    var ajaxsreq = getXMLHTTPRequest();
    if (ajaxsreq) {
        var url = ajaxURL;
        ajaxsreq.open("GET", url, true);
        ajaxsreq.onreadystatechange = function(){
            remoteAjaxCallBack(ajaxsreq, objDefId);
        };
        if ( csrfToken ) {
            ajaxsreq.setRequestHeader('x-xsrf_token', csrfToken);
        }
        ajaxsreq.send(null);
    }
}

function ptAjaxCallBack(cache, divElement, textValue)
{

    if(cache.state == 'LOGGEDOUT' ) {
        var elementName = divElement['elementName'];
        var comboBox = window[elementName];
        comboBox.reset();
        comboBox.populate(cache.v,-1);
        comboBox.update(true);
        var elementId = comboBox.txtview.id;
        var textElement = document.getElementById(elementId);
        if(textElement) {
            textElement.disabled = false;
        }
        window[elementName] = comboBox;
        window[elementName].refreshList();
        return;
    }
    if (cache.state == 'NA' ) {
        return;
    }
    var elementName = divElement['elementName'];
    var idElementName = divElement['idElementName'];
    var hiddenDisplay = document.getElementById(idElementName + '_disp');

    var comboBox = window[elementName];
    if(comboBox) {
        var elementId = comboBox.txtview.id;
        var textElement = document.getElementById(elementId);
        comboBox.reset();
        comboBox.populate(cache.v,-1);
        comboBox.update(true);
        if(hiddenDisplay) {
            if(textElement) {
                textElement.value = hiddenDisplay.value;
            }
        }
        if(textElement) {
            textElement.disabled = false;
        }
        window[elementName] = comboBox;
        window[elementName].refreshList();
    }
}

function remoteAjaxCallBack(ajaxsreq, objDefId)
{
    if (ajaxsreq && ajaxsreq.readyState == READY_STATE_COMPLETE && (!ajaxsreq.status || ajaxsreq.status==200)) {
        var cache = { v: [], state: 0 };
        var resultXML = ajaxsreq.responseText;
        if(resultXML.indexOf("<Error>") == 0
            || resultXML.indexOf('<title>Sign in to Intacct</title>') >0
            || resultXML.indexOf('<title>Sign out from Intacct</title>') > 0) {

            cache.state == 'LOGGEDOUT';
            var suggestions = new Array();
            var nameValuePair = new Array();
            nameValuePair[0] = ComboBox2.ERROR_MESSAGE;
            nameValuePair[1] = -1;
            suggestions[0] = nameValuePair;
            cache.v = suggestions;
        } else  if (resultXML.indexOf("<title>Sorry, Not Authorized</title>") > 0) {
            cache.state == 'NA';
        } else {
            var resultArr = resultXML.split("\n");
            var resultLen = resultArr.length;
            var suggestions = new Array();

            var pos = 2;
            var k = 0;
            while (pos<resultLen) {
                var oRecId = resultArr[pos++];
                if (pos>=resultLen)
                    break;
                var oValue = resultArr[pos++];
                var suggestionIndex = k++;
                var nameValuePair = new Array();
                nameValuePair[0] = oValue;
                nameValuePair[1] = oRecId;
                suggestions[suggestionIndex] = nameValuePair;
            }
            //get the combo box instance from div
            //suggestions.sort();

            cache.v = suggestions;
        }

        if ( window.ptComboWaiting[objDefId] ) {
            for ( var ix = 0; ix < window.ptComboWaiting[objDefId].length; ix++ ) {
                var c = window.ptComboWaiting[objDefId][ix];
                ptAjaxCallBack(cache, c.d, c.t);
            }

            window.ptComboWaiting[objDefId] = null;
        }

    }

}

function setIdElementValue(comboBox, comboBoxItem) {
    if(comboBox && comboBox.isIdBased) {
        var elementId = comboBox.txtview.id;
        var textElement = document.getElementById(elementId);
        var divElement = document.getElementById('placeholder_'+elementId);
        var idElementName = null;
        if(divElement) {
            idElementName = divElement['idElementName'];
        }
        //this is needed as platform adds two hidden elements to the editor
        //with the same id and call to getElementById returns the first element in
        //the list which didnt have the id
        var idElement = null;
        var idElementCollection = document.getElementsByName(idElementName);
        if(idElementCollection.length > 0) {
            for(var i=0;i<idElementCollection.length;i++) {
                var innerElement = idElementCollection[i];
                if(innerElement.id == idElementName) {
                    idElement = innerElement;
                    break;
                }
            }
        }

        var hiddenDisplay = document.getElementById(idElementName + '_disp');
        if(idElement) {
            var selectedItem = comboBox.getSelectedItem();
            if(selectedItem) {
                if(selectedItem[1] == -1) {
                    idElement.value = '0';
                    return;
                }
                idElement.value = selectedItem[1];
                if(hiddenDisplay) {
                    hiddenDisplay.value = selectedItem[0];
                }
            } else if(comboBoxItem) {
                if(comboBoxItem[1] == -1) {
                    idElement.value = '0';
                    return;
                }
                idElement.value = comboBoxItem[1];
                if(textElement) {
                    textElement.value = comboBoxItem[0];
                }
                if(hiddenDisplay) {
                    hiddenDisplay.value = comboBoxItem[0];
                }
            } else {
                idElement.value = '0';
                if(hiddenDisplay) {
                    hiddenDisplay = '';
                }
            }
            if ( idElement.meta ) {
                idElement.meta.getDataFromHTML();
            }
            var hiddenChange = idElement.getAttribute("hidenChange");
            if (hiddenChange) {
                eval(hiddenChange);
            }
        }
    }
}


function comboBoxAdd_callback(options) {
    try {
        if (rbv_yuiDialog!=null) {
            rbv_yuiDialog.hide();
        }
    }
    catch (e) {}

    var elementNamePrefix = (options['elementNamePrefix'] == undefined) ? 'txt' : options['elementNamePrefix'];


    var elementId = elementNamePrefix+options['inputName'];
    var divElement = document.getElementById('placeholder_'+elementId);
    var textElement = document.getElementById(elementId);
    var idElementName = options['inputName'];
    var idElement = null;
    var idElementCollection = document.getElementsByName(idElementName);
    if(idElementCollection.length > 0) {
        for(var i=0;i<idElementCollection.length;i++) {
            var innerElement = idElementCollection[i];
            if(innerElement.id == idElementName) {
                idElement = innerElement;
                break;
            }
        }
    }
    if(divElement) {
        var idValue = options['dataId'];
        var textValue = options['dataValue'];

        var noAjaxCall = false;
        if(options['noAjaxCall'] != null) {
            noAjaxCall = options['noAjaxCall'];
        }

        if(textElement) {
            textElement.value = textValue;
        }
        if(idElement) {
            idElement.value = idValue;
        }
        var innerElemetName = divElement['idElementName'];
        var hiddenDisplay = document.getElementById(innerElemetName + '_disp');
        if(hiddenDisplay) {
            hiddenDisplay.value = textValue;
        }
        if ( idElement.meta ) {
            idElement.meta.getDataFromHTML();
        }
        var hiddenChange = idElement.getAttribute("hidenChange");
        if (hiddenChange) {
            eval(hiddenChange);
        }

        if(!noAjaxCall) {
            var url = divElement['ajaxURL'];
            var csrfToken = divElement['ajaxCsrfToken'];
            remoteAjaxCall(url, csrfToken, divElement);
        }
    }
}

function comboBox_areAnyCombosOpen() {
    for (var i=0; i < Global_combo_array.length; i++) {
        if (jq('#' + Global_combo_array[i].name + 'sel:visible').length) {
            return true;
        }
    }

    return false;
}

function comboBox2_default_add_return(comboBoxName , addedValue) {
    var fromComboBox =  window[comboBoxName];
    if(fromComboBox != null && (fromComboBox instanceof ComboBox2)) {
        var is_existing_value = false;
        var currentOptions = fromComboBox.options;
        if (currentOptions != null) {
            for (var i=0;i<currentOptions.length;i++) {
                var innerValue = currentOptions[i];
                if (innerValue != null && innerValue.length > 0) {
                    if (innerValue[0].toLowerCase() == addedValue.toLowerCase()) {
                        is_existing_value = true;
                        break;
                    }
                }
            }
        }
        if (is_existing_value == false) {
            return;
        }
        if(fromComboBox.addReturnOverride != null) {
            fromComboBox.addReturnOverride.call(fromComboBox, addedValue);
        } else {

            if(currentOptions) {
                var newValue = new Array();
                newValue[0] = addedValue;
                currentOptions[currentOptions.length] = newValue;
                fromComboBox.options = currentOptions;
            }
        }
        fromComboBox.update(true);
        fromComboBox.refreshList();
    }
}
