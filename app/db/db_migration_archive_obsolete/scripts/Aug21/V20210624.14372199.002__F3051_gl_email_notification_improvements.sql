--liquibase formatted sql
--changeset raja.m<PERSON><PERSON><PERSON><PERSON>an:20210624.14372199.002 runOnChange:false logicalFilePath:*********.14372199.002__F3051_gl_email_notification_improvements.sql

-----------------------------------------------------------------------------------------------------------------------------------------------------
-- Start Ticket 14372199 - PDLC-2389-21  F3051: JE Approval Improvements continued - Phase III - exposing the setup to entity level - DATA MIGARATION
-----------------------------------------------------------------------------------------------------------------------------------------------------

BEGIN
  DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
END;
/
-- Create default email notification for the existing approval setup
INSERT INTO glapproveremailconfig (
    journalkey,
    record#,
    cny#,
    notify,
    frequency,
    locationkey
)
SELECT
    journalkey,
    get_nextrecordid(cny#, 'glapproveremailconfig') AS record#,
    cny#,
    'A' AS notify,
    'I' AS frequency,
    locationkey
FROM
    glapproverconfig
WHERE
        cny# IN (
        SELECT
            cny#
        FROM
            modulepref
        WHERE
            modulekey LIKE '%GL%'
          AND property LIKE 'JEAPPROVAL_NOTIFY'
          AND value LIKE 'T'
    )
  AND locationkey IS NOT NULL
GROUP BY
    cny#,
    journalkey,
    locationkey
ORDER BY
    cny#,
    record#
/
BEGIN
  DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/