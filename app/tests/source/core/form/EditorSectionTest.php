<?php
namespace tests\source\core\form;

use EditorSection;
use FormEditor;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

/**
 * Class EditorSectionTest
 *
 * @covers EditorSection
 * @group unit
 */
class EditorSectionTest extends TestCase
{
    public function testConstructor()
    {
        /** @var MockObject|FormEditor $formEditorMock */
        $formEditorMock = $this->getMockBuilder(FormEditor::class)
            ->disableOriginalConstructor()
            ->getMock();

        $params = [];
        $instance = new EditorSection($params, $formEditorMock);

        self::assertEquals([], $instance->params);
        self::assertSame($formEditorMock, $instance->editor);
        self::assertInstanceOf(FormEditor::class, $instance->editor);
    }

    /**
     * @return array
     */
    public function encodeCustomDataProvider(): array
    {
        return [
            [
                [ 'title' => '><input/onclick=alert(1)>',  'custom' => true ],
                '&gt;&lt;input/onclick=alert(1)&gt;'
            ],
            [
                [ 'title' => '"><img src=x onerror=window.open(\'https://www.google.com/\');>',  'custom' => true ],
                '&quot;&gt;&lt;img src=x onerror=window.open(&#039;https://www.google.com/&#039;);&gt;'
            ],
            [
                [ 'title' => 'Enable Payment Manager®', 'custom' => false ],
                'Enable Payment Manager®'
            ],
            [
                [ 'title' => 'Enable Payment Manager<sup>&reg;</sup>', 'custom' => false ],
                'Enable Payment Manager<sup>&reg;</sup>'
            ],
            [
                [ 'title' => 'Enable Payment Manager<sup>&#162;</sup>', 'custom' => false ],
                'Enable Payment Manager<sup>&#162;</sup>'
            ],
            [
                [ 'title' => 'Payment Manager Plus<sup>SM</sup>', 'custom' => false ],
                'Payment Manager Plus<sup>SM</sup>'
            ],
            [
                [ 'title' => 'Enable Payment Manager<sup><input/onclick=alert(1)></sup>', 'custom' => false ],
                'Enable Payment Manager&lt;sup&gt;&lt;input/onclick=alert(1)&gt;&lt;/sup&gt;'
            ],
            [
                [ 'title' => '<sup>SM</sup><input/onclick=alert(1)>',  'custom' => false ],
                '<sup>SM</sup>&lt;input/onclick=alert(1)&gt;'
            ],
            [
                [ 'title' => '<sup>&cent;</sup><sup>&euro;</sup><sup>&pound;</sup><sup>&#162;</sup>',  'custom' => false ],
                '<sup>&cent;</sup><sup>&euro;</sup><sup>&pound;</sup><sup>&#162;</sup>'
            ]
        ];
    }

    /**
     * @dataProvider encodeCustomDataProvider
     * @param array  $params
     * @param string $expected
     */
    public function testEncodeTitle(array $params, string $expected)
    {
        /** @var MockObject|FormEditor $formEditorMock */
        $formEditorMock = $this->getMockBuilder(FormEditor::class)
            ->disableOriginalConstructor()
            ->getMock();

        $instance = new EditorSection($params, $formEditorMock);

        self::assertTrue(isset($instance->params['title']));
        self::assertEquals($expected, $instance->params['title']);
    }
}