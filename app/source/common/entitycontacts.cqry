<?
        // Note: at one time these were auto-generated, but the .ent file no longer goes through ent2qry.
        //
    $kentitycontactsQueries = array(
        'QRY_ENTITYCONTACTS_INSERT' => array(
            'QUERY' => 'INSERT INTO entitycontacts (record#,categoryname,entity,contact#,whencreated,whenmodified,createdby,modifiedby,isprimary,isbilltopayto,isshiptoreturnto,is1099,vendorkey, customerkey, employeekey, cny#) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ',
            'ARGTYPES' => array('integer','text','text','integer','timestamp','timestamp','integer','integer','text','text','text','text','integer','integer','integer', 'integer'),
        ),
        'QRY_ENTITYCONTACTS_UPDATE' => array(
            'QUERY' => 'UPDATE entitycontacts SET categoryname=?, entity=?, contact#=?, whencreated=?, whenmodified=?, createdby=?, modifiedby=?, isprimary=?, isbilltopayto=?, isshiptoreturnto=?, is1099=?, vendorkey=?, customerkey=?, employeekey=? WHERE record# =?  AND cny# =?  ',
            'ARGTYPES' => array('text','text','integer','timestamp','timestamp','integer','integer','integer','integer','text','text','text','text','integer','integer', 'integer'),
        ),
        'QRY_ENTITYCONTACTS_SELECT_SINGLE_VID' => array(
            'QUERY' => "SELECT entitycontacts.record#,entitycontacts.categoryname,contact.name,entitycontacts.entity,to_char(new_time(entitycontacts.whenmodified, 'PDT', 'GMT'), 'MM/DD/YYYY HH24:MI:SS'),to_char(new_time(entitycontacts.whencreated, 'PDT', 'GMT'), 'MM/DD/YYYY HH24:MI:SS'),entitycontacts.createdby,entitycontacts.modifiedby,entitycontacts.isprimary,entitycontacts.isbilltopayto,entitycontacts.isshiptoreturnto,entitycontacts.is1099,entitycontacts.vendorkey, entitycontacts.customerkey, entitycontacts.employeekey  FROM entitycontacts entitycontacts,contact contact WHERE (entitycontacts.record# =  ? ) and entitycontacts.contact# = contact.record#  (+)   and contact.cny#  (+)  = ?  and entitycontacts.cny# = ? ",
            'ARGTYPES' => array('integer', 'integer', 'integer'),
        ),
        'QRY_ENTITYCONTACTS_SELECT_RAW_VID' => array(
            'QUERY' => 'SELECT * FROM entitycontacts WHERE record# =?  AND cny# =?  ',
            'ARGTYPES' => array('integer' ,'integer' ),
        ),
        'QRY_ENTITYCONTACTS_DELETE_VID' => array(
            'QUERY' => 'DELETE FROM entitycontacts WHERE record# =?  AND cny# =? ',
            'ARGTYPES' => array('integer' ,'integer' ),
        ),
        'QRY_ENTITYCONTACTS_SELECT_BY_PARENT' => array(
            'QUERY' => 'SELECT entitycontacts.record#,entitycontacts.categoryname,contact.name,entitycontacts.entity,entitycontacts.whencreated,entitycontacts.whenmodified,entitycontacts.createdby,entitycontacts.modifiedby,entitycontacts.isprimary,entitycontacts.isbilltopayto,entitycontacts.isshiptoreturnto,entitycontacts.is1099, entitycontacts.vendorkey, entitycontacts.customerkey, entitycontacts.employeekey FROM entitycontacts entitycontacts,contact contact WHERE   entitycontacts.entity = ?  and entitycontacts.contact# = contact.record#  (+)   and contact.cny# (+) = ?  and entitycontacts.cny# (+) = ? ',
            'ARGTYPES' => array('text', 'integer', 'integer'),
        ),
        'QRY_ENTITYCONTACTS_DELETE_BY_PARENT' => array(
            'QUERY' => 'DELETE FROM entitycontacts WHERE entity =?  AND cny# =? ',
            'ARGTYPES' => array('text' ,'integer' ),
        ),
    );


    $kentitycontactsQueries['QRY_ENTITYCONTACTS_SELECT_BY_CONTACTNAME'] = array(
        'QUERY' => 'SELECT entitycontacts.record#,entitycontacts.categoryname,entitycontacts.contact#,entitycontacts.entity,contacthead.name,entitycontacts.whencreated,entitycontacts.whenmodified,entitycontacts.createdby,entitycontacts.modifiedby,entitycontacts.isprimary,entitycontacts.isbilltopayto,entitycontacts.isshiptoreturnto,entitycontacts.is1099,entitycontacts.vendorkey, entitycontacts.customerkey, entitycontacts.employeekey FROM entitycontacts entitycontacts,contacthead contacthead WHERE contacthead.name = ? and entitycontacts.contact# = contacthead.record# (+) and contacthead.cny# (+) = ?  and entitycontacts.cny# (+) = ?',
        'ARGTYPES' => array('text', 'integer', 'integer'),
    );