<?xml version='1.0' encoding='UTF-8'?>
<ROOT>
    <title>IA.CONFIGURE_SPEND_MANAGEMENT</title>
    <view system="true">
        <events>
            <load>hideShowProjectDateCheckbox();</load>
        </events>
        <pages>
            <page className="columnSetupPadding">
                <section isCollapsible="true" title="IA.BUDGET">
                    <subsection className="subSection qx-cfg-subsection">
                        <field readonly="true" path="BUDGET"></field>
                        <field readonly="true" path="STARTPERIOD"></field>
                        <field readonly="true" path="SC_NOBUDGETASZERO"></field>
                    </subsection>
                </section>
                <section isCollapsible="true" title="IA.NOTIFICATIONS">
                    <subsection className="subSection qx-cfg-subsection">
                        <field readonly="true" path="SC_BUDGETADMINEMAIL"></field>
                        <field readonly="true" path="SC_OVERBUDGETNOTIFY"></field>
                        <field readonly="true" path="SC_NOTIFYPREFERENCE"></field>
                    </subsection>
                </section>
                <section isCollapsible="true" title="IA.DIMENSIONS_SETUP">
                    <subsection className="subSection qx-cfg-subsection">
                        <field readonly="true" path="VISIBLEDIMENSIOS"></field>
                        <field readonly="true" path="SC_ENTITIESVALIDATED"></field>
                    </subsection>
                </section>
                <section isCollapsible="true" customFields="no" title="IA.DURATION">
                    <subsection className="subSection qx-cfg-subsection">
                        <field readonly="true" path="REPORTINGPERIOD" fullname="IA.DURATION"></field>
                        <field readonly="true" path="SC_USEPROJECTDATES"></field>
                    </subsection>
                </section>
                <section isCollapsible="true" customFields="no" title="IA.APPLICATIONS">
                    <subsection className="subSection qx-cfg-subsection">
                        <field className="font_bold" readonly="true" path="SC_ENABLEIN_PO">
                            <events>
                                <change>hideShowPoDocItemGLGroupsSection(this.meta);</change>
                            </events>
                        </field>
                        <field readonly="true" path="SC_WARNORSTOP_PO"></field>
                    </subsection>
                    <subsection className="subSection qx-cfg-subsection">
                        <field className="font_bold" readonly="true" path="SC_ENABLEIN_AP">
                            <events>
                                <change>hideShowStopWarnAP(this.meta);</change>
                            </events>
                        </field>
                        <field readonly="true" path="SC_WARNORSTOP_AP"></field>
                    </subsection>
                    <subsection className="subSection qx-cfg-subsection">
                        <field className="font_bold" readonly="true" path="SC_ENABLEIN_GL">
                            <events>
                                <change>hideShowStopWarnGL(this.meta);</change>
                            </events>
                        </field>
                        <field readonly="true" path="SC_WARNORSTOP_GL"></field>
                        <field readonly="true" path="SC_ACCOUNTGROUP"></field>
                    </subsection>
                </section>
                <section isCollapsible="true" customFields="no" title="IA.COMMITTED_EXPENSES">
                    <subsection className="subSection qx-cfg-subsection">
                        <field readonly="true" path="SC_INCLUDECOMMITTED" hidden="true">
                            <events>
                                <change>hideShowUDBsCtrl();</change>
                            </events>
                        </field>
                        <field readonly="true" path="SC_UDBS" hidden="true"></field>
                    </subsection>
                </section>
                <section isCollapsible="true" title="IA.PURCHASING" id="purchasingdocsection">
                    <subsection className="subSection qx-cfg-subsection">
                        <!--<field path="SC_PODOCS"></field>-->
                        <grid path="PO_DOCS_GRID" noDragDrop="true" isCollapsible="true">
                            <column>
                                <field path="PODOC"></field>
                            </column>
                            <column>
                                <field fullname="IA.CREATED_AT" readonly="true">
                                    <type type="text" ptype="text"></type>
                                    <path>PO_CREATEDAT</path>
                                </field>
                            </column>
                        </grid>
                    </subsection>
                </section>
                <section isCollapsible="true" title="IA.ITEM_GL_GROUP" id="itemglgroupsection">
                    <subsection className="subSection qx-cfg-subsection">
                        <field readonly="true" path="DF_GLACCOUNT"></field>
                        <button id="itemglgroup" name="IA.IMPORT_MAPPING" path="ITEMGLGROUPMAP">
                            <events>
                                <click>itemglgroupmap();</click>
                            </events>
                        </button>
                        <grid path="ITEM_GL_GRID" noDragDrop="true" isCollapsible="true">
                            <column>
                                <field path="ITEMGLGROUP"></field>
                            </column>
                            <column>
                                <field userUIControl="ItemGLGrpAccountControl">
                                    <path>GLACCOUNT</path>
                                </field>
                            </column>
                            <column>
                                <field fullname="IA.CREATED_AT" readonly="true">
                                    <type type="text" ptype="text"></type>
                                    <path>IGLGRP_CREATEDAT</path>
                                </field>
                            </column>
                        </grid>
                    </subsection>
                </section>
            </page>
        </pages>
    </view>
    <helpfile>Configuring_Spend_Control</helpfile>
</ROOT>
