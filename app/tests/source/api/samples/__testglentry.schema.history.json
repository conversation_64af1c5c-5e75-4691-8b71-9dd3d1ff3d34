{"ia::definition": {"methodPermissions": {"GET": ["gl/lists/glbatch/view"], "POST": ["gl/lists/glbatch/create"], "PATCH": ["gl/lists/glbatch/edit"], "DELETE": ["gl/lists/glbatch/delete"]}}, "s1": {"hash": "0", "type": "ownedObject", "adapter": "TestGLEntryAPIAdapterS1"}, "s2": {"hash": "0", "type": "ownedObject", "adapter": "TestGLEntryAPIAdapterS2"}, "s3": {"hash": "0", "type": "ownedObject", "uiMetadataHash": "0"}}