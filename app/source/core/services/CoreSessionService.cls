<?php

class CoreSessionService
{

    /**
     * @param array $request
     * @param array $extraContext
     *
     * @return array
     */
    public function id(/** @noinspection PhpUnusedParameterInspection */ array $request, array $extraContext) : array
    {
        // get the current session id, could be: UI session, API session, unit test session
        $sessionId = Session::getKey();
        if (empty($sessionId)) {
            logToFileError('Invalid session id: ' . $sessionId);
            throw new Exception('Invalid session id: ' . $sessionId);
        }

        $senderId = OAuth2Pdo::getSenderId($extraContext['Control']['clientId']);
        $userId = GetMyLogin();
        $cnyId = GetMyCompanyTitle();
        $password = GetMyPassword();

        Request::$r->_sess = '';
        $ok = IASessionHandler::processAuthentication(
            $sessionId, $senderId, true, $userId, $cnyId, $password,
            $partnerInfo, $locationId, '', false, false,
            IASessionHandler::IMS_SESSION_KEY, true);
        if ($ok) {
            $result = Request::$r->_sess;
            Request::$r->_sess = $sessionId;
        } else {
            logToFileError('====================>>>' . PHP_EOL . Globals::$g->gErr->myToString(false) . PHP_EOL . '<<<====================');
            logToFileError('Could not create session');
            throw new Exception('Could not create session');
        }

        $sessionHandler = IASessionHandler::getInstance($result);
        $sessionExpiresAt = $sessionHandler->expiresAt();
        $expiresIn = $sessionExpiresAt - time();

        return [
            'sessionId' => $sessionHandler->getKey(),
            'expiresIn' => $expiresIn,
        ];
    }
}