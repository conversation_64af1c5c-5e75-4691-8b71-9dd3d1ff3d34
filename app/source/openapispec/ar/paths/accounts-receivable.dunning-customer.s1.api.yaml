openapi: 3.0.0
info:
  title: accounts-receivable-dunning-customer
  description: accounts-receivable.dunning-customer API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Dunning customers
    description: Dunning customers are customers to whom dunning notices have been sent to.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/accounts-receivable/dunning-customer:
    get:
      summary: List dunning customers
      description: Returns a collection with a key, ID, and link for each dunning customer.
      tags:
        - Dunning customers
      operationId: list-accounts-receivable-dunning-customer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of dunning-customer objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of dunning customers:
                  value:
                    ia::result:
                      - key: '1'
                        id: DN-2
                        href: "/objects/accounts-receivable/dunning-customer/1"
                      - key: '2'
                        id: DN-3
                        href: "/objects/accounts-receivable/dunning-customer/2"
                    ia::meta:
                      totalCount: 2
                      start: 1
                      pageSize: 100
        '400':
          $ref: '#/components/responses/400error'
  '/objects/accounts-receivable/dunning-customer/{key}':
    parameters:
      - name: key
        description: system-assigned unique key for the dunning customer.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a dunning customer
      description: Returns detailed information for a particular dunning customer.
      tags:
        - Dunning customers
      operationId: get-accounts-receivable-dunning-customer-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the dunning-customer
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/accounts-receivable-dunning-customer'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the dunning customer:
                  value:
                    ia::result:
                      id: "1"
                      key: "1"
                      dunningNoticeCustomerId: "DN-2"
                      dunningNotice:
                        id: "1"
                        key: "1"
                        audit:
                          modifiedDateTime: '2024-08-02T00:00:00Z'
                          createdDateTime: '2024-08-02T00:00:00Z'
                          modifiedBy: '1'
                          createdBy: '1'
                        href: "/objects/accounts-receivable/dunning-notice/1"
                      customer:
                        key: "1"
                        id: "1"
                        name: "Power Aerospace Materials"
                        href: "/objects/accounts-receivable/customer/1"
                      invoiceCount: 2
                      totalAmountOverdue: "499.00"
                      totalTxnAmountOverdue: "499.00"
                      sendDate: "09/13/2024 01:24:51"
                      noticeEmail:
                        date: "2024-04-17"
                        to: ""
                        cc: ""
                        bcc: ""
                      dunningTemplate: "AR - Dunning notice"
                      dunningNoticeAttachment: "Generated pdf data"
                      deliveryMethod: "printed"
                      invoices:
                        - id: "1"
                          key: "1"
                          dunningCustomer:
                            id: "1"
                            key: "1"
                            href: "/objects/accounts-receivable/dunning-customer/1"
                          arInvoice:
                            id: "97"
                            key: "97"
                            invoiceNumber:
                            invoiceDate: "2023-12-26"
                            dueDate: "2023-12-31"
                            totalBaseAmount: "400.00"
                            totalTxnAmount: "400.00"
                            totalBaseAmountDue: "400.00"
                            referenceNumber:
                            totalTxnAmountDue: "499.00"
                            currency: "USD"
                            href: "/objects/accounts-receivable/invoice/97"
                          href: "/objects/accounts-receivable/dunning-invoice/1"
                        - id: "2"
                          key: "2"
                          dunningCustomer:
                            id: "1"
                            key: "1"
                            href: "/objects/accounts-receivable/dunning-customer/1"
                          arInvoice:
                            id: "127"
                            key: "127"
                            invoiceNumber: "Inv-1-Doc"
                            invoiceDate: "2023-12-27"
                            dueDate: "2023-12-31"
                            totalBaseAmount: "100.00"
                            totalTxnAmount: "100.00"
                            totalBaseAmountDue: "99.00"
                            referenceNumber:
                            totalTxnAmountDue: "499.00"
                            currency: "USD"
                            href: "/objects/accounts-receivable/invoice/127"
                          href: "/objects/accounts-receivable/dunning-invoice/2"
                      href: "/objects/accounts-receivable/dunning-customer/1"
                    ia::meta:
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    accounts-receivable-dunning-customer:
      $ref: ../models/accounts-receivable.dunning-customer.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml