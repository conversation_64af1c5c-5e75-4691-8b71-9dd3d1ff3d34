--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__PKG_SL_PYMT_UTILS.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE PACKAGE SL_PYMT_UTILS AS
    -- Type definitions
    TYPE payment_record IS RECORD (
        paymentkey     PRENTRYPYMTRECS.PAYMENTKEY%TYPE,
        payitemkey     PRENTRYPYMTRECS.PAYITEMKEY%TYPE,
        amount         PRENTRYPYMTRECS.AMOUNT%TYPE,
        trx_amount     PRENTRYPYMTRECS.TRX_AMOUNT%TYPE
    );

    TYPE txn_record IS RECORD (
        recordkey      PRENTRYPYMTRECS.RECORDKEY%TYPE,
        paiditemkey    PRENTRYPYMTRECS.PAIDITEMKEY%TYPE,
        invbaseamt     PRENTRYPYMTRECS.INVBASEAMT%TYPE,
        invtrxamt      PRENTRYPYMTRECS.INVTRXAMT%TYPE
    );

    -- Procedure declarations
    PROCEDURE setcurrencyandforeignbankflag
    (
        p_cny#                IN  PRENTRYPYMTRECS.CNY#%TYPE,
        p_recordkey           IN  PRENTRYPYMTRECS.RECORDKEY%TYPE,
        p_curr                IN  PRENTRYPYMTRECS.CURRENCY%TYPE,
        p_currency            OUT VARCHAR2,
        p_basebillforeignbank OUT VARCHAR2
    );

    PROCEDURE processpayment
    (
        p_paymentdetails         IN OUT SL_PYMT_UTILS.payment_record,
        p_recorddetails          IN OUT SL_PYMT_UTILS.txn_record,
        p_currency               IN OUT PRENTRYPYMTRECS.CURRENCY%TYPE,
        p_cny#                   IN PRENTRYPYMTRECS.CNY#%TYPE,
        p_parentpaymentkey       IN PRENTRYPYMTRECS.PARENTPYMT%TYPE,
        p_parentpymt             IN PRENTRYPYMTRECS.PARENTPYMT%TYPE,
        p_state                  IN PRENTRYPYMTRECS.STATE%TYPE,
        p_paymentdate            IN PRENTRYPYMTRECS.PAYMENTDATE%TYPE,
        p_whencreated            IN PRENTRYPYMTRECS.WHENCREATED%TYPE,
        p_createdby              IN PRENTRYPYMTRECS.CREATEDBY%TYPE,
        p_basebillforeignbank    IN VARCHAR2,
        p_isrefund               IN VARCHAR2 := 'F'
    );

END SL_PYMT_UTILS;
/

