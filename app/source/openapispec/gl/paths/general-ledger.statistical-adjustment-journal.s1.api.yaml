openapi: 3.0.0
info:
  title: general-ledger-statistical-adjustment-journal
  description: GL Statistical adjustment Journal
  version: '1.0'
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
tags:
  - name: Statistical adjustment journals
    description: |
      'Use the statistical adjustment journals page to add a new statistical adjustment journal or change the information for an existing one.
      'Before you can create statistical adjustment transactions, you must create one or more statistical adjustment journals.'
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/general-ledger/statistical-adjustment-journal:
    get:
      summary: List Staistical adjustment journals
      description: 'Returns a collection with a key, ID, and link for each statistical adjustment journal'
      tags:
        - Statistical adjustment journals
      operationId: list-general-ledger-statistical-adjustment-journal
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of Statistical adjustment journal objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                GET all statistical adjustment journals example:
                  value:
                    'ia::result':
                      - key: '36'
                        id: STATADJ
                        href: /objects/general-ledger/statistical-adjustment-journal/36
                      - key: '34'
                        id: EMPTIMEADJ
                        href: /objects/general-ledger/statistical-adjustment-journal/34
                      - key: '35'
                        id: EMPCNTADJ
                        href: /objects/general-ledger/statistical-adjustment-journal/35
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a statistical adjustment journal
      description: Creates a new statistical adjustment journal.
      tags:
        - Statistical adjustment journals
      operationId: create-general-ledger-statistical-adjustment-journal
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/general-ledger-statistical-adjustment-journal'
                - $ref: '#/components/schemas/general-ledger-statistical-adjustment-journalRequiredProperties'
            examples:
              Simple POST request:
                value:
                  id: STATADJ
                  name: Statistical Adjustment Journal
                  status: active
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New statistical-adjustment-journal
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Simple POST response:
                  value:
                    'ia::result':
                      key: '36'
                      id: STATADJ
                      href: /objects/general-ledger/statistical-adjustment-journal/88
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
  /objects/general-ledger/statistical-adjustment-journal/{key}:
    parameters:
      - name: key
        description: System-assigned key for the statistical-adjustment-journal.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get an statistical adjustment journal
      description: Returns detailed information for a particular statistical adjustment journal.
      tags:
        - Statistical adjustment journals
      operationId: get-general-ledger-statistical-adjustment-journal-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the statistical-adjustment-journal
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/general-ledger-statistical-adjustment-journal'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Simple GET response:
                  value:
                    'ia::result':
                      id: STATADJ
                      name: Statistical Adjustment Journal
                      status: active
                      disallowDirectPosting: false
                      audit:
                        createdDateTime: '2016-06-27T17:16:13Z'
                        modifiedDateTime: '2016-06-27T17:16:13Z'
                        createdBy: '1'
                        modifiedBy: '1'
                      key: '36'
                      href: /objects/general-ledger/statistical-adjustment-journal/36
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update an statistical adjustment journal
      description: Updates an existing statistical adjustment journal by setting field values. Any fields not provided remain unchanged.
      tags:
        - Statistical adjustment journals
      operationId: update-general-ledger-statistical-adjustment-journal-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: ../models/general-ledger.statistical-adjustment-journal.s1.schema.yaml
                - type: object
                  properties:
                    id:
                      readOnly: true
                      example: STATADJ
            examples:
              Simple PATCH request:
                value:
                  name: Statistical Adjustment Journal
                  status: active
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated statistical-adjustment-journal
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Simple PATCH response:
                  value:
                    'ia::result':
                      key: '36'
                      id: STATADJ
                      href: /objects/general-ledger/statistical-adjustment-journal/36
                    'ia::meta':
                      totalCount: 1
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a statistical adjustment journal
      description: Deletes a statistical adjustment journal.
      tags:
        - Statistical adjustment journals
      operationId: delete-general-ledger-statistical-adjustment-journal-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    general-ledger-statistical-adjustment-journal:
      $ref: ../models/general-ledger.statistical-adjustment-journal.s1.schema.yaml
    general-ledger-statistical-adjustment-journalRequiredProperties:
      type: object
      required:
        - id
        - name
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
