title: Reporting account set permission
x-mappedTo: reportingacheaderpermissions
x-ownedBy: general-ledger/reporting-account-set
type: object
description: Reporting account set permissions.
properties:
  key:
    type: string
    description: System-assigned key for the reporting-account-set-permission.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: Reporting account permission id.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  href:
    type: string
    description: Endpoint for the reporting-account-set-permission.
    readOnly: true
    example: /objects/general-ledger/reporting-account-set-permission/23
  permissionAppliesTo:
    type: string
    description:  |-
      Sets the type of user or user group that is allowed access to the reporting account set.
      - `everyone` - Allow or deny everyone to the reporting account set.
      - `group` - Allow or deny a user group to the reporting account set.
      - `user` - Allow or deny a user to the reporting account set.
    x-mappedTo: USERTYPE
    example: 'everyone'
    enum:
      - 'everyone'
      - 'group'
      - 'user'
    x-mappedToValues:
      - 'Everyone'
      - 'Group'
      - 'User'
  accessRights:
    type: string
    description: Indicates if the `permissionAppliesTo` is allowed/denied access to the reporting account set.
    x-mappedTo: ISALLOWED
    example: 'allow'
    x-mappedToType: string
    default: 'allow'
    enum:
      - 'allow'
      - 'deny'
    x-mappedToValues:
      - 'true'
      - 'false'
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
  userGroup:
    type: object
    description: User group object reference.
    x-mappedTo: usergroup
    x-object: company-config/user-group
    properties:
      key:
        type: string
        description: User group key.
        x-mappedTo: UGROUPKEY
        example: '21'
      id:
        type: string
        description: User group id. Same as key.
        x-mappedTo: UGROUPKEY
        example: '21'
      href:
        type: string
        description: URL for the user group.
        readOnly: true
        example: /objects/company-config/user-group/21
  user:
    type: object
    description: User object reference.
    x-mappedTo: userinfo
    x-object: company-config/user
    properties:
      key:
        type: string
        x-mappedTo: USERINFOKEY
        description: System-assigned key for the user
        example: "2"
      id:
        type: string
        x-mappedTo: USERID
        description: User id. Same as key.
        example: "Admin"
      href:
        type: string
        readOnly: true
        description: URL endpoint of the user
        example: /objects/company-config/user/2
  reportingAccountSet:
    type: object
    description: Reporting account set reference.
    x-mappedTo: reportingacheader
    x-object: general-ledger/reporting-account-set
    properties:
      id:
        type: string
        x-mappedTo: REPORTINGACHEADERKEY
        example: '132'
        readOnly: true
      key:
        type: string
        x-mappedTo: REPORTINGACHEADERKEY
        example: '132'
        readOnly: true
      href:
        type: string
        readOnly: true
        example: /objects/general-ledger/reporting-account-set/132