--OWNER_CONTACT: <EMAIL>

-- Drop constraint for external authorization type column
DECLARE
  CK_EXTERNASSOC_TYPE_EXIST NUMBER;
BEGIN
  SELECT COUNT(*)
  INTO CK_EXTERNASSOC_TYPE_EXIST
  FROM all_constraints
  WHERE constraint_name = 'CK_EXTERNASSOC_TYPE'
    AND table_name = 'EXTERNASSOC'
    AND owner = sys_context('userenv', 'current_schema');

  IF CK_EXTERNASSOC_TYPE_EXIST > 0 THEN
    EXECUTE IMMEDIATE 'ALTER TABLE externassoc DROP CONSTRAINT ck_externassoc_type';
  END IF;
END;
/

-- Add constraint for external authorization type column
DECLARE
  CK_EXTERNASSOC_TYPE_EXIST NUMBER;
BEGIN
  SELECT COUNT(*)
  INTO CK_EXTERNASSOC_TYPE_EXIST
  FROM all_constraints
  WHERE constraint_name = 'CK_EXTERNASSOC_TYPE'
    AND table_name = 'EXTERNASSOC'
    AND owner = sys_context('userenv', 'current_schema');

  IF CK_EXTERNASSOC_TYPE_EXIST = 0 THEN
    EXECUTE IMMEDIATE 'ALTER TABLE externassoc ADD CONSTRAINT ck_externassoc_type CHECK (type IN (''C'', ''P'', ''I'', ''T'', ''S'', ''R'')) ENABLE VALIDATE';
  END IF;
END;
/
