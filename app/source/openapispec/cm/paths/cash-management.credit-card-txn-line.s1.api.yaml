openapi: 3.0.0
info:
  title: cash-management-credit-card-txn-line
  description: cash-management.credit-card-txn-line API
  version: '1.0'
  contact:
    name: <PERSON>
    email: <EMAIL>
tags:
  - name: Credit card transaction lines
    description: Line items in a credit card transaction represent individual charges, adjustments or credits applied to credit and debit cards. Each line item contains detailed information such as amounts, currency, exchange rate, and tax information.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/cash-management/credit-card-txn-line:
    get:
      summary: List credit card transaction line items
      description: Returns a collection of credit card transaction line items with a key, ID, and link for each item. This operation is mostly for use in testing; use query to find objects that meet certain criteria and to specify properties that are returned.
      x-documentationFlags:
        subscription: Cash Management
        userPermissions:
          - userType: Business
            permissions: List, View Credit card transactions
      tags:
        - Credit card transaction lines
      operationId: list-cash-management-credit-card-txn-line
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of credit-card-txn-line objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List credit card transaction lines:
                  value:
                    'ia::result':
                      - key: '13'
                        id: '13'
                        href: /objects/cash-management/credit-card-txn-line/13
                      - key: '5'
                        id: '5'
                        href: /objects/cash-management/credit-card-txn-line/5
                      - key: '15'
                        id: '15'
                        href: /objects/cash-management/credit-card-txn-line/15
                    'ia::meta':
                      totalCount: 3
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  /objects/cash-management/credit-card-txn-line/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for the credit card transaction line item.
        in: path
        required: true
        schema:
          type: string
    get:
      summary: Get a credit card transaction line item
      description: Returns detailed information for a specified credit card transaction line item.
      x-documentationFlags:
        subscription: Cash Management
        userPermissions:
          - userType: Business
            permissions: List, View Credit card transactions
      tags:
        - Credit card transaction lines
      operationId: get-cash-management-credit-card-txn-line-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the credit card transaction line item
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/cash-management-credit-card-txn-line'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Get a credit card transaction line:
                  value:
                    'ia::result':
                      id: '5299'
                      key: '5299'
                      creditCardTxn:
                        id: '1482'
                        key: '1482'
                        href: /objects/cash-management/credit-card-txn/1482
                      glAccount:
                        key: '259'
                        id: '6254.04'
                        name: Travel
                        href: /objects/general-ledger/account/259
                      accountLabel:
                        key: '15'
                        id: Travel Expenses
                      amount: '741.00'
                      txnAmount: '1000.00'
                      dimensions:
                        department:
                          key: '28'
                          id: PS
                          name: Professional Services
                          href: /objects/company-config/department/28
                        location:
                          key: '7'
                          id: '7'
                          name: Canada
                          href: /objects/company-config/location/7
                        project:
                          key: '35'
                          id: QSF - BTI
                          name: Quick Start Financial's - Berkeley Technology Inc
                          href: /objects/projects/project/35
                        customer:
                          key: '14'
                          id: BTI
                          name: Berkeley Technology Inc
                          href: /objects/accounts-receivable/customer/14
                        vendor:
                          key: '56'
                          id: '210'
                          name: Office Supply and Copier Co.
                          href: /objects/accounts-payable/vendor/56
                        employee:
                          key: '27'
                          id: '12'
                          name: John Smith
                          href: /objects/company-config/employee/27
                        item:
                          key: '117'
                          id: DELL
                          name: DELL Laptops
                          href: /objects/inventory-control/item/117
                        class:
                          key: '3'
                          id: WSD
                          name: Whole Sales Distribution
                          href: /objects/company-config/class/3
                      baseLocation:
                        name: Canada
                        key: '7'
                        href: /objects/company-config/location/7
                      description: Travel expenses - John Smith
                      currency:
                        exchangeRate:
                          date: '2025-04-11'
                          rate: 0.741
                        txnCurrency: CAD
                        baseCurrency: USD
                      lineNumber: 1
                      totalPaid: '0.00'
                      txnTotalPaid: '0.00'
                      audit:
                        createdDateTime: '2025-04-11T12:56:46Z'
                        modifiedDateTime: '2025-04-11T12:56:46Z'
                        createdByUser:
                          key: '159'
                          href: /objects/company-config/user/159
                        createdBy: '159'
                        modifiedByUser:
                          key: '159'
                          href: /objects/company-config/user/159
                        modifiedBy: '159'
                      taxDetail:
                        key: null
                        taxRate: null
                        id: null
                      isBillable: false
                      isBilled: false
                      taxEntries: []
                      href: /objects/cash-management/credit-card-txn-line/5299
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    cash-management-credit-card-txn-line:
      $ref: ../models/cash-management.credit-card-txn-line.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
