<?php
/**
 *    FILE: CustomFieldCheckbox.cls
 *    AUTHOR: NaveenS
 *    DESCRIPTION:
 *
 *    (C) 2005, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */
require_once 'CustomField.cls';
require_once 'globals.ent';

/**
 * Class CustomFieldCheckbox
 */
class CustomFieldCheckbox extends CustomField
{
    /**
     * @return array
     */
    function GetTypeInfo()
    {
        global $gBooleanType;
        $type = $gBooleanType;

        return $type;
    }

    /**
     * @return string[]|bool[]
     */
    function GetFieldInfo()
    {
        $fieldInfo = parent::GetFieldInfo();
        $this->required = false;
        $fieldInfo['required'] = false;
        $fieldInfo['default'] =
            is_array($this->dataDescription['checkbox']) ? $this->dataDescription['checkbox']['checked'][0] : "false";

        return $fieldInfo;
    }


    /**  THIS IS A PROBLEM SINCE IT CASUSES PROBLEMS IN GENERATED REPORTS; THE REPORT SCHEMA EXPECTS THIS TO BE A VARCHAR
     * -- SEE PARENT CLASS METHOD WHICH DEFINES THIS BY DEFAULT AS A 'TEXT'
     * function GetPrimitiveType()
     * {
     * return $this->type;
     * }
     */
    /**
     * Generates the DDL SQL statement for adding this field to an
     * Amazon Redshift database
     *
     * @return string
     */
    public function idwDdlAddColumn()
    {
        return $this->customFieldID . "    " . "boolean";
    }
    
    public function toArray() : array
    {
        $baseCustomFieldData = parent::toArray();
        return array_merge($baseCustomFieldData, [
            'CHECKBOXDEFAULT' => $this->dataDescription['checkbox']['checked'][0] ?? "false",
        ]);
    }
    
    /**
     * @return array
     */
    public function getRequiredAPIFields() : array
    {
        return [];
    }
    
    /**
     * @return string[]
     */
    public function getAllSpecificAPIFields() : array
    {
        return ['CHECKBOXDEFAULT'];
    }
}
