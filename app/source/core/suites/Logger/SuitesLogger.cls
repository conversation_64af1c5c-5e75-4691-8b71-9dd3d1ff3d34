<?php

class SuitesLogger implements SuitesLoggerInterface
{

    const DEFAULT_LOG_FILE = '/tmp/acct.log';
    
    const LOG_PREFIX = 'sSUITES - ';
    
    /** @var string $logFile */
    private string $logFile;
    
    /**
     * @param string                $logFile
     */
    public function __construct(string $logFile = self::DEFAULT_LOG_FILE)
    {
        $this->logFile = $logFile;
    }

    /**
     * @param string $message
     */
    public function info(string $message) : void
    {
        $this->logToFile(
            str_replace(PHP_EOL, '', $message),
            $this->logFile,
            true,
            LogManager::INFO
        );
    }

    /**
     * @param string $message
     */
    public function critical(string $message) : void
    {
        $this->logToFile(
            str_replace(PHP_EOL, '', $message),
            $this->logFile,
            true,
            LogManager::CRITICAL
        );
    }

    /**
     * @param string $message
     */
    public function error(string $message) : void
    {
        $this->logToFile(
            str_replace(PHP_EOL, '', $message),
            $this->logFile,
            true,
            LogManager::ERROR
        );
    }


    /**
     * @param string $message
     * @param string $logFile
     * @param bool   $bMarkDateTime
     * @param string $level
     *
     * @codeCoverageIgnore
     */
    protected function logToFile(string $message, string $logFile, bool $bMarkDateTime, string $level)
    {
        LogToFile(self::LOG_PREFIX . $message, $logFile, $bMarkDateTime, $level);
    }
}
