--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__TRIG_ARD_GLBATCH.sql
--OWNER_CONTACT: <EMAIL>

CREATE OR REPLACE TRIGGER ARD_GL<PERSON>TCH AFTER DELETE ON GLBATCH FOR EACH ROW
BEGIN
    DBMS_APPLICATION_INFO.read_client_info (acct_utils.trigger_enabled);
    IF acct_utils.trigger_enabled <> 'F' THEN
        /*
        Delete the corresponding Supdocmaps record for the glbatch record.
        */
        delete from supdocmaps
        where   cny# = :old.cny#
        and     recordid = to_char(:old.record#)
        and     transactiontype = 'GLTRANS';
        INSERT INTO DELETE_LOG (CNY#, RECORDKEY, OBJECT, DOCTYPE, OBJ_DEF_ID, WHENCREATED, WHENDELETED, DELETEDBY) VALUES(
            :old.cny#,
            :old.record#,
            'glbatch',
            null,
            null,
            :old.whencreated,
            (CURRENT_TIMESTAMP AT TIME ZONE 'GMT'),
            SYS_CONTEXT('TMCtx', 'USERKEY')
        );
    END IF;
END ARD_GLBATCH;
/
