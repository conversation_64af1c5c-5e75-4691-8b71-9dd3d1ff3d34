<?php

/**
 * Grouping class for Contract
 *
 * Class ContractGrouper
 */
class ContractGrouper extends GrouperDecorator
{
    const ID = "CONTRACTKEY";

    /**
     * ContractGrouper constructor.
     *
     * @param Grouper $grouper
     */
    function __construct(Grouper $grouper)
    {
        parent::__construct($grouper);
    }

    /**
     * @param GenInvoiceBillableLine $billableLine
     *
     * @return string
     */
    protected function getGroupValue($billableLine)
    {
        return $billableLine->getContractKey();
    }

    /**
     * @return string
     */
    public function getId()
    {
        return self::ID;
    }
}