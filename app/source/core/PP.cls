<?php
//=============================================================================
//
//	FILE:			pp.inc
//	AUTHOR:			
//	DESCRIPTION:	
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================


class PP
{

    /**
     * @var int $structDepth
     */
    protected $structDepth;
    /**
     * @var bool $quoteKeys
     */
    protected $quoteKeys;
    /**
     * @var string[] $delimMap
     */
    protected $delimMap;

    /**
     * @param string $_type
     *
     */
    function __construct($_type = 'PERL')
    {
        $this->quoteKeys = true;
        switch ($_type) {
            case 'PHP' :
                $this->delimMap = [
                    '{'     => 'array(',
                    '}'     => ')',
                    '['     => 'array(',
                    ']'     => ')',
                    'undef' => 'false',
                ];
                $this->structDepth = 0;
                break;
            case 'PERL':
                break;
            case 'JS':
                $this->delimMap = [
                    '{'     => '{',
                    '}'     => '}',
                    '['     => '[',
                    ']'     => ']',
                    '=>'    => ':',
                    'undef' => 'false',
                ];
                $this->quoteKeys = true;
                $this->structDepth = 0;
                break;
            default:
                dieFL("invalid PP type $_type");
                break;
        }
    }

    /**
     * @param array $r
     *
     * @return string
     */
    function xp($r)
    {
        return $this->_PodEncodeR($r);
    }

    /**
     * @param string $str
     *
     * @return string
     */
    private function _PodQuote(&$str)
    {
        if ( ! isset($str) ) {
            return 'undef';
        }
        if ( ! is_string($str) ) {
            return $str;
        }

        $str = strtr($str, "'", "\\'");

        return "'" . $str . "'";
    }

    /**
     * @param string $_d
     *
     * @return string
     */
    function _PodMap($_d)
    {
        if ( isset($this->delimMap) && isset($this->delimMap[$_d]) ) {
            return $this->delimMap[$_d];
        }

        return $_d;
    }

    /**
     * @param array      $r
     * @param int        $compact
     * @param int|string $inden
     *
     * @return string
     */
    private function _PodEncodeR(&$r, $compact = 0, $inden = 0)
    {
        if ( ! isset($this->structDepth) ) {
            $this->structDepth = 0;
        }
        $this->structDepth++;
        $oldinden = $inden ?: '';
        /** @noinspection PhpUnusedLocalVariableInspection */
        $comma = ',';
        if ( ! ( $inden ) ) {
            $inden = $compact ? '' : '    ';
        } else {
            $inden = $inden . ( $compact ? '' : '    ' );
        }

        if ( ! isset($r) ) {
            // 		echo( "pod_encode_rI: undefined argument" );
            $this->structDepth--;

            return $this->_PodMap('undef');
        }
        $result = '';
        $cnt = count($r);
        if ( ! is_array($r) ) {
            /* @var string $r */
            $result .= $this->_PodQuote($r);
        } else if ( ! isset($r[0]) ) {
            if ( $cnt == 0 ) {
                $result .= $this->_PodMap("{") . " " . $this->_PodMap("}");
            } else {
                $result .= $this->_PodMap("{") . "\n$inden";
                $i = 0;
                foreach ( $r as $k => $v ) {
                    $i++;
                    $qk = $this->quoteKeys ? $this->_PodQuote($k) : $k;
                    $result .= "$qk" . $this->_PodMap('=>') . " ";
                    $nextinden = ( $i == $cnt ) ? $oldinden : $inden;
                    $comma = ( $i == $cnt ) ? '' : ',';
                    if ( ! isset($r[$k]) ) {
                        $result .= $this->_PodMap("undef") . "$comma\n$nextinden";
                    } else {
                        $el = $r[$k];
                        if ( is_array($el) ) {
                            $result .= $this->_PodEncodeR($el, $compact, $inden) . "$comma\n$nextinden";
                        } else {
                            $qel = $this->_PodQuote($el);
                            $result .= "$qel$comma\n$nextinden";
                        }
                    }
                }
                $result .= $this->_PodMap("}");
            }
        } else {
            if ( $cnt == 0 ) {
                $result .= $this->_PodMap('[') . " " . $this->_PodMap(']');
            } else if ( $cnt == 1 && ! is_array($r[0]) ) {
                $result .= $this->_PodMap('[') . ' ' .
                           $this->_PodQuote($r[0]) . ' ' . $this->_PodMap(']');
            } else {
                $result .= $this->_PodMap("[") . "\n$inden";
                for ( $i = 0; $i < $cnt; $i++ ) {
                    $nextinden = ( $i == $cnt - 1 ) ? $oldinden : $inden;
                    $comma = ( $i == $cnt - 1 ) ? '' : ',';
                    $el = $r[$i];
                    if ( ! isset($el) ) {
                        $result .= $this->_PodMap("undef") . " $comma\n$nextinden";
                    } else if ( is_array($el) ) {
                        $result .= $this->_PodEncodeR($el, $compact, $inden) . "$comma\n$nextinden";
                    } else {
                        $qel = $this->_PodQuote($el);
                        $result .= "$qel$comma\n$nextinden";
                    }
                }
                $result .= $this->_PodMap("]");
            }
        }
        $this->structDepth--;

        return $result;
    }

}

