var jq=jQuery.noConflict();
jq(function() {
    jq('#fromdate')
        .datepicker(
            {
                showOn: 'button',
                buttonImage: '../resources/images/ia-app/buttons/calendar.png',
                buttonImageOnly: true,
                buttonText: 'From Date',
                // minDate:
                onSelect: function(selected) {
                    jq('#fromdate')
                        .datepicker('option', 'minDate', selected);
                }
            }
        );
});

function copyToClipboard(caller, id)
{
    jq('#' + id).select();
    document.execCommand('copy');
    document.getSelection().removeAllRanges();
    jq(caller).focus();
}

function showDataFixPopup(id, dataFixIndex, element)
{
    var key = "datafix_"+dataFixIndex;
    var sql = '';
    var cnyKey = document.getElementById('cnykey').value;
    var cnyId = document.getElementById('cnytitle').value;
    var dbInfo = document.getElementById('dbinfo').value;

    if (dataFixIndex == 'all' && dataFixes[id]) {
        sql = '';
        for (const fix in dataFixes[id]) {
            sql += dataFixes[id][fix] + "\n/\n\n";
        }
    } else if (dataFixes[id][key]) {
        sql = dataFixes[id][key] + "\n/\n";
    } else {
        return;
    }

    var html = '<span class="dataFixHeader">Data Fix #' + dataFixIndex + '</span>'
               + '<span class="closePopup" onclick="closeDatafixPopup()">X</span>'
               + '<textarea id="sql" class="sql" spellcheck="false" readonly>' + sql + '</textarea>'
               + '<div class="data-fix-options"><span>'
               + '<a href="#" onclick="copyToClipboard(this, \'sql\');return false;">Copy All</a></span></div>';

    var pos = element.getBoundingClientRect();

    jq('#datafix').css({'position': 'absolute',  'left': pos.left+20});

    jq('#datafix').html(html);
    jq('#datafix').css('display', 'block');

    return false;
}

function closeDatafixPopup()
{
    jq('#datafix').css('display', 'none');

    return false;
}

function toggleShow(id) {
    show(id);

    hide(id + '_plus', 'inline');
    show(id + '_minus', 'inline');
}

function toggleHide(id) {
    hide(id);

    hide(id + '_minus', 'inline');
    show(id + '_plus', 'inline');
}

function show(id, type)
{
    type = !type ? 'inline' : type;
    document.getElementById(id).style.display = type;
}

function hide(id)
{
    document.getElementById(id).style.display = 'none';
}

function getDatafixAjaxURL()
{
    var ajaxURL = "cn_datafix_ajax.phtml";
    return ajaxURL + '?&random=' + Math.random();
}

function performActionInloop(indexes , task , action) {
    for (i = 0; i < indexes.length; i++) {
        if(action == 'hide') {
            hide(task +'_'+ indexes[i]);
        }else if(action == 'show'){
            show( task + '_'+indexes[i]);
        }else if(action == 'showErrorMsg'){
            showErrorMsg(task ,indexes[i]);
        }
    }
}

function doApplyAndRecal(noRecalc, indexes, category, keys, date, cndKey ,resolve)
{
    performActionInloop(indexes , 'applybutton' , 'hide');
    performActionInloop(indexes , 'applyspinner','show');

    var xmlhttp = new XMLHttpRequest();
    var cny = document.getElementById('cnytitle');

    var jrnlElem = document.getElementById('jrnl');
    var jrnl = jrnlElem.options[jrnlElem.selectedIndex].value;

    var pioElem = document.getElementById('postinopen');
    var postInOpen = pioElem.checked;

    var url = getDatafixAjaxURL();
    var postParams = "&.cmd=applyAndRecalc&.cnytitle=" + encodeURIComponent(cny.value) + "&.category=" + category + "&.jrnl=" + jrnl
        + "&.keys=" + keys + "&.date=" + encodeURIComponent(date) +  "&.cndkey=" + cndKey +  "&.noRecalc=" + noRecalc;

    if (postInOpen) {
        postParams += "&.postinopen=1";
    }

    xmlhttp.onreadystatechange = function () {
        var  status = false;
        if (xmlhttp.readyState == 4) {
            if ( xmlhttp.status == 200 ) {
                if ( ifStillLoggedIn(xmlhttp.responseText) ) {
                    var myArr = JSON.parse(xmlhttp.responseText);
                    if ( myArr ) {
                        if ( ! myArr['STATUS'] ) {
                            performActionInloop(indexes , myArr['ERRMSG'],'showErrorMsg');
                            performActionInloop(indexes , 'applybutton','show');
                        } else {
                            performActionInloop(indexes , 'applysuccess','show');
                        }

                    }
                    status = myArr['STATUS'];
                }
            } else {
                performActionInloop(indexes , xmlhttp.status,'showErrorMsg');
                performActionInloop(indexes , 'applybutton','show');
            }
            performActionInloop(indexes , 'applyspinner','hide');
            if(resolve){
                resolve(status);
            }

        }
    };
    xmlhttp.open("POST", url, true);
    xmlhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xmlhttp.send(postParams);
}

function ifStillLoggedIn(responseText)
{
    if(responseText.indexOf("Please log in") == -1) {
        return true;
    }

    location.href = 'querytool.phtml';
}

function showErrorMsg(msg , index)
{
    var msgHTML = msg;
    show('error-'+ index);
    document.getElementById('error-'+ index).setAttribute('title', msgHTML);

}

var stopAutoRecal = {}
var executedElement = {}
function  stopApplyAndRecalc(category){
    stopAutoRecal[category] =  { 'stop' : true };
}

const callEachApplyAndRecalc = async category => {
    enableStopButton(category);
    if(!executedElement[category]){
        executedElement[category] = {} ;
    }
    stopAutoRecal[category] =  { 'stop' : false };
    // function callEachApplyAndRecalc(category ) {
    var elementDetails = {};
    var checkedElement = {} ;
    document.getElementsByName(category).forEach(function (innerEle) {
        if (innerEle.checked) {
            var indexKey = innerEle.dataset.cnkey +'_'+ innerEle.dataset.cndkey;

            if(category == 'noOnExpense' || category == 'wrongGLBatchKeyMappedRevenue'){
                indexKey = indexKey+'_'+innerEle.dataset.key;
            }

            if(!elementDetails[indexKey]){
                elementDetails[indexKey] = { 'keyArray' : [innerEle.dataset.key]  , 'date' : innerEle.dataset.date , 'indexArray' : [innerEle.dataset.datapopupindex] ,
                    'noreCalc':innerEle.dataset.norecalc ,'category' :  innerEle.dataset.category , 'cndKey' : innerEle.dataset.cndkey }
            }else{
                elementDetails[indexKey]['keyArray'].push(innerEle.dataset.key);
                elementDetails[indexKey]['indexArray'].push(innerEle.dataset.datapopupindex);
            }
        }
    })
    console.log(elementDetails) ;
    var callBackStatus = true;
    for (let indexKey in elementDetails){
        if(!executedElement[category][indexKey]) {
            var category = elementDetails[indexKey]['category'];
            var date = elementDetails[indexKey]['date'];
            var noRecalc = elementDetails[indexKey]['noreCalc'];
            var indexArray = elementDetails[indexKey]['indexArray'];
            var keyArray = elementDetails[indexKey]['keyArray'];
            var cndKey = elementDetails[indexKey]['cndKey'];
            await doApplyAndRecalPromise(noRecalc, indexArray, category, keyArray, date, cndKey).then(status => {
                callBackStatus = status;
            });
            executedElement[category][indexKey] = true;
            if (!callBackStatus || (stopAutoRecal[category] && stopAutoRecal[category]['stop'])) {
                enableRunButton(category);
                disableStopButton(category);
                break;
            }
        }
    }

    disableStopButton(category);

}

const doApplyAndRecalPromise = (noRecalc, indexArray, category, keyArray, date, cndKey) => {
    return new Promise(resolve => doApplyAndRecal(noRecalc, indexArray, category, keyArray, date, cndKey ,resolve))
}

function disableStopButton(category){
    var runButton = document.getElementById(category+'_stop');
    runButton.disabled = true;
}

function enableStopButton(category){
    var runButton = document.getElementById(category+'_stop');
    runButton.disabled = false;
}

function enableRunButton(category) {
    var runButton = document.getElementById(category+'_run');
    runButton.disabled = false;
    runButton.value = 'Resume';
}

function applyAdjustments(applyJournal, applyGL) {
    var message, type;

    if (applyJournal && applyGL) {
        type = 'Journal and GL';
    } else if (applyJournal) {
        type = 'Journal';
    } else {
        type = 'GL';
    }

    message = 'Are you sure you want to apply all contract ' + type + ' adjustments?';

    if (confirm(message)) {
        jq('#msg')
            .html('')
            .hide();
        jq('.applyAdjustments')
            .prop('disabled', true);
        jq('#applyspinner')
            .show();

        jq.ajax(
            {
                type: 'POST',
                data: {
                    '.cmd': 'applyAdjustments',
                    '.cnytitle': encodeURIComponent(jq('#cnytitle')
                        .val()),
                    '.cn': encodeURIComponent(jq('#cn')
                        .val()),
                    '.jrnl': jq('#jrnl')
                        .val(),
                    '.applyJournal': applyJournal,
                    '.applyGL': applyGL,
                },
                url: getDatafixAjaxURL(),
                success: function(response) {
                    var responseArr = JSON.parse(response), msgs, msgField = jq('#msg');

                    jq('#applyspinner')
                        .hide();
                    jq('.applyAdjustments')
                        .prop('disabled', false);

                    if ( responseArr['STATUS'] ) {
                        msgField.removeClass('error');
                        msgs = responseArr['MSG'];
                    } else {
                        msgField.addClass('error');
                        msgs = responseArr['ERRMSG'];
                    }

                    if ( msgs ) {
                        var msg = msgs.join('<br>');
                        msgField.html(msg)
                            .show();
                    }
                }
            }
        );
    }
}