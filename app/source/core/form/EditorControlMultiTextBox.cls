<?php

/**
 * Class EditorControlMultiTextBox
 */
class EditorControlMultiTextBox extends EditorControlText
{
    /**
     * @param array       $params
     * @param EditorField $editorField
     */
    public function __construct(&$params, EditorField $editorField)
	{
		parent::__construct($params, $editorField);
	}
	
	protected function CalcParams()
	{
		parent::CalcParams();
		$entity = $this->params['type']['entity'];
		if( $entity == 'glaccount' )
		{ 
			GetAccountNoInfo($accNoInfo);
			if( $accNoInfo['PRIMACCTNOLEN'] ){
				$this->params['type']['maxlength']		= $accNoInfo['PRIMACCTNOLEN'];
				$this->params['type']['delimiter']		= $accNoInfo['ACCTNOSEPERATOR'];
				$this->params['type']['size']			= $this->params['type']['maxlength'];
				$this->params['type']['textboxsize']		= $accNoInfo['SUBACCTNOL<PERSON>'];
				$this->params['type']['textboxcount']	= intval($accNoInfo['SUBACCTNOLEN'] ? 
													((MAX_ACCT_NO_LEN - $accNoInfo['PRIMACCTNOLEN']) / 
														($accNoInfo['SUBACCTNOLEN'] + 1 )) : 0);
			}
		}
		if (!isset($this->params['maxlength'])) {
		    $this->params['maxlength'] = $this->params['type']['maxlength']; 
		}
		if (!isset($this->params['size'])) {
		    $this->params['size'] = $this->params['maxlength'] ? min($this->params['maxlength'],50) : 40; 
		}
		
		global $kValueDelimiter;
		$localDelim = $this->params['type']['delimiter'] ?: $kValueDelimiter;
		
		$this->params['type']['delimiter'] = $localDelim;
		$this->params['type']['count']		= $this->params['type']['textboxcount'] ?: 0;
		$this->params['type']['splitsize']  = $this->params['type']['textboxsize'] ?: 4;
	}
}
