<?
/**
 * EmployeePosition entity picker
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2021 Sage Intacct Inc.
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Picker class for EmployeePosition object
 */
class EmployeePositionPicker extends NPicker
{
    function __construct()
    {
        parent::__construct(
            [
                'entity'        => 'employeeposition',
                'fields'        => ['POSITIONID', 'NAME'],
                'pickfield'     => 'POSITIONID',
                'addlPickFields' => array('NAME'),
                'nonencodedfields' => [],
            ]
        );
    }
}