--OWNER_CONTACT:<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_V_TSLABORGLRESOLVE.sql

CREATE OR REPLACE FORCE VIEW v_tslaborglresolve (cny#,tskey,tsentryl<PERSON>o,glbatchkey,glentryl<PERSON>o,glent<PERSON><PERSON><PERSON><PERSON><PERSON>,tsentry<PERSON>,glentrykey,amount,trx_amount,costrate,percentage) AS
SELECT
    r.cny#,
    r.tskey,
    r.tsentrylineno,
    r.glbatchkey,
    r.glentrylineno,
    r.glentryo<PERSON>,
    te.record#,
    ge.record#,
    r.amount,
    r.trx_amount,
    r.costrate,
    r.percentage
  FROM
    tsglresolve r,
    timesheetentry te,
    glentry ge,
    basejournal j,
    glbatch gl
  WHERE r.cny#        = te.cny#
  AND r.tskey         = te.timesheetkey
  AND r.tsentrylineno = te.lineno
  AND r.cny#          = ge.cny#
  AND r.glbatchkey    = ge.batch#
  AND r.glentrylineno = ge.line_no
  AND r.cny#          = gl.cny#
  AND gl.record#      = r.glbatchkey
  AND r.cny#          = j.cny#
  AND j.record#       = gl.journal#
  AND j.statistical   = 'F';