<?
$_batch = Request::$r->_batch;
$_recordtype = Request::$r->_recordtype;
/** @noinspection PhpUndefinedVariableInspection */
$obj = array(
    'title' => 'IA.EXPENSES',
    'placeholdertitle' => 'IA.SUMMARY_TITLE_EXPENSES',
    'filter' => "lower(PRRECORD.PRBATCHKEY) = lower('$_batch')",
    'addlabel' => I18N::getSingleToken('IA.EMPLOYEE_EXPENSE_SHEET'),
    'fieldlabels' => array(
        'IA.LAST_NAME', 'IA.FIRST_NAME', 'IA.EXPENSE_HASHKEY', 'IA.AMOUNT', '', 'IA.DATE'
    ),
    'qryfields' => array(
        'prrecord.record#',
        'contact.firstname', 
        'contact.lastname',
        'recordid', 
        'prrecord.totalentered', 
        'prrecord.state', 
        'prrecord.whencreated',
        'prrecord.totalpaid',
        'prrecord.totalselected',
        'prrecord.totaldue'
    ),
    'tblfields' => array(
        'LASTNAME', 'FIRSTNAME', 'RECORDID', 'TOTALENTERED', 'STATE', 'WHENCREATED'
    ),
    'fullnames' => array(
        'CONTACT.LASTNAME', 'CONTACT.FIRSTNAME', 'RECORDID', 
        'PRRECORD.TOTALENTERED', 'PRRECORD.STATE', 'PRRECORD.WHENCREATED'
    ),
    'groomtype' => array(
        'CHAR', 'CHAR', 'CHAR', 'CHAR', 'EXPENSESTATE', 'DATE'
    ),
    'format' => array(
        'TOTALENTERED' => array(
            'calign' =>'right',
            'cwidth' => '1%'
        ),
        /*
        'WHENCREATED' => array(
        'calign' => 'center',
        'cwidth' => '1%'
        ),
        'STATE' => array(
        'calign' => 'center',
        'cwidth' => '1%'
        ),
        */
        'RECORDID' => array(
            'cwidth' => '1%'
        )
    ),
    'hlpfile' => 'EE03',
    'sortcol' => 'LASTNAME:a',
    'detail' => "prrecord.phtml?.batch=$_batch&.op=".GetIdForRecType($_recordtype),
    'editurl' => FwdUrl("editor.phtml?.batch=$_batch&.recordtype=$urlrecordtype", $embeddedDone),
    'addurl' => FwdUrl("editor.phtml?.batch=$_batch&.recordtype=$urlrecordtype", $embeddedDone),
    'isTokenUsed' => true
);

