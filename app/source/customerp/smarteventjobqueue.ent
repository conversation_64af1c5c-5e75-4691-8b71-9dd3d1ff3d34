<?php
/**
 * File description
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Intacct Corporation, All Rights Reserved
 */

require 'fifodispatcherqueue.ent';
$kSchemas['smarteventjobqueue'] = $kSchemas['fifodispatcherqueue'];

$kSchemas['smarteventjobqueue']['ownedobjects'] =  [
    [
        'fkey' => SmartEventJobDetailsManager::SMARTEVENTJOBNO,
        'invfkey' => 'RECORDNO',
        'entity' => 'smarteventjobdetails',
        'path' => 'SMARTEVENTS'
    ]
];
