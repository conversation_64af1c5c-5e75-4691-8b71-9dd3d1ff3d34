declare
oCount NUMBER;
nCount NUMBER;

begin
-- Update element if exists with old values
  SELECT count(*) into oCount FROM cstoolnames where id = 'CSTOOLS_TOOLNAME_ENG_RATE_LIMIT_PROFILE';
  SELECT count(*) into nCount FROM cstoolnames where id = 'CSTOOLS_TOOLNAME_ENG_RATE_LIMIT_MANAGER';
  IF (nCount <= 0) AND (oCount > 0) THEN
    execute immediate '
update CSTOOLNAMES set HREF=''rate_limits.phtml'', NAME=''Rate Limit Manager'', ID=''CSTOOLS_TOOLNAME_ENG_RATE_LIMIT_MANAGER'' where ID=''CSTOOLS_TOOLNAME_ENG_RATE_LIMIT_PROFILE''
';

END IF;
end;

