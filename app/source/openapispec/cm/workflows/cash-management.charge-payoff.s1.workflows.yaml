cash-management-charge-payoff-actions-reverse-request:
  type: object
  x-mappedTo: chargepayoff
  properties:
    key:
      type: string
      description: System-assigned key for the charge payoff.
      x-mappedTo: RECORDNO
      example: '23'
    reversedDate:
      type: string
      format: date
      description: Date this transaction is reversed.
      x-mappedTo: VOIDDATE
      example: '2024-01-23'
    notes:
      type: string
      description: Notes or comments about the reason for the reverse of charge payoff.
      x-mappedTo: DESCRIPTION
      example: Reversed the charge payoff for duplicate entry
  required:
    - key
    - reversedDate
cash-management-charge-payoff-actions-reverse-response:
  type: object
  x-mappedTo: chargepayoff
  properties:
    key:
      type: string
      description: System-assigned unique record key for the new reverse charge payoff.
      x-mappedTo: RECORDNO
      readOnly: true
      example: '24'
    id:
      type: string
      description: Unique identifier for the new reverse charge payoff.
      x-mappedTo: RECORDNO
      readOnly: true
      example: '24'
    href:
      type: string
      description: URL endpoint for the reverse charge payoff.
      readOnly: true
      example: /objects/cash-management/charge-payoff/24
    state:
      type: string
      description: Charge payoff state after reverse
      readOnly: true
      x-mappedTo: STATE
      example: voided