function onLoadEvents() {

}

function BeforeSubmit() {
    var nrOfBuilders = window.editor.view.stdButtons.value['NROFBUILDERS'];
    var buildersGrid = window.editor.view.value['BUILDERS_GRID'];

    var actualBuilderCount = 0;
    for(var i=0; i<buildersGrid.length; i++){
        if(buildersGrid[i].USER !== undefined && buildersGrid[i].USER !== '')
            actualBuilderCount++;
    }

    var isValid = true;
    // check for duplicates
    for ( var i = 0; i < buildersGrid.length; i ++ ) {
        for ( var j = i + 1; j < buildersGrid.length; j ++ ) {
            if ( buildersGrid[i].USER !== '' && buildersGrid[j].USER !== '' && buildersGrid[i].USER == buildersGrid[j].USER) {
                window.view.clearMessages();
                window.view.addMessage(MESSAGE_ERROR, 'You can only have 1 Explorer!');
                isValid = false;
                break;
            }
        }
    }

    if (nrOfBuilders !== '-1' && actualBuilderCount > nrOfBuilders) {
        if (isValid) {
            window.view.clearMessages();
            isValid = false;
        }

        window.view.addMessage(MESSAGE_ERROR, 'You have already defined your Single Explorer. In order to define another Explorer, please replace the existing one.');
    }

    return isValid;
}

