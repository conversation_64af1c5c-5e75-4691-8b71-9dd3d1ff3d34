<?
/**
 *    FILE:            aropenbatch.ent
 *    AUTHOR:            <PERSON><PERSON>
 *    DESCRIPTION:    entity for aropenbatch, extends from arbatch.
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
*/
require 'arbatch.ent';
$kSchemas['aropenbatch'] = $kSchemas['arbatch'];
$kSchemas['aropenbatch']['dbfilters'] = array(
    array('aropenbatch.recordtype', 'in', array('ra', 'ri', 'rp', 'ro', 'rf')),
    array('aropenbatch.open', '=', 'T')
);
$kSchemas['aropenbatch']['printas'] = 'IA.SUMMARY';
$kSchemas['aropenbatch']['pluralprintas'] = 'IA.SUMMARIES';
