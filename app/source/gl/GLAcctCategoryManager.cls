<?
/***	FILE:
*	AUTHOR:			Senthil
*	DESCRIPTION:	entity manager for GLAcctCategory
*
*	(C) 2000, Intacct Corporation, All Rights Reserved
*
*	This document contains trade secret data that belongs to Intacct
*	Corporation and is protected by the copyright laws.  Information
*	herein may not be used, copied or disclosed in whole or in part
*	without prior written consent from Intacct Corporation.
*/

class GLAcctCategoryManager extends EntityManager
{

    function __construct($params)
    {
        parent::__construct($params);

        $industryType = GetMyIndustryType();
        if ($industryType !== '') {
            $industryFilter = [
                $industryType,
                IsModuleConfigured(Globals::$g->kDBBid) ? $industryType . '-DBB' : null,
                IsModuleConfigured(Globals::$g->kSIFRid) ? $industryType . '-SIFR' : null,
                IsModuleConfigured(Globals::$g->kSIEMRid) ? $industryType . '-EMR' : null
            ];

            $filters[] = [ 'INDUSTRYCODE', 'IN', array_filter($industryFilter) ];

            $this->_schemas[$this->_entity]['dbfilters'] = ( $this->GetDBFilters() ) ? : $filters;
        }
    }

}

