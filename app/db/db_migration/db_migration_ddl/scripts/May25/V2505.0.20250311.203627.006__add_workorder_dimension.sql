--OWNER_CONTACT: <EMAIL>

/*
== DELTA ONLY FOR DEV SCHEMAS ==
Description:
Repair action: IGNORE
*/

CREATE TABLE WORKORDERSTATE (
                                CNY#                NUMBER(15),
                                RECORD#             NUMBER(15),
                                NAME                VARCHAR2(200 CHAR),
                                STATUS              CHAR(1 CHAR),
                                PARENTKEY           NUMBER(15),
                                DEPTKEY             NUMBER(15),
                                LOCATIONKEY         NUMBER(15),
                                WHENCREATED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
                                WHENMODIFIED DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
                                CREATEDBY           NUMBER(15),
                                MODIFIEDBY          NUMBER(15),
                                CUSTFIELD1          VARCHAR2(4000 CHAR),
                                CUSTFIELD2          VARCHAR2(4000 CHAR),
                                CUSTFIELD3          VARCHAR2(4000 CHAR),
                                CUSTFIELD4          VARCHAR2(4000 CHAR),
                                CUSTFIELD5          VARCHAR2(4000 CHAR),
                                CUSTFIELD6          VARCHAR2(4000 CHAR),
                                CUSTFIELD7          VARCHAR2(4000 CHAR),
                                CUSTFIELD8          VARCHAR2(4000 CHAR),
                                CUSTFIELD9          VARCHAR2(4000 CHAR),
                                CUSTFIELD10         VARCHAR2(4000 CHAR),
                                CUSTFIELD11         VARCHAR2(4000 CHAR),
                                CUSTFIELD12         VARCHAR2(4000 CHAR),
                                CUSTFIELD13         VARCHAR2(4000 CHAR),
                                CUSTFIELD14         VARCHAR2(4000 CHAR),
                                CUSTFIELD15         VARCHAR2(4000 CHAR),
                                CUSTFIELD16         VARCHAR2(4000 CHAR),
                                CUSTFIELD17         VARCHAR2(4000 CHAR),
                                CUSTFIELD18         VARCHAR2(4000 CHAR),
                                CUSTFIELD19         VARCHAR2(4000 CHAR),
                                CUSTFIELD20         VARCHAR2(4000 CHAR),
                                CUSTFIELD21         VARCHAR2(4000 CHAR),
                                CUSTFIELD22         VARCHAR2(4000 CHAR),
                                CUSTFIELD23         VARCHAR2(4000 CHAR),
                                CUSTFIELD24         VARCHAR2(4000 CHAR),
                                CUSTFIELD25         VARCHAR2(4000 CHAR),
                                REL1                NUMBER(15, 0),
                                REL2                NUMBER(15, 0),
                                REL3                NUMBER(15, 0),
                                REL4                NUMBER(15, 0),
                                REL5                NUMBER(15, 0),
                                REL6                NUMBER(15, 0),
                                REL7                NUMBER(15, 0),
                                REL8                NUMBER(15, 0),
                                REL9                NUMBER(15, 0),
                                REL10               NUMBER(15, 0),
                                REL11               NUMBER(15, 0),
                                REL12               NUMBER(15, 0),
                                REL13               NUMBER(15, 0),
                                REL14               NUMBER(15, 0),
                                REL15               NUMBER(15, 0),
                                REL16               NUMBER(15, 0),
                                REL17               NUMBER(15, 0),
                                REL18               NUMBER(15, 0),
                                REL19               NUMBER(15, 0),
                                REL20               NUMBER(15, 0),
                                REL21               NUMBER(15, 0),
                                REL22               NUMBER(15, 0),
                                REL23               NUMBER(15, 0),
                                REL24               NUMBER(15, 0),
                                REL25               NUMBER(15, 0),
                                REL26               NUMBER(15, 0),
                                REL27               NUMBER(15, 0),
                                REL28               NUMBER(15, 0),
                                REL29               NUMBER(15, 0),
                                REL30               NUMBER(15, 0),
                                REL31               NUMBER(15, 0),
                                REL32               NUMBER(15, 0),
                                REL33               NUMBER(15, 0),
                                REL34               NUMBER(15, 0),
                                REL35               NUMBER(15, 0),
                                REL36               NUMBER(15, 0),
                                REL37               NUMBER(15, 0),
                                REL38               NUMBER(15, 0),
                                REL39               NUMBER(15, 0),
                                REL40               NUMBER(15, 0),
                                REL41               NUMBER(15, 0),
                                REL42               NUMBER(15, 0),
                                REL43               NUMBER(15, 0),
                                REL44               NUMBER(15, 0),
                                REL45               NUMBER(15, 0),
                                REL46               NUMBER(15, 0),
                                REL47               NUMBER(15, 0),
                                REL48               NUMBER(15, 0),
                                REL49               NUMBER(15, 0),
                                REL50               NUMBER(15, 0),
                                REL51               NUMBER(15, 0),
                                REL52               NUMBER(15, 0),
                                REL53               NUMBER(15, 0),
                                REL54               NUMBER(15, 0),
                                REL55               NUMBER(15, 0),
                                REL56               NUMBER(15, 0),
                                REL57               NUMBER(15, 0),
                                REL58               NUMBER(15, 0),
                                REL59               NUMBER(15, 0),
                                REL60               NUMBER(15, 0),
                                REL61               NUMBER(15, 0),
                                REL62               NUMBER(15, 0),
                                REL63               NUMBER(15, 0),
                                REL64               NUMBER(15, 0),
                                REL65               NUMBER(15, 0),
                                REL66               NUMBER(15, 0),
                                REL67               NUMBER(15, 0),
                                REL68               NUMBER(15, 0),
                                REL69               NUMBER(15, 0),
                                REL70               NUMBER(15, 0),
                                REL71               NUMBER(15, 0),
                                REL72               NUMBER(15, 0),
                                REL73               NUMBER(15, 0),
                                REL74               NUMBER(15, 0),
                                REL75               NUMBER(15, 0),
                                REL76               NUMBER(15, 0),
                                REL77               NUMBER(15, 0),
                                REL78               NUMBER(15, 0),
                                REL79               NUMBER(15, 0),
                                REL80               NUMBER(15, 0),
                                REL81               NUMBER(15, 0),
                                REL82               NUMBER(15, 0),
                                REL83               NUMBER(15, 0),
                                REL84               NUMBER(15, 0),
                                REL85               NUMBER(15, 0),
                                REL86               NUMBER(15, 0),
                                REL87               NUMBER(15, 0),
                                REL88               NUMBER(15, 0),
                                REL89               NUMBER(15, 0),
                                REL90               NUMBER(15, 0),
                                REL91               NUMBER(15, 0),
                                REL92               NUMBER(15, 0),
                                REL93               NUMBER(15, 0),
                                REL94               NUMBER(15, 0),
                                REL95               NUMBER(15, 0),
                                REL96               NUMBER(15, 0),
                                REL97               NUMBER(15, 0),
                                REL98               NUMBER(15, 0),
                                REL99               NUMBER(15, 0),
                                REL100              NUMBER(15, 0),
                                SI_UUID             VARCHAR2(36 CHAR),
                                CONSTRAINT PK_WORKORDERSTATE PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX ENABLE,
                                CONSTRAINT UQ_WORKORDERSTATE_NAME UNIQUE (CNY#, NAME) USING INDEX TABLESPACE ACCTINDX ENABLE,
                                CONSTRAINT FK_WORKORDERSTATE_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON DELETE CASCADE DEFERRABLE ENABLE,
                                CONSTRAINT FK_WORKORDERSTATE_PARENTKEY FOREIGN KEY (CNY#, PARENTKEY) REFERENCES WORKORDERSTATE (CNY#, RECORD#) DEFERRABLE ENABLE,
                                CONSTRAINT CK_WORKORDERSTATE_STATUS CHECK (status in ('T', 'F')),
                                CONSTRAINT FK_WORKORDERSTATE_DEPTKEY FOREIGN KEY (CNY#, DEPTKEY) REFERENCES DEPARTMENT (CNY#, RECORD#) DEFERRABLE ENABLE,
                                CONSTRAINT FK_WORKORDERSTATE_LOCATIONKEY FOREIGN KEY (CNY#, LOCATIONKEY) REFERENCES LOCATION (CNY#, RECORD#) DEFERRABLE ENABLE,
                                CONSTRAINT FK_WORKORDERSTATE_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
                                CONSTRAINT FK_WORKORDERSTATE_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE
)TABLESPACE ACCTDATA
/

CREATE INDEX IX_WORKORDERSTATE_CREATEDBY ON WORKORDERSTATE (CNY#, CREATEDBY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_WORKORDERSTATE_MODIFIEDBY ON WORKORDERSTATE (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_WORKORDERSTATE_DEPTKEY ON WORKORDERSTATE (CNY#, DEPTKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_WORKORDERSTATE_LOCATIONKEY ON WORKORDERSTATE (CNY#, LOCATIONKEY) TABLESPACE ACCTINDX
/

CREATE INDEX IX_WORKORDERSTATE_PARENTKEY ON WORKORDERSTATE (CNY#, PARENTKEY) TABLESPACE ACCTINDX
/