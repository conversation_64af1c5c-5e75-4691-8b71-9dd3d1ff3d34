//=============================================================================
//
//	FILE:        create_client.js
//	AUTHOR:      <PERSON><PERSON> <<EMAIL>>
//	DESCRIPTION: javascript functions for Create Client Wizard
//
//	(C)2024, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================
'use strict';

/**
 * change the company setup according to the selected country
 *
 * @param {object} p_data
 */
function setDefaultOptions(p_data) {
    var default_timezone = p_data.console_timezone;
    var selected_country = document.getElementById('country').value;
    var selected_country_code = p_data.countries[selected_country].code;

    if (p_data.countries_setup.hasOwnProperty(selected_country_code)) {
        default_timezone = p_data.countries_setup[selected_country_code].country_defaults.time_zone;
    }

    // set the timezone
    document.getElementById('time_zone').value = default_timezone;
}

/**
 * Show a loader/spinner
 *
 * @returns {boolean}
 */
function showLoader() {
    document.getElementById('loader').style.display = 'flex';
    return true;
}
