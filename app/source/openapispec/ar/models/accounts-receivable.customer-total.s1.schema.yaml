title: accounts-receivable/customer-total
x-mappedTo: customertotal
type: object
description: Provides information about how much is owed to a customer.
readOnly: true
properties:
  key:
    type: string
    description: System-assigned key for the customer total.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '1'
  id:
    type: string
    description: Customer totals ID.
    readOnly: true
    x-mappedTo: CUSTOMERKEY
    example: '5'
  href:
    type: string
    description: Endpoint for the customer total.
    readOnly: true
    example: /objects/accounts-receivable/customer-total/1
  totalDue:
    type: string
    description: Total due.
    x-mappedTo: TOTALDUE
    format: decimal-precision-2
    example: '100.01'
  customer:
    type: object
    x-mappedTo: customer
    description: The customer who owes the amount due.
    x-object: accounts-receivable/customer
    properties:
      key:
        type: string
        description: System-assigned key for the customer.
        x-mappedTo: CUSTOMERKEY
        example: '5'
      id:
        type: string
        description: Customer ID.
        x-mappedTo: CUSTOMERID
        example: 'cust_id_531214'
      name:
        type: string
        description: Name of the customer.
        x-mappedTo: CUSTOMERNAME
        example: 'cnxt'
      href:
        type: string
        description: Endpoint for the customer.
        readOnly: true
        example: /objects/accounts-receivable/customer/5
  entity:
    type: object
    allOf:
      - $ref: ../../common/references/entity-ref.s1.schema.yaml
      - type: object
        properties:
          id:
            type: string
            description: User-assigned ID for the objects Entity Location
            x-mappedTo: ENTITYID
            x-mutable: false
            readOnly: true
            example: NYC
          name:
            type: string
            description: Name of the entity.
            x-mappedTo: ENTITYNAME
            example: 'New York City'