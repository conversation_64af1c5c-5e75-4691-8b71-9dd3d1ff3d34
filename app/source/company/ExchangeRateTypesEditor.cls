<?php

/**
 * =============================================================================
 *
 * FILE:        ExchangeRateTypesEditor.cls
 * AUTHOR:        
 * DESCRIPTION:   
 *
 * (C)2000,2009 Intacct Corporation, All Rights Reserved
 *
 * Intacct Corporation Proprietary Information.
 * This document contains trade secret data that belongs to Intacct
 * corporation and is protected by the copyright laws. Information herein
 * may not be used, copied or disclosed in whole or part without prior
 * written consent from Intacct Corporation.
 * =============================================================================
 */

class ExchangeRateTypesEditor extends FormEditor
{
    /**
     * @param array $_params
     */
    public function __construct($_params = array())
    {
        parent::__construct($_params);
        AT($this->textTokens, "IA.CONFIRM_DEFAULT_EXCHANGE_RATE_TYPE");
        AT($this->textTokens, "IA.CONFIRM_NO_DEFAULT_EXCHANGE_RATE_TYPE");
    }

    /**
     * @param array $obj
     *
     * @return bool
     */
    function prepareObjectForCopyNew(&$obj)
    {        
        $ok = parent::prepareObjectForCopyNew($obj);        
        if($obj['ISDEFAULT']=='true') {
            $obj['ISDEFAULT']='false';           
        }
        return $ok;
    }

    /**
     * @param array $_params
     * @param array $obj
     * @param bool  $ok
     *
     * @return bool
     */
    protected function innerProcessCreateAction(&$_params, &$obj, $ok)
    {
        $ok = parent::innerProcessCreateAction($_params, $obj, $ok);
        
        // Save platform relationship info here
        if (!util_isPlatformDisabled()) {
            /** @noinspection PhpUndefinedVariableInspection */
            Pt_StandardUtil::savePlatformRelationships($_params['entity'], $objId, TYPE_NEW);
        }       
        
        return $ok;
    }

    /**
     * @return array
     */
    protected function getJavaScriptFileNames()
    {
        return array('../resources/js/exchangeratetypes.js');
    }

    /**
     * @return array
     */
    function getEditorGlobals()
    {
        $vars = parent::getEditorGlobals();
        $entityMgr = $this->GetManager('exchangeratetypes');
        // Get translated default exchange rate type
        $default_exchangeRateType = $entityMgr->GetDefaultExchangeRateType(false, true);

        $vars['default_exchrateType_name'] = $default_exchangeRateType[0]['NAME'];
        return $vars;
        
    }

}

