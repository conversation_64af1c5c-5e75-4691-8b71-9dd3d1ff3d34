<?php

/**
 * class SforceSyncProcessorItem to sync the Salesforce object 'Product' from Salseforce to Intacct
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

require_once 'SforceConstants.inc';

class SforceSyncProcessorItem extends SforceSyncProcessor
{
    /**
     * @param string[] &$sfdcProduct
     * @param string[] $sfObj
     *
     * @return bool
     */
    protected function validateRawData($sfdcProduct, $sfObj)
    {
        if (!parent::validateRawData($sfdcProduct, $sfObj)) {
            return false;
        }

        $gErr = Globals::$g->gErr;

        if (!in_array(
            $sfdcProduct[SforceNamespaceHandler::getPrefix().'Item_Type__c'], SforceSyncConstants::getValidItemtype()
        )
        ) {
            $gErr->addIAError(
                'SFDC-1245', __FILE__.'.'.__LINE__,
                'Item type '.$sfdcProduct[SforceNamespaceHandler::getPrefix().'Item_Type__c'].' is not valid.',
                ['SFDC_PRODUCT_NAME_SPACE_HANDLER' => $sfdcProduct[SforceNamespaceHandler::getPrefix().'Item_Type__c']]
            );
            return false;
        }

        $validate = array(
            'ProductCode' => 'Product Code',
            SforceNamespaceHandler::getPrefix().'Item_Type__c' => 'Item Type'
        );

        //For item type 'Non-Inventory (Sales only)', 'Non-Inventory', 'Kit' and 'Stockable Kit',
        //No need to verify for 'Purchasing Unit', 'Ratio to Standard' (PO) and 'Cost Method'
        if ($sfdcProduct[SforceNamespaceHandler::getPrefix().'Item_Type__c'] == SforceSyncConstants::INV) {
            $validate[SforceNamespaceHandler::getPrefix().'Cost_Method__c'] = SforceSyncConstants::COST_METHOD;
            if (!in_array(
                $sfdcProduct[SforceNamespaceHandler::getPrefix().'Cost_Method__c'],
                SforceSyncConstants::getValidCostMethod()
            )
            ) {
                $gErr->addIAError(
                    'SFDC-1246', __FILE__.'.'.__LINE__,
                    'Cost method '.$sfdcProduct[SforceNamespaceHandler::getPrefix().'Cost_Method__c'].' is not valid.',
                    ['COST_METHOD_PRODUCT_NAME_SPACE_HANDLER' => $sfdcProduct[SforceNamespaceHandler::getPrefix().'Cost_Method__c']]
                );
                return false;
            }
        }

        foreach ($validate as $key => $val) {
            if (!isset($sfdcProduct[$key]) || $sfdcProduct[$key] == '') {
                $gErr->addIAError(
                    'SFDC-1244', __FILE__.'.'.__LINE__,
                    $val.' is missing at Salesforce for the product ' . $sfdcProduct['Name'],
                    ['VAL' => $val,'SFDC_PRODUCT_NAME' => $sfdcProduct['Name']]
                );
                return false;
            }
        }
        return true;
    }

    /**
     * Use this function to modify any value
     *
     * @param array    $sfdcProduct Values from Salesforce
     * @param array    $output      Intacct query output for object
     * @param string   $action      Type of action
     * @param string[] $itemValues
     *
     * @return string[]
     */
    protected function prepareInnerValue(&$sfdcProduct, $output, $action, $itemValues)
    {
        //Dont update base price if action is set & pricing rule is Intacct
        if ( !( $action == 'SET' && $this->sfpreferences['SFORCEPRICINGOPT'] == 'I' ) ) {
            $basePrice = $this->getBasePrice($sfdcProduct);
            $itemValues['BASEPRICE'] = $basePrice;
        }
        $itemValues['REQUEST_ORIGINATOR'] = 'SFORCE';
        return $itemValues;
    }

    /**
     * @param string[] $sfdcProduct
     *
     * @return string
     */
    private function getBasePrice($sfdcProduct)
    {
        $unitPrice = '0.0';
        // Base price to Standard price sync
        $pricebookId = $this->subMgr->GetExternalId('Base Price List', 'SOPRICELIST', 'SFORCE');

        //TODO: Documentation: If Standard Price Book is not sync then don't read that and set base price as Zero.
        /*if($pricebookId == '') {
            $whereClause1 = " Name = 'Standard Price Book'";
            $result1 = $this->_DoSFQuery(array('Id'), 'Pricebook2', $whereClause1);
            $pricebookId = $result1[0]['ID'];
        }*/

        if ($pricebookId != '') {
            $whereClause = " Pricebook2Id = '$pricebookId' and Product2Id = '" . $sfdcProduct['Id'] . "'";
            // Is Sales force company a MCP company?
            /** @noinspection PhpUnusedLocalVariableInspection */
            $warnmsg = '';
            if ($this->sfpreferences['SFORCEISMCP'] == 'true') {
                $basecurr = GetBaseCurrency();
                $whereClause .= " and CurrencyIsoCode = '" . $basecurr . "'";
                //$warnmsg = ' in base currency ' . $basecurr;
            }

            $select = 'UnitPrice';
            $sfobjName = 'PricebookEntry';
            $sfObjData = $this->query($select, $sfobjName, $whereClause);
            $unitPriceVal = $this->formatSfObjDataForIntacct($sfObjData, array($select));
            if (!empty($unitPriceVal)) {
                $unitPrice = $unitPriceVal['UnitPrice'];
            }
        }
        return $unitPrice;
    }

    /**
     * Format Salesforce data for Intacct
     *
     * @param stdClass $sfObjData Values from Salesforce
     * @param array    $fields    Fields from Intacct
     *
     * @return array
     */
    public function formatSfObjDataForIntacct($sfObjData, $fields)
    {
        $sfValues = parent::formatSfObjDataForIntacct($sfObjData, $fields);
        $itemTypes = array('Non-Inventory', 'Non-Inventory (Sales only)');
        if ( in_array($sfValues[SforceNamespaceHandler::getPrefix().'Item_Type__c'], $itemTypes)) {
            unset($sfValues[SforceNamespaceHandler::getPrefix().'Cost_Method__c']);
        }
        return $sfValues;
    }

}
