<?php
/**
 * Manager class for GL account group
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Manager class for GL account group
 */
class GLAcctGrpManager extends EntityManager
{
    const NO_ROLLUP_FOR_DIMENSIONS = ['item','affiliateentity'];
    // Variable used to store MEMBERTYPE info
    /* @var string $memberType */
    public $memberType = "";

    /* @var string $dimensionMemberType */
    public $dimensionMemberType = "";

    /* @var bool $initAcctBase */
    private $initAcctBase = false;

    /* @var bool $isia */
    public $isia = false;

    /**
     * @var array
     */
    protected $dimHeader = ['vendor','customer','project','employee','item','class','contract','task','warehouse','costtype', 'fixedasset','affiliateentity'];


    /* @var string[] $accountComponents */
    public static $accountComponents = array(
        'Accounts', 'Groups', 'Statistical Accounts', 'Computation', 'Category', 'Statistical Category'
    );

    /**
     * Retrieve single object
     *
     * @param int    $ID     record#
     * @param array  $fields list of fields
     *
     * @return array|false Object of account group
     */
    public function Get($ID, $fields = null)
    {
        $obj = parent::get($ID, $fields);

        if ( $obj === false ) {
            return false;
        }

        $this->transformMembers($obj);
        $this->handleStatMembers($obj);

        return $obj;
    }

    /**
     * @param $ignoreDimensionList
     *
     * @return bool
     */
    protected function ignoreDimensions(&$ignoreDimensionList) : bool
    {
        return false;
    }

    /**
     * Need to unset array elements based on member type value
     *
     * @param array &$obj Member type validvalue
     *
     * @return bool true always
     */
    private function handleStatMembers(&$obj)
    {
        if ( $obj['MEMBERTYPE'] == 'Statistical Accounts' ) {
            unset($obj['GLACCTRANGES']);
        } else if ( $obj['MEMBERTYPE'] == 'Accounts' ) {
            unset($obj['GLSTATACCTRANGES']);
        } else if ( $obj['MEMBERTYPE'] == 'Statistical Category' ) {
            unset($obj['GLCATGRPS']);
        } else if ( $obj['MEMBERTYPE'] == 'Category' ) {
            unset($obj['GLSTATCATGRPS']);
        }

        return true;
    }

    /**
     * To fetch department record# using ID
     *
     * @param string $dept Department id
     *
     * @return string Record# if found
     */
    private function getDeptId($dept)
    {
        if ( $dept != '' ) {
            $gManagerFactory = Globals::$g->gManagerFactory;
            $deptMgr = $gManagerFactory->getManager('department');

            $filter = array(
                'selects' => array('RECORDNO'),
                'filters' => array(array(
                                    array('DEPARTMENTID', '=', $dept),
                                    array('STATUS', '!=', 'inactive')
                                   )
                             )
            );

            $res = $deptMgr->GetList($filter);

            return $res[0]['RECORDNO'];
        }
        return '';
    }

    /**
     * To fetch location record# using ID
     *
     * @param string $loc location id
     *
     * @return string Record# if found
     */
    private function getLocId($loc)
    {

        if ( $loc != '' ) {
            $gManagerFactory = Globals::$g->gManagerFactory;
            $objLocMgr = $gManagerFactory->getManager('location');

            $filter = array(
                'selects' => array('RECORDNO'),
                'filters' => array(array(
                                    array('LOCATIONID', '=', $loc),
                                    array('STATUS', '!=', 'inactive')
                                   )
                             )
            );

            $res = $objLocMgr->GetList($filter);

            return $res[0]['RECORDNO'];
        }
        return '';
    }

    /**
     * To add new account group
     *
     * @param array &$values Array of new account group details
     *
     * @return bool true if success
     */
    protected function regularAdd(&$values)
    {
        $gErr = Globals::$g->gErr;
        $source = "GLAcctGrpManager::Add";

        $this->setDefaults($values);

        $ok = $this->translate($values);

        $ok = $ok && $this->validateAcctGroup($values, 'ADD');

        $this->_QM->beginTrx($source);

        $ok = $ok && parent::regularAdd($values);

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( !$ok ) {
            $objName = 'Account group';
            $errorCode = 'GL-0630';
            $placeholder1 = [];
            if ( !in_array($values['MEMBERTYPE'], self::$accountComponents) ) {
                $objName = $values['MEMBERTYPE'].' structure';
                $errorCode = 'GL-0631';
                $placeholder1 = ['MEMBERTYPE' => $values['MEMBERTYPE']];
            }
            $msg = "Could not create $objName ";
            $gErr->addIAError($errorCode, __FILE__ . ':' . __LINE__, $msg, $placeholder1);
            epp("$source: Error: $msg");
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * To update existing accout group
     *
     * @param array &$values Array of modified account group details
     *
     * @return bool true if success
     */
    protected function regularSet(&$values)
    {
        $gErr = Globals::$g->gErr;
        $source = "GLAcctGrpManager::Set";

        $this->setDefaults($values);

        $ok = $this->translate($values);

        $ok = $ok && $this->validateAcctGroup($values, 'SET');

        $this->_QM->beginTrx($source);

        $ok = $ok && parent::regularSet($values);

        // update project GL group account data:
        $groupsToUpdate = [];
        if ($ok) {
            $groupsToUpdate = ProjectGLGrpAcctHelper::getGroupParents($values['RECORDNO'], true);
            if ($groupsToUpdate === false) {
                // error
                $ok = false;
            }
        }
        $ok = $ok && ProjectGLGrpAcctHelper::refreshDataForGroup($groupsToUpdate);

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( !$ok ) {
            $objName = 'Account group';
            $errorCode = 'GL-0632';
            $placeholder1 = [];
            if ( !in_array($values['MEMBERTYPE'], self::$accountComponents) ) {
                $objName = $values['MEMBERTYPE'].' structure';
                $errorCode = 'GL-0633';
                $placeholder1 = ['MEMBERTYPE' => $values['MEMBERTYPE']];
            }
            $msg = "We couldn’t update the ". $objName;
            $gErr->addIAError($errorCode, __FILE__ . ':' . __LINE__, $msg, $placeholder1);// i18n todo - (code change review)
            epp("$source: Error: $msg");
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * Set default values
     *
     * @param array &$values Array of values
     *
     * @return bool true always
     */
    private function setDefaults(&$values)
    {
        // populate header title and total titles if not set
        if ( !isset($values['TITLE']) || $values['TITLE'] == "" ) {
            $values['TITLE'] = $values['NAME'];
        }
        if ( !isset($values['TOTALTITLE']) || $values['TOTALTITLE'] == "" ) {
            $values['TOTALTITLE'] = "Total " . $values['NAME'];
        }

        if ( !isset($values['ASOF']) ) {
            $values['ASOF'] = 'P';
        }

        if ( !$this->isia ) {
            // set it to allways true
            $values[':applyasof'] = 'T';

            if ( !isset($values['HOWCREATED']) ) {
                $values['HOWCREATED'] = 'U';
            }

            if ( isl_substr($values['FILTERDEPT'], 0, 8) != 'specific' ) {
                $values['DEPTNO'] = "";
            }

            if ( isl_substr($values['FILTERLOC'], 0, 8) != 'specific' ) {
                $values['LOCNO'] = "";
            }

            $dimInfo = $this->GetDimensionFields();

            foreach ( $dimInfo as $val ) {
                $fltFld = "FILTER" . isl_strtoupper($val['fullname']);
                $dimFldId = $val['path'];

                if ( isset($values[$fltFld]) && isl_substr($values[$fltFld], 0, 8) != 'specific' ) {
                    $values[$dimFldId] = "";
                }
            }
        }

        return true;
    }

    /**
     * Used to pass default values and translate it as required by entity manager
     *
     * @param array &$values Array of values needs to be transalted
     *
     * @return bool true if success
     */
    private function translate(&$values)
    {
        $gErr = Globals::$g->gErr;

        // normal balance expects the literal 'debit' or 'credit'
        // based on whether 1 0r -1 is passed set the valid literal!!
        if ( $values['NORMAL_BALANCE'] == '-1' ) {
            $values['NORMAL_BALANCE'] = 'debit';
        }
        if ( $values['NORMAL_BALANCE'] == '1' ) {
            $values['NORMAL_BALANCE'] = 'credit';
        }

        if ( $values['INCLUDECHILDAMT'] == 'F' ) {
            $values['INCLUDECHILDAMT'] = 'false';
        }

        // translate all members
        $memTyp = $this->getMemTyp();
        /** @noinspection PhpUnusedLocalVariableInspection */
        $mt = isl_strtolower($memTyp[$values['MEMBERTYPE']][1]);

        $ok = $this->translateMembers($values);
        $ok = $ok && $this->translateGroupPurpose($values);

        if ( !in_array($values['MEMBERTYPE'], self::$accountComponents) ) {
            $allDimObjs = IADimensions::getAllDimensionObjectProperties(false);
            $allowInclChild = [];
            foreach ($allDimObjs as $dimKey => $dimProperties) {
                if (in_array($dimKey, self::NO_ROLLUP_FOR_DIMENSIONS)) {
                    continue;
                }
                $allowInclChild[] = $dimProperties['internal_label_plural'];
            }
            if ($values['MEMBERTYPE']
                && (                !in_array($values['MEMBERTYPE'], $allowInclChild))
            ) {
                $values['INCLUDECHILDAMT'] = 'false';
            }


            $values['ASOF'] = '';
            $values['NORMAL_BALANCE'] = 'debit';

            $values['FILTERLOC'] = 'nofilter';
            $values['FILTERDEPT'] = 'nofilter';

            $dimInfo = $this->GetDimensionFields();
            foreach ( $dimInfo as $k => $val ) {
                $internalPath = $dimInfo['internalpath']??$k;
                $values['FILTER' . isl_strtoupper($internalPath)] = 'nofilter';
            }
        }


        if ( !$this->isia ) {
            // While coming from CSV or XML APIs we need to do a lookup and copy group ids to LOCNO or DEPTNO
            if ( isset($values['LOCGRPID']) && $values['LOCGRPID'] != "" ) {
                $values['LOCNO'] = $values['LOCGRPID'];
            }
            if ( isset($values['DEPTGRPID']) && $values['DEPTGRPID'] != "" ) {
                $values['DEPTNO'] = $values['DEPTGRPID'];
            }

            $locId = "";
            $deptId = "";
            if ( IsGroup('location', $values['LOCNO'], $locgrprec) ) {
                $values['LOCGRPKEY'] = $locgrprec;
                $values['LOCATIONKEY'] = '';
            } else {
                [$locId] = explode('--', $values['LOCNO']);
                $locId = $this->getLocId($locId);
                $values['LOCGRPKEY'] = '';
                $values['LOCATIONKEY'] = $locId;
            }

            if ( isl_substr($values['FILTERLOC'], 0, 8) == 'specific'
                && $values['LOCGRPKEY'] == '' && $values['LOCATIONKEY'] == ''
            ) {
                $values['LOCNO'] = $values['LOCNO'] ?? null;
                $msg = "Invalid Location " . $values['LOCNO'] . " selected";
                $gErr->addIAError(
                    'GL-0490', __FILE__ . ':' . __LINE__, $msg, ['LOCNO' => $values['LOCNO']]
                );
                $ok = false;
            }

            if ( IsGroup('department', $values['DEPTNO'], $deptgrprec) ) {
                $values['DEPTGRPKEY'] = $deptgrprec;
                $values['DEPTKEY'] = '';
            } else {
                [$deptId] = explode('--', $values['DEPTNO']);
                $deptId = $this->getDeptId($deptId);
                $values['DEPTGRPKEY'] = '';
                $values['DEPTKEY'] = $deptId;
            }

            if ( isl_substr($values['FILTERDEPT'], 0, 8) == 'specific'
                && $values['DEPTGRPKEY'] == '' && $values['DEPTKEY'] == ''
            ) {
                $values['DEPTNO'] = $values['DEPTNO']?? null;
                $msg = "Invalid Department " . $values['DEPTNO'] . " selected";
                $gErr->addIAError(
                    'GL-0489',
                    __FILE__ . ':' . __LINE__,
                    $msg,['DEPTNO'=>$values['DEPTNO']]
                );
                $ok = false;
            }

            $values['DEPTKEY'] = $deptId;
            $values['LOCATIONKEY'] = $locId;

            // we need to translate the dimensionsfields
            $ok = $ok && $this->translateDimensionFields($values);
        }

        $this->memberType = $memTyp[$values['MEMBERTYPE']][0];
        $this->dimensionMemberType = '';
        if ( in_array($this->memberType, array('LOC', 'DEP', 'VEN', 'CUS', 'PRJ', 'EMP', 'ITM', 'CLS'))) {
            $this->dimensionMemberType = $this->memberType;
            $this->memberType = 'DG';
        }

        if ( strstr($values['MEMBERTYPE'], 'Group of') || $values['MEMBERTYPE'] == 'Groups' ) {
            $values['ISLEAF'] = "F";
        } else if ( $values['MEMBERTYPE'] == 'Computation' ) {
            $values['ISLEAF'] = "";
        } else {
            $values['ISLEAF'] = "T";
        }

        // replace multiple spaces to single space in NAME field
        $values['NAME'] = isl_preg_replace('/\s\s+/', ' ', $values['NAME']);

        return $ok;
    }

    /**
     * Validate $values based on business logic
     *
     * @param array &$values Values to be validated
     * @param string  $action  ADD or SET
     *
     * @return bool returns true if valid
     */
    public function validateAcctGroup(&$values, $action='')
    {
        $gErr = Globals::$g->gErr;
        $ok = true;

        // required for validations
        $industry = ($values['INDUSTRYCODE']) ?: '';

        if (!$this->initAcctBase) {
            InitAccountBase($this->isia, $industry);
            $this->initAcctBase = true;
        }

        if ( !$this->isia ) {
            if ( !CompanyType($accrual) ) {
                eppp("CompanyType() adds an approriate error.");
                return false;
            }

            if (!(IsMCMESubscribed() && !GetContextLocation()) && isset($values['ISKPI']) && $values['ISKPI'] !== '') {
                $values['ISKPI'] = false;
            }

            if ( isl_substr($values['FILTERDEPT'], 0, 8) == 'specific'
                && (!isset($values['DEPTNO']) || $values['DEPTNO'] == '')
            ) {
                $gErr->addError(
                    'GL-0491', __FILE__ . ':' . __LINE__, ' Department filter cannot be empty'
                );
                $ok = false;
            }

            if ( isl_substr($values['FILTERLOC'], 0, 8) == 'specific'
                && (!isset($values['LOCNO']) || $values['LOCNO'] == '')
            ) {
                $gErr->addError(
                    'GL-0492', __FILE__ . ':' . __LINE__, ' Location filter cannot be empty'
                );
                $ok = false;
            }

            // Validate dimensions only if they are subscribed
            $dimInfo = $this->GetDimensionFields();
            //epp('$dimInfo:');eppp($dimInfo);dieFL();

            foreach ( $dimInfo as $val ) {
                $fltFld = "FILTER" . isl_strtoupper($val['fullname']);
                $dimFldId = $val['path'];

                if ( isset($values[$fltFld]) && isl_substr($values[$fltFld], 0, 8) == 'specific'
                    && (!isset($values[$dimFldId]) || $values[$dimFldId] == '')
                ) {
                    $gErr->addIAError(
                        'GL-0535', __FILE__ . ':' . __LINE__, $val['fullname']
                        . ' filter cannot be empty', ['FULLNAME' => $val['fullname']]
                    );
                    $ok = false;
                }
            }

            if ( !util_isPlatformDisabled() ) {
                $ptMap = GLEntryManager::getPTFieldsMap();

                foreach ( $ptMap as $defId => $fieldName ) {
                    $fltFld = "FILTER" . isl_strtoupper($fieldName);

                    if ( isl_substr($values[$fltFld], 0, 8) == 'specific'
                        && (!isset($values[$fieldName]) || $values[$fieldName] == '0' || $values[$fieldName] == '')
                    ) {

                        $objDef = Pt_DataObjectDefManager::getById($defId);
                        $fullname = $objDef->__toString();

                        $gErr->addIAError(
                            'GL-0535', __FILE__ . ':' . __LINE__, $fullname
                            . ' filter cannot be empty', ['FULLNAME' => $fullname]
                        );
                        $ok = false;
                    }

                    // this does not need rel as param, glacctgrp/ glentry always use the new storage
                    if ( Pt_RelationshipManagerChoose::newRelationshipRedesignReadNew()
                         && ( $values[$fltFld] == 'nofilter' || $values[$fltFld] == 'nullvalue' ) ) {
                        $keys = array_keys($values);
                        $index = array_search($fltFld, $keys);
                        $pos = false === $index ? count($values) : $index + 1;
                        $valuesInsert = [ $fieldName => null ];
                        $values = array_merge(array_slice($values, 0, $pos), $valuesInsert, array_slice($values, $pos));
                    }
                }
            }
        }

        if ( !isl_trim($values['NAME']) ) {
            $gErr->addError('GL-0451', __FILE__ . ':' . __LINE__, 'The Name field cannot be empty ');
            $ok = false;
        }

        if ( is_numeric($values['NAME']) ) {
            $gErr->addError('GL-0452', __FILE__ . ':' . __LINE__, 'The Name should not contain only numbers');
            $ok = false;
        }
        if ( $ok && $action == 'ADD' ) {
            $ok = $this->validateNewRec($values);
        } else if ( $ok && $action == 'SET' ) {
            $filter = array(
                'selects' => array('NAME', 'MEMBERTYPE'),
                'filters' => array(array(array('RECORDNO', '=', $values['RECORDNO'])))
            );

            $res = $this->GetList($filter);

            foreach ( $res as $acctgrp ) {
                if ( $acctgrp['NAME'] != $values['NAME'] ) {
                    $gErr->addError(
                        'GL-0453',
                        __FILE__ . ':' . __LINE__,
                        "Name can not be changed in edit operation"
                    );
                    $ok = false;
                }

                if ( $acctgrp['MEMBERTYPE'] != $values['MEMBERTYPE'] ) {
                    $gErr->addError(
                        'GL-0454',
                        __FILE__ . ':' . __LINE__,
                        "Structure type can not be changed in edit operation"
                    );
                    $ok = false;
                }
            }
        }

        $ok = $ok && $this->validateMembers($values);

        return $ok;
    }

    /**
     * Validate new record
     *
     * @param array $values Array of values
     *
     * @return bool
     */
    public function validateNewRec($values)
    {
        $gErr = Globals::$g->gErr;
        $ok = true;

        $filter = array(
            'selects' => array('NAME', 'HOWCREATED', 'MEMBERTYPE'),
            'filters' => array(array(array('NAME', '=', $values['NAME'])))
        );

        if ( IsMultiEntityCompany() ) {
            $_sess = Session::getKey();
            SetUserContext($_sess, '', '', 'F');
            $res = $this->GetList($filter);
            SetUserContext($_sess, GetScriptType(), GetContextLocation(), 'T');
        } else {
            $res = $this->GetList($filter);
        }

        if ( count($res) > 0 ) {
            $dupfound = false;
            foreach ( $res as $acctgrp ) {
                if ( $acctgrp['NAME'] == $values['NAME'] && $acctgrp['HOWCREATED'] == $values['HOWCREATED'] ) {
                    $dupfound = true;
                    break;
                }
            }
            if ( $dupfound ) {
                $objName = 'Account Group';
                $errorCode = 'GL-0634';
                $placeholder1 = ['NAME' => $values['NAME']];
                /** @noinspection PhpUndefinedVariableInspection */
                if ( !in_array($acctgrp['MEMBERTYPE'], self::$accountComponents) ) {
                    $objName = $acctgrp['MEMBERTYPE'].' structure';
                    $errorCode = 'GL-0635';
                    $placeholder1 = ['NAME' => $values['NAME'], 'MEMBERTYPE' => $acctgrp['MEMBERTYPE']];
                }
                $gErr->addIAError(
                    $errorCode, __FILE__ . ':' . __LINE__, 'Duplicate Name', [],
                    'The name \'' . $values['NAME'] . '\' already exists in '.$objName, $placeholder1,
                    'Enter a name which doesn\'t exist', []
                );// i18n todo - (code change review)
                $ok = false;
            }
        }

        return $ok;
    }

    /**
     * Check whether account group is used in financail and projecr module configuration screens
     *
     * @param string $ID Account group record#
     *
     * @return bool true if it is used
     */
    public function canDeleteAcctGrp($ID)
    {
        $gErr = Globals::$g->gErr;

        $ok = true;

        $args = array();
        $args[0] = "select ri.category, ri.name, ri.re_type, ri.title from reportinfo ri, glacctgrp where ri.cny#=:1 and (".
            " exists (select 1 from reportgroups rg where rg.cny#=ri.cny# and rg.report#=ri.record# ";
        $args[0] .= "and rg.acctgrp# = glacctgrp.record#) or exists (select 1 from reportrows rr where rr.cny#=ri.cny#";
        $args[0] .= " and rr.report#=ri.record# and rr.rowacctgrpkey = glacctgrp.record#) or ";
        $args[0] .= "exists (select 1 from reportcols rc where rc.cny#=ri.cny# and rc.report#=ri.record# ";
        $args[0] .= "and rc.colacctgrpkey = glacctgrp.record#) ) and ri.cny# = glacctgrp.cny# and glacctgrp.name =:2";

        $args[1] = GetMyCompany();
        $args[2] = $ID;

        $res = QueryResult($args);

        $finReport = array();
        $finGraph = array();
        $finCard = array();
        foreach ( $res as $val ) {
            if ( $val['CATEGORY'] == 'glfinancial' ) {
                $finReport[] = $val['NAME'];
            }
            if ( $val['CATEGORY'] == 'glgraph') {
                if($val['RE_TYPE'] == 'DB_KPI') {
                    $title = $val['TITLE'];
                    $title = isl_substr($title, 0, isl_strrpos($title, '.'));
                    $finCard[] = $title;
                } else {
                    $finGraph[] = $val['NAME'];
                }
            }
        }
        if ( !empty($finReport) ) {
            $rMsg = "'$ID' is used in the following Financial Report(s) : "
            . join(", ", $finReport);
            $gErr->addIAError('GL-0536', __FILE__ . ':' . __LINE__,
                'Error on deleting', [],
                $rMsg, ['ID' => $ID , 'FINREPORT' => join(", ", $finReport)]
            );
            $ok = false;
        }
        if ( !empty($finGraph) ) {
            $rMsg = "'$ID' is used in the following Financial Graph(s) : "
            . join(", ", $finGraph);
            $gErr->addIAError('GL-0537', __FILE__ . ':' . __LINE__,
                'Error on deleting', [],
                $rMsg, ['ID' => $ID, 'FINGRAPH' => join(", ", $finGraph)]
            );
            $ok = false;
        }
        if ( !empty($finCard) ) {
            $rMsg = "'$ID' is used in the following Performance Card(s) : "
                . join(", ", $finCard);
            $gErr->addIAError('GL-0538', __FILE__ . ':' . __LINE__,
                'Error on deleting', [],
                $rMsg, ['ID' => $ID, 'FINCARD' => join(", ", $finCard)]
            );
            $ok = false;
        }


        // Let's make sure those account groups have not been choosen in the project configuration screen
        $paPrefs = PASetupManager::getConfig();
        $acctGrps = array(
            'PAYMENTSACCOUNTGROUP',
            'DEFERREDREVENUEACCOUNTGROUP',
            'REVENUEACCOUNTGROUP',
            'EXPENSESACCOUNTGROUP',
            'COSTACCOUNTGROUP',
            'WAGESACCOUNTGROUP',
            'GROSSPROFITACCOUNTGROUP',
            'NETINCOMEACCOUNTGROUP'
        );

        foreach ( $acctGrps as $acctGrp ) {
            if ( $paPrefs[$acctGrp] == $ID ) {
                $gMsg = "This Account Group has been used in the Project Configuration screen";
                $gErr->addIAError(
                    'GL-0455',
                    __FILE__ . ':' . __LINE__,
                    'Unable to delete record', [],
                    "Could not delete the Account Group '$ID'", ['ID' => $ID],
                    $gMsg, []
                );
                $ok = false;
            }
        }

        return $ok;
    }

    /**
     * Delete a record from the database
     * -- Deletes any orphaned temporary reports from reportinfo
     * -- if the accountgroup is used in them and not in any permanent reports
     *
     * @param string|int $ID Account group record#
     *
     * @return bool true if success
     */
    function Delete($ID)
    {
        $gErr = Globals::$g->gErr;

        $source = 'GLAcctGrpManager::Delete()';
        $ok = $this->_QM->beginTrx($source);

        $ok = $ok && $this->canDeleteAcctGrp($ID);

        // get the deleted group's parents (if any) to handle post-delete action:
        $groupsToUpdate = [];
        if ($ok) {
            $grpRecNo = $this->GetRecordNoFromVid($ID);

            // (Note: we don't need to include the deleted group for update - FK constraint will take care of that)
            $groupsToUpdate = ProjectGLGrpAcctHelper::getGroupParents($grpRecNo, false);
            if ($groupsToUpdate === false) {
                // error
                $ok = false;
            }
        }

        $ok = $ok && parent::Delete($ID);

        // update project GL group account data:
        if ($ok && !empty($groupsToUpdate)) {
            $ok = ProjectGLGrpAcctHelper::refreshDataForGroup($groupsToUpdate);
        }

        $ok = $ok && $this->_QM->commitTrx($source);
        if ( !$ok ) {
            $msg = "Could not delete the Account Group/ Dimension structure '$ID'";
            $gErr->addIAError('GL-0456', __FILE__ . ':' . __LINE__, $msg, ['ID' => $ID]);
            epp("$source: Error: $msg");
            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }



    /**
     * Need to provide owned objects based on MEMBERTYPE selection,
     * to avoid unnecessary queries to other tables
     *
     * @return array List of owned objects
     */
    public function &GetOwnedObjects()
    {
        $objs = parent::GetOwnedObjects();

        if ( $this->memberType != "" ) {
            foreach ( $objs as $k => $v ) {
                if ( $v['path'] !== $this->memberType ) {
                    unset($objs[$k]);
                }
            }
        }
        return $objs;
    }

    /**
     * Based on MEMBERTYPE value we need to unset arrays
     *
     * @param array $values Array of values
     *
     * @return array Modified array
     */
    public function API_FormatObject($values)
    {
        $values = parent::API_FormatObject($values);
        $memTyp = $this->getMemTyp();

        $parent = $memTyp[$values['MEMBERTYPE']][0];
        $child = $memTyp[$values['MEMBERTYPE']][1];

        if ( is_array($values[$parent][$child][0]) ) {
            $values[$parent] = $values[$parent][$child];
        } else if ( is_array($values[$parent][$child]) ) {
            $values[$parent] = array($values[$parent][$child]);
        }

        unset($values[$parent][$child]);

        return $values;
    }

    /**
     * To store list of Meber types and corresponding managers
     *
     * @var string[][] $memTyp
     */
    public $memTyp;

    /**
     * Returns $this->memTyp value
     *
     * @return array
     */
    protected function getMemTyp()
    {
        if ( !isset($this->memTyp) ) {
            $this->memTyp = array(
                'Accounts' => array('GLACCTRANGES', 'ACCTRANGE'),
                'Statistical Accounts' => array('GLSTATACCTRANGES', 'ACCTRANGE'),
                'Category' => array('GLCATGRPS', 'GLCOACATMEMBER'),
                'Statistical Category' => array('GLSTATCATGRPS', 'GLCOACATMEMBER'),
                'Groups' => array('GLACCTGRPS', 'GLACCTGRPMEMBER'),
                'Computation' => array('GLCOMPGRPS', 'GLCOMPGRPMEMBER'),
            );

            $showOnlyTheseDims = IADimensions::getActiveGLDimensionsOrdered();

            foreach ($showOnlyTheseDims as $val) {
                $label = $val['internal_label_plural'] ??  I18N::getSingleToken($val['label']);
                $this->memTyp[$label] = array('GLDIMGRPS', 'GLDIMGRPMEMBER');
                $this->memTyp['Group of '.$label] = array('GLACCTGRPS', 'GLACCTGRPMEMBER');
            }
        }

        return $this->memTyp;
    }

    /**
     * Validate members
     *
     * @param array &$values Array of values
     *
     * @return bool true if valid
     */
    private function validateMembers(&$values)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $memTyp = $this->getMemTyp();

        if ( isset($memTyp[$values['MEMBERTYPE']]) ) {
            /* @var GLAcctGrpManager|GLDimGrpMemberManager $Mgr */
            $Mgr = $gManagerFactory->getManager(isl_strtolower($memTyp[$values['MEMBERTYPE']][1]));
        }

        $ok = true;

        if ( isset($Mgr) && isset($values[$memTyp[$values['MEMBERTYPE']][0]])
            && count($values[$memTyp[$values['MEMBERTYPE']][0]]) > 0
        ) {
            $ok = $Mgr->validateMembers($values[$memTyp[$values['MEMBERTYPE']][0]], $values['MEMBERTYPE']);
        }

        return $ok;
    }

    /**
     * Translate members
     *
     * @param array &$values Array of values
     *
     * @return bool true if valid
     */
    private function translateMembers(&$values)
    {
        $memTyp = $this->getMemTyp();

        if ( isset($memTyp[$values['MEMBERTYPE']]) ) {
            /* @var GLAcctGrpManager|GLDimGrpMemberManager $Mgr */
            $Mgr = Globals::$g->gManagerFactory->getManager(isl_strtolower($memTyp[$values['MEMBERTYPE']][1]));
        }

        $ok = true;

        if ( isset($Mgr) && isset($values[$memTyp[$values['MEMBERTYPE']][0]])
            && count($values[$memTyp[$values['MEMBERTYPE']][0]]) > 0
        ) {
            $ok = $Mgr->translateMembers($values);
        }

        return $ok;
    }

    /**
     * Transform members
     *
     * @param array &$values Array of values
     *
     * @return bool true if valid
     */
    private function transformMembers(&$values)
    {
        $ok = true;

        if ( isset($values['GLDIMGRPS'])
            && count($values['GLDIMGRPS']) > 0
        ) {
            $memTyp = $this->getMemTyp();
            if(isset($memTyp[$values['MEMBERTYPE']])){
                /* @var GLAcctGrpManager|GLDimGrpMemberManager $Mgr */
                $Mgr = Globals::$g->gManagerFactory->getManager(isl_strtolower($memTyp[$values['MEMBERTYPE']][1]));

                $ok = $Mgr->transformMembers($values);
            }
        }

        return $ok;
    }

    /**
     * Builds Account group map
     *
     * @param string      $type    Membert type
     * @param array|null  $records Record list
     *
     * @return array List of account groups
     */
    public function BuildAcctGrpDetails($type, $records = null)
    {
        $acctGrpDetail = array();

        $cny = GetMyCompany();

        $select = "SELECT g.record#,
			g.name as group_name,
			g.title as heading_title,
			g.totaltitle as total_title,
	        agp.name as account_group_purpose,
		    g.acctgroupmanager as account_group_manager,
			decode(g.normal_balance, 1, 'DB', -1, 'CR', '') as normal_balance,
			decode(g.membertype, 'C', 'COMP', 'T', 'C', 'L', 'SC', g.membertype) as member_type,
			decode(g.asof, 'P', 'F', 'B', 'S', 'E', 'E', '') as calc_amount,
			g.applyasof as apply_budget,
			'' as members,
			'' as ranges,
			'' as category,
			'' as computation,
			decode(g.dbcr, 'B', 'BO', 'D', 'DB', 'C', 'CR', '') as filter_dbcr,
			decode(g.filterdept, 2, 'A', 1, 'H', 4, 'T', 3, 'N', '') as f_department_type,
			nvl(d.dept_no, dg.id) as f_department,
			decode(g.filterloc, 2, 'A', 1, 'H', 4, 'T', 3, 'N', '') as f_location_type,
			nvl(l.location_no, lg.id) as f_location,
			decode(g.filtervend, 2, 'A', 1, 'H', 4, 'T', 3, 'N', '') as f_vendor_type,
			decode(g.filtercust, 2, 'A', 1, 'H', 4, 'T', 3, 'N', '') as f_customer_type,
			decode(g.filterproj, 2, 'A', 1, 'H', 4, 'T', 3, 'N', '') as f_project_type,
			decode(g.filteremp, 2, 'A', 1, 'H', 4, 'T', 3, 'N', '') as f_employee_type,
			decode(g.filteritem, 2, 'A', 1, 'H', 4, 'T', 3, 'N', '') as f_item_type,
			decode(g.filterclass, 2, 'A', 1, 'H', 4, 'T', 3, 'N', '') as f_class_type,
            decode(g.filtercontract, 2, 'A', 1, 'H', 4, 'T', 3, 'N', '') as f_contract_type,
            decode(g.filtertask, 2, 'A', 1, 'H', 4, 'T', 3, 'N', '') as f_task_type,
            decode(g.filterwhse, 2, 'A', 1, 'H', 4, 'T', 3, 'N', '') as f_warehouse_type,
            decode(g.filtercosttype, 2, 'A', 1, 'H', 4, 'T', 3, 'N', '') as f_costtype_type,
		    decode(g.filterasset, 2, 'A', 1, 'H', 4, 'T', 3, 'N', '') as f_asset_type,
		    decode(g.filteraffiliateentity, 2, 'A', 1, 'H', 4, 'T', 3, 'N', '') as f_affiliateentity_type";
        
        $from = " FROM glacctgrp g, department d, departmentgroup dg, location l, locationgroup lg, gltags agp ";
        
        $where = " WHERE g.cny# = :1
			and g.howcreated = 'U'
            and g.membertype in ('A', 'G', 'S',  'C', 'T', 'L')
			and d.cny# (+)= g.cny#
			and d.record# (+)= g.deptkey
			and dg.cny# (+)= g.cny#
			and dg.record# (+)= g.deptgrpkey
			and l.cny# (+)= g.cny#
			and l.record# (+)= g.locationkey
			and lg.cny# (+)= g.cny#
			and lg.record# (+)= g.locgrpkey
            and agp.cny# (+)= g.cny#
            and agp.record# (+)= g.glacctgrppurposekey
            and agp.type (+)= 'GP' ";
        
        $dims = IADimensions::getAllDimensionObjectProperties();
        foreach ($this->dimHeader as $dimKey) {
            $dim = $dims[$dimKey];
            $alias = $dim['internalpath'] ? : $dim['entity'];
            $select .= ", " . $alias . "." . $dim['dimfkid'] . " as f_" . $alias ;
            $from .= ", " . $dim['table'] . " $alias";
            $where .= " and $alias.cny#(+) = g.cny#";
            $where .= " and $alias.record#(+) = g." . $dim['dimdbkey'];
            
        }

        $sqlGroup = $select.$from.$where;
        
        $sqlMember = "
		select g.record#, g.name, gc.name as child_group
		from glacctgrp g, glacctgrpmembers m, glacctgrp gc
		where g.cny# = :1
			and g.howcreated = 'U'
			and g.membertype in ('G')
			and m.cny# = g.cny#
			and m.parent# = g.record#
			and gc.cny# = m.cny#
			and gc.record# = m.child#
	";
        $sqlRange = "
		select g.record#, g.name, r.rangefrom, r.rangeto
		from glacctgrp g, glacctgrpranges r
		where g.cny# = :1
			and g.howcreated = 'U'
			and g.membertype in ('A', 'S')
			and r.cny# = g.cny#
			and r.parentkey = g.record#
	";
        $sqlCategory = "
		select g.record#, g.name, c.name as child_group
		from glacctgrp g, coacatmembers m, iacoacat c
		where g.cny# = :1
			and g.howcreated = 'U'
			and g.membertype in ('T', 'L')
			and m.cny# = g.cny#
			and m.parent# = g.record#
			and c.record# = m.categorykey
	";
        $sqlComputation = "
		select g.record#, g.name,
			m.record# as comp_record#,
			m.lhsconst as left_operand_constant,
			gcl.name as left_operand_account_group,
			al.acct_no as left_operand_account,
			decode(m.lhsasof, 'P', 'F', 'B', 'S', 'E', 'E', '') as left_operand_calc_amt,
			m.operator as operator,
			m.rhsconst as right_operand_constant,
			gcr.name as right_operand_account_group,
			ar.acct_no as right_operand_account,
			decode(m.rhsasof, 'P', 'F', 'B', 'S', 'E', 'E', '') as right_operand_calc_amt,
			m.precision as precision,
			m.displayas as display_as,
			m.uom as unit_of_measure,
			decode(m.uomalignment, 1, 'L', 2, 'R', '') as alignment
		from glacctgrp g, compgrpmembers m, glacctgrp gcl, glacctgrp gcr, baseaccount al, baseaccount ar
		where g.cny# = :1
			and g.howcreated = 'U'
			and g.membertype in ('C')
			and m.cny# = g.cny#
			and m.parentkey = g.record#
			and gcl.cny# (+)= m.cny#
			and gcl.record# (+)= m.lhsacctgrpkey
			and gcr.cny# (+)= m.cny#
			and gcr.record# (+)= m.rhsacctgrpkey
			and al.cny# (+)= m.cny#
			and al.record# (+)= m.lhsacctkey
			and ar.cny# (+)= m.cny#
			and ar.record# (+)= m.rhsacctkey
	";

        //Check for filters
        $inClause = '';
        $query = array();

        if (!empty($records)) {
            $query = $this->appendFilterToAcctGrpDetails($records, $cny);
            $inClause = $query[0];
        } else {
            $query[0]='';
            $query[1] = $cny;
        }

        if ( $type == 'G' ) {
            $query[0] = $sqlGroup . $inClause . " order by g.name asc";
            $arrGroup = QueryResult($query) ?: [];

            $acctGrpDetail = array();
            foreach ( $arrGroup as $group ) {
                $keyGroup = $group['GROUP_NAME'];
                $acctGrpDetail[$keyGroup] = $group;
            }
        } else if ( $type == 'M' ) {
            $query[0] = $sqlGroup . $inClause . " order by g.name asc";
            $arrGroup = QueryResult($query);
            $query[0] = $sqlMember . $inClause . " order by g.name, m.sortord";
            $arrMember = QueryResult($query);
            $query[0] = $sqlRange . $inClause . " order by g.name, r.sortord";
            $arrRange = QueryResult($query);
            $query[0] = $sqlCategory . $inClause . " order by g.name, m.sortord";
            $arrCategory = QueryResult($query);
            $query[0] = $sqlComputation . $inClause . " order by g.name asc";
            $arrComputation = QueryResult($query);

            $acctGrpDetail = $this->MergeAcctGrpMembers(
                $arrGroup, $arrMember, $arrRange, $arrCategory, $arrComputation
            );
        }

        return $acctGrpDetail;
    }

    /**
     * Merge account groups
     *
     * @param array $arrGroup       List of account groups
     * @param array $arrMember      List of groups
     * @param array $arrRange       List of ranges
     * @param array $arrCategory    List of categories
     * @param array $arrComputation List of computation
     *
     * @return array
     */
    private function MergeAcctGrpMembers($arrGroup, $arrMember, $arrRange, $arrCategory, $arrComputation)
    {
        $arrReturn = array();

        // re-group by record# and concatenate values for same record#
        $arrMemberGrouping = array();
        $countArrMember = Util::php7Count($arrMember);
        for ( $i = 0; $i < $countArrMember; $i++ ) {
            $arrMemberGrouping[$arrMember[$i]['RECORD#']][] = $arrMember[$i]['CHILD_GROUP'];
        }

        $arrRangeGrouping = array();
        $countArrRange = Util::php7Count($arrRange);
        for ( $i = 0; $i < $countArrRange; $i++ ) {
            if ( $arrRange[$i]['RANGEFROM'] == $arrRange[$i]['RANGETO'] ) {
                $arrRangeGrouping[$arrRange[$i]['RECORD#']][] = $arrRange[$i]['RANGEFROM'];
            } else {
                $arrRangeGrouping[$arrRange[$i]['RECORD#']][] = $arrRange[$i]['RANGEFROM'] . ':'
                    . $arrRange[$i]['RANGETO'];
            }
        }

        $arrCategoryGrouping = array();
        $countArrCategory = Util::php7Count($arrCategory);
        for ( $i = 0; $i < $countArrCategory; $i++ ) {
            $arrCategoryGrouping[$arrCategory[$i]['RECORD#']][] = $arrCategory[$i]['CHILD_GROUP'];
        }

        $arrComputationGrouping = array();
        $countArrComputation = Util::php7Count($arrComputation);
        for ( $i = 0; $i < $countArrComputation; $i++ ) {
            $arrComputationGrouping[$arrComputation[$i]['RECORD#']][] = $arrComputation[$i];
        }

        // insert into 'MEMBERS', 'RANGES', 'CATEGORY', 'COMPUTATION' keys
        $countArrGroup = Util::php7Count($arrGroup);
        for ( $i = 0; $i < $countArrGroup; $i++ ) {
            $group = $arrGroup[$i];

            switch ( $group['MEMBER_TYPE'] ) {
            case 'G':
                foreach ( $arrMemberGrouping as $k => $v ) {
                    if ( $k == $group['RECORD#'] ) {
                        $group['MEMBERS'] = $v;
                        break;
                    }
                }
                break;
            case 'A':
            case 'S':
                foreach ( $arrRangeGrouping as $k => $v ) {
                    if ( $k == $group['RECORD#'] ) {
                        $group['RANGES'] = $v;
                        break;
                    }
                }
                break;
            case 'C':
            case 'SC':
                foreach ( $arrCategoryGrouping as $k => $v ) {
                    if ( $k == $group['RECORD#'] ) {
                        $group['CATEGORY'] = $v;
                        break;
                    }
                }
                break;
            case 'COMP':
                foreach ( $arrComputationGrouping as $k => $v ) {
                    if ( $k == $group['RECORD#'] ) {
                        $arrCompTemp = $v[0];  // first one

                        unset($arrCompTemp['RECORD#']);
                        unset($arrCompTemp['NAME']);

                        $group['COMPUTATION'] = $arrCompTemp;
                        break;
                    }
                }
                break;
            default:
                if ( isl_substr($group['MEMBER_TYPE'], isl_strlen($group['MEMBER_TYPE'])-2, 2) == 'GD' ) {
                    foreach ( $arrMemberGrouping as $k => $v ) {
                        if ( $k == $group['RECORD#'] ) {
                            $group['MEMBERS'] = $v;
                            break;
                        }
                    }
                }
                break;
            }

            $keyGroup = $group['GROUP_NAME'];
            $arrReturn[$keyGroup] = $group;
        }

        return $arrReturn;
    }

    /**
     * GLAcctGrp manager has VID other than RECORDNO, hence we need to convert the keys to NAME
     * appropriately. This object is similar to LocationEntityManager.
     *
     * @param int|string|int[]|string[]|null $recordNos    Array list of IDs (RECORDNO) for the record to get
     * @param string[]|null                  $returnFields Array list of fields to return
     *
     * @return array List of GLAcctGrp objects
     */
    public function API_Get($recordNos, $returnFields=null)
    {
        if ( empty($recordNos) ) {
            $recordNos = [];
        } else if ( !is_array($recordNos) ) {
            $recordNos = explode(',', $recordNos);
        }
        //  For each recordno, translate to the vid
        $vids = array();
        foreach ( $recordNos as $nextRecId ) {
            $tempVID = $this->GetVidFromRecordNo($nextRecId);
            if ( $tempVID != '' ) {
                $vids[] = $tempVID;
            }
        }

        if ( empty($recordNos) ) {
            // default return 100 records
            $returnRecordSet = parent::API_Get(array(), $returnFields);
        } else if ( !empty($vids) ) {
            // available records with valid vid
            $returnRecordSet = parent::API_Get($vids, $returnFields);
        } else {
            // Invalid recordnumbers are passed and no vids available
            $returnRecordSet = false;
        }
        return $returnRecordSet;
    }


    /**
     * @return array
     */
    public static function getDimensionIDToDimensionComponentMapping()
    {
        //For use with the financial report wizard.
        //result is also ordered by IADimensions::getActiveGLDimensionsOrdered;

        //This maps dimension ids (used to control ordering of UI menus in the report) to
        //the internal values for dimension components and dimension group components.
        //Also contains the simple names for the dimensions.

        //Then key maps to the the demension name that is used in IADimensions::getActiveGLDimensionsOrdered()
        $dimOrder = IADimensions::getActiveGLDimensionsOrdered();
        //epp('$dimOrder:');eppp($dimOrder);dieFL();

        $result = array(); //This $result to be ordered by the //IADimensions::getActiveGLDimensionsOrdered

        $labels = array_column($dimOrder, 'label');
        //Dimension tokens
        I18N::addToken('IA.HIERARCHY_OF_OBJDEF');
        I18N::addToken('IA.HIERARCHY_OF_OBJDEF_STRUCTURES');
        I18N::addTokens(I18N::tokenArrayToObjectArray($labels));
        $textMap = I18N::getText();

        $resultmapping =  array ();
        for ($dimIndex = 0; $dimIndex < count($dimOrder); $dimIndex++) {
            $elm = $dimOrder[$dimIndex];
            $resultmapping[$elm['dimensionid']] = array(
                'standard' => $elm['standard'],
                'label' => $elm['label'],
                'dim_name' => $elm['internal_label'] ?? $elm['label'],
                'dim_entity' => $elm['entity'],
                'pt_entity' => $elm['pt_entity'] ?? '',
                'dim_name_pl' => $elm['internal_label_plural'] ?? I18N::getSingleToken($elm['label']),
                'dim_component_id_internal' =>$elm['componentval'],
                'dim_component_id_internal_dg' =>$elm['componentval'].'GD',
                'udd' => $elm['udd'],
                'id' => (strtoupper(($elm['internalpath'] ?? '' )?:$elm['entity'])).'ID'
            );
        }
        $gManagerFactory = Globals::$g->gManagerFactory;
        for ($dimIndex = 0; $dimIndex < count($dimOrder); $dimIndex++) {
            $dimid = $dimOrder[$dimIndex]['dimensionid'];
            $result[$dimid] = $resultmapping[$dimid];
            $result[$dimid]['displayAsDimType'] =  $dimOrder[$dimIndex]['udd'] == 1
                                                  ? $dimOrder[$dimIndex]['label']
                                                  : I18N::getSingleToken($dimOrder[$dimIndex]['label']);
            if(empty($dimOrder[$dimIndex]['udd'])){
                $entMgr = $gManagerFactory->getManager($dimOrder[$dimIndex]['entity']);
                $allEntity = $entMgr->GetPluralPrintAs();
            }
            $result[$dimid]['displayAsDimTypePlural'] = $dimOrder[$dimIndex]['udd'] == 1
                                                  ? $dimOrder[$dimIndex]['label'] : I18N::getSingleToken($allEntity);

            $result[$dimid]['displayAsDimType_dg'] = I18N::getSingleToken('IA.HIERARCHY_OF_OBJDEF',
                                                                          I18N::mapToPlaceholderArray(
                                                                              ['OBJECT_DEFINITION_NAME' => $result[$dimid]['displayAsDimType']]
                                                                          ));
            $result[$dimid]['displayAsDimType_dgs'] = I18N::getSingleToken('IA.HIERARCHY_OF_OBJDEF_STRUCTURES',
                                                                          I18N::mapToPlaceholderArray(
                                                                              ['OBJECT_DEFINITION_NAME' => $result[$dimid]['displayAsDimType']]
                                                                          ));
            $result[$dimid]['displayAsDimTypePlural_dg'] = I18N::getSingleToken('IA.HIERARCHY_OF_OBJDEF',
                                                                                I18N::mapToPlaceholderArray(
                                                                                    ['OBJECT_DEFINITION_NAME' => $result[$dimid]['displayAsDimTypePlural']]
                                                                                ));
        }
        return $result;
    }

    function MergeObjectSchema()
    {

        parent::MergeObjectSchema();

        $schema = & $this->_schemas[$this->_entity]['schema'];

        $ptMap = GLEntryManager::getPTFieldsMap();
        $glDimOrder = GLSetupManager::getCustomDimensionKeyOrder();

        for ( $i=0; $i<count($glDimOrder); $i++ ) {
            $defId = $glDimOrder[ $i ];

            if ( $defId != "" ) {
                $fieldPath = isl_strtoupper($ptMap[ $defId ]);

                $schema[ "FILTER" . $fieldPath ] = "filtercustdim" . ($i + 1);
            }
        }

    }

    function MergeObjectFieldList()
    {

        parent::MergeObjectFieldList();

        $schemaobject = & $this->_schemas[$this->_entity]['object'];

        $ptMap = GLEntryManager::getPTFieldsMap();

        foreach ( $ptMap as $fieldPath) {
            $schemaobject[] = "FILTER" . isl_strtoupper($fieldPath);
        }

    }

    /**
     *
     */
    public function MergeFieldInfo()
    {
        parent::MergeFieldInfo();
        $additionalTokens = [
            'IA.NO_FILTER','IA.SPECIFIC_FULLNAME','IA.NO_FULLNAME_SPECIFIED'
        ];
        I18N::addTokens(I18N::tokenArrayToObjectArray($additionalTokens));
        
        foreach ($this->_schemas[$this->_entity]['fieldinfo'] as $key => $field) {
            $thisFld = &$this->_schemas[$this->_entity]['fieldinfo'][$key];
            $thisBaseFld = &$this->_schemas[$this->_entity]['basefieldinfo'][$key];
            if ($field['path'] == 'MEMBERTYPE') {
                $showOnlyTheseDims = IADimensions::getActiveGLDimensionsOrdered();
                $validLablesMap = [];
                foreach ($showOnlyTheseDims as $val) {
                    //std dim
                    if(!$val['udd']){
                        $token = 'IA.' . 'GROUP_OF_'. strtoupper($val['entity']) . ($val['entity'] == 'class' ? 'ES' : 'S') ;
                        $validLablesMap[$val['entity']] = $token;
                        I18N::addToken($token);
                    }
                }
                I18N::addToken('IA.GROUP_OF_UDDS');
                I18N::getText();
                foreach ($showOnlyTheseDims as $val) {
                    $thisFld['type']['validlabels'][] = $val['label'];
                    $thisFld['type']['validvalues'][] = $val['internal_label_plural'] ?? $val['label'];
                    $thisFld['type']['_validivalues'][] = $val['componentval'];

                    $thisFld['type']['validlabels'][] = $val['udd'] ? I18N::getSingleToken('IA.GROUP_OF_UDDS', [[
                        'name' => 'UDDS' , 'value' => $val['label']
                    ]]) : I18N::getSingleToken($validLablesMap[$val['entity']]);
                    $thisFld['type']['validvalues'][] = 'Group of '. ($val['internal_label_plural'] ?? $val['label']);
                    $thisFld['type']['_validivalues'][] = $val['componentval'].'GD';

                    $thisBaseFld['type']['validlabels'][] = $val['label'];
                    $thisBaseFld['type']['validvalues'][] = $val['internal_label_plural'] ?? $val['label'];
                    $thisBaseFld['type']['_validivalues'][] = $val['componentval'];

                    $thisBaseFld['type']['validlabels'][] = $val['udd'] ? I18N::getSingleToken('IA.GROUP_OF_UDDS', [[
                        'name' => 'UDDS' , 'value' => $val['label']
                    ]]) : I18N::getSingleToken($validLablesMap[$val['entity']]);
                    $thisBaseFld['type']['validvalues'][] = 'Group of '. ($val['internal_label_plural'] ?? $val['label']);
                    $thisBaseFld['type']['_validivalues'][] = $val['componentval'].'GD';
                    
                };
            }
            else if(in_array($field['path'], ['VENDORID', 'CUSTOMERID', 'CLASSID'])){
                $thisFld['type']['pickentity'] = $thisFld['type']['entity'];
                $thisBaseFld['type']['pickentity'] = $thisBaseFld['type']['entity'];

                $thisFld['type']['addlPickFields'] = [ 'NAME' ];
                $thisBaseFld['type']['addlPickFields'] = [ 'NAME' ];

                $thisFld['type']['pickentity_key'] = $thisFld['type']['security_key']  ?? null;
                $thisBaseFld['type']['pickentity_key'] = $thisBaseFld['type']['security_key'] ?? null;
            }
        }
        
        $fieldInfo = &$this->_schemas[$this->_entity]['fieldinfo'];
        $this->MergeObjectFieldInfo($fieldInfo); //russ: a little hacky but need to do it here I guess?

        $gle_def_id = Util_StandardObjectMap::getObjectId('glentry');
        $arrFields = Pt_DataFieldDefManager::getRawGLDimensionFields($gle_def_id);

        $custDimFieldsMap = GLEntryManager::getPTFieldsMap();
        $flippedCustDimFieldsMap = array_flip($custDimFieldsMap);

        $i = 1;
        $noFilter = I18N::getSingleToken('IA.NO_FILTER');
        foreach ($arrFields as $rawField) {
            $path = isl_strtoupper($rawField['FIELD_NAME']);
            $fullname = $rawField['DISPLAY_LABEL'];

            $def_id = $flippedCustDimFieldsMap[ $path ];
            if (!isset($def_id)) {
                continue;
            }

            $from = "custdim_" . $def_id;

            // Add drop-down for filter type
            $fieldInfo[] = array(
                'fullname' => $fullname,
                'type' => array(
                    'ptype' => 'enum',
                    'type' => 'enum',
                    'validlabels' => array(
                        $noFilter,
                        I18N::getSingleToken('IA.SPECIFIC_FULLNAME', [[
                            'name' => 'FULLNAME' , 'value' => $fullname
                        ]]),
                        I18N::getSingleToken('IA.NO_FULLNAME_SPECIFIED', [[
                            'name' => 'FULLNAME' , 'value' => $fullname
                        ]]),
                    ),
                    'validvalues' => array(
                        'nofilter',
                        'specific',
                        'nullvalue',
                    ),
                    '_validivalues' => array(
                        '2',
                        '4',
                        '3',
                    ),
                ),
                'path' => "FILTER$path",
                'id' => 50 + $i++,
                'events' => array(
                    'change' => "showHidePickerFromForm(this, '$from', '$path', 'FILTER$path')",
                ),
                'className' => 'glFilters',
                'udd' => true,
            );
        }
    }

    /**
     * @param int $grpComp
     * @param string|null  $memberType
     *
     * @return array|bool
     */
    function getDimensionMembers($grpComp, &$memberType=null)
    {
        $res = $this->Get($grpComp);

        $grp['RECORDNO'] = $res['RECORDNO'];
        $grp['MEMBERTYPE'] = $res['MEMBERTYPE'];
        $grp['CHILDREN'] = array();
        $grp['INCLUDECHILDAMT'] = $res['INCLUDECHILDAMT'];

        if ( in_array(
            $grp['MEMBERTYPE'], array(
            'Accounts', 'Groups', 'Statistical Accounts', 'Computation',
            'Category', 'Statistical Category'
             )
        )
        ) {
            return false;
        }

        $memberType = $grp['MEMBERTYPE'];

        if ( isl_strstr($grp['MEMBERTYPE'], 'Group of') ) {
            $members = $res['GLACCTGRPS'];
            foreach ($members as $member) {
                $grp['CHILDREN'][] = $member['CHILDNAME'];
            }

            $children = $this->getDimensionMembersOfGroup($grp);

        } else {

            $dims = IADimensions::getReportDimensionObjectsInfo('gl', false, !util_isPlatformDisabled());

            foreach ($res['GLDIMGRPS'] as $member) {

                // lets find the groupkey from report dimensions map
                foreach ( $dims as $dimentity => $dim ) {

                    $dimGrpDBKey = $dim['dimgrpdbkey'];
                    if(in_array($dimentity, array('department','location'))) {
                        $dimGrpDBKey = strtoupper($dimentity).'GROUPKEY';
                    }

                    if ( $dimGrpDBKey != '' && $member[ $dimGrpDBKey ] != '' ) {
                        $grp['DIMGRPCHILDREN'][] = $member[ $dimGrpDBKey ];
                        break;
                    }
                }

            }

            /** @noinspection PhpUndefinedVariableInspection */
            $children = $this->getDimensionGroupMembers($grp, $dimentity);
        }

        $children = array_unique($children);

        return $children;
    }

    /**
     * @param array $grps
     *
     * @return array
     */
    function getDimensionMembersOfGroup($grps)
    {

        $children = array();
        $members = $grps['CHILDREN'];

        foreach ($members as $member) {
            $res = $this->Get($member);
            $grp = array();
            $grp['MEMBERTYPE'] = $res['MEMBERTYPE'];
            $grp['INCLUDECHILDAMT'] = $res['INCLUDECHILDAMT'];

            if ( isl_strstr($grp['MEMBERTYPE'], 'Group of') ) {
                foreach ($res['GLACCTGRPS'] as $cmember) {
                    $grp['CHILDREN'][] = $cmember['CHILDNAME'];
                }
                $grpChildren = $this->getDimensionMembersOfGroup($grp);
                $children = INTACCTarray_merge($children, $grpChildren);
            } else {

                $dims = IADimensions::getReportDimensionObjectsInfo('gl', false, !util_isPlatformDisabled());
                foreach ($res['GLDIMGRPS'] as $dg_member) {

                    // lets find the groupkey from report dimensions map
                    foreach ( $dims as $dimentity => $dim ) {

                        $dimGrpDBKey = $dim['dimgrpdbkey'];
                        if(in_array($dimentity, array('department','location'))) {
                            $dimGrpDBKey = strtoupper($dimentity).'GROUPKEY';
                        }

                        if ( $dimGrpDBKey != '' && $dg_member[ $dimGrpDBKey ] != '' ) {
                            $grp['DIMGRPCHILDREN'][] = $dg_member[ $dimGrpDBKey ];
                            break;
                        }
                    }
                }

                /** @noinspection PhpUndefinedVariableInspection */
                $grpChildren = $this->getDimensionGroupMembers($grp, $dimentity);
                $children = INTACCTarray_merge($children, $grpChildren);
            }
        }

        return $children;

    }

    /**
     * @param array $grp
     * @param string $dimentity
     *
     * @return array
     */
    function getDimensionGroupMembers($grp, $dimentity)
    {
        $allMembers = array();

        if (empty($grp['DIMGRPCHILDREN'])) {
            return $allMembers;
        }

        $dims = IADimensions::getReportDimensionObjectsInfo('gl', false, !util_isPlatformDisabled());
        $dimInfo = $dims[$dimentity];

        $grpManager = DimensionGroupManager::getInstance($dimentity);

        foreach ($grp['DIMGRPCHILDREN'] as $child) {
            $childGrpMembers = $grpManager->getGroupMembers($child, false);
            $allMembers = INTACCTarray_merge($allMembers, $childGrpMembers['MEMBERRECS']);
        }

        if ( $grp['INCLUDECHILDAMT'] == 'true' ) {

            $cny = GetMyCompany();
            $mstTable = $dimInfo['table'];
            $parentKey = 'parentkey';

            if ($dimentity == 'department') {
                $parentKey = 'parent#';
            }

            if ( $mstTable!= '' ) {
                $grpQry = " select distinct record# from " . $mstTable . " where cny# = $cny";
                $grpQry .= " start with $mstTable.cny# = $cny ";
                if ( count($allMembers) ) {
                    $allMembers = array_unique($allMembers);
                    $stmt = " ";
                    /** @noinspection PhpUndefinedVariableInspection */
                    $grpQry .= PrepINClauseStmt($stmt, $allMembers, " and $mstTable.record# ", false, "dimgrp" . $table);
                    $grpQry .= " connect by prior $mstTable.record# = $parentKey and $mstTable.cny# = $cny";
                }
                $res = QueryResult($grpQry);
                $allMembers = array();
                foreach ($res as $r) {
                    $allMembers[] = $r['RECORD#'];
                }
            }
        }

        $allMembers = array_unique($allMembers);

        return $allMembers;
    }

    /**
     * @param array $fieldinfo
     */
    public function MergeObjectFieldInfo(&$fieldinfo)
    {
        $componentInfo = self::getDimensionIDToDimensionComponentMapping();

        foreach ($componentInfo as $key => $value) {
            $GLDIMGRPGRPS_entry =  array(
                'path' => 'GLDIMGRPGRPS_'.isl_strtoupper($value['dim_entity']),
                'clazz' => 'AGField',
                'hidden' => true,
                "noLabel" => true,
                'picklist1Name' => 'IA.DIMENSION_STRUCTURE',
                'readonlylabel' => 'IA.MEMBERS',
                'addbuttonname' => 'IA.ADD_TO_STRUCTURE',
                "sortable" => true,
                'picksectionheader' => 'IA.DIMENSION_REPORT_STRUCTURE_TO_INCLUDE',
                'listsectionheader' => 'IA.DIMENSION_STRUCTURES',
                'picksectionhelp' => "IA.DONT_HAVE_GROUP_CLICK_DROP_DOWN_TO_ADD",
                'pickvaluekeyname' => 'CHILDNAME',
                'type' => array(
                    'type' => 'ptr',
                    'ptype' => 'ptr',
                    'entity' => 'glacctgrp',
                    'pickentity' => 'acctgrppick',
                    'createAction' => 'create&opSubAction=dimension&dimGroupTypeRestriction='.$value['dim_component_id_internal'].'',
                    'restrict' => array(
                        array('value' => array($value['dim_component_id_internal'], $value['dim_component_id_internal_dg']), 'pickField' => 'MEMBERTYPE'),
                        array('value' => 'System_%', 'operand' => 'NOT LIKE',   'pickField' => 'PICKID'),
                    ),
                    'validvalues' => array(),
                    'validlabels' => array(),
                ),
            );
            $GLDIMGRPS_entry = array(
                'path' => 'GLDIMGRPS_'.isl_strtoupper($value['dim_entity']),
                'clazz' => 'AGField',
                'hidden' => true,
                "noLabel" => true,
                'picklist1Name' => '', // set by the Editor Cls file
                'readonlylabel' => 'IA.MEMBERS',
                'addbuttonname' => 'IA.ADD_TO_REPORT_STRUCTURES',
                "sortable" => true,
                'picksectionheader' =>  '', // set by the Editor Cls file
                'listsectionheader' =>  '', // set by the Editor Cls file
                'picksectionhelp' => "IA.DONT_HAVE_GROUP_CLICK_DROP_DOWN_TO_ADD",
                'pickvaluekeyname' => 'CHILDNAME',
                'type' => array(
                    'type' => 'ptr',
                    'ptype' => 'ptr',
                    'entity' => ! $value['standard'] ? 'ptreportview' : isl_strtolower($value['dim_entity']).'group',
                    'pickentity' => ! $value['standard'] ? 'ptreportview' : isl_strtolower($value['dim_entity']).'group',
                    'validvalues' => array(),
                    'validlabels' => array(),
                ),
            );

            if ( ! $value['standard'] ) {
                $GLDIMGRPS_entry['type']['restrict'] = array(
                    array(
                        'value' => $key,
                        'pickField' => 'OBJ_DEF_ID'
                    )
                );
            }

            $fieldinfo[] = $GLDIMGRPGRPS_entry;;
            $fieldinfo[] = $GLDIMGRPS_entry;
        }
    }

    /**
     * The function appends the filter to the account group query
     *
     * @param array $records Record ids to be filtered
     * @param int $cny cny#
     * @return array
     */
    private function appendFilterToAcctGrpDetails($records, $cny) {
        $query = "";
        $qry = array($query, $cny);
        $qry = PrepINClauseStmt($qry, $records, " and g.record# ");
        return $qry;
    }

    /**
     * This function will translate the Grouppurpose for Add and Set from Vid to Record#
     * @param array $values
     *
     * @return bool
     */
    private function translateGroupPurpose(&$values)
    {
        $ok = true;
        if (isset($values['GLACCTGRPPURPOSEID'])
            && $values['GLACCTGRPPURPOSEID'] != ''
        ) {
            $mgr = Globals::$g->gManagerFactory->getManager('glacctgrppurpose');
            $querySpec = [
                'selects' => ['RECORDNO'],
                'filters' => [
                    [
                        ['NAME', '=', $values['GLACCTGRPPURPOSEID']],
                        ['STATUS', '=', 'active'],
                    ],
                ],
            ];
            $values['GLACCTGRPPURPOSEKEY'] = $mgr->GetList($querySpec)[0]['RECORDNO'];
            $ok = $ok && !empty($values['GLACCTGRPPURPOSEKEY']);
            if (!$ok) {
                Globals::$g->gErr->addError(
                    'GL-0457', __FILE__ . __LINE__,
                    'The Account group purpose you\'re adding doesn\'t exist or isn\'t active.', '',
                    'Select an active Account group purpose, change the status of the one to add, or create the new one.'
                );
            }

            return $ok;
        } else {
            $values['GLACCTGRPPURPOSEKEY'] = '';
            return $ok;
        }
    }

    /**
     * This function will return a map for Account Group and its Purpose
     *
     * @return array
     */
    public function GetPurposeMap()
    {
        $querySpec = [
            'selects' => ['NAME', 'GLACCTGRPPURPOSEID'],
            'filters' => [
                [
                    ['GLACCTGRPPURPOSEID', 'is not null'],
                ],
            ],
            'orders'  => [
                ['NAME', 'asc'],
            ],
        ];
        $result = $this->GetList($querySpec);
        $ret = [];
        foreach ($result as $record) {
            $ret[$record['GLACCTGRPPURPOSEID']][] = [$record['NAME'], "<>" => "Account Groups"];
        }

        return $ret;
    }

    /**
     * @return string[]
     */
    public static function getAccountComponents() : array
    {
        return self::$accountComponents;
    }

    /**
     * @return bool
     */
    public function GetAllowInactiveDimensions()
    {
        return GetUserPreferences($pref, 'REPORT_FILTER', false) === 'F';
    }

    /**
     * This function runs the smart link/rule for account groups and not for dimension structure
     *
     * @return bool
     */
    function LoadSmartlinks($types = null, $showInactive = false)
    {
        if(Request::$r->opSubAction === 'account'){
            return parent::LoadSmartlinks($types,$showInactive);
        }
        return false;
    }

    public function getDynamicFields(string $groupName): array
    {
        $retArr = [];
        if ($groupName === 'REPORTFILTER') {
            $customDimensionObj = IADimensions::getCustomDimensionObjects();
            $schema = $this->_getSchema();

            foreach ($customDimensionObj as $uddObj) {
                $fieldPath = "FILTERGLDIM" . isl_strtoupper($uddObj['entity']);
                if (isset($schema[$fieldPath])) {
                    $retArr[APIDynamicObjectSchemaLoader::API_NAMESPACE_DEFAULT_PREFIX . $uddObj['entity']] = [
                        'fullname' => $uddObj['label'],
                        'path' => $fieldPath,
                        'desc' => $uddObj['label'],
                        'type' => [
                            'ptype' => 'enum',
                            'type' => 'enum',
                            'validlabels' => array('', 'IA.NO_FILTER', 'IA.SPECIFIC_LOCATIONS_INCLUDING_SUB_LOCATIONS',
                                'IA.SPECIFIC_LOCATIONS', 'IA.NO_LOCATION_SPECIFIED'),
                            'validvalues' => array('', 'nofilter', 'specifichierarchy', 'specific', 'nullvalue'),
                            'apivalues' => array(null, 'noFilter', 'specificHierarchy', 'specific', 'nullValue'),
                        ]
                    ];
                }
            }
        }
        return $retArr;
    }
}
