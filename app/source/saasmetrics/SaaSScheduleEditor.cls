<?php
/**
 * File SaaSScheduleEditor.cls contains the editor class for saasschedule
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Sage Intacct Inc., All Rights Reserved
 */

/**
 * Saas Metrics Change Type
 *
 *
 */
class SaaSScheduleEditor extends FormEditor
{

    /** @var bool $isMCPEnabled */
    private $isMCPEnabled;
    /** @var bool $isMCMESubscribed */
    private $isMCMESubscribed;
    /** @var  array $modprefs */
    private $modprefs;
    /** @var string[] $scheduleValues */
    private $scheduleValues;

    protected const localTokens = [
        'IA.LOCATION_ENTITY'
    ];

    public const jsTokens = [
        "IA.SAAS_SCHEDULE_DATE_ERROR_MSG"
    ];

    /**
     * @param array $params The parameters of the class
     */

    public function __construct($params = [])
    {

        $this->additionalTokens = array_merge($this->additionalTokens, self::localTokens);
        $this->textTokens = array_merge($this->textTokens, self::jsTokens);
        parent::__construct($params);

        $this->isMCPEnabled = ContractUtil::isMCPEnabled();
        $this->isMCMESubscribed = IsMCMESubscribed();

        $kSAASMTid = Globals::$g->kSAASMTid;

        if ( !GetModulePreferences($kSAASMTid, $this->modprefs) ) {
            Globals::$g->gErr->addError(
                'BL02000058', GetFL(), _("Unable to retrieve the SaaS Metrics module preferences")
            );
        }
    }

    /**
     * This is a hook functions for subclases to adjust the metadata according to the current data & state.
     * At the time this function is called:
     *   - the data is available and it is in view format.
     *   - the metadata is expanded and the view objects are built - use $this->getView() call to get a refernece to
     *     the view object.
     *
     * @param array &$obj  the data
     *
     * @return bool true on success and false on failure - make sure an error is raised in case of failure
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        $view = $this->getView();
        $state = $this->getState();

        // enforce addig department, if feature is enabled and the restriction checkbox is on
        if ( hasHiddenDepartment() ) {
            $view->findAndSetProperty(
                [ 'path' => 'DEPARTMENTID' ],
                [ 'required' => true ]
            );
        }

        if ( $this->kShowNewState == $state ) {
              $this->defaultDimensionValues($obj);
              $view->findAndSetProperty(['path' => 'SAASSCHEDULEENTRIES'], ['hidden' => true], EditorComponentFactory::TYPE_GRID);
              // load the exchange rate type from the saas module preference
             GetModulePreferences(Globals::$g->kSAASMTid, $modPref);
             $exchangeRate = $modPref['SAS_EXCH_RATE_TYPE_ID'];
             $obj['EXCHRATETYPE'] = $exchangeRate;
        }

        if ( $this->getVerb() == 'view' || $this->getVerb() == 'edit' ) {

            $obj['DEPARTMENTID'] = $obj['DEPARTMENTID'] . '--' . $obj['DEPARTMENTNAME'];
            $obj['LOCATIONID'] = $obj['LOCATIONID'] . '--' . $obj['LOCATIONNAME'];
            $obj['CHANGETYPEID'] = $obj['CHANGETYPEID'] . '--' . $obj['CHANGETYPENAME'];
            $obj['ITEMID'] = $obj['ITEMID'] . '--' . $obj['ITEMNAME'];
            $obj['CUSTOMERID'] = $obj['CUSTOMERID'] . '--' . $obj['CUSTOMERNAME'];
        }

        if ( $this->getVerb() == 'view') {
            if ($obj['CONTRACTDETAILKEY']) {
                $view->findAndSetProperty([ 'path' => 'CONTRACTDETAILKEY' ], [ 'hidden' => false ]);
                $obj['CONTRACTDETAILKEY'] = SaaSMetricsUtil::getContractDetailDisplay($obj['CONTRACTDETAILKEY']);
            }
            if ($obj['CREATEDFROM']) {
                $view->findAndSetProperty([ 'path' => 'CREATEDFROM' ], [ 'hidden' => false ]);
            }
        }


        $matches = [];
        $view->findComponents(['path' => 'COSTTYPEID'], EditorComponentFactory::TYPE_FIELD, $matches);
        foreach ($matches as $match){
            $match->setProperty('readonly',false);
        }

        if ( $this->isMCPEnabled ) {
            $view->findAndSetProperty([ 'path' => 'EXCHRATETYPE' ], [ 'hidden' => false ]);
            $view->findAndSetProperty([ 'path' => 'EXCH_RATE_DATE' ], [ 'hidden' => false ]);
            $view->findAndSetProperty([ 'path' => 'EXCHANGE_RATE' ], [ 'hidden' => false ]);
            //$view->findAndSetProperty([ 'path' => 'BASEFLATAMOUNT' ], [ 'hidden' => false ]);
            $this->findAndSetExchRate($obj);
        }

        return true;
    }

    /**
     * getJavaScriptFileNames
     *
     * @return string[] $jsfiles
     */
    protected function getJavaScriptFileNames()
    {
        $jsfiles[] = "../resources/js/saasschedule.js";
        return $jsfiles;
    }

    /**
     * @param array $params
     */
    protected function buildDynamicMetadata(&$params)
    {

        $dimensionSection = [];
        $dimensionList = [];
        self::findElements($params, array('id' => 'dimensions'), EditorComponentFactory::TYPE_SECTION, $dimensionSection);
        self::findElements($dimensionSection, array(), EditorComponentFactory::TYPE_FIELD, $dimensionList);


        foreach ($dimensionList[0] as &$oneDim) {
            if ($oneDim['path'] == 'TASKID') {
                $oneDim['events'] = ['change' => 'onTaskSelectCheck(this);'];
            }
            if ($oneDim['path'] == 'PROJECTID') {
                $oneDim['events'] = ['change' => 'onProjectChange(this);'];
            }
        }

        if ( IsMultiEntityCompany() ) {
            $entityMatch = array();
            self::findElements($params, array('path' => 'LOCATIONID'), EditorComponentFactory::TYPE_FIELD, $entityMatch);
            if ( $entityMatch[0] ) {
                $entityMatch[0]['required'] = true;
                $entityMatch[0]['fullname'] = GT($this->textMap, 'IA.LOCATION_ENTITY');;
                if ( $this->isMCMESubscribed ) {
                    $entityMatch[0]['events']['change'] .= "entityChange(this.meta);";
                }
            }
            self::findAndSetMetadata($params, array('path' => 'HELPTEXT'), array('hidden' => false));
        }

    }


    /**
     * defaultDimensionValues
     *
     * @param array $obj
     */
    // This function was copied from contractdetaileditor
    private function defaultDimensionValues(&$obj)
    {
        if (empty($obj['LOCATIONID'])) {
            $obj['LOCATIONID'] = $this->scheduleValues['LOCATIONID'];
        }
        if (empty($obj['DEPARTMENTID'])) {
            $obj['DEPARTMENTID'] = $this->scheduleValues['DEPARTMENTID'];
        }

        $dimFields = IADimensions::getAllDimensionObjectProperties(!util_isPlatformDisabled());
        $dimUsed = IADimensions::getDimensionsOrder();
        //$obj['dimFields'] = $dimFields;
        if (!util_isPlatformDisabled()) {
            $platformDef = Pt_StandardUtil::getObjDef('saasschedule');
            $platformRels = Pt_StandardUtil::getRelationshipFields($platformDef);
            $this->setPlatformRelsAtrribute($platformRels, $this->scheduleValues);
        }

        foreach ($dimFields as $key => $field) {
            if (!in_array($key, $dimUsed)) {
                continue;
            }

            if ($field['standard']) {
                $dimfkid = strtoupper($field['dimfkid']);
                if (empty($obj[$dimfkid])) {
                    $obj[$dimfkid] = $this->scheduleValues[$dimfkid];
                }
            } else {
                $path = 'GLDIM' . str_replace(" ", "_", strtoupper($field['fullname']));
                $dispPath = $path . '_disp';
                if (empty($obj[$path])) {
                    $obj[$path] = $this->scheduleValues[$path];
                    $obj[$dispPath] = $this->scheduleValues[$dispPath];
                }
            }
        }
    }

    /**
     * All dimensions are available
     *
     * @return array
     */
    protected function getPTNotSupportedDimensionFieldNames()
    {
        return array();
    }

    /**
     * Return map of standard dimension field name to field name used in current page
     * @param string $prefix
     *
     * @return null|array
     */
    protected function getPTDimensionFieldNameTranslationMap($prefix = '')
    {
        $map = parent::getPTDimensionFieldNameTranslationMap();
        if ( $map == null ) {
            $map = array();
        }

        $map['LOCATIONID'] = 'LOCATION';
        $map['DEPARTMENTID'] = 'DEPARTMENT';

        return $map;
    }

    /**
     * Returns dimension ids in GL order without module filtering
     *
     * @return array
     */
    protected function getDimensionIDsInOrder()
    {
        $dimOrder = IADimensions::getDimensionIDsInGLOrder('', !util_isPlatformDisabled());

        return $dimOrder;
    }

    /**
     * MergeOwnerDimensions
     *     override parent
     *
     * @param array &$_params params array
     */
    protected function MergeOwnerDimensions(&$_params)
    {
        parent::MergeOwnerDimensions($_params);

        $this->mergeDimensionforNonOwnedObject($_params, 'saasschedule');

        // getDefaultsDimensionsFields() for 'saasschedule' does not handle LOC AND DEPT. So set Auto-fill flag on LOC and DEPT dimension
        $deptLocDim = array('department' => 'DEPARTMENTID', 'location' => 'LOCATIONID');
        if ( !util_isPlatformDisabled() ) {
            foreach ( $deptLocDim as $name => $id ) {
                $matches = array();
                self::findElements(
                    $_params, array('path' => $id), EditorComponentFactory::TYPE_FIELD, $matches);
                $matches[0]['standard'] = true;
                $matches[0]['isDimension'] = true;
                $matches[0]['autofillrelated'] = Pt_StandardUtil::autoFillRelated($name);
            }
        }
    }

    /**
     * mergeDimensionforNonOwnedObject
     *
     * @param array  $_params
     * @param string $entity
     */
    private function mergeDimensionforNonOwnedObject(&$_params, $entity)
    {
        $nodes = $this->getDefaultsDimensionsFields($_params, $entity);
        $fieldCnt = count($nodes);

        // if the ownedobject has dimensions we add them to the default container
        if ( !isset($nodes) || $fieldCnt <= 0 ) {
            return;
        }

        // let's see if we have a default container for the dimension header fields.
        $matches = array();
        $container = null;

        self::findElements($_params['view'], array('dimFields' => $entity), null, $matches);
        if ( is_array($matches) && count($matches) > 0 ) {
            $container = &$matches[0];
        }

        if ( isset($container) ) {
            foreach ( $nodes as &$node ) {

                if ( $node['path'] == 'PROJECTID' ) {
                    $node['events'] = [ 'change' => 'onProjectChange(this);' ];
                }

                $this->addCustomFieldToLayout($node, $container);
            }
        }
    }

   /**
     * findAndSetExchRate
     *
     * @param array $obj
     */
    private function findAndSetExchRate(&$obj)
    {
        if ( $obj['EXCHANGE_RATE'] == '' && $obj['EXCH_RATE_DATE'] != ''
             && $obj['EXCHRATETYPE'] != '' ) {
            if ( $obj['CURRENCY'] === $obj['BASECURR'] ) {
                $obj['EXCHANGE_RATE'] = 1;
            } else {
                $exchRateMgr = Globals::$g->gManagerFactory->getManager('exchangerate');
                $exch_rate = $exchRateMgr->GetTrxExchangeRateByTypeID(
                    $obj['EXCH_RATE_TYPE_ID'], $obj['CURRENCY'],
                    $obj['BASECURR'], $obj['EXCH_RATE_DATE']
                );
                if ( $exch_rate != '' ) {
                    $obj['EXCHANGE_RATE'] = $exch_rate;
                }
            }
        }
    }

    /**
     * Map the ajax action to a PHP function
     *
     * @param string $cmd the Ajax action requested
     *
     * @return bool false if command not found
     */
    protected function runAjax($cmd)
    {
        $ok = true;

        switch ($cmd) {
            case 'getExchangeRate':
                $this->ajaxGetExchangeRate();
                break;
            default:
                $ok = parent::runAjax($cmd);
                break;
        }

        return $ok;
    }

    /**
     * Get the exchange rate from the posted parameters
     */
    protected function ajaxGetExchangeRate()
    {
        // Gather the parameters
        $fromcurrency = Request::$r->fromcurrency;
        $tocurrency = Request::$r->tocurrency;
        $exchratedate = Request::$r->exchratedate;
        $exchratetype = Request::$r->exchratetype;

        $rate = $this->getEntityMgr()
                     ->calculateExchRate($exchratetype, $fromcurrency, $tocurrency, $exchratedate);

        echo '"' . $rate . '"';
    }

    /**
     *
     * @param array $obj Form values
     *
     * @return bool
     */
    protected function processObjectForViewing(&$obj)
    {
        Request::$r->SetCurrentObject($obj);
        return true;
    }

    /**
     * Get the editor button list
     *
     * @param string $state the editor state
     *
     * @return array the buttons list
     */
    public function getStandardButtons($state)
    {
        $currentObj = Request::$r->GetCurrentObject();
        if ($currentObj['CREATEDFROM'] === 'Contract') {
            if ($this->CanShowAuditTrail()) {
                $this->configureAuditTrailButton( $values );
            }
            $this->addHelpButton( $values );
            return $values;
        }
        $buttons = [];
        $buttons = INTACCTarray_merge(parent::getStandardButtons($state), $buttons);
        return $buttons;
    }

}