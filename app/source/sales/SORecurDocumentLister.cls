<?php
//
//================================================================================
//	FILE:			SORecurDocumentLister.cls
//	AUTHOR:			bharris
//	DESCRIPTION:	OE (so) Recurring Document Lister class.
//
//	(C)2005, Intacct Corporation, All Rights Reserved
//
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information
//	herein may not be used, copied or disclosed in whole or part
//	without prior written consent from Intacct Corporation.
//================================================================================
//

import('RecurDocumentLister');

class SORecurDocumentLister extends RecurDocumentLister
{
    
    /** @var  array $origFields */
    private $origFields;

    /**
     * SORecurDocument specific tokens
     * @var string[]
     */
    protected $additionalTokens = [];
    /**
     * Initializes $params array which is passed to parent constructor.
     */
    function __construct()
    {
        $this->filter                   = [ 'SCHEDULE', 'SUCCESS' ];
        $transaction_type               = Request::$r->_dt;
        $this->params                   = [];
        $this->params['entity']         = 'sorecurdocument';
        $this->params['helpfile']       = 'Viewing_and_Managing_a_List_of_Recurring_Templates';
        $this->params['templatename']   = $transaction_type;
        $this->params['opkey']          = 'so/lists/sodocument';
        $this->params['importtype']     = 'sorecurdocument';

        if ( !isset($transaction_type) || $transaction_type=='' ) {
            $this->params['fields']    = array('CUSTOMERNAME', 'DOCID','RECORDNO', 'FREQUENCY', 'NEXTEXECDATE', 'EXECCOUNT');
        } else {
            $this->params['fields']    = array('CUSTOMERNAME', 'FREQUENCY', 'NEXTEXECDATE', 'EXECCOUNT');
        }
        if (IsMCPSubscribed()) {
            $this->params['fields'][] = 'CURRENCY';
        }
        if (IsMCMESubscribed()) {
            $this->params['fields'][] = 'BASECURR';
        }
        $this->params['fields'][] = 'STATUS';
        if ( !isset($transaction_type) || $transaction_type=='' ) {
            $this->params['fields'][] = 'CONTRACTID';
        }
        $this->additionalTokens[] = 'IA.SUCCESS_PAYMENT_FAILED';
        $this->additionalTokens[] = 'IA.SUCCESS';
        $this->additionalTokens[] = 'IA.IN_TRANSIT';
        $this->additionalTokens[] = 'IA.FAILED';
        $this->additionalTokens[] = 'IA.SCHEDULE_ERROR';
        
        parent::__construct($this->params);
    }

    /**
     *
     */
    function BuildTable() 
    {
        global $kCCPaymentMethods;

        $this->Init();
        $this->origFields = $this->_params['_fields'];
        $this->_params['_fields'][] = 'PAYMETHOD';
        $this->_params['_fields'][] = 'CUSTOMERBANKACCOUNT.BANKNAME';
        $this->_params['_fields'][] = 'CUSTOMERBANKACCOUNT.ACCOUNTNUMBER';
        $this->_params['_fields'][] = 'CUSTOMERCREDITCARD.CARDID';
        $this->_params['_fields'][] = 'ACCOUNTTYPE';
        $this->_params['_fields'][] = 'BANKNAME';
        $this->_params['_fields'][] = 'GLACCTNO';
        $this->_params['_fields'][] = 'GLACCTTITLE';
        $this->_params['_fields'][] = 'LASTPAYMENTRESULT';
        parent::BuildTable();
        $table = &$this->table;
        for ($i = 0; $i < count($table); $i++) {
            if (!empty($table[$i]['CUSTOMERBANKACCOUNTKEY'])) {
                if ($table[$i]['PAYMETHOD'] == 'Online ACH Debit') {
                    $this->table[$i]['CUSTOMERBANKACCOUNTKEY'] = 
                    $table[$i]['CUSTOMERBANKACCOUNT.BANKNAME'] . '--' . $table[$i]['CUSTOMERBANKACCOUNT.ACCOUNTNUMBER'];
                } else {
                    $this->table[$i]['CUSTOMERBANKACCOUNTKEY'] = '';
                }
            }
            if (!empty($table[$i]['CUSTOMERCREDITCARDKEY'])) {
                if ($table[$i]['PAYMETHOD'] == 'Online Charge Card') {
                    $this->table[$i]['CUSTOMERCREDITCARDKEY'] = $table[$i]['CUSTOMERCREDITCARD.CARDID'];
                } else {
                    $this->table[$i]['CUSTOMERCREDITCARDKEY'] = '';
                }
            }
            if (!empty($table[$i]['BANKACCOUNTID'])) {
                if ($table[$i]['ACCOUNTTYPE'] == 'Bank') {
                    $this->table[$i]['BANKACCOUNTID'] = $table[$i]['BANKACCOUNTID'] . '--' . $table[$i]['BANKNAME'];
                } else {
                    $this->table[$i]['BANKACCOUNTID'] = '';
                }
            }
            if (!empty($table[$i]['GLACCOUNTKEY'])) {
                if ($table[$i]['ACCOUNTTYPE'] == 'Undeposited') {
                    $this->table[$i]['GLACCOUNTKEY'] = $table[$i]['GLACCTNO'] . '--' . $table[$i]['GLACCTTITLE'];
                } else {
                    $this->table[$i]['GLACCOUNTKEY'] = '';
                }
            }
            if (!empty($table[$i]['CREDITCARDTYPE'])) {
                if ($table[$i]['PAYMETHOD'] == 'Credit Card') {
                    $this->table[$i]['CREDITCARDTYPE'] = $kCCPaymentMethods[$table[$i]['CREDITCARDTYPE']];
                } else {
                    $this->table[$i]['CREDITCARDTYPE'] = '';
                }
            }
        }
    }

    /**
     * @param array $schedoplog
     * @param array $row
     *
     * @return string
     */
    protected function getStatus($schedoplog, $row)
    {
        if ($row['LASTPAYMENTRESULT'] == 'F') {
            return GT($this->textMap, 'IA.SUCCESS_PAYMENT_FAILED');
        } else {
            switch ($schedoplog['SUCCESS']) {
            case 'S':
                return GT($this->textMap, 'IA.SUCCESS');
            case 'T':
                return GT($this->textMap, 'IA.IN_TRANSIT');
            default :
                return GT($this->textMap, 'IA.FAILED');
            }
        }
    }

    /**
     * @param string[] $flds
     * @param string[] $lbls
     * @param array    $ords
     * @param array    $cboxs
     */
    function SetOutputFields($flds, $lbls, $ords=array(), $cboxs=array())
    {
        parent::SetOutputFields($this->origFields, [], $ords, $cboxs);
    }

}    // finish class SORecurDocumentLister

