<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <xsl:import href="../../private/xslinc/report_helpers.xsl"/>
    <xsl:include href="../../private/xslinc/aparage_common.xsl"/>
    <xsl:param name="dataonly">N</xsl:param>
    <xsl:param name="css_styles"/>
    <xsl:param name="stylesheetpath">../resources/css/report-alt.css</xsl:param>
    <xsl:param name="stylesheetpath1">../resources/css/intacct_styles.css</xsl:param>
    <xsl:param name="stylesheetpath2">../resources/theme/blue/css/layout_style.css</xsl:param>
    <xsl:output encoding="UTF-8" method="html"/>
    <xsl:variable name="space"><![CDATA[&nbsp;]]></xsl:variable>

</xsl:stylesheet>