<!doctype html>

<title>CodeMirror: FCL mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<link rel="stylesheet" href="../../theme/elegant.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="fcl.js"></script>
<style>.CodeMirror {border:1px solid #999; background:#ffc}</style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">FCL</a>
  </ul>
</div>

<article>
<h2>FCL mode</h2>
<form><textarea id="code" name="code">
  FUNCTION_BLOCK Fuzzy_FB
      VAR_INPUT
          TimeDay : REAL; (* RANGE(0 .. 23) *)
          ApplicateHost: REAL;
          TimeConfiguration: REAL;
          TimeRequirements: REAL;
      END_VAR

      VAR_OUTPUT
          ProbabilityDistribution: REAL;
          ProbabilityAccess: REAL;
      END_VAR

      FUZZIFY TimeDay
          TERM inside := (0, 0) (8, 1) (22,0);
          TERM outside := (0, 1) (8, 0) (22, 1);
      END_FUZZIFY

      FUZZIFY ApplicateHost
          TERM few := (0, 1) (100, 0) (200, 0);
          TERM many := (0, 0) (100, 0) (200, 1);
      END_FUZZIFY

      FUZZIFY TimeConfiguration
          TERM recently := (0, 1) (30, 1) (120, 0);
          TERM long := (0, 0) (30, 0) (120, 1);
      END_FUZZIFY

      FUZZIFY TimeRequirements
          TERM recently := (0, 1) (30, 1) (365, 0);
          TERM long := (0, 0) (30, 0) (365, 1);
      END_FUZZIFY

      DEFUZZIFY ProbabilityAccess
          TERM height := 1;
          TERM medium := 0.5;
          TERM low := 0;
          ACCU: MAX;
          METHOD: COGS;
          DEFAULT := 0;
      END_DEFUZZIFY

      DEFUZZIFY ProbabilityDistribution
          TERM height := 1;
          TERM medium := 0.5;
          TERM low := 0;
          ACCU: MAX;
          METHOD: COGS;
          DEFAULT := 0;
      END_DEFUZZIFY

      RULEBLOCK No1
          AND : MIN;
          RULE 1 : IF TimeDay IS outside AND ApplicateHost IS few THEN ProbabilityAccess IS height;
          RULE 2 : IF ApplicateHost IS many THEN ProbabilityAccess IS height;
          RULE 3 : IF TimeDay IS inside AND ApplicateHost IS few THEN ProbabilityAccess IS low;
      END_RULEBLOCK

      RULEBLOCK No2
          AND : MIN;
          RULE 1 : IF ApplicateHost IS many THEN ProbabilityDistribution IS height;
      END_RULEBLOCK

  END_FUNCTION_BLOCK
</textarea></form>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        theme: "elegant",
        matchBrackets: true,
        indentUnit: 8,
        tabSize: 8,
        indentWithTabs: true,
        mode: "text/x-fcl"
      });
    </script>

    <p><strong>MIME type:</strong> <code>text/x-fcl</code></p>
  </article>
