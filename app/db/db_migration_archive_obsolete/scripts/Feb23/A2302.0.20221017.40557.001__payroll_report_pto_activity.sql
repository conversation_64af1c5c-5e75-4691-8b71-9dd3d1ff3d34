update iapolicy set module='pay' where name='Payroll Report PTO Activity' and type = 'U' and module='pa'
/

--drop index if exist
DECLARE
    index_exists number := 0;
BEGIN
    select count(*) into index_exists
    from user_indexes where index_name = 'IX_PRPA_CNY' and table_name = 'PAYROLLREPORTPTOACTIVITY';
    if (index_exists = 1) then
        execute immediate 'drop index IX_PRPA_CNY';
    end if;
end;
/

-- indexes
CREATE INDEX IX_PRPA_CNY ON PAYROLLREPORTPTOACTIVITY (CNY#) TABLESPACE ACCTINDX INVISIBLE
/