accounts-payable-payment-actions-submit-request:
  type: object
  x-mappedTo: appymt
  properties:
    key:
      type: string
      description: System-assigned key for the payment.
      x-mappedTo: RECORDNO
      example: '132'
  required:
    - key
accounts-payable-payment-actions-submit-response:
  type: object
  x-mappedTo: appymt
  properties:
    key:
      type: string
      x-mappedTo: RECORDNO
      example: '132'
    id:
      type: string
      x-mappedTo: RECORDNO
      example: '132'
    href:
      type: string
      example: /objects/accounts-payable/payment/132
    state:
      type: string
      description: State of the entry
      x-mappedTo: STATE
      enum:
        - submitted
        - confirmed
      x-mappedToValues:
        - S
        - C
      example: submitted
accounts-payable-payment-actions-reverse-request:
  type: object
  x-mappedTo: appymt
  properties:
    key:
      type: string
      description: System-assigned key for the payment.
      x-mappedTo: RECORDNO
      example: '132'
    paymentDate:
      type: string
      format: date
      example: '2021-01-23'
      description: The date to make the payment.
      x-mappedTo: PAYMENTDATE
    description:
      type: string
      description: Description of the payment (memo).
      x-mappedTo: DESCRIPTION
      nullable: true
      example: From billing through August 31
  required:
    - key
accounts-payable-payment-actions-reverse-response:
  type: object
  x-mappedTo: appymt
  properties:
    key:
      type: string
      x-mappedTo: RECORDNO
      example: '132'
    id:
      type: string
      x-mappedTo: RECORDNO
      example: '132'
    href:
      type: string
      example: /objects/accounts-payable/payment/132
    state:
      type: string
      description: State of the entry
      x-mappedTo: STATE
      enum:
        - reversed
        - approved
      x-mappedToValues:
        - V
        - A
      example: reversed
accounts-payable-payment-actions-print-request:
  type: object
  x-mappedTo: appymt
  properties:
    key:
      type: string
      description: System-assigned key for the payment.
      x-mappedTo: RECORDNO
      example: '132'
  required:
    - key
accounts-payable-payment-actions-print-response:
  type: object
  x-mappedTo: appymt
  properties:
    key:
      type: string
      x-mappedTo: RECORDNO
      example: '132'
    id:
      type: string
      x-mappedTo: RECORDNO
      example: '132'
    href:
      type: string
      example: /objects/accounts-payable/payment/132
    state:
      type: string
      description: State of the entry
      x-mappedTo: STATE
      enum:
        - delivered
      x-mappedToValues:
        - P
      example: delivered
accounts-payable-payment-actions-confirm-request:
  type: object
  x-mappedTo: appymt
  properties:
    key:
      type: string
      description: System-assigned key for the payment.
      x-mappedTo: RECORDNO
      example: '132'
  required:
    - key
accounts-payable-payment-actions-confirm-response:
  type: object
  x-mappedTo: appymt
  properties:
    key:
      type: string
      x-mappedTo: RECORDNO
      example: '132'
    id:
      type: string
      x-mappedTo: RECORDNO
      example: '132'
    href:
      type: string
      example: /objects/accounts-payable/payment/132
    state:
      type: string
      description: State of the entry
      x-mappedTo: STATE
      enum:
        - confirmed
      x-mappedToValues:
        - C
      example: confirmed
accounts-payable-payment-actions-approve-request:
  type: object
  x-mappedTo: appymt
  properties:
    key:
      type: string
      description: System-assigned key for the payment.
      x-mappedTo: RECORDNO
      example: '132'
    notes:
      type: string
      description: Notes or comments about this payment.
      x-mappedTo: COMMENTS
      example: Approved, ready for use
  required:
    - key
accounts-payable-payment-actions-approve-response:
  type: object
  x-mappedTo: appymt
  properties:
    key:
      type: string
      x-mappedTo: RECORDNO
      example: '132'
    id:
      type: string
      x-mappedTo: RECORDNO
      example: '132'
    href:
      type: string
      example: /objects/accounts-payable/payment/132
    state:
      type: string
      description: State of the entry
      readOnly: true
      x-mappedTo: STATE
      enum:
        - approved
      x-mappedToValues:
        - A
      example: approved
accounts-payable-payment-actions-decline-request:
  type: object
  x-mappedTo: appymt
  properties:
    key:
      type: string
      description: System-assigned key for the payment.
      x-mappedTo: RECORDNO
      example: '132'
    notes:
      type: string
      description: Notes or comments about this payment.
      x-mappedTo: COMMENTS
      example: Declined, missing information
  required:
    - key
accounts-payable-payment-actions-decline-response:
  type: object
  x-mappedTo: appymt
  properties:
    key:
      type: string
      x-mappedTo: RECORDNO
      example: '132'
    id:
      type: string
      x-mappedTo: RECORDNO
      example: '132'
    href:
      type: string
      example: /objects/accounts-payable/payment/132
    state:
      type: string
      description: State of the entry
      x-mappedTo: STATE
      enum:
        - declined
      x-mappedToValues:
        - R
      example: declined