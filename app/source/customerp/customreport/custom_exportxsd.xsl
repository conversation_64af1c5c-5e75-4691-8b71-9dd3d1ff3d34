<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
<xsl:output indent="yes" encoding="UTF-8"/>

<xsl:template match="reportdef">
	<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">

		<!-- root tag of export xml -->
		<xs:element name="REPORT" type="ReportType"/>

		<xs:complexType name="ReportType">
			<xs:sequence>
				<xs:element name="DATA" type="dataType" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
		
		<xs:complexType name="dataType">
			<xs:sequence>
				<xsl:for-each select="COLUMN">
					<xsl:choose>
						<xsl:when test="@type = 'integer'">
							<xs:element name="{@name}" type="xs:integer"/>
						</xsl:when>
						<xsl:when test="@type = 'decimal'">
							<xs:element name="{@name}" type="xs:decimal"/>
						</xsl:when>
						<xsl:when test="@type = 'date'">
							<xs:element name="{@name}" type="xs:date"/>
						</xsl:when>
						<xsl:when test="@type = 'text'">
							<xs:element name="{@name}" type="xs:string"/>
						</xsl:when>
						<xsl:when test="@type = 'char'">
							<xs:element name="{@name}" type="xs:string"/>
						</xsl:when>
						<xsl:otherwise>
							<xs:element name="{@name}" type="xs:string"/>
						</xsl:otherwise>
					</xsl:choose>
				</xsl:for-each>
			</xs:sequence>
		</xs:complexType>
	</xs:schema>
</xsl:template>

</xsl:stylesheet>