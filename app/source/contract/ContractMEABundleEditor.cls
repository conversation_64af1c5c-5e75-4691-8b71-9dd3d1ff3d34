<?php
/**
 * File ContractMEABundleEditor.cls contains the class ContractMEABundleEditor
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

/**
 * Contract MEA Bundle form handler
 *
 * Class ContractMEABundleEditor
 */
class ContractMEABundleEditor extends ContractBundleEditor
{
    /**
     * @param array $params the parameters of the class
     */
    public function __construct($params = array())
    {
        parent::__construct($params);

        $this->additionalTokens = array_merge($this->additionalTokens, [
            'IA.APPLY_MEA_AND_SAVE',
            'IA.PREVIEW_MEA_ALLOCATION',
            'IA.DISTRIBUTE'
        ]);

        $this->textTokens = array_merge($this->textTokens, [
            'IA.MEAALLOCATIONS_PREVIEW',
            'IA.MEAALLOCATIONS_JS_ERROR_NEGATIVE_SALE_LINE',
            'IA.MEAALLOCATIONS_JS_ERROR_NEGATIVE_DEBOOK_LINE',
            'IA.MEAALLOCATIONS_JS_ERROR_BUNDLE_TOTAL',
        ]);
    }

    /**
     * @return string
     */
    protected function getLabelForSaveButton()
    {
        return 'IA.APPLY_MEA_AND_SAVE';
    }

    /**
     * getJavaScriptFileNames
     * Get the list of JS files to include in the editor screen
     *
     * @return array the list of JS files to include
     */
    protected function getJavaScriptFileNames()
    {
        return array('../resources/js/contractbundle.js',);
    }

    /**
     * @return array
     */
    protected function getEditorGlobals()
    {
        $vars = parent::getEditorGlobals();

        $vars['cnnegative_create_op'] = GetOperationId('cn/lists/contractnegativebilling/create');
        $vars['cnnegative_view_op'] = GetOperationId('cn/lists/contractnegativebilling/view');

        return $vars;
    }

    /**
     * getStandardButtons
     * Get the editor button list
     *
     * @param string $state the editor state
     *
     * @return array the buttons list
     */
    public function getStandardButtons($state)
    {
        $values = parent::getStandardButtons($state);

        // show preview MEA only in edit/new mode...
        if ( $state === $this->kShowEditState || $state === $this->kShowNewState ) {
            $pValues = array();
            $this->setButtonDetails(
                $pValues,
                Editor_SubmitBtnID,
                'cancelbutton',
                'IA.PREVIEW_MEA_ALLOCATION',
                'previewBtn',
                false,
                'previewMEA();',
                false
            );

            $values = INTACCTarray_merge($pValues, $values);
        }

        return $values;
    }

    /**
     * @return bool
     */
    protected function CanEdit()
    {
        return false;
    }

    /**
     * @param array $_params
     *
     * @return bool
     */
    protected function ProcessEditAction(&$_params)
    {
        $name = Request::$r->{Globals::$g->kId};
        $ok = true;
        if ( !$this->getEntityMgr()->isActive($name) ) {
            $ok = $ok && parent::ProcessViewAction($_params);
        } else {
            $ok = $ok && parent::ProcessEditAction($_params);
        }
        return $ok;
    }

    /**
     * @param array $params
     */
    protected function buildDynamicMetadata(&$params)
    {
        parent::buildDynamicMetadata($params);
    }

    /**
     * @param array $obj
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        $ok = parent::mediateDataAndMetadata($obj);

        $view = $this->getView();
        $viewChildren = $view->getChildren();
        /** @var EditorPage $editorPage */
        $editorPage = $viewChildren[0];
        ContractUtil::findAndSetJournalsMetadata($editorPage->params, null, null, EditorComponentFactory::TYPE_FIELD,
                                                 EditorComponentFactory::TYPE_FIELD);
        $state = $this->getState();
        if ($state === $this->kShowNewState && count($obj['ENTRIES'] ?? []) < count($obj[':origEntries'] ?? [])) {
            // After a prior bundle creation error and user clicked Go back, restore original entries but
            // preserve BUNDLENOs entered by user
            $lineNoToIdx = [];
            foreach ($obj[':origEntries'] as $i => $entry) {
                $lineNoToIdx[$entry['CONTRACTDETAILLINENO']] = $i;  // create map of lineno to index
            };
            $tmpEntries = $obj[':origEntries'];
            foreach ($obj['ENTRIES'] as $entry) {
                if (!empty($entry['BUNDLENO'])) {
                    $origEntriesIdx = $lineNoToIdx[$entry['CONTRACTDETAILLINENO']];
                    $tmpEntries[$origEntriesIdx]['BUNDLENO'] = $entry['BUNDLENO'];
                }
            }
            $obj['ENTRIES'] = $tmpEntries;
        }
        if (isNonEmptyArray($obj['ENTRIES'])) {
            foreach ( $obj['ENTRIES'] as &$entry ) {
                if (isArrayValueTrue($entry, 'ITEMDEFAULTBUNDLE') && !isArrayValueProvided($entry, 'BUNDLENO')) {
                    $entry['BUNDLENO'] = '1';
                }
                $contractLineType = $entry['CONTRACTLINETYPE'];
                if ($contractLineType === ContractDetailManager::LINETYPE_DISCOUNT) {
                    $contractId = $entry['CONTRACTID'];
                    $contractDetailKey = $entry['CONTRACTDETAILKEY'];
                    $contractLineNo = $entry['CONTRACTDETAILLINENO'];
                    $href = "javascript:renderNegativeBillingView('$contractId', $contractDetailKey, $contractLineNo);";
                    $entry['DISTRIBUTEDISCOUNTLINK'] = '<a href="' . $href . '" target1="_blank">'.GT($this->textMap,"IA.DISTRIBUTE").'</a>';
                }
            }
            unset($entry);
        }

        if ( empty($obj['CONTRACTID']) && isset($this->contractid) && $this->contractid !== '' ) {
            $obj['CONTRACTID'] = $this->contractid;
            $obj['CONTRACTKEY'] = $this->contractid;
        }

        // show MEA allocation on view mode...
        if ( $state === $this->kShowViewState ) {
            $view->findAndSetProperty(['path' => 'MEAallocationSection'], ['hidden' => false], EditorComponentFactory::TYPE_SECTION);
            $mea_alloc_table = array();
            $view->findComponents(array('id' => 'MEAallocationSection'), EditorComponentFactory::TYPE_SECTION, $mea_alloc_table);
            if ( $mea_alloc_table[0] ) {
                $mea_alloc_table[0]->setProperty('hidden', false);
            }
        }

        if($state == $this->kShowNewState){ // if new MEA created then pick config value
                $obj['FVAMOUNTBASEDON'] = ContractUtil::getDateTypeToUseForFVPrice();
        }else{
            if (empty($obj['FVAMOUNTBASEDON'])) {
                // value not found in DB while viewing MEA, default value always be MEA effective date
                $obj['FVAMOUNTBASEDON'] = ContractUtil::FVPRICE_MEA_EFFECTIVE_DATE;
            }
        }

        $meaAllocations = & $obj['MEA_ALLOCATION_TABLE'];
        if ( $meaAllocations !== null ) {
            foreach ( $meaAllocations as & $oneAllocation ) {
                $oneAllocation['MEAPERCENT'] = "" . iround($oneAllocation['MEAPERCENT'], ContractUtil::KIT_ALLOCATION_PRECISION) . "";
                $oneAllocation['MEAAMOUNT'] = "" . iround($oneAllocation['MEAAMOUNT'], ContractUtil::AMOUNT_PRECISION) . "";
                $oneAllocation['MEABASEAMOUNT'] = "" . iround($oneAllocation['MEABASEAMOUNT'], ContractUtil::AMOUNT_PRECISION) . "";
                $oneAllocation['FAIRVALUEPRICE'] = $oneAllocation['FVUNITPRICE'];
                $oneAllocation['FAIRVALUEEXTPRICE'] = $oneAllocation['FVEXTPRICE'];
                $oneAllocation['MULTIPLIER'] = "" . iround($oneAllocation['MULTIPLIER'], 0) . "";
            }
        }

        $numEntries = count($obj['ENTRIES'] ?? []);
        if ($numEntries < 2) {
            if ($numEntries == 1) {
                Globals::$g->gErr->addError('CN-0147', __FILE__ . ':' . __LINE__, "A multiple element arrangement consists of at least two contract lines. This contract has only one contract line. Add another contract line and then try creating an MEA allocation scheme again.");
            } else {
                Globals::$g->gErr->addError('CN-0148', __FILE__ . ':' . __LINE__, "A multiple element arrangement consists of at least two contract lines. This contract has no contract line. Add another contract line and then try creating an MEA allocation scheme again.");
            }
            $ok = false;
        }

        // if we had errors, lets show error page...
        if ( Globals::$g->gErr->hasErrors() ) {
            $this->state = $this->kErrorState;
        }

        // Default HANDLEOPENENTRIES
        $cnsetupMgr = Globals::$g->gManagerFactory->getManager('cnsetup');
        $obj['HANDLEOPENENTRIES'] = $cnsetupMgr->getPostMEAOpenEntriesDefault();

        return $ok;
    }

    /**
     * get data from entity manager and format the data for display
     *
     * @param string   $entity  entity or object
     * @param string   $objId   entityid or objectid
     * @param string   $doctype
     * @param string[] $fields
     *
     * @return array the formatted result set
     */
    protected function getEntityData($entity, $objId, $doctype='', $fields=null)
    {
        $objId = html_entity_decode($objId);
        $entityData = parent::getEntityData($entity, $objId, $doctype, $fields);

        $allocationQuery = "select cad.CNY#,cad.RECORD#,cad.CBEREC,cad.NAME,cad.VALUE as bundleno,cad.ITEMKEY,item.itemid as ITEMNAME,
                                cad.MEAEFFECTIVEDATE,cad.APPLYTOJOURNAL1,cad.APPLYTOJOURNAL2,cad.MEAAMOUNT,cad.MEABASEAMOUNT,
                                cad.MEAPERCENT,cad.RATE,cad.QUANTITY,cad.MULTIPLIER,cad.TOTALFLATAMOUNT,cad.TOTALFLATBASEAMOUNT,
                                cad.FVUNITPRICE,cad.FVEXTPRICE,cad.COMPUTATIONMEMO,cad.MULTIPLIERDETAIL,cad.FLATAMOUNT,cad.LINENO             
                            from v_contractallocforbundle cad 
                            inner join icitem item on (cad.itemkey = item.record# and cad.cny# = item.cny#)
                            where cad.cny# = :1 and cad.name = :2";

        $qparams = array($allocationQuery, GetMyCompany(), $objId);
        $queryResult = QueryResult($qparams);

        $entityData['MEA_ALLOCATION_TABLE'] = $queryResult;
        return $entityData;
    }

    /**
     * @return ContractMEABundleManager
     */
    public function getEntityMgr()
    {
        /** @var ContractMEABundleManager $innerManager */
        $innerManager = parent::getEntityMgr();
        return $innerManager;
    }

    /**
     * Calls javascript function if IPADDRESSFILTER is not set to NONE
     *
     * @param array  $buttons
     * @param string $id
     * @param string $name
     * @param string $button
     * @param string $action
     * @param bool   $submitData
     * @param string $jsCode
     * @param bool   $serverAction
     * @param bool   $disableValidation
     * @param string $args
     */
    protected function setButtonDetails(&$buttons, $id, $name, $button, $action, $submitData = true, $jsCode = '',
                                        $serverAction = true, $disableValidation = false, $args = '')
    {
        if ( $id == Editor_SaveBtnID ) {
            $jsCode = "validateOverrides();";
        }
        parent::setButtonDetails($buttons, $id, $name, $button, $action, $submitData, $jsCode,
                                 $serverAction, $disableValidation, $args);
    }

    /**
     * @return bool
     */
    protected function CanSaveAndNew() {
        return false;
    }

}
