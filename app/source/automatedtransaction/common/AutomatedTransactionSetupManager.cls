<?php

/**
 * Manager class for AutomatedTransactionSetupManager
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Intacct Corporation, All Rights Reserved
 */

class AutomatedTransactionSetupManager extends EntityManager
{
    const STATE_INPROGRESS = 'In progress';
    const STATE_SUBSCRIBED = 'Subscribed';
    const STATE_FAILED = 'Failed';

    const PROVIDER_SAIL = 'SAIL';

    private static AutomatedTransactionCommon $providerObj;

    /**
     * @param array $params parameters
     */
    public function __construct($params = [])
    {
        $params['moduleKey']= AutomatedTransactionInteractions::MODULEID;
        parent::__construct($params);
    }

    /**
     * @param string $newProvider
     * @return bool
     */
    public static function configureProvider(string $newProvider): bool
    {
        $ok = true;

        // Step 1: First delete current active provider
        self::disableActiveProviders();

        // Step 2: Then make the new provider as in progress
        $values = array(
            'MODULEKEY' => AutomatedTransactionInteractions::MODULEID,
            'PROVIDER' => $newProvider,
            'STATE' => AutomatedTransactionSetupManager::STATE_INPROGRESS
        );
        $atrxSetup = Globals::$g->gManagerFactory->getManager('automatedtransactionsetup');
        $ok = $ok && $atrxSetup->add($values);

        GetModulePreferences(AutomatedTransactionInteractions::MODULEID, $prefs);
        $prefs['MODULE_CONFIGURED'] = 'T';
        $ok = $ok && SetModulePreferences(AutomatedTransactionInteractions::MODULEID, $prefs);

        // Step 3: Add jobs to queue
        $ok = $ok && AutomatedTransactionSetupManager::getActiveProviderObject()->addSubscriptionToQueue();

        return $ok;
    }

    /**
     * @return bool
     */
    public static function disableActiveProviders(): bool
    {
        $ok = true;
        // Delete current provider
        $atrxSetup = Globals::$g->gManagerFactory->getManager('automatedtransactionsetup');
        $rec = $atrxSetup->GetList(
            [
                'selects' => ['RECORDNO'],
                'filters' => [[
                    ['MODULEKEY', "=", AutomatedTransactionInteractions::MODULEID]
                ]]
            ]
        );
        if (!empty($rec)) {
            $ok = $ok && $atrxSetup->Delete($rec[0]['RECORDNO']);
        }
        return $ok;
    }

    /**
     * @return AutomatedTransactionCommon|null
     */
    public static function getActiveProviderObject(): AutomatedTransactionCommon|null
    {
        $obj = null;
        if (!empty(self::$providerObj)) {
            return self::$providerObj;
        }

        $atrxSetup = Globals::$g->gManagerFactory->getManager('automatedtransactionsetup');
        $filterCondtion = array(
            "filters" => [[
                ['MODULEKEY', "=", AutomatedTransactionInteractions::MODULEID],
            ]]
        );
        $rec = $atrxSetup->GetList($filterCondtion);
        if (!empty($rec)) {
            $class = preg_replace('/[^a-zA-Z]/', '', $rec[0]['PROVIDER']);
            $fullClassName = $class . 'AutomatedTransaction';
            if (class_exists($fullClassName)) {
                self::$providerObj = new $fullClassName();
                $obj = self::$providerObj;
            } else {
                logToFileError("Class: ".$fullClassName." does not exist");
                Globals::$g->gErr->addError('BL03000106', __FILE__ . '.' . __LINE__, "Cannot find object");
            }
        }

        return $obj;
    }
}