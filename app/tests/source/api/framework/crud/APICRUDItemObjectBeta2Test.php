<?php
/**
 * Focus on validating item level bulk & mixop handling
 *
 * <AUTHOR>
 * @copyright Copyright (C)2020 Sage Intacct Corporation, All Rights Reserved
 */

namespace tests\source\api\framework\crud;
require_once (__DIR__.'/../APITestHelper.cls');

/**
 * Class APICRUDItemObjectTest
 * @package tests\source\api\framework\crud
 */
class APICRUDItemObjectBeta2Test extends \unitTest\core\UnitTestBaseContext
{

    /**
     * webURL query
     * @var string
     */
    var $queryJSON_WebURL = '{
         "object" : "__testglbatch",
         "fields": [
             "webURL",
             "key"
         ],
         "filters": [{
             "$eq" : { "key": "headerKey" }
         }]
        }';

    /**
     * patch request with URI in the format of:
     * '{version}/objects/{itemObject}'
     * w/ 1 new item, 1 updated item, and 1 deleted item
     *
     * @throws \APIException
     */
    public function testSimpleMixOp_SingleParentPATCH()
    {
        // create new header & items
        [$headerKey, $itemKeys] = $this->createSingleHeaderWithItems();
        $updateKey = $itemKeys[0];
        $deleteKey = $itemKeys[1];

        // patch header with
        // 1 new item, 1 updated item, and 1 deleted item
        [$payload, $timeStamp] = $this->payloadMixOp_SingleParent($updateKey, $deleteKey);
        $parameters = [
            'sessionId'     => 'sessionid',
            'senderId'      => 'senderid',
            'clientId'      => 'clientid',
            'url'           => 'v5-test-beta2/objects/gl/__testglbatch/'.$headerKey.'/__testglentry',
            'httpMethod'    => 'PATCH',
            'body'          => $payload,
        ];
        $dispatcher = \APIDispatcher::getDispatcher($parameters);
        $result = $dispatcher->dispatch();

        // sanity check the result
        $this->assertNotNull($result);
        $this->assertTrue($result->getStatus() === 200);
        $response = $result->getResponseBody()[\APIConstants::IA_RESULT_KEY];
        $this->assertTrue(count($response) === 3);

        // make sure deleted item does not return anything
        $this->assertEmpty((array)$response[1]);

        // make sure added & updated items are returning Ids & Keys
        $this->assertTrue(count(array_column($response,\APIConstants::API_OBJECT_KEY_FIELD_NAME)) === 2);
        $this->assertTrue(count(array_column($response,\APIConstants::API_OBJECT_ID_FIELD_NAME)) === 2);

        // verify the add
        $this->assertNotTrue(in_array($response[2][\APIConstants::API_OBJECT_KEY_FIELD_NAME], $itemKeys));

        // verify the update
        $updatedItem = $this->getItemByHeaderAndItemKeys($headerKey, $updateKey);
        $this->assertTrue($updatedItem['description'] === strval($timeStamp));

        // verify the delete
        $this->getMissingItemByHeaderAndItemKeys($headerKey, $deleteKey);
    }

    /**
     * patch request invalid mix operation property values
     * make sure the invalid values are returned in error details
     *
     * @throws \APIException
     */
    public function testUnkonwnMixOp_SingleParentPATCH()
    {
        // create new header & items
        [$headerKey, $itemKeys] = $this->createSingleHeaderWithItems();
        $deleteKey1 = $itemKeys[0];
        $deleteKey2 = $itemKeys[1];
        $op1 = "hello-23R2";
        $op2 = "Kitty-I18N";

        // patch header with
        // 1 new item, 1 updated item, and 1 deleted item
        [$payload, ] = $this->payloadMixOp_unknownOp($deleteKey1, $deleteKey2, $op1, $op2);
        $parameters = [
            'sessionId'     => 'sessionid',
            'senderId'      => 'senderid',
            'clientId'      => 'clientid',
            'url'           => 'v5-test-beta2/objects/gl/__testglbatch/'.$headerKey,
            'httpMethod'    => 'PATCH',
            'body'          => $payload,
        ];
        $dispatcher = \APIDispatcher::getDispatcher($parameters);
        $result = $dispatcher->dispatch();

        $this->assertNotNull($result, "mix-op patch result should not be null");
        $this->assertNotEmpty($errorResponse = $result->getResponseBody() ?? [], "mix-op error response should not be empty");
        $this->assertNotNull($errorDetails = $errorResponse[\APIConstants::IA_RESULT_KEY][\APIError::KEY_ERROR][\APIError::KEY_DETAILS]);
        $this->assertIsArray($errorDetails);
        $this->assertTrue(strcontains($errorDetails[0][\APIError::KEY_ERROR_MESSAGE] ?? '', $op1),
                          '1st ia::operation value is not reported properly');
        $this->assertTrue(strcontains($errorDetails[1][\APIError::KEY_ERROR_MESSAGE] ?? '', $op2),
                          '2nd ia::operation value is not reported properly');
    }

    /**
     * patch request with URI in the format of:
     * '{version}/objects/{itemObject}'
     * w/ 1 updated item, and 1 deleted item
     *
     * @throws \APIException
     */
    public function testSimpleMixOp_ItemTopPATCH()
    {
        // create new header & items
        [$headerKey, $itemKeys] = $this->createSingleHeaderWithItems();
        $updateKey = $itemKeys[0];
        $deleteKey = $itemKeys[1];

        // patch header with
        // 1 updated item, and 1 deleted item
        [$payload, $timeStamp] = $this->payloadMixOp_ItemTop($updateKey, $deleteKey);
        $parameters = [
            'sessionId'     => 'sessionid',
            'senderId'      => 'senderid',
            'clientId'      => 'clientid',
            'url'           => 'v5-test-beta2/objects/gl/__testglbatch/' . $headerKey . '/__testglentry',
            'httpMethod'    => 'PATCH',
            'body'          => $payload,
        ];
        $dispatcher = \APIDispatcher::getDispatcher($parameters);
        $result = $dispatcher->dispatch();

        // sanity check the result
        $this->assertNotNull($result);
        $this->assertTrue($result->getStatus() === 200);
        $response = $result->getResponseBody()[\APIConstants::IA_RESULT_KEY];
        $this->assertTrue(count($response) === 2);

        // make sure deleted item does not return anything
        $this->assertEmpty((array)$response[1]);

        // make sure added & updated items are returning Ids & Keys
        $this->assertTrue(count(array_column($response,\APIConstants::API_OBJECT_KEY_FIELD_NAME)) === 1);
        $this->assertTrue(count(array_column($response,\APIConstants::API_OBJECT_ID_FIELD_NAME)) === 1);

        // verify the update
        $updatedItem = $this->getItemByHeaderAndItemKeys($headerKey, $updateKey);
        $this->assertTrue($updatedItem['description'] === strval($timeStamp));

        // verify the delete
        $this->getMissingItemByHeaderAndItemKeys($headerKey, $deleteKey);
    }

    /**
     * patch request [MIXED OPS] with URI in the format of:
     * '{version}/objects/{itemObject}'
     * w/ 2 non-existing items, one with lower case 'delete` and one with upper case 'DELETE'
     *
     * ensures delete operation is case-insensitive
     * <p>
     * By default, attempts to delete non-existing items are treated as 204, so will not throw 404
     * more details:
     * https://intacct.atlassian.net/wiki/spaces/AD/pages/1991935298/204+vs+404+-+Configurable+Behavior+of+Deleting+an+Not+Existing+Record
     * @throws \APIException
     */
    public function testSimpleMixOp_Delete_NonExistentItem()
    {
        // create new header & items
        [$headerKey, $itemKeys] = $this->createSingleHeaderWithItems();
        $lastKey = $itemKeys[count($itemKeys) - 1];
        //warning: don't change the below two lines. Keys need to be distinct even if its non-existent
        $nonExistentKey1 =  strval(intval($lastKey) + 1);
        $nonExistentKey2 =  strval(intval($lastKey) + 2);

        // create a delete payload with 2 non-existent items, one with lower case `delete` and one with upper case `Delete`
        $payload = $this->payloadMixOp_Delete($nonExistentKey1, $nonExistentKey2);
        $parameters = [
            'sessionId'     => 'sessionid',
            'senderId'      => 'senderid',
            'clientId'      => 'clientid',
            'url'           => 'v5-test-beta2/objects/gl/__testglbatch/'.$headerKey,
            'httpMethod'    => 'PATCH',
            'body'          => $payload,
        ];
        $dispatcher = \APIDispatcher::getDispatcher($parameters);
        $result = $dispatcher->dispatch();

        $this->assertNotNull($result);
        $this->assertTrue($result->getStatus() === 200);
        $response = $result->getResponseBody()[\APIConstants::IA_RESULT_KEY];
        $this->assertTrue(count($response) === 3);
    }

    /**
     * patch request [MIXED OPS] with URI in the format of:
     * '{version}/objects/{itemObject}'
     * w/ 2 items, one with lower case 'delete` and one with upper case 'DELETE'
     *
     * ensures the delete operation is case-insensitive
     * @throws \APIException
     */
    public function testSimpleMixOp_Delete_ExistentItem()
    {
        // create new header & items
        [$headerKey, $itemKeys] = $this->createSingleHeaderWithItems();
        $deleteKey1 = $itemKeys[0];
        $deleteKey2 = $itemKeys[1];

        // create a delete payload with 2 items, one with lower case `delete` and one with upper case `Delete`
        $payload = $this->payloadMixOp_Delete($deleteKey1, $deleteKey2);
        $parameters = [
            'sessionId'     => 'sessionid',
            'senderId'      => 'senderid',
            'clientId'      => 'clientid',
            'url'           => 'v5-test-beta2/objects/gl/__testglbatch/'.$headerKey,
            'httpMethod'    => 'PATCH',
            'body'          => $payload,
        ];
        $dispatcher = \APIDispatcher::getDispatcher($parameters);
        $result = $dispatcher->dispatch();

        $this->assertNotNull($result);
        $this->assertTrue($result->getStatus() === 200);
        $response = $result->getResponseBody()[\APIConstants::IA_RESULT_KEY];
        $this->assertTrue(count($response) === 3);

        //ensure items are indeed deleted
        $this->getMissingItemByHeaderAndItemKeys($headerKey, $deleteKey1);
        $this->getMissingItemByHeaderAndItemKeys($headerKey, $deleteKey2);

    }

    /**
     * ensure webURL exists in top-level objects and doesn't exist in related objects
     * <p>
     * API schema is set up with only webURL, but no Location key field
     * @return void
     * @throws \APIException
     */
    public function testWebURL()
    {
        // data setup
        [$headerKey, ] = $this->createSingleHeaderWithItems();

        $url = 'v5-test-beta2/objects/gl/__testglbatch/'.$headerKey;
        $result = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);

        //ensure webURL in top-level
        self::assertArrayHasKey(\APIConstants::API_WEB_URL_FIELD_NAME, $result[\APIConstants::IA_RESULT_KEY]);
        self::assertNotEmpty($result[\APIConstants::IA_RESULT_KEY][\APIConstants::API_WEB_URL_FIELD_NAME]);

        //ensure we have related objects, and they don't have webURL
        self::assertGreaterThanOrEqual(1,$result[\APIConstants::IA_RESULT_KEY]['entries'] );

        foreach ( $result[\APIConstants::IA_RESULT_KEY]['entries'] as $entry ) {
            self::assertArrayNotHasKey(\APIConstants::API_WEB_URL_FIELD_NAME, $entry);
        }
    }

    /**
     * fetch location key field.
     * <p>
     * API schema is set up with location key field, but no webURL
     *
     * @return void
     * @throws \APIException
     */
    public function testLocationKey()
    {
        trigger_error('TestDeprecatedFeature is deprecated', E_USER_DEPRECATED);
        // data setup
        [$headerKey, ] = $this->createSingleHeaderWithItems();

        $url = 'v5-test-beta2/objects/gl/__testglbatch/'.$headerKey;
        $result = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);

        //ensure location key field exists
        self::assertArrayNotHasKey('locationKey', $result[\APIConstants::IA_RESULT_KEY]);
    }

    /**
     * fetch location key field and webURL when both are defined
     * <p>
     * API schema is set up with location key field and webURL
     *
     * @return void
     * @throws \APIException
     */
    public function testLocationKey_WebURL()
    {
        // data setup
        [$headerKey, ] = $this->createSingleHeaderWithItems();

        $url = 'v5-test-beta2/objects/gl/__testglbatch/'.$headerKey;
        $result = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);

        //ensure location key fields exists
        self::assertArrayNotHasKey('locationKey', $result[\APIConstants::IA_RESULT_KEY]);

        //ensure webURL in top-level
        self::assertArrayHasKey(\APIConstants::API_WEB_URL_FIELD_NAME, $result[\APIConstants::IA_RESULT_KEY]);
        self::assertNotEmpty($result[\APIConstants::IA_RESULT_KEY][\APIConstants::API_WEB_URL_FIELD_NAME]);
    }

    /**
     * ensure webURL exists in top-level objects and doesn't exist in related objects
     * this is for calls with multiple keys like:
     * /objects/__testglbatch/<key1>,<key2>
     * @return void
     * @throws \APIException
     */
    public function testWebURL_MultipleKeys()
    {
        // data setup
        [$headerKey1, ] = $this->createSingleHeaderWithItems();
        [$headerKey2, ] = $this->createSingleHeaderWithItems();

        $url = 'v5-test-beta2/objects/gl/__testglbatch/'.$headerKey1 . ',' . $headerKey2 ;
        $results = \APITestHelper::executeAndValidateAPIDispatcher(2, 0, $url, \APIConstants::API_HTTP_METHOD_GET);

        foreach ( $results[\APIConstants::IA_RESULT_KEY] as $rec ) {
            self::assertArrayHasKey(\APIConstants::API_WEB_URL_FIELD_NAME,$rec);
            self::assertNotEmpty($rec['webURL']);

            self::assertGreaterThanOrEqual(1,$rec['entries'] );

            foreach ( $rec['entries'] as $entry ) {
                self::assertArrayNotHasKey(\APIConstants::API_WEB_URL_FIELD_NAME,$entry);
            }
        }
    }

    /**
     * ensure owned objects locationKey value is not used while
     * building webURL for the Base/Top-Level object. See the example structure below
     * <pre>
     * glbatch
     *  - locationKey : null
     *  - glentry
     *      - locationKey: 12
     * </pre>
     * The locationKey of owned object glentry (in this example) should have no bearing whatsoever for creating webURL
     * for top-level object glbatch
     * <p>
     * <b> Approach </b>
     * <li> use services/query with `webURL` in payload with a filter value (key) to get the reference value for topLevel webURL
     * <li> call Get on the object with that key
     * <li> ensure both webURL are the same
     * <li> secondary validation, also ensure the Get response for the object has owned objects entries
     * @return void
     */
    public function testWebURL_TopLevelLocationKey()
    {
        // data setup
        [$headerKey, ] = $this->createSingleHeaderWithItems();

        //prepare expected webURL value
        $queryJSON_WebURL = str_replace('headerKey', $headerKey  ,$this->queryJSON_WebURL);
        $queryResult = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, "v5-test/services/query",
                                                                       \APIConstants::API_HTTP_METHOD_POST,
                                                                       $queryJSON_WebURL, [], false);
        $expectedWebURL = null;
        foreach ( $queryResult[\APIConstants::IA_RESULT_KEY] as $rec ) {
            self::assertNotEmpty($rec['webURL']);
            $expectedWebURL = $rec['webURL'];
        }

        // test Get
        $url = 'v5-test-beta2/objects/gl/__testglbatch/'.$headerKey;
        $result = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);

        //ensure webURL in top-level
        self::assertArrayHasKey(\APIConstants::API_WEB_URL_FIELD_NAME, $result[\APIConstants::IA_RESULT_KEY]);
        self::assertNotEmpty($result[\APIConstants::IA_RESULT_KEY][\APIConstants::API_WEB_URL_FIELD_NAME]);

        //ensure webURL from query and Get are same
        self::assertSame($expectedWebURL, $result[\APIConstants::IA_RESULT_KEY][\APIConstants::API_WEB_URL_FIELD_NAME]);

        //ensure we have owned objects, and they don't have webURL
        self::assertGreaterThanOrEqual(1,$result[\APIConstants::IA_RESULT_KEY]['entries'] );

        foreach ( $result[\APIConstants::IA_RESULT_KEY]['entries'] as $entry ) {
            self::assertArrayNotHasKey(\APIConstants::API_WEB_URL_FIELD_NAME, $entry);
        }
    }

    /**
     * ensure webURL exists in top-level objects and doesn't exist in related objects
     * this is for sub resource calls like:
     * /objects/__testglbatch/<key>/__testglentry/<entryKey>
     * @return void
     * @throws \APIException
     */
    public function testWebURL_subResource()
    {
        // data setup
        [$headerKey, $itemKeys ] = $this->createSingleHeaderWithItems();

        $url = 'v5-test-beta2/objects/gl/__testglbatch/'.$headerKey . '/__testglentry/' . $itemKeys[0];
        $result = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $url, \APIConstants::API_HTTP_METHOD_GET);

        //ensure webURL in top-level
        self::assertArrayHasKey(\APIConstants::API_WEB_URL_FIELD_NAME, $result[\APIConstants::IA_RESULT_KEY]);
        self::assertNotEmpty($result[\APIConstants::IA_RESULT_KEY][\APIConstants::API_WEB_URL_FIELD_NAME]);

        //ensure we have related object, and they don't have webURL
        self::assertArrayNotHasKey(\APIConstants::API_WEB_URL_FIELD_NAME,$result[\APIConstants::IA_RESULT_KEY]['glbatch'] );
    }

    /**
     * ensure webURL generated for direct/resource calls like (/object/__testglentry/<key>)
     * and subresource calls (/object/__testglbatch/<key>/__testglentry/<entryKey> are same
     * @return void
     * @throws \APIException
     */
    public function testWebURL_subResource_resource_Same()
    {
        // data setup
        [$headerKey, $itemKeys ] = $this->createSingleHeaderWithItems();

        //sub resource style
        $subResourceURL = 'v5-test-beta2/objects/gl/__testglbatch/'.$headerKey . '/__testglentry/' . $itemKeys[0];
        $subResourceResult = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $subResourceURL, \APIConstants::API_HTTP_METHOD_GET);
        self::assertArrayHasKey(\APIConstants::API_WEB_URL_FIELD_NAME, $subResourceResult[\APIConstants::IA_RESULT_KEY]);
        self::assertNotEmpty($subResourceResult[\APIConstants::IA_RESULT_KEY][\APIConstants::API_WEB_URL_FIELD_NAME]);
        $webURLfromSubResourceCall = $subResourceResult[\APIConstants::IA_RESULT_KEY][\APIConstants::API_WEB_URL_FIELD_NAME];

        //resource or direct style
        $resourceURL = 'v5-test-beta2/objects/gl/__testglentry/' . $itemKeys[0];
        $resourceResult = \APITestHelper::executeAndValidateAPIDispatcher(1, 0, $resourceURL, \APIConstants::API_HTTP_METHOD_GET);
        self::assertArrayHasKey(\APIConstants::API_WEB_URL_FIELD_NAME, $resourceResult[\APIConstants::IA_RESULT_KEY]);
        self::assertNotEmpty($resourceResult[\APIConstants::IA_RESULT_KEY][\APIConstants::API_WEB_URL_FIELD_NAME]);
        $webURLfromResourceCall = $resourceResult[\APIConstants::IA_RESULT_KEY][\APIConstants::API_WEB_URL_FIELD_NAME];

        self::assertSame($webURLfromResourceCall, $webURLfromSubResourceCall);

    }

    /**
     * @return array
     * @throws \APIException
     */
    public function createSingleHeaderWithItems() : array
    {
        $request = $this->payloadSingleHeaderWithItems();
        $parameters = [
            'sessionId'     => 'sessionid',
            'senderId'      => 'senderid',
            'clientId'      => 'clientid',
            'url'           => 'v5-test-beta2/objects/gl/__testglbatch/',
            'httpMethod'    => 'POST',
            'body'          => $request,
        ];
        $dispatcher = \APIDispatcher::getDispatcher($parameters);
        $result = $dispatcher->dispatch();

        // sanity validate the POST result
        $this->assertNotNull($result);
        $this->assertTrue($result->getStatus() === 201);
        $response = $result->getResponseBody();
        $headerKey = $response[\APIConstants::IA_RESULT_KEY][\APIConstants::API_OBJECT_KEY_FIELD_NAME];
        $this->assertNotEmpty($headerKey);
        $this->assertNotNull($headerKey);

        // retrieve the item keys
        $itemKeys = $this->getItemKeys($headerKey);
        return [$headerKey, $itemKeys];
    }

    /**
     * @param  string $headerKey
     * @return array
     * @throws \APIException
     */
    public function getItemKeys(string $headerKey) : array
    {
        $parameters = [
            'sessionId'     => 'sessionid',
            'senderId'      => 'senderid',
            'clientId'      => 'clientid',
            'url'           => 'v5-test-beta2/objects/gl/__testglbatch/'.$headerKey.'/__testglentry',
            'httpMethod'    => 'GET',
        ];
        $dispatcher = \APIDispatcher::getDispatcher($parameters);
        $result = $dispatcher->dispatch();
        $this->assertNotNull($result);
        $this->assertTrue($result->getStatus() === 200);
        $response = $result->getResponseBody();
        $itemKeys = array_column($response[\APIConstants::IA_RESULT_KEY],\APIConstants::API_OBJECT_KEY_FIELD_NAME);
        $this->assertTrue(count($itemKeys) > 0);
        return $itemKeys;
    }

    /**
     * @param  string $headerKey
     * @param  string $itemKey
     * @return array
     * @throws \APIException
     */
    public function getItemByHeaderAndItemKeys(string $headerKey, string $itemKey) : array
    {
        $parameters = [
            'sessionId'     => 'sessionid',
            'senderId'      => 'senderid',
            'clientId'      => 'clientid',
            'url'           => 'v5-test-beta2/objects/gl/__testglbatch/'.$headerKey.'/__testglentry/' . $itemKey,
            'httpMethod'    => 'GET',
        ];
        $dispatcher = \APIDispatcher::getDispatcher($parameters);
        $result = $dispatcher->dispatch();
        $this->assertNotNull($result);
        $this->assertTrue($result->getStatus() === 200);
        $response = $result->getResponseBody()[\APIConstants::IA_RESULT_KEY];
        return $response;
    }

    /**
     * @param  string $headerKey
     * @param  string $itemKey
     *
     * @throws \APIException
     */
    public function getMissingItemByHeaderAndItemKeys(string $headerKey, string $itemKey)
    {
        $parameters = [
            'sessionId'     => 'sessionid',
            'senderId'      => 'senderid',
            'clientId'      => 'clientid',
            'url'           => 'v5-test-beta2/objects/gl/__testglbatch/'.$headerKey.'/__testglentry/' . $itemKey,
            'httpMethod'    => 'GET',
        ];
        $dispatcher = \APIDispatcher::getDispatcher($parameters);
        $result = $dispatcher->dispatch();
        $this->assertNotNull($result);
        $this->assertTrue($result->getStatus() === 404);
    }

    /**
     * @return string
     */
    public function payloadSingleHeaderWithItems() : string
    {
        $payload = json_encode(\APITestHelper::getGLBatchV05Input());
        $this->assertNotEmpty($payload);
        $this->assertNotFalse($payload);
        return $payload;
    }

    /**
     * @param  string $updateKey
     * @param  string $deleteKey
     * @return array
     */
    public function payloadMixOp_SingleParent(string $updateKey, string $deleteKey) : array
    {
        $timeStamp = time();
        $mixOpPayload = [
            [   // update an existing item
                'key'         => $updateKey,
                'description' => strval($timeStamp)
            ],
            [   // delete an existing item
                'key'           => $deleteKey,
                'ia::operation' => 'DELETE'
            ],
            // add a new item
            \APITestHelper::getGLEntryV05Input()
        ];
        $payload = json_encode($mixOpPayload);
        $this->assertNotEmpty($payload);
        $this->assertNotFalse($payload);
        return [$payload, $timeStamp];
    }

    /**
     * @param string $updateKey
     * @param string $deleteKey
     * @param string $op1
     * @param string $op2
     *
     * @return array
     */
    public function payloadMixOp_unknownOp(string $updateKey, string $deleteKey, string $op1, string $op2) : array
    {
        $timeStamp = time();
        $mixOpPayload = [
            "entries" => [
                [   // update an existing item
                    'key'         => $updateKey,
                    'description' => strval($timeStamp),
                    'ia::operation' => $op1
                ],
                [   // delete an existing item
                    'key'           => $deleteKey,
                    'ia::operation' => $op2
                ]
            ]
        ];
        $payload = json_encode($mixOpPayload);
        return [$payload, $timeStamp];
    }

    /**
     * for request URI in the format of:
     * '{version}/objects/{itemObject}'
     *
     * @param  string $updateKey
     * @param  string $deleteKey
     * @return array
     */
    public function payloadMixOp_ItemTop(string $updateKey, string $deleteKey) : array
    {
        $timeStamp = time();
        $mixOpPayload = [
            [   // update an existing item
                'key'         => $updateKey,
                'description' => strval($timeStamp)
            ],
            [   // delete an existing item
                'key'           => $deleteKey,
                'ia::operation' => 'DELETE'
            ]
        ];
        $payload = json_encode($mixOpPayload);
        $this->assertNotEmpty($payload);
        $this->assertNotFalse($payload);
        return [$payload, $timeStamp];
    }

    /**
     * creates a payload for mix-op with two item keys. </p>
     * this method creates two item entries, one with lowercase `delete` and one with uppercase `DELETE`
     *
     * @param string $itemKey1
     * @param string $itemKey2
     *
     * @return string
     */
    public function payloadMixOp_Delete(string $itemKey1, string $itemKey2)
    {
        $mixOpPayload = [ 'entries' => [
            [   // lower case delete operation
                'key'         => $itemKey1,
                'ia::operation' => 'delete',
            ],
            [   // upper case DELETE operation
                'key'           => $itemKey2,
                'ia::operation' => 'DELETE',
            ],
        ],
        ];
        $payload = json_encode($mixOpPayload);
        $this->assertNotEmpty($payload);
        $this->assertNotFalse($payload);
        return $payload;
    }

}
