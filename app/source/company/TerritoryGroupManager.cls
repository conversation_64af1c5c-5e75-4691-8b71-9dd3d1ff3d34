<?php
/**
 * Manager class for Territory Group
 * 
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

import('EntityManager');

/**
 * Manager class for Territory Group
 */
class TerritoryGroupManager extends EntityManager
{

    /**
     * __construct     
     * 
     * @param array $params entitymanager param
     */
    function __construct($params = array())
    {
        parent::__construct($params);
    }

    /**
     * Get     
     * 
     * @param string $ID     batch no
     * @param array  $fields fields to get
     * 
     * @return array
     */
    function Get($ID, $fields=null)
    {
        list($ID) = explode("--", $ID);
        $obj = parent::get($ID, $fields);
        return $obj;
    }

    /**
     * Add     
     * 
     * @param array &$values to be added to the table
     * 
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $ok = $this->TranslateValues($values);

        if ( $ok ) {
            $source = "TerritoryGroupManager::Add";
            $ok = $this->_QM->beginTrx($source);
            $ok = $ok && parent::regularAdd($values);

            $ok = $ok && $this->_QM->commitTrx($source);
            if ( ! $ok ) {
                $this->_QM->rollbackTrx($source);
            }
        }

        return $ok;
    }

    /**
     * Wrapper for php5.4 conversion.  Provides by-value wrapper for Set.  Can be removed, and SetByRef renamed to Set,
     * when it's verified that it's safe to call this class' Set by-reference.
     *
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        return $this->SetByValue($values);
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    function SetByValue($values)
    {
        return $this->SetByRef($values);
    }
    
    /**
     * SetByRef    
     *      
     * @param array &$values new values
     * 
     * @return bool
     */
    function SetByRef(&$values)    
    {
        $ok = $this->TranslateValues($values);

        if ( $ok ) {
            $source = "TerritoryGroupManager::Set";
            $ok = $this->_QM->beginTrx($source);
            $ok = $ok && parent::regularSet($values);
            $ok = $ok && $this->_QM->commitTrx($source);
            if ( ! $ok ) {
                $this->_QM->rollbackTrx($source);
            }
        }

        return $ok;
    }
    
    /**
     * Validate the record
     *
     * @param array &$values  the object values
     * 
     * @return bool true on success and false on failure
     */
    private function TranslateValues(&$values)
    {
        $ok = true;
        $gErr = Globals::$g->gErr;
        $gManagerFactory = Globals::$g->gManagerFactory;
        $terrMgr = $gManagerFactory->getManager('territory');
        $members = &$values['MEMBERS'];
        if (countArray($members) == 0) {
            $gErr->addError('BL01001973', __FILE__ . '.' . __LINE__, 'Members cannot be empty.');
            return false;
        }
        foreach ( $members as $key => &$terr ) {
            if(isset($terr['TERRITORYKEY'])){
                continue;
            }
            [$terrid] = explode('--', $terr['TERRITORYID']);
            $params = array(
                'selects' => array('RECORDNO'),
                'filters' => array(array(array('TERRITORYID', '=', $terrid))),
            );
            $recs = $terrMgr->GetList($params);
            if ( !isset($recs[0]['RECORDNO']) ) {
                $dict = Dictionary::getInstance();
                $objectName = $dict->GetRenamedText('Territory');
                $msg = sprintf(_('Invalid %1$s \'%2$s\' selected'), $objectName, $terrid);
                $corr = sprintf(_("Pick a valid %s."), $objectName);
                $gErr->addError('BL01001973', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                $ok = false;
            }else{
                $terr['TERRITORYKEY'] = $recs[0]['RECORDNO'];
            }

            $terr['SORTORD'] = $key;
        }
        return $ok;
    }


    /**
     * API_Get
     *
     * @param null|int|array $recordNos    Record numbers to be fetched
     * 
     * @param null|array     $returnFields Fields to be returned
     * 
     * @return array Set of records of the provided recordno's
     */
    
    public function API_Get($recordNos, $returnFields=null)
    {
        if (empty($recordNos)) {
            $recordNos = null;
        } else if (!is_array($recordNos)) {
            $recordNos = explode(',', $recordNos);
        }
        //  For each recordno, translate to the vid
        $vids = array();
        foreach ($recordNos ?? [] as $nextRecId) {
            $tempVID = $this->GetVidFromRecordNo($nextRecId);
            if ( $tempVID != '' ) {
                $vids[] = $tempVID;
            }
        }
        
        if ( empty($recordNos) ) {
            // default return 100 records
            $returnRecordSet = parent::API_Get(array(), $returnFields);
        } else if ( !empty($vids) ) {
            // available records with valid vid
            $returnRecordSet = parent::API_Get($vids, $returnFields);
        } else {
            // Invalid recordnumbers are passed and no vids available
            $returnRecordSet = false;
        }
        return $returnRecordSet;
    }
}
