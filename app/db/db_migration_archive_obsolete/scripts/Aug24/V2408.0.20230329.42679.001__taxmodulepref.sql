--changeset object:1 runOnChange:true logicalFilePath:V2408.0.20230329.42679.001__taxmodulepref.sql

/*

== DELTA ONLY FOR DEV SCHEMAS ==

Description: Adding column SI_UUID to the TAXSETUP table

Repair action: EXECUTE

*/

CREATE TABLE TAXSETUP
(
    CNY#                        NUMBER(15,0),
    RECORD#                     NUMBER(15,0),
    MODULEKEY                   VARCHAR2(10 CHAR) DEFAULT '64.TAX' CONSTRAINT NN_TAXSETUP_MODULEKEY NOT NULL ENABLE,
    MODU<PERSON>_CONFIGURED			CHAR(1 CHAR) DEFAULT 'T',
    <PERSON>ING<PERSON><PERSON><PERSON><PERSON>ITAX            CHAR(1 CHAR),
    SING<PERSON>TAXSOLUTION           VARCHAR2(30 CHAR),
	DNCHECKTAXID                CHAR(1 CHAR) DEFAULT 'F',
	DNFLTRTAXDETAIL             CHAR(1 CHAR) DEFAULT 'F',
	ISMIGRATED                  CHAR(1 CHAR) DEFAULT 'F',
	WHENMIGRATED                DATE,
	<PERSON>ETA<PERSON><PERSON><PERSON><PERSON>                 VARCHAR2(30 CHAR),
	<PERSON>OTA<PERSON>ENGINE                 VARCHAR2(30 CHAR),
	ARTAXENGINE                 VARCHAR2(30 CHAR),
	MIGRATEDTAXSOLUTION         VARCHAR2(30 CHAR),
	CREATEDBY                   NUMBER(15,0)	 DEFAULT SYS_CONTEXT('TMCtx', 'USERKEY'),
	MODIFIEDBY                  NUMBER(15,0)	 DEFAULT SYS_CONTEXT('TMCtx', 'USERKEY'),
    WHENCREATED                 DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
    WHENMODIFIED                DATE DEFAULT CURRENT_TIMESTAMP at TIME zone 'GMT',
    SI_UUID                     VARCHAR2(36 CHAR),
	CONSTRAINT PK_TAXSETUP PRIMARY KEY (CNY#, RECORD#) USING INDEX TABLESPACE ACCTINDX,
    CONSTRAINT FK_TAXSETUP_CNY FOREIGN KEY (CNY#) REFERENCES COMPANY (RECORD#) ON  DELETE CASCADE DEFERRABLE ENABLE,
	CONSTRAINT FK_TAXSETUP_CREATEDBY FOREIGN KEY (CNY#, CREATEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE,
    CONSTRAINT FK_TAXSETUP_MODIFIEDBY FOREIGN KEY (CNY#, MODIFIEDBY) REFERENCES USERINFO (CNY#, RECORD#) DEFERRABLE ENABLE
)
TABLESPACE ACCTDATA
/

CREATE INDEX IX_TAXSETUP_CREATEDBY ON TAXSETUP (CNY#, CREATEDBY) TABLESPACE ACCTINDX INVISIBLE
/

CREATE INDEX IX_TAXSETUP_MODIFIEDBY ON TAXSETUP (CNY#, MODIFIEDBY) TABLESPACE ACCTINDX INVISIBLE
/

-- Creating backup table TEMP_MODULEPREF_TAX
CREATE TABLE TEMP_MODULEPREF_TAX TABLESPACE ACCTDATA
AS SELECT * FROM MODULEPREF WHERE MODULEKEY = '64.TAX'
/

-- There is a trigger on taxsetup to capture billing event, if we don't disable it, there will be an entry
-- in Salesforce saying all the companies are newly subscribed
-- hence disabling trigger
BEGIN
  DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
END;
/

-- Migrating current MODULEPREF data to TAXSETUP table for 64.TAX MODULEKEY
DECLARE
CURSOR COMPANY_CUR IS
SELECT C.RECORD#
FROM COMPANY C
WHERE
    EXISTS ( SELECT 1 FROM MODULEPREF M WHERE M.CNY# = C.RECORD# AND M.MODULEKEY = '64.TAX' ) AND
    NOT EXISTS ( SELECT 1 FROM TAXSETUP T WHERE T.CNY# = C.RECORD# );
BEGIN
FOR CNY IN COMPANY_CUR LOOP
BEGIN
INSERT INTO TAXSETUP
(
    CNY#, RECORD#, MODULEKEY, MODULE_CONFIGURED, SINGLEORMULTITAX, SINGLETAXSOLUTION, DNCHECKTAXID, DNFLTRTAXDETAIL,
    ISMIGRATED, WHENMIGRATED, OETAXENGINE, POTAXENGINE, ARTAXENGINE, MIGRATEDTAXSOLUTION, CREATEDBY,
    MODIFIEDBY, WHENCREATED, WHENMODIFIED
)
with TAXMODULEPREF AS
         (
             SELECT CNY#, PROPERTY, VALUE FROM MODULEPREF WHERE MODULEKEY = '64.TAX' and cny# = CNY.RECORD# AND LOCATIONKEY IS NULL
         )
       SELECT CNY.RECORD#, GET_NEXTRECORDID(CNY.RECORD#, 'TAXSETUP') AS RECORD#, '64.TAX',
       DECODE((SELECT m.VALUE FROM TAXMODULEPREF m WHERE m.PROPERTY = 'MODULE_CONFIGURED'), 'T', 'T', 'F') AS MODULE_CONFIGURED,
       DECODE((SELECT m.VALUE FROM TAXMODULEPREF m WHERE m.PROPERTY = 'SINGLEORMULTITAX'), 'Multiple', 'M', 'S') AS SINGLEORMULTITAX,
       (SELECT m.VALUE from TAXMODULEPREF m WHERE m.PROPERTY = 'SINGLETAXSOLUTION') AS SINGLETAXSOLUTION,
       DECODE((SELECT m.VALUE FROM TAXMODULEPREF m WHERE m.PROPERTY = 'DNCHECKTAXID'), 'T', 'T', 'F') AS DNCHECKTAXID,
       DECODE((SELECT m.VALUE from TAXMODULEPREF m WHERE m.PROPERTY = 'DNFLTRTAXDETAIL'), 'T', 'T', 'F') as DNFLTRTAXDETAIL,
       DECODE((SELECT m.VALUE from TAXMODULEPREF m WHERE m.PROPERTY = 'ISMIGRATED'), 'T', 'T', 'F') AS ISMIGRATED,
       to_date((SELECT m.VALUE from TAXMODULEPREF m WHERE m.PROPERTY = 'WHENMIGRATED'),'MM/DD/YYYY') AS WHENMIGRATED,
       (SELECT m.VALUE from TAXMODULEPREF m WHERE m.PROPERTY = 'OE_TAXENGINE') AS OETAXENGINE,
       (SELECT m.VALUE from TAXMODULEPREF m WHERE m.PROPERTY = 'PO_TAXENGINE') AS POTAXENGINE,
       (SELECT m.VALUE from TAXMODULEPREF m WHERE m.PROPERTY = 'AR_TAXENGINE') AS ARTAXENGINE,
       (SELECT m.VALUE from TAXMODULEPREF m WHERE m.PROPERTY = 'MIGRATED_TAXSOLUTION') AS MIGRATEDTAXSOLUTION,
       NULL AS CREATEDBY, NULL AS MODIFIEDBY, NULL AS WHENCREATED, NULL AS WHENMODIFIED
FROM DUAL;
COMMIT;

EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            raise_application_error(-20001,'CNY#: ' || CNY.RECORD# || ' AN ERROR WAS ENCOUNTERED - '||SQLCODE||' -ERROR- '||SQLERRM);
END;
END LOOP;
END;
/

-- enable trigger
BEGIN
  DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/

--DELETE FROM MODULEPREF WHERE MODULEKEY = '64.TAX';
