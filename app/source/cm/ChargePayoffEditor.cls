<?php

/**
 * Editor class for the Charge Payoff object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Intacct Corporation All, Rights Reserved
 */

/**
 * Editor class for the Charge Payoff object
 */
class ChargePayoffEditor extends SubLedgerTxnEditor
{

    /**
     * @var string $viewApBill
     */
    private $viewApBill = 'ap/lists/apbill/view';
    /**
     * @param array $params
     */
    public function __construct($params = array())
    {
        parent::__construct($params);
    }

    /**
     * Return an array of javascript files to include into the page
     *
     * @return array the list of javascript files to include
     */
    protected function getJavaScriptFileNames()
    {
        $jsFiles = parent::getJavaScriptFileNames();
        $jsFiles[] = '../resources/js/chargepayoff.js';
        return $jsFiles;
    }

    /**
     * Map the ajax action to a PHP function
     *
     * @param string $cmd the Ajax action requested
     *
     * @return bool|null
     */
    protected function runAjax($cmd)
    {
        if ( parent::runAjax($cmd) ) {
            return null;
        }

        switch ($cmd) {
            case 'getCharges':
                $this->ajaxGetCharges();
                return null;
        }

        return true;
    }

    /**
     * Helper function to get data from entity manager and format the data for display
     *
     * @param string        $entity   entity or object
     * @param string        $objId    entityid or objectid
     * @param string        $doctype
     * @param string[]|null $fields
     *
     * @return array the formatted result set
     */
    protected function getEntityData($entity, $objId, $doctype='', $fields=null)
    {
        // Get the data
        $entityData = parent::getEntityData($entity, $objId, $doctype, $fields);

        // Get the payments
        if ( !empty($entityData['RECORDNO']) ) {

            /* @var ChargePayoffManager $mgr */
            $mgr=$this->getEntityMgr();
            $entityData['ITEMS'] = $mgr->getChargesPayments($entityData['RECORDNO']);

            // Build the drill down on the date
            foreach ( $entityData['ITEMS'] as &$charge ) {
                $this->buildDateDrillDown($charge);
            }
            unset($charge); // Unset the reference
        }

        return $entityData;
    }

    /**
     * Get the path of the entry memo field
     *
     * @return string the memo path
     */
    protected function getDefaultEntryMemoField()
    {
        return null;
    }

    /**
     * This is a hook functions for subclases to add the dynamic metadata into the current layout.
     * At the time this function is called, the data, state and view objects are not available.
     * The subclass must operate on the given params structure.
     *
     * @param array $params the metadata
     */
    protected function buildDynamicMetadata(&$params)
    {
        // Get the current action value
        $action = Request::$r->{$this->kAction};
        if ( !$action ) {
            $action = $this->kDefaultVerbActions[Request::$r->_do];
        }

        if ( $action == 'view' ) {
            // Remove the filter section
            $matches = array();
            self::findElements($params, array('id' => 'CCTxnFilters'), EditorComponentFactory::TYPE_SUBSECTION, $matches);
            $matches[0]['hidden'] = true;

            $matches = array();
            self::findElements($params, array('path' =>  'SELECTED_TOTAL_AMOUNT'), EditorComponentFactory::TYPE_FIELD, $matches);
            $matches[0]['hidden'] = true;

            $matches = array();
            self::findElements($params, array('path' =>  'AMOUNT_DUE'), EditorComponentFactory::TYPE_FIELD, $matches);
            $matches[0]['hidden'] = true;
        }

        if ( $action == 'new' ) {
            // In create mode if the AP is setup to have a user specific batch to post in we got to
            // show the list of summary in which to create the bills
            /* @var ChargePayoffManager $mgr */
            $mgr=$this->getEntityMgr();

            if ( $mgr->getHasUserSpecifiedBatch() ) {
                $matches = array();
                self::findElements($params, array('path' => 'APBATCH'), EditorComponentFactory::TYPE_FIELD, $matches);
                $matches[0]['hidden'] = false;
                $matches[0]['required'] = true;
            }
        }

        // Get the grid and set the entity based if we are in view or add mode
        $grid = array();
        self::findElements($params, array('path' => 'ITEMS'), EditorComponentFactory::TYPE_GRID, $grid);
        $grid[0]['entity'] = ( $action == 'view' ) ? 'payoffpayment' : 'payoffcharge';
        // $grid[0]['hidden'] = ( $action == 'view' ) ? false : true;

        // Remove unecessary columns
        if ( $action == 'view' ) {
            $toHide = array(
                'AMOUNTTOPAY'
            );
            //Hide select column
            $grid[0]['enableSelect'] = false;
            unset($grid[0]['selectColumn']);
        } else {
            $toHide = array(
                'AMOUNTPAID',
                'TRX_AMOUNTPAID'
            );
        }

        foreach ( $toHide as $fieldPath ) {
            $matches = array();
            self::findElements($grid, array('path' => $fieldPath), EditorComponentFactory::TYPE_FIELD, $matches);
            $matches[0]['hidden'] = true;
        }

        // Show all MCP fields if MCP is enabled
        if ( $this->getIsMCPEnabled() ) {
            $mcpFields = array(
                'CURRENCY', // This is also in the filter section so we findElement out of $params not $grid
                'EXCHANGE_RATE',
                'EXCH_RATE_DATE',
                'EXCH_RATE_TYPE',
                'TOTALENTERED',
                'TOTALDUE',
                'AMOUNTPAID',
                'TOTALENTEREDHEADER'
            );
            foreach ( $mcpFields as $fieldPath ) {
                if ( in_array($fieldPath, $toHide) ) {
                    continue;
                }
                $matches = array();
                self::findElements($params, array('path' => $fieldPath), EditorComponentFactory::TYPE_FIELD, $matches);
                foreach ( $matches as &$match ) {
                    $match['hidden'] = false;
                }
                unset($match);
            }
        } else {
            // For non-MCP we will change the fullname from Trx Total to Total.
            $rename = array(
                'TRX_TOTALENTERED' => 'IA.TOTAL_ENTERED',
                'TRX_TOTALDUE' => 'IA.TOTAL_DUE',
                'TRX_AMOUNTPAID' => 'IA.TOTAL_PAID'
            );
            foreach ( $rename as $fieldPath => $fullname) {
                $matches = array();
                self::findElements($grid, array('path' => $fieldPath), EditorComponentFactory::TYPE_FIELD, $matches);
                $matches[0]['fullname'] = $fullname;
            }
        }
    }


    /**
     * This is a hook functions for subclases to adjust the metadata according to the current data & state
     *
     * @param array $obj the data
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        $view = $this->getView();

        // Default the date
        if ( empty($obj['WHENCREATED']) ) {
            $obj['WHENCREATED'] = GetCurrentDate();
        }


        // If my array of charges if not empty but I am in create mode it means we are coming back from
        // the error screen. In that case we can show the grid with the unpaid charges right away.
        if ( $this->kShowNewState && !empty($obj['ITEMS']) ) {
            $view->findAndSetProperty(
                array('path' => 'ITEMS'), array('hidden' => false), EditorComponentFactory::TYPE_GRID
            );
            $view->findAndSetProperty(
                array('id' => 'noRowSection'), array('hidden' => true), EditorComponentFactory::TYPE_SECTION
            );
        }

        // Build the reversal / document links
        $this->buildReversalLinks($obj);

        $action = Request::$r->{$this->kAction};
        if ( !$action ) {
            $action = $this->kDefaultVerbActions[Request::$r->_do];
        }
        if ( $action == 'view' ) {
            // build bill link
            $this->buildApBillLink($obj);
        }
        // Build the transaction header
        $this->prepareTxnHeader($obj);

        $this->showGLPostingDetailsTab($obj);

        return true;
    }

    /**
     * Get the charges from the posted parameters
     */
    private function ajaxGetCharges()
    {
        /* @var ChargePayoffManager $mgr */
        $mgr = $this->getEntityMgr();

        $filters = $this->getCCTransactionQueryFilters();
        $params = [
            'filters' => [ $filters ]
        ];
        $charges = $mgr->getChargesToPayOff($params);
        $amountDue = 0;
        foreach ( $charges as &$charge ) {
            //client side sorting of date wouldn't work unless date is formatted in YMD format
            $charge['WHENCREATED_COPY'] = ReformatDate($charge['WHENCREATED'], IADATE_STDFORMAT, '/Ymd');
            // Build the drill down on the date
            $this->buildDateDrillDown($charge);
            $amountDue += $charge['TRX_TOTALDUE'];
        }
        unset($charge); // Unset the reference

        // $totalTxns =  count($charges);
        // Reset the readtime in case the transaction was modified since we will be up to date
        $result = array(
            'readtime' => ServeCurrentTimestamp(1),
            'items' => $charges,
            'financialEntity' => Request::$r->{'financialEntity'},
            'amountDue'=> $amountDue
        );
        echo json_encode($result);
    }

    /**
     * No edit of charge payoff
     *
     * @return bool false
     */
    protected function canEdit()
    {
        return false;
    }

    /**
     * No duplicate of charge payoff
     *
     * @return bool false
     */
    protected function canDuplicate()
    {
        return false;
    }

    /**
     * No save and new button needed for this screen
     *
     * @param array   $buttons          the button list
     * @param string  $id                the button id
     * @param string  $name              the button name
     * @param string  $button            the button label
     * @param string  $action            the button action
     * @param bool $submitData        true if submit the data else false
     * @param string  $jsCode            javascript code to attach to it
     * @param bool $serverAction      true if server action else false
     * @param bool $disableValidation true to disable the JS validation else false
     * @param string $args              extra arguments to pass
     *
     */
    protected function createMoreActionEntry(
        &$buttons, $id, $name, $button, $action, $submitData = true, $jsCode = '',
        $serverAction = true, $disableValidation = false, $args = ''
    ) {
        if ( $id != Editor_SaveAndNewBtnID ) {
            if ($id !== 'audittrailbuttid') {
                $submitData = true;
                $jsCode = '';
                $serverAction = true;
                $disableValidation = false;
                $args = '';
            }
            parent::createMoreActionEntry(
                $buttons, $id, $name, $button, $action, $submitData, $jsCode,
                $serverAction, $disableValidation, $args
            );
        }
    }



    /**
     * Build link to view bill
     * @param array $obj the data
     */
    private function buildApBillLink(&$obj)
    {
        $view = $this->getView();
        $matches = [];
        $view->findComponents([ 'path' => 'BILL_RECORDID' ], EditorComponentFactory::TYPE_FIELD, $matches);
        if ( $matches ) {
            // Show the link
            $matches[0]->setProperty('hidden', false);

            $viewop  = GetOperationId($this->viewApBill);
            $recordhash = urlencode($obj['BILL_RECORDNO']);
            $recordtype = urlencode('pi');

            // Set the url
            $events = $matches[0]->getProperty('events');

            $_sess = Session::getKey();
            $url = 'editor.phtml?.do=view&.recordtype='.$recordtype.'&.r='.$recordhash.'&.op='.$viewop.'&.popup=1';
            $url .= '&.sess=' . $_sess;
            $url = FwdUrl($url);

            $events['click'] .= 'Launch("' . $url . '", "scmwindow", 1200, 800);';
            $matches[0]->setProperty('events', $events);
        }
    }

    /**
     * Get the list of buttons for the screen
     *
     * @param string $state current state
     *
     * @return array buttons
     */
    public function getStandardButtons($state)
    {
        $buttons = [];
        // Get the current action value
        $action = Request::$r->{$this->kAction};
        if ( ! $action ) {
            $action = $this->kDefaultVerbActions[Request::$r->_do];
        }

        if ( $action == 'view' ) {
            $buttons = parent::getStandardButtons($state);
        }
        else {
            $buttonsProperty = [
                "IA.POST"      => [
                    'action'    => $this->kCreateAction,
                    'isSaveNew' => false,
                    'isDefault' => false,
                    'id'        => Editor_SaveBtnID,
                ],
                "IA.POST_AND_NEW" => [
                    'action'    => $this->kCreateAction,
                    'isSaveNew' => true,
                    'isDefault' => true,
                    'id'        => Editor_SaveAndNewBtnID,
                ],
            ];
            $this->createSplitButtonEntry($buttons, $buttonsProperty);
            $this->setButtonDetails($buttons, Editor_CancelBtnID, 'cancelbutton', "IA.CANCEL", 'cancel', false);
        }

        return $buttons;
    }

    /**
     * @return array
     */
    private function getCCTransactionQueryFilters()
    {
        $filters[]  = [ 'ACCOUNTKEY', '=', Request::$r->accountKey];
        // Add the currency filter if applicable
        $currency = Request::$r->currency ;
        if ( $this->getIsMCPEnabled() && ! empty($currency) ) {
            $filters[]  = [ 'CURRENCY', '=', $currency];
        }

        $columnWhenCreated = 'WHENCREATED';
        $fromDate = Request::$r->{'fromTxnDate'};
        $toDate = Request::$r->{'toTxnDate'};
        if ( ! empty($fromDate) && ! empty($toDate) ) {
            $filters[]  = [ $columnWhenCreated, 'BETWEEN', [ $fromDate, $toDate ] ];
        } else if ( ! empty($fromDate) ) {
            $filters[]  = [ $columnWhenCreated, '>=', $fromDate ];
        } else if ( ! empty($toDate) ) {
            $filters[]  = [ $columnWhenCreated, '<=', $toDate ];
        }

        return $filters;
    }

    /**
     * Return the confirmation message for create action.
     *
     * @param string $entityDesc
     * @param array $obj
     *
     * @return string the confirmation message
     */
    protected function getConfirmationMsg($entityDesc, $obj)
    {
        if ( $obj['RECORDNO'] ) {
            $entityDesc = "charge payoff ";
        }
        $ret = parent::getConfirmationMsg($entityDesc, $obj);

        return $ret;
    }

    /**
     * @return bool
     */
    protected function includeGLPostingTab()
    {
        $isCashOnly = IsCompanyCashAndNotMultibooks();
        //hide GL postings tab if company book is CASH only
        if (!$isCashOnly)
        {
            return true;
        }
        return false;
    }

    /**
     * Figure out if the transaction has a UI header or not
     *
     * @return bool true if the transaction has a UI header else false
     */
    protected function hasTxnHeader()
    {
        return true;
    }

    /**
     * Return the list of globals to have for the editor
     *
     * @return array the list of globals
     */
    protected function getEditorGlobals()
    {
        $vars = parent::getEditorGlobals();
        if ( $vars == null ) {
            $vars = [];
        }
        $vars['view_bill'] = GetOperationId($this->viewApBill);
        return $vars;
    }


    /**
     * to show GL posting tab
     *
     * @param array $obj the transaction data
     *
     */
    protected function showGLPostingDetailsTab(&$obj)
    {
        parent::showGLPostingDetailsTab($obj);

        // Show posting details tab on view mode
        if ( $this->state == $this->kShowViewState  || $this->state == $this->kShowEditState ) {
            // Get the view
            $view = $this->getView();
            // Get the grid for AP bills section
            $billGrid = [];
            $view->findComponents([ 'path' => 'AP_BILL_LIST' ], EditorComponentFactory::TYPE_GRID, $billGrid);
            $billGrid = $billGrid[0];
            if ( ! isNullOrBlank($billGrid) ) {
                $billGrid->setProperty('hidden', false);
                $billGrid->setProperty('readonly', true);
                //populate bill details
                /* @var ChargePayoffManager $mgr */
                $mgr = $this->getEntityMgr();
                $mgr->getBillInfo($obj);
            }
        }
    }

    /**
     * Build the drill down URL to the original transaction
     *
     * @param array $transaction the transaction data
     */
    protected function buildDrillDownUrl($transaction)
    {
        $url = parent::buildDrillDownUrl($transaction);
        if (!empty($url)) {
            $url .= '&.refreshGrid=true';
        }
        return $url;
    }

    /**
     * In charge payoff screen we don't want to show dimension defaults
     *
     * @return bool
     */
    protected function isShowGridDefaults()
    {
        return false;
    }
}
