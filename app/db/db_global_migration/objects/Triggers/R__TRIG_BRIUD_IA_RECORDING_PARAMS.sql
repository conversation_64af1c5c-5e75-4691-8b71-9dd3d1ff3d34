CREATE OR <PERSON><PERSON><PERSON>CE EDITIONABLE TRIGGER briud_ia_recording_params BEFORE INSERT OR UPDATE OR DELETE ON ia_recording_params FOR EACH ROW
DECLARE
  ENABLED CHAR(1);
  rec ia_recording_params.record#%TYPE;
BEGIN
  dbms_application_info.read_client_info (ENABLED);
  IF ENABLED <> 'F' THEN
    CASE
      WHEN inserting THEN
        :NEW.whencreated := CURRENT_TIMESTAMP AT TIME ZONE 'GMT';
        :NEW.whenmodified := CURRENT_TIMESTAMP AT TIME ZONE 'GMT';
        if :NEW.record# IS NULL THEN
          SELECT get_nextrecordid('', 'ia_recording_params') INTO :NEW.record# FROM dual;
        END if;
      WHEN updating THEN
        :NEW.whenmodified := CURRENT_TIMESTAMP AT TIME ZONE 'GMT';
      WHEN deleting THEN
        INSERT INTO ia_recording_params_log (whencreated, record#)
        VALUES (CURRENT_TIMES<PERSON>MP AT TIME ZONE 'GMT', :OLD.record#);
    <PERSON>ND CASE;
  END IF;
END briud_ia_recording_params;
/
