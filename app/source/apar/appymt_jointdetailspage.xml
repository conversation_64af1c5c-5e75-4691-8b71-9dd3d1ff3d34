<floatingPage id="jointLineDetailPage" compact="true" modal="true" fullscreen="true" topbuttons="true">
    <title>IA.PAYEE_LINE_DETAILS</title>
    <events>
        <close>onJointLineControlClick(false, true);</close>
    </events>
    <pages>
        <page>
            <flexstack>
                <flexsection>
                    <flexstack>
                        <section id="billLineControlSection" className="horizontal headerCustom" customFields="no">
                            <field hidden="true">
                                <path>BILLRECORDNO</path>
                            </field>
                            <field hidden="true">
                                <path>RECORDTYPE</path>
                            </field>
                            <field fullname="IA.BILL_KEY" readonly="true" isToken="true">
                                <path>BILLRECORDID</path>
                                <type type='text' ptype='href'></type>
                                <events>
                                    <click>openDrilldownPage(this.meta);</click>
                                </events>
                                <default>IA.VIEW_DETAILS</default>
                            </field>
                            <field fullname="IA.LINE_NO" readonly="true">
                                <path>LINENO</path>
                            </field>
                            <field fullname="IA.VENDOR" readonly="true">
                                <path>BILLVENDORNAME</path>
                            </field>
                            <field fullname="IA.VENDOR_ID" hidden="true">
                                <path>BILLVENDORID</path>
                            </field>
                            <field fullname="IA.BILL_DATE" readonly="true">
                                <path>BILLWHENCREATED</path>
                                <type assoc="T">
                                    <type>date</type>
                                    <ptype>date</ptype>
                                </type>
                            </field>
                            <field fullname="IA.DUE_DATE" readonly="true">
                                <path>BILLWHENDUE</path>
                                <type assoc="T">
                                    <type>date</type>
                                    <ptype>date</ptype>
                                </type>
                            </field>
                            <field fullname="IA.AMOUNT_DUE" readonly="true">
                                <path>BILLJOINTDETAIL_TRX_TOTALDUE</path>
                                <type assoc="T">
                                    <ptype>decimal</ptype>
                                    <type>currency</type>
                                </type>
                                <default>0</default>
                            </field>
                        </section>
                        <grid path="JOINTLINEITEMS" noDragDrop="true" className="columns3Grid" hasFixedNumOfRows="true"
                              noNewRows="true" deleteOnGrid="false" showDelete="false" customFields="no">
                            <column>
                                <field readonly="true" required="false">
                                    <path>VENDORID</path>
                                    <fullname>IA.VENDOR</fullname>
                                    <fieldentity>vendor</fieldentity>
                                    <type assoc="T">
                                        <ptype>text</ptype>
                                        <type>text</type>
                                    </type>
                                </field>
                            </column>
                            <column>
                                <field readonly="true">
                                    <path>JOINTPAYEENAME</path>
                                    <fullname>IA.JOINT_PAYEE</fullname>
                                    <type assoc="T">
                                        <ptype>text</ptype>
                                        <type>text</type>
                                    </type>
                                </field>
                            </column>
                            <column>
                                <field readonly="true">
                                    <path>JOINTPAYEEPRINTAS</path>
                                    <fullname>IA.JOINT_PAYEE_PRINT_AS</fullname>
                                    <type assoc="T">
                                        <ptype>text</ptype>
                                        <type>text</type>
                                    </type>
                                </field>
                            </column>
                            <column>
                                <field readonly="true" required="false">
                                    <path>CURRENCY</path>
                                    <fullname>IA.CURRENCY</fullname>
                                    <fieldentity>payableitem</fieldentity>
                                    <type assoc="T">
                                        <ptype>text</ptype>
                                        <type>text</type>
                                    </type>
                                </field>
                            </column>
                            <column>
                                <field readonly="true" required="false">
                                    <path>CREDITSAVAILABLE</path>
                                    <fullname>IA.CREDITS_AVAILABLE</fullname>
                                    <fieldentity>payable</fieldentity>
                                    <type assoc="T">
                                        <ptype>decimal</ptype>
                                        <type>currency</type>
                                    </type>
                                    <default>0</default>
                                </field>
                            </column>
                            <column>
                                <field formatZero="true" hasTotal="true">
                                    <path>CREDITSAPPLIED</path>
                                    <fullname>IA.CREDITS_TO_APPLY</fullname>
                                    <fieldentity>payableitem</fieldentity>
                                    <type assoc="T">
                                        <ptype>decimal</ptype>
                                        <type>currency</type>
                                    </type>
                                    <default>0</default>
                                    <events>
                                        <change>applyBillCredits(this.meta, true, true);</change>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <field>
                                    <path>CREDITDETAILS</path>
                                    <type type='text' ptype='href'></type>
                                    <hreftxt>IA.CREDIT_DETAILS</hreftxt>
                                    <events>
                                        <click>populateCreditLineDetails(this);</click>
                                    </events>
                                </field>
                            </column>
                            <column>
                                <field hasTotal="true" className="qx-grid-total">
                                    <path>PAYMENTAMOUNT</path>
                                    <fullname>IA.AMOUNT_TO_PAY</fullname>
                                    <fieldentity>payableitem</fieldentity>
                                    <type assoc="T">
                                        <ptype>decimal</ptype>
                                        <type>currency</type>
                                    </type>
                                    <default>0</default>
                                    <events>
                                        <change>populateBillPayment(this.meta, true, true);</change>
                                    </events>
                                </field>
                            </column>
                        </grid>
                    </flexstack>
                </flexsection>
            </flexstack>
        </page>
    </pages>
    <footer>
        <button className="right" id="blSaveButton">
            <name>IA.SAVE</name>
            <events>
                <click>onJointLineControlClick(true, true);</click>
            </events>
        </button>
        <button className="right" id="blCancelButton">
            <name>IA.CANCEL</name>
            <events>
                <click>onJointLineControlClick(false, true);</click>
            </events>
        </button>
    </footer>
</floatingPage>