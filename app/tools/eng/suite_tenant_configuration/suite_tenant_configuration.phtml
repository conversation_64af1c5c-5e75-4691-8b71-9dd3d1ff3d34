<?php

try {
    //extract($_REQUEST, EXTR_PREFIX_ALL, "r");
    // need this, because inspections don't like extract()
    $r_page = Request::$r->page;

    // check requested page
    $pages = array("index");
    if (in_array($r_page, $pages, true)) {
        $page = $r_page;
    } else if ($r_page === null) {
        // default page
        $page = "index";
    } else {
        // invalid page; 404
        include "cs_not_found.inc";

        exit;
    }

    require_once "cs_common.inc";

    // unnecessary, but to keep inspections happy...
    global $g_login_id, $g_oMemCache;
    
    // messages passed from one page to other
    $message = $g_oMemCache->get("cs_message_" . $g_login_id);
    if ($message !== false) {
        $t["message"] = array("type" => $message["type"], "text" => $message["text"]);
        
        $g_oMemCache->delete("cs_message_" . $g_login_id);
    } else {
        $t["message"] = null;
    }
    
    $t["tool_scripts"] = array("suite_tenant_configuration.js");

    // load page script
    ob_start();
    require "suite_tenant_configuration_" . $page . ".inc";

} catch (Exception $e) {
    ob_clean();

    $t_exception = $e;
    include_once "cs_exception.inc";
}

header("Content-Type: text/html; charset=utf-8");
ob_end_flush();
