<?php
//=============================================================================
//
//	FILE:			RenewalMacroUpdateForSOTrans.phtml
//	AUTHOR:			Nirmal Shukla
//	DESCRIPTION:	
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

require_once 'perm_util.inc';
require_once 'common_cs.js';
Head('#FFFFFF', '#000000', '#006699', '#990000', '#000000');
$_gotopage = 'RenewalMacroUpdateForSOTrans.phtml';
PrintCommonComponents("", $_gotopage);

$_cny       = Request::$r->_cny;
$_cnyTitle  = Request::$r->_cnyTitle;
$_show      = Request::$r->_show;
$_impfile   = $_FILES['_impfile']['tmp_name'];
$_redirect  = Request::$r->_redirect;
$_emailaddress = Request::$r->_emailaddress;



if ( $_redirect == '1') {
    if ($_cny != '') {
        $podInfo = $gPODManager->getPODInfoForCny($_cny, 'cny#');
        $destCny = ".cny=$_cny";
    } elseif ($_cnyTitle != '') {
        $podInfo = $gPODManager->getPODInfoForCny($_cnyTitle);
        $destCny = ".cnyTitle=$_cnyTitle";
    } else {
        $podInfo = false;
    }
    if ($gPODManager->arePODGroupsEnabled()) {
        if ($podInfo != false) {
            $destPODId = $podInfo[PODInfo::POD_ID];
            if ($destPODId != $gPODManager->getCurrentPOD()->getId()) {
                $url = "RenewalMacroUpdateForSOTrans.phtml?$destCny";
                $gPODManager->redirectCSTools($url, $destPODId);
            }
        } else {
            $companyFound = false;
        }
    }
}

?>
<html>
<body >
<script type="text/javascript" language="javascript" src="RenewalMacroUpdateForSOTrans.js"></script>
<form name="form1" method="POST" ENCTYPE="multipart/form-data" action="RenewalMacroUpdateForSOTrans.phtml">
    <input type="hidden" name=".redirect" value="0"/>
<br>
<table bordercolor="#6633CC" border=1 cellspacing=1 align="center">
    <tr>
    <td bgcolor="#6633CC" align="center">
    <font color="#FFFFFF"><h3>Update Historical Sales Order Transction <br>with Renewal Template</h3></font>
    </td>
    </tr>
    <tr>
    <td>
    <table>
        <tr>
            <td colspan="2">
                <table border=0 cellspacing=1 align="center" width="100%">
                    <tr>
                        <td><font color="#FF0000">*Cny#</font></td>
                        <td><input type="text" name=".cny" size="10" value="<?=$_cny?>"></td>
                    </tr>
                    <tr>
                        <td ></td>
                        <td >OR</td>
                    </tr>
                    <tr>
                        <td><font color="#FF0000">*Cny Title&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</font></td>
                        <td><input type="text" name=".cnyTitle" size="50" value="<?=$_cnyTitle?>">
                        </td>
                    </tr>
                    <tr>  </tr>
                    <tr>  </tr>
                    <tr>  </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td><font color="#FF0000">*CSV file to upload</font></td>
            <td><input type="file" name=".impfile" size="50"></td>
        </tr>
        <tr>
            <td><font color="#FF0000">*Email Id</font></td>
            <td>
                <input type="text" name=".emailaddress" size="50" value="<?=$_emailaddress?>">
            </td>
        </tr>
        <tr>
            <td colspan="2" align="center">
                <input type="submit" name=".show" value="Update">
            </td>
        </tr>
        <tr>
            <td></td>
            <td></td>
        </tr>
    </table>
    </td>
    </tr>
    </table>
<?php
$issubmitted = ($_show != '');
// In case of Form Submission, Check all required values
if ($issubmitted) {
    if ( ($_cny != '' || $_cnyTitle != '') && $_impfile != '' && $_emailaddress != '') {
        $comp = $_cny;
        if ($comp == '') {
            //Assuming 1 can never be a Cny#
            //Only, In case user doesn't provide Cny#
            $comp = 1;
        }
        //Setting Dummy environment
        Globals::$g->_userid = "1@$comp";
        // Set schema info
        SetDBSchema($comp, 'cny');

        MyConnect();

        // Check company
        if ($comp!='1') {
            $cnyqry = array("SELECT title FROM company WHERE record# = :1", $comp);
        } elseif ($_cnyTitle!='') {
            $cnyqry = array("SELECT record#, title FROM company WHERE title = :1", $_cnyTitle);
        }

        $res = QueryResult($cnyqry);
        $errcny = false;
        if ($res[0]['TITLE'] == '') {
            $errcny = true;
            echo '<br><br><br><table bordercolor="#FF0000" border=1 cellspacing=1 align="center" width="50%">
                <tr bgcolor=#FF0000" align="center">
                <td colspan="2"><font color="#FFFFFF"><h2>Incorrect Company</h2></font>
                </td></tr></table>';
        }

        //This will only happen in case of User providing Company title.
        if (isset($res[0]['RECORD#']) && $res[0]['RECORD#'] != '') {
            //Setting actual environment
            $company = $res[0]['RECORD#'];
            Globals::$g->_userid = "1@$company";
            SetDBSchema($company, 'cny');
        }

        // Proceed if there is no error condition and all the values are supplied.
        if (!$errcny) {
            include_once 'csvimport_renewalmacro_for_historicalsotrans.cls';
            $imp = new csvimport_renewalmacro_for_historicalsotrans();
            
            // Set Environment
            $logsave = ToggleLogToFile(false);
            $importparams['impfile'] = $_impfile;
            $importparams['impfilename'] = $_FILES['_impfile']['name'];
            $importparams['emailaddress'] = $_emailaddress;
            $returnparams = array();
            
            //  Start the Import and collect is Pass/Fail in $ok
            $ok = $imp->ImportCSVFile($importparams, $returnparams);

            if ($ok) {
                // Pass Condition
                $ResultMess = "All transactions updated SUCCESSFULLY";
                $borderColor = $bgColor = "#66CC00";
            } else {
                // Fail Condition
                $ResultMess = "All or few transactions may not have been updated ".
                    "successfully.<br> An email has been sent containing details. ";
                $borderColor = $bgColor = "#FF0000";
            }
            ToggleLogToFile($logsave);

            // The following will be executed both Pass/Fail Conditions.
            echo "<br><br><br><table bordercolor=$borderColor border=1 cellspacing=1 align=\"center\" width=\"50%\">
                <tr bgcolor=$bgColor align=\"center\">
                <td colspan=\"2\"><font color=\"#FFFFFF\"><h2>$ResultMess</h2></font></td></tr></table>";
        }
    } else {
        // Throw the error and bypass everything in case All required detail are not provided.
        echo '<br><br><br>
        <table bordercolor="#FF0000" border=1 cellspacing=1 align="center" width="50%">
        <tr bgcolor="#FF0000" align="center">
        <td colspan="2">
        <font color="#FFFFFF"><h2>All the details are *Mandatory</h2></font>
        </td>
        </tr>
        </table>';
    }
} 
?>
</form>
</body>
</html>