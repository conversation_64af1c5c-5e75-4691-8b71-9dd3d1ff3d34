<?php

final class BankFeedDispatcher extends Dispatcher
{
    const BANKFEEDPROCESSOR_PREFIX = 'BankFeed Processor';
    /**
     * @return string
     */
    public function getLogPrefix(): string
    {
        return self::BANKFEEDPROCESSOR_PREFIX;
    }

    /**
     * @return string
     */
    public function getConfigSectionName(): string
    {
        return 'BANK_FEED_DISPATCHER';
    }

    /**
     * @param DBSchemaInfo|null $dbInfo
     *
     * @return int
     */
    public function getDBBandwidth(DBSchemaInfo $dbInfo = null): int
    {
        return $dbInfo->getBankFeedWorkflowBandwidth();
    }

    /**
     * @param DBSchemaInfo|null $dbInfo
     *
     * @return int
     */
    public function getDBTimeLimit(DBSchemaInfo $dbInfo = null): int
    {
        return $dbInfo->getBankFeedWorkflowTimeLimit();
    }

    /**
     * @return string
     */
    public function getControlLockPrefix(): string
    {
        return 'bfDispatch';
    }

    /**
     * @param int[]  $dbsToRunOn
     * @param string $companiesList
     *
     * @return array|bool
     */
    public function getJobList($dbsToRunOn, $companiesList)
    {
        $debugLog = (!Globals::$g->islive && Request::$r->_dlog) ? Request::$r->_dlog : '';
        $pendingJobs = DBRunner::runOnSomeDBsInCurrentPOD(
            [ $this, 'queryCnyList' ],
            [$companiesList, $debugLog],
            $dbsToRunOn,
            false
        );

        return $pendingJobs;
    }

    /**
     * Get cny# based on job id
     *
     * @param string $jobId
     *
     * @return int|false
     */
    public function parseCompanyFromJobID(string $jobId): int
    {
        $cnyObject = explode('-', $jobId);
        return (int)$cnyObject[0];
    }

    /**
     * Query the DB for distinct cny's in elastic search object queue
     *
     * @param array  $companiesList
     *
     * @param string $dlog
     *
     * @return false|string[][]
     * @throws IAException
     */
    public function queryCnyList($companiesList = [], $dlog='')
    {
        if ($dlog) {
            dbg_log_setfile("/tmp/" . $dlog);
        }

        $in = '';
        if (!empty($companiesList)) {
            $in = " wpb.cny# in (SELECT record# FROM company WHERE status = 'T' AND moved  = 'F' AND title IN ($companiesList))  and ";
        }

        $featureConfigWhereClause = '';
        if (FeatureConfigManagerFactory::getInstance()
                                             ->isFeatureSetByCompany('DISABLE_BANK_FEED_DISPATCHER') ) {
                $featureConfigWhereClause = " and not exists (select 1 from featureconfig fc where fc.featureid = 'DISABLE_BANK_FEED_DISPATCHER'
                and fc.cny#= wpb.cny#) ";
        }

        $wpbbankschedulercheck = " and not exists ( select 1 FROM wpbbankscheduler sch 
        where (sch.refreshstatus in ('Q','T') ) 
        and sch.cny#=wpb.cny# and sch.wpbbankaccountkey = wpb.record#)";

        $qry = "select distinct wpb.cny# C, wpb.locationkey E
        from wpbbankaccount wpb, company c 
                where "  . $in . " 
                c.record# = wpb.cny#
                and c.status = 'T' and c.moved = 'F'
                and (wpb.nextupdate < CURRENT_TIMESTAMP AT TIME ZONE 'GMT' or wpb.nextupdate is null)
        AND ( (nvl(wpb.refreshstatus2,'S') <> 'R') or 
                (wpb.nextupdate < cast(CURRENT_TIMESTAMP AT TIME ZONE 'GMT'-TO_DSINTERVAL('0 23:59:59') as date)) or
                (wpb.lastbankfeeddate < cast(current_timestamp AT TIME ZONE 'GMT' - to_dsinterval('0 2:0:0') as date))
            )
                
        and (wpb.status in ( 'CONNECTED', 'AUTHREQUIRED', 'VERIFYINGAUTH','PENDING') or wpb.importconnectionstatus in ('CONNECTED')) and wpb.accountstatus = 'T' " .
               $wpbbankschedulercheck .
               $featureConfigWhereClause ;

        $dbResult = QueryResult($qry);
        $jobList = [];
        foreach ($dbResult as $row) {
            $jobList[] = $row['C'] . '-' . $row['E'];
        }
        // $jobList[] = '********' . '-' . '1';
        if ($dlog) {
            dbg_log("Bank feed dispatcher job list:");
            dbg_log($jobList);
        }
        return $jobList;
    }
}
