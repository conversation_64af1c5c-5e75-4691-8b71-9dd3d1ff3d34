<?php

class Pt_CspConfig {

    /* @var string $cspConnect */
    private $cspConnect;
    /* @var string $cspFont */
    private $cspFont;
    /* @var string $cspFrame */
    private $cspFrame;
    /* @var string $cspImage */
    private $cspImage;
    /* @var string $cspMedia */
    private $cspMedia;
    /* @var string $cspObject */
    private $cspObject;
    /* @var string $cspStyle */
    private $cspStyle;
    /* @var string $cspScript */
    private $cspScript;

    public function __construct()
    {
        $this->cspConnect = '';
        $this->cspFont = '';
        $this->cspFrame = '';
        $this->cspImage = '';
        $this->cspMedia = '';
        $this->cspObject = '';
        $this->cspStyle = '';
        $this->cspScript = '';
    }

    /**
     * @param array $props
     * @param bool  $convertNewlines
     */
    public function setFromProps($props, $convertNewlines)
    {
        if ( $convertNewlines ) {
            // 39227: WTF! Platform stores properties in the database with \n converted to \\n in util_arrayToText
            //        but does not convert \\n back to \n when reading from the database in util_arrayFromText
            $this->cspConnect = str_replace("\\n", "\n", $props[FIELD_CSP_CONNECT] ?? '');
            $this->cspFont = str_replace("\\n", "\n", $props[FIELD_CSP_FONT] ?? '');
            $this->cspFrame = str_replace("\\n", "\n", $props[FIELD_CSP_FRAME] ?? '');
            $this->cspImage = str_replace("\\n", "\n", $props[FIELD_CSP_IMAGE] ?? '');
            $this->cspMedia = str_replace("\\n", "\n", $props[FIELD_CSP_MEDIA] ?? '');
            $this->cspObject = str_replace("\\n", "\n", $props[FIELD_CSP_OBJECT] ?? '');
            $this->cspScript = str_replace("\\n", "\n", $props[FIELD_CSP_SCRIPT] ?? '');
            $this->cspStyle = str_replace("\\n", "\n", $props[FIELD_CSP_STYLE] ?? '');
        } else {
            $this->cspConnect = $props[FIELD_CSP_CONNECT] ?? '';
            $this->cspFont = $props[FIELD_CSP_FONT] ?? '';
            $this->cspFrame = $props[FIELD_CSP_FRAME] ?? '';
            $this->cspImage = $props[FIELD_CSP_IMAGE] ?? '';
            $this->cspMedia = $props[FIELD_CSP_MEDIA] ?? '';
            $this->cspObject = $props[FIELD_CSP_OBJECT] ?? '';
            $this->cspScript = $props[FIELD_CSP_SCRIPT] ?? '';
            $this->cspStyle = $props[FIELD_CSP_STYLE] ?? '';
        }
    }

    /**
     * @param string $cspConnect
     * @param string $cspFont
     * @param string $cspFrame
     * @param string $cspImage
     * @param string $cspMedia
     * @param string $cspObject
     * @param string $cspStyle
     * @param string $cspScript
     */
    public function set($cspConnect, $cspFont, $cspFrame, $cspImage, $cspMedia, $cspObject,
                        $cspStyle, $cspScript)
    {
        $this->cspConnect = $cspConnect;
        $this->cspFont = $cspFont;
        $this->cspFrame = $cspFrame;
        $this->cspImage = $cspImage;
        $this->cspMedia = $cspMedia;
        $this->cspObject = $cspObject;
        $this->cspStyle = $cspStyle;
        $this->cspScript = $cspScript;
    }

    /**
     * @return string[]
     */
    public function getCspProps()
    {
        return [
            FIELD_CSP_CONNECT => $this->cspConnect,
            FIELD_CSP_FONT    => $this->cspFont,
            FIELD_CSP_FRAME   => $this->cspFrame,
            FIELD_CSP_IMAGE   => $this->cspImage,
            FIELD_CSP_MEDIA   => $this->cspMedia,
            FIELD_CSP_OBJECT  => $this->cspObject,
            FIELD_CSP_SCRIPT  => $this->cspScript,
            FIELD_CSP_STYLE   => $this->cspStyle,
        ];
    }

    /**
     * sets needed CSP headers for user-injected stuff
     *
     * @param CspPolicy $outerPolicy if not null will add csp policies to it rather than
     *                               immediately setting the http header
     */
    public function setCspHeaders($outerPolicy = null)
    {
        $cspPolicy = $outerPolicy ?? IACspPolicyManager::getCspPolicy();

        $cspSettings = [
            CspPolicy::POLICY_CONNECT => $this->cspConnect,
            CspPolicy::POLICY_FONT => $this->cspFont,
            CspPolicy::POLICY_FRAME => $this->cspFrame,
            CspPolicy::POLICY_IMAGE => $this->cspImage,
            CspPolicy::POLICY_MEDIA => $this->cspMedia,
            CspPolicy::POLICY_OBJECT => $this->cspObject,
            CspPolicy::POLICY_SCRIPT => $this->cspScript,
            CspPolicy::POLICY_STYLE => $this->cspStyle,
        ];

        $cspPolicy->addSettings($cspSettings);

    }

}
