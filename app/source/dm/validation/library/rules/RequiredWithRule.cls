<?php
/**
 * Copyright (c) 2016-2019 <PERSON>, licensed under MIT
 */

/**
 * required_with:field_1,field_2,...
 *
 * The field under validation must be present and not empty only if any of the other specified fields are present and
 * has a value
 */
class RequiredWithRule extends RequiredRule
{

    
    protected bool $implicit = true;

    public function __construct()
    {
        $this->fillableParams = [ 'fields' ];
    }

    /**
     * Check the $value is valid
     *
     * @param mixed $value
     *
     * @return bool
     */
    public function check($value) : bool
    {
        $this->requireParameters([ 'fields' ]);
    
        $column = $this->getAttributeAlias();
        $this->message = new ErrorMessage(
            sprintf("The %s is required", $column),
            'DM-0040',
            ['COLUMN' => $column]
        );

        $fields = $this->parameter('fields');
        $fields = is_array($fields) ? $fields : [ $fields ];
        $validator = $this->validation->getValidator();
        $requiredValidator = $validator('required');

        foreach ( $fields as $field ) {
            if ( $this->validation->hasValue($field) ) {
                $this->setAttributeAsRequired();

                return $requiredValidator->check($value, []);
            }
        }

        return true;
    }
}
