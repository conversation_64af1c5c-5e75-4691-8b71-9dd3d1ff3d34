<?php
/**
 * AMEXRESTObjexts.inc
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

/**
 *  REST based object for all AMEX web service interaction.
 *
 */
/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class AddressInfoObj
{
    /**
     * @var string $addressLine1
     */
    public $addressLine1;	// Max Length 150 (mandatory)
    /**
     * @var string $addressLine2
     */
    public $addressLine2;	// Max Length 150
    /**
     * @var string $addressLine3
     */
    public $addressLine3;	// Max Length 150
    /**
     * @var string $addressLine4
     */
    public $addressLine4;	// Max Length 150
    /**
     * @var string $addressLine5
     */
    public $addressLine5;	// Max Length 150
    /**
     * @var string $city
     */
    public $city;	// Max Length 50 (mandatory)
    /**
     * @var string $regionCode
     */
    public $regionCode;	// Max Length 2(for US, mandatory)
    /**
     * @var string $countryName
     */
    public $countryName;	// Max Length 20 (mandatory)
    /**
     * @var string $postalCode
     */
    public $postalCode;	// Max Length 20 (mandatory)
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class PaymentMethodObj
{
    /**
     * @var string $PaymentMethod
     */
    public $PaymentMethod;  // Max Length 2 (mandatory)
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class ContactDetailsObj
{
    /**
     * @var string $jobTitle
     */
    public $jobTitle;		// Max length 30
    /**
     * @var string $salutation
     */
    public $salutation;
    /**
     * @var string $firstName
     */
    public $firstName;    // Max Length 50 (mandatory)
    /**
     * @var string $middleName
     */
    public $middleName;
    /**
     * @var string $lastName
     */
    public $lastName;     // Max Length 50 (mandatory)
    /**
     * @var string $emailId
     */
    public $emailId;
    /**
     * @var string $primaryPhoneNo
     */
    public $primaryPhoneNo;
    /**
     * @var string $alternatePhoneNo
     */
    public $alternatePhoneNo;
    /**
     * @var string $mobilePhoneNo
     */
    public $mobilePhoneNo;
    /**
     * @var string $extension
     */
    public $extension;
    /**
     * @var string $faxNo
     */
    public $faxNo;
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class CheckSettingsInfoObj
{
    /**
     * @var string $custFeeBillInd
     */
    public $custFeeBillInd;
    /**
     * @var string $checkNumberingInd
     */
    public $checkNumberingInd;
    /**
     * @var string $checkPrintServiceInd
     */
    public $checkPrintServiceInd;
    /**
     * @var string $fastForwardServiceInd
     */
    public $fastForwardServiceInd;
    /**
     * @var string $supplierNotificationInd
     */
    public $supplierNotificationInd;
    /**
     * @var string $holdInd
     */
    public $holdInd;

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class CheckDeliveryIndicatorObj
{
    /**
     * @var string $deliveryIndicator
     */
    public $deliveryIndicator;	  // Max Length 30
    /**
     * @var string $mailVendorMethodCode
     */
    public $mailVendorMethodCode;  // Max Length 30
    /**
     * @var string $mailVendorAccountNo
     */
    public $mailVendorAccountNo;  // Max Length 30

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class OrgCheckDetailsObj
{
    /**
     * @var CheckSettingsInfoObj $checkSettings
     */
    public $checkSettings;
    /**
     * @var CheckDeliveryIndicatorObj $checkDeliveryIndicator
     */
    public $checkDeliveryIndicator;

    public function __construct()
    {
        $this->checkSettings = new CheckSettingsInfoObj();
        $this->checkDeliveryIndicator = new CheckDeliveryIndicatorObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class TransactionHistoryDetails
{
    /**
     * @var int $transactionCount
     */
    public $transactionCount;
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class UserAgentDetails
{
    /**
     * @var string $webActionPath
     */
    public $webActionPath;
    /**
     * @var string $browserType
     */
    public $browserType;
    /**
     * @var string $browserVersion
     */
    public $browserVersion;
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class SystemInformationDetails
{
    /**
     * @var string $ipAddress
     */
    public $ipAddress;
    /**
     * @var string $deviceId
     */
    public $deviceId;
    /**
     * @var string $sessionId
     */
    public $sessionId;
    /**
     * @var string $adminSubmitDate
     */
    public $adminSubmitDate;
    /**
     * @var UserAgentDetails $userAgent
     */
    public $userAgent;

    public function __construct()
    {
        $this->userAgent = new UserAgentDetails();
    }
}


/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class AdminDetailsObj
{
    /**
     * @var string $firstName
     */
    public $firstName;
    /**
     * @var string $lastName
     */
    public $lastName;
    /**
     * @var string $emailId
     */
    public $emailId;
    /**
     * @var string $phoneNumber
     */
    public $phoneNumber;
    /**
     * @var string $userId
     */
    public $userId;
    /**
     * @var string $adminLoginVolume
     */
    public $adminLoginVolume;
    /**
     * @var string $adminUserTenure
     */
    public $adminUserTenure;
    /**
     * @var SystemInformationDetails  $systemInformation
     */
    public $systemInformation;

    public function __construct()
    {
        $this->systemInformation = new SystemInformationDetails();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class ACHOrgDetailsObj
{
    /**
     * @var string $doingBusinessAsName
     */
    public $doingBusinessAsName;
    /**
     * @var string $legalName
     */
    public $legalName;
    /**
     * @var string $orgPhoneNumber
     */
    public $orgPhoneNumber;
    /**
     * @var string $orgEmailId
     */
    public $orgEmailId;
    /**
     * @var string $taxId
     */
    public $taxId;
    /**
     * @var string $supplierLoginTenure
     */
    public $supplierLoginTenure;
    /**
     * @var int $userCount
     */
    public $userCount;
    /**
     * @var TransactionHistoryDetails $transactionHistory
     */
    public $transactionHistory;
    /**
     * @var AdminDetailsObj $adminDetails
     */
    public $adminDetails;
    /**
     * @var AddressInfoObj $mailingAddress
     */
    public $mailingAddress;

    public function __construct()
    {
        $this->transactionHistory = new TransactionHistoryDetails();
        $this->adminDetails = new AdminDetailsObj();
        $this->mailingAddress = new AddressInfoObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class CardOrgDetailsObj
{
    /**
     * @var string $orgEmailId
     */
    public $orgEmailId;
    /**
     * @var AdminDetailsObj $adminDetails
     */
    public $adminDetails;

    public function __construct()
    {
        $this->adminDetails = new AdminDetailsObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class CommonRequestContextObj
{
    /**
     * @var string $requestId
     */
    public $requestId;	    // Max Length 32 (mandatory)
    /**
     * @var string $partnerId
     */
    public $partnerId;       // Max Length 36 (mandatory)
    /**
     * @var string $partnerName
     */
    public $partnerName;    // Max Length 40 (mandatory)
    /**
     * @var string $timestamp
     */
    public $timestamp;      // Max Length 24 (mandatory) (Format: yyyy-MM-dd'T'HH:mm:ss.SSSZ)

    /**
     * @param string|bool $reqId
     */
    public function populateCommonRequestContext($reqId) {
        $this->requestId = $reqId;
        $this->partnerId = GetValueForIAAMEXCFGProperty('AMEX_REST_CLIENT_ID', ia_cfg::DECRYPT_KEY);
        $this->partnerName = GetValueForIAAMEXCFGProperty('AMEX_INTACCT_PARTNER_NAME');
        //$this->timestamp = date("c"); // Timestamp in ISO 8601 format
        $now = new DateTime('NOW');
        $t = explode(" ",microtime());
        $this->timestamp = $now->format('Y-m-d\TH:i:s' . substr((string)$t[0],1,4) . 'P');
    }

}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class SubscribeService
{
    /**
     * @var string $serviceCode
     */
    public $serviceCode;
    /**
     * @var string $orgTermsAndConditionsInd
     */
    public $orgTermsAndConditionsInd;
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class OrganizationInformObj
{
    /**
     * @var string $parentOrgId
     */
    public $parentOrgId;
    /**
     * @var string $organizationStatus
     */
    public $organizationStatus;
    /**
     * @var SubscribeService[] $subscribeServices
     */
    public $subscribeServices;       	// Array of SubscribeService objects
    /**
     * @var string $orgName
     */
    public $orgName;
    /**
     * @var string $orgShortName
     */
    public $orgShortName;
    /** @var ContactDetailsObj $contactDetails */
    public $contactDetails;
    /** @var AddressInfoObj $orgAddress */
    public $orgAddress;
    /** @var  ACHOrgDetailsObj $achOrgDetails */
    public $achOrgDetails;
    /** @var OrgCheckDetailsObj $orgCheckDetails */
    public $orgCheckDetails;
    /** @var  CardOrgDetailsObj $cardOrgDetails */
    public $cardOrgDetails;

    /**
     * var array $unsubscribeServices
     */
    public $unsubscribeServices;

    public function __construct()
    {
        $this->contactDetails = array();
        $this->orgAddress = new AddressInfoObj();
        $this->orgCheckDetails = new OrgCheckDetailsObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class CheckReturnAddressObj
{
    /**
     * @var string $companyName
     */
    public $companyName;
    /**
     * @var AddressInfoObj $returnAddress
     */
    public $returnAddress;      // AddressInfoObj

    public function __construct()
    {
        $this->returnAddress = new AddressInfoObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class SignatureDetailsObj
{
    /**
     * @var string $thresholdLimit
     */
    public $thresholdLimit;
    /**
     * @var string $signature1Image
     */
    public $signature1Image;
    /**
     * @var string $signature1Indicator
     */
    public $signature1Indicator;
    /**
     * @var string $signature1FileType
     */
    public $signature1FileType;
    /**
     * @var string $signature2Image
     */
    public $signature2Image;
    /**
     * @var string $signature2Indicator
     */
    public $signature2Indicator;
    /**
     * @var string $signature2FileType
     */
    public $signature2FileType;
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class CheckAcctDetailsObj
{
    /**
     * @var string $partnerAccountId
     */
    public $partnerAccountId;
    /**
     * @var string $accountStatus
     */
    public $accountStatus;
    /**
     * @var string $bankName
     */
    public $bankName;
    /**
     * @var AddressInfoObj $bankAddress
     */
    public $bankAddress;            // AddressInfoObj
    /**
     * @var string $nameOnAccount
     */
    public $nameOnAccount;
    /**
     * @var string $routingNumber
     */
    public $routingNumber;
    /**
     * @var string $accountNumber
     */
    public $accountNumber;
    /**
     * @var string $enableThreshold
     */
    public $enableThreshold;
    /**
     * @var string $checkLayoutIndicator
     */
    public $checkLayoutIndicator;    // For future use
    /**
     * @var string $checkLayoutImg
     */
    public $checkLayoutImg;         // For future use
    /**
     * @var string $checkLayoutFileType
     */
    public $checkLayoutFileType;    // For future use
    /**
     * @var string $logoIndicator
     */
    public $logoIndicator;          // For future use
    /**
     * @var string $logoImage
     */
    public $logoImage;              // For future use
    /**
     * @var string $logoFileType
     */
    public $logoFileType;           // For future use
    /**
     * @var SignatureDetailsObj $signatureDetails
     */
    public $signatureDetails;       // SignatureDetailsObj
    /**
     * @var CheckReturnAddressObj $checkReturnAddress
     */
    public $checkReturnAddress;     // CheckReturnAddressObj
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class OrgSupplierObj
{
    /** @var CommonRequestContextObj $commonRequestContext */
    public $commonRequestContext;
    /**
     * @var string $enrollmentActionType
     */
    public $enrollmentActionType;   // Max Length 20 (mandatory - ADD/UPDATE)
    /**
     * @var string $enrollmentCategory
     */
    public $enrollmentCategory;
    /**
     * @var string $supplierOrgRollbackInd
     */
    public $supplierOrgRollbackInd;
    /**
     * @var string $supplierOrgServiceCode
     */
    public $supplierOrgServiceCode;
    /**
     * @var string $organizationId
     */
    public $organizationId;          // Max Length 100 (mandatory)
    /**
     * @var string $associatedOrgId
     */
    public $associatedOrgId;

    /** @var OrganizationInformObj $organizationInfo */
    public $organizationInfo;

    public function __construct()
    {
        $this->commonRequestContext = new CommonRequestContextObj();
        $this->organizationInfo = new OrganizationInformObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class OrgSupplierDataObj
{
    /** @var OrgSupplierObj $data */
    public $data;

    /**
     * OrgSupplierDataObj constructor.
     */
    public function __construct()
    {
        $this->data = new OrgSupplierObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class OrgSupplierEnrollmentObj
{
    /**
     * @var OrgSupplierDataObj $manageEnrollmentRequest
     */
    public $manageEnrollmentRequest;    // Class OrgSupplierDataObj

    public function __construct()
    {
        $this->manageEnrollmentRequest = new OrgSupplierDataObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class CheckAcctDetailsInfoObj
{
    /**
     * @var CheckAcctDetailsObj[] $checkAcctDetails
     */
    public $checkAcctDetails;

    public function __construct()
    {
        $this->checkAcctDetails = array();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class CardAcctDetailsObj
{
    /**
     * @var string $partnerAccountId
     */
    public $partnerAccountId;
    /**
     * @var string $accountStatus
     */
    public $accountStatus;
    /**
     * @var string $cardNumber
     */
    public $cardNumber;
    /**
     * @var string $cardHolderName
     */
    public $cardHolderName;
    /**
     * @var string $expiryDate
     */
    public $expiryDate;
    /**
     * @var string $cardType
     */
    public $cardType;
    /**
     * @var string $defaultCardIndicator
     */
    public $defaultCardIndicator;
    /**
     * @var string $countryCode
     */
    public $countryCode;
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class CardAcctDetailsInfoObj
{
    /**
     * @var CardAcctDetailsObj[] $cardAcctDetails
     */
    public $cardAcctDetails;

    public function __construct()
    {
        $this->cardAcctDetails = array();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class ABODetailsObj
{
    /**
     * @var string $firstName
     */
    public $firstName;

    /**
     * @var string $lastName
     */
    public $lastName;
    /**
     * @var string $phoneNumber
     */
    public $phoneNumber;
    /**
     * @var string $emailId
     */
    public $emailId;
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class BankAccountHistoryObj
{
    /**
     * @var string $accountCreationDate
     */
    public $accountCreationDate;
    /**
     * @var int $transactionCount
     */
    public $transactionCount;
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class ACHAcctDetailsObj
{
    /**
     * @var string|int $partnerAccountId
     */
    public $partnerAccountId;
    /**
     * @var string $accountStatus
     */
    public $accountStatus;
    /**
     * @var string $nameOnAccount
     */
    public $nameOnAccount;
    /**
     * @var string $accountType
     */
    public $accountType;
    /**
     * @var string $accountNumber
     */
    public $accountNumber;
    /**
     * @var string $routingNumber
     */
    public $routingNumber;
    /**
     * @var string $bankName
     */
    public $bankName;
    /**
     * @var string $acctTermsAndConditionsInd
     */
    public $acctTermsAndConditionsInd;
    /**
     * @var ABODetailsObj $aboDetails
     */
    public $aboDetails; // ABODetailsObj object
    /**
     * @var AdminDetailsObj $adminDetails
     */
    public $adminDetails;   // AdminDetailsObj object
    /**
     * @var BankAccountHistoryObj $bankAccountHistory
     */
    public $bankAccountHistory; // BankAccountHistoryObj object
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class ACHAcctDetailsInfoObj
{
    /**
     * @var ACHAcctDetailsObj[] $achAcctDetails
     */
    public $achAcctDetails;

    /**
     * ACHAcctDetailsInfoObj constructor.
     */
    public function __construct()
    {
        $this->achAcctDetails = array();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class AccountInfoObj
{

    /**
     * @var CommonRequestContextObj $commonRequestContext
     */
    public $commonRequestContext;

    /**
     * @var string $enrollmentActionType
     */
    public $enrollmentActionType;   // Max Length 20 (mandatory - ADD/UPDATE)

    /**
     * @var string $enrollmentCategory
     */
    public $enrollmentCategory;

    /**
     * @var string $organizationId
     */
    public $organizationId;         // Max Length 100 (mandatory)

    /**
     * @var string $associatedOrgId
     */
    public $associatedOrgId;

    /**
     * @var CheckAcctDetailsInfoObj|ACHAcctDetailsInfoObj|CardAcctDetailsInfoObj $accountDetails
     */
    public $accountDetails;

    /**
     * @param string $acctServ
     */
    public function __construct($acctServ = OutsourcedCheckUtils::OUTSOURCE_CHECK_METHOD)
    {
        $this->commonRequestContext = new CommonRequestContextObj();
        if ($acctServ == OutsourcedCheckUtils::OUTSOURCE_CC_METHOD) {
            $this->accountDetails = new CardAcctDetailsInfoObj();
        } elseif ($acctServ == OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD) {
            $this->accountDetails = new ACHAcctDetailsInfoObj();
        } else {
            $this->accountDetails = new CheckAcctDetailsInfoObj();
        }
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class AccountDataObj
{
    /**
     * @var AccountInfoObj $data
     */
    public $data;    // Class AccountInfoObj

    /**
     * @param string $acctServ
     */
    public function __construct($acctServ = OutsourcedCheckUtils::OUTSOURCE_CHECK_METHOD)
    {
        $this->data = new AccountInfoObj($acctServ);
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class AccountEnrollmentObj
{
    /**
     * @var AccountDataObj $manageEnrollmentRequest
     */
    public $manageEnrollmentRequest;    // Class OrgSupplierDataObj

    /**
     * @param string $acctServ
     */
    public function __construct($acctServ = OutsourcedCheckUtils::OUTSOURCE_CHECK_METHOD)
    {
        $this->manageEnrollmentRequest = new AccountDataObj($acctServ);
    }
}


/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class EnrollResponseInfoObj
{
    /**
     * @var CommonRequestContextObj $commonRequestContext
     */
    public $commonRequestContext;   // Class CommonRequestContextObj
    /**
     * @var string $organizationId
     */
    public $organizationId;         // Max Length 100 (mandatory)
    /**
     * @var string $responseCode
     */
    public $responseCode;
    /**
     * @var string $responseDesc
     */
    public $responseDesc;

    public function __construct()
    {
        $this->commonRequestContext = new CommonRequestContextObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class EnrollResponseDataObj
{
    /**
     * @var EnrollResponseInfoObj $data
     */
    public $data;    // Class AccountInfoObj

    public function __construct()
    {
        $this->data = new EnrollResponseInfoObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class EnrollmentResponseObj
{
    /**
     * @var EnrollResponseDataObj $manageEnrollmentResponse
     */
    public $manageEnrollmentResponse;    // Class EnrollResponseDataObj

    public function __construct()
    {
        $this->manageEnrollmentResponse = new EnrollResponseDataObj();
    }
}


/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class AcctDetailsObj
{
    /**
     * @var string $paymentMethod
     */
    public $paymentMethod;
    /**
     * @var string $partnerAccountId
     */
    public $partnerAccountId;
    /**
     * @var string $acctStatus
     */
    public $acctStatus;
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class AcctEnrollResponseInfoObj
{
    /**
     * @var CommonRequestContextObj $commonRequestContext
     */
    public $commonRequestContext;   // Class CommonRequestContextObj
    /**
     * @var string $organizationId
     */
    public $organizationId;         // Max Length 100 (mandatory)
    /**
     * @var AcctDetailsObj[] $acctDetails
     */
    public $acctDetails;            // Array of AcctDetailsObj objects

    public function __construct()
    {
        $this->commonRequestContext = new CommonRequestContextObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class AcctEnrollResponseDataObj
{
    /**
     * @var AcctEnrollResponseInfoObj $data
     */
    public $data;    // Class AccountInfoObj

    public function __construct()
    {
        $this->data = new AcctEnrollResponseInfoObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class AcctEnrollmentResponseObj
{
    /**
     * @var AcctEnrollResponseDataObj $manageEnrollmentResponse
     */
    public $manageEnrollmentResponse;    // Class EnrollResponseDataObj

    public function __construct()
    {
        /** @noinspection PhpUndefinedVariableInspection */
        $this->$manageEnrollmentResponse = new AcctEnrollResponseDataObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class OrgSupplierStatusCatInfoObj
{
    /**
     * @var string $serviceCode
     */
    public $serviceCode;
    /**
     * @var string $organizationCategory
     */
    public $organizationCategory;

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class OrgSupplierStatusInfoObj
{
    /**
     * @var CommonRequestContextObj $commonRequestContext
     */
    public $commonRequestContext;
    /**
     * @var string $organizationId
     */
    public $organizationId;         // Max Length 100 (mandatory)
    /**
     * @var string $associatedOrgId
     */
    public $associatedOrgId;
    /**
     * @var string $statusCategory
     */
    public $statusCategory;

    /**
     * @var OrgSupplierStatusCatInfoObj $organizationInfo
     */
    public $organizationInfo;

    public function __construct()
    {
        $this->commonRequestContext = new CommonRequestContextObj();
        $this->organizationInfo = new OrgSupplierStatusCatInfoObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class OrgSupplierStatusDataObj
{
    /** @var OrgSupplierStatusInfoObj $data */
    public $data;

    public function __construct()
    {
        $this->data = new OrgSupplierStatusInfoObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class OrgSupplierStatusObj
{
    /** @var OrgSupplierStatusDataObj $getStatusRequest */
    public $getStatusRequest;

    public function __construct()
    {
        $this->getStatusRequest = new OrgSupplierStatusDataObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class AccountDetailsStatusInfoObj
{
    /**
     * @var string $partnerAccountId
     */
    public $partnerAccountId;
    /**
     * @var string $paymentMethod
     */
    public $paymentMethod;
    /**
     * @var string $binaryDataIndicator
     */
    public $binaryDataIndicator;

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class AccountStatusInfoObj
{
    /**
     * @var CommonRequestContextObj $commonRequestContext
     */
    public $commonRequestContext;   // Class CommonRequestContextObj
    /**
     * @var string $organizationId
     */
    public $organizationId;         // Max Length 100 (mandatory)
    /**
     * @var string $associatedOrgId
     */
    public $associatedOrgId;
    /**
     * @var string $statusCategory
     */
    public $statusCategory;
    /**
     * @var AccountDetailsStatusInfoObj $accountDetails
     */
    public $accountDetails;

    public function __construct()
    {
        $this->commonRequestContext = new CommonRequestContextObj();
        $this->accountDetails = new AccountDetailsStatusInfoObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class AccountStatusDataObj
{
    /**
     * @var AccountStatusInfoObj $data
     */
    public $data;    // Class AccountStatusInfoObj

    public function __construct()
    {
        $this->data = new AccountStatusInfoObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class AccountStatusObj
{
    /**
     * @var AccountStatusDataObj $getStatusRequest
     */
    public $getStatusRequest;    // Class AccountStatusDataObj

    public function __construct()
    {
        $this->getStatusRequest = new AccountStatusDataObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class pymtDocAttachmentObj
{
    /**
     * @var string $pymtAttachId
     */
    public $pymtAttachId;
    /**
     * @var string $pymtAttachFile
     */
    public $pymtAttachFile;

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class BuyerAcctAcctDetailsObj
{
    /**
     * @var string $accountId
     */
    public $accountId;
    /**
     * @var string $checkNo
     */
    public $checkNo;
    /**
     * @var string $checkDate
     */
    public $checkDate;
    /**
     * @var string $checkMemo
     */
    public $checkMemo;

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class NewContactObj
{
    /**
     * @var string $contactName
     */
    public $contactName;
    /**
     * @var string $contactPhone
     */
    public $contactPhone;
    /**
     * @var string $contactEmail
     */
    public $contactEmail;

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class ContactDetailsInfoObj
{
    /**
     * @var string $contactId
     */
    public $contactId;
    /**
     * @var NewContactObj $newContact
     */
    public $newContact;     // Class NewContactObj

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class CheckDeliveryDetailsObj
{
    /**
     * @var string $deliveryMethod
     */
    public $deliveryMethod;
    /**
     * @var string $mailVendor
     */
    public $mailVendor;
    /**
     * @var string $mailVendorAccountNo
     */
    public $mailVendorAccountNo;

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class PayorAttachmentObj
{
    /**
     * @var string $payorAttachmentId
     */
    public $payorAttachmentId;

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class DropInsertIndObj
{
    /**
     * @var string $dropInsertIndicatorId
     */
    public $dropInsertIndicatorId;

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class PymtCheckDetailsObj
{
    /**
     * @var string $vendorName
     */
    public $vendorName;
    /**
     * @var AddressInfoObj $checkDestinationAddress
     */
    public $checkDestinationAddress;    // Class AddressInfoObj
    /**
     * @var ContactDetailsInfoObj $contactDetails
     */
    public $contactDetails;             // Class ContactDetailsInfoObj
    /**
     * @var CheckDeliveryDetailsObj $checkDeliveryDetails
     */
    public $checkDeliveryDetails;       // Class CheckDeliveryDetailsObj
    /**
     * @var PayorAttachmentObj $payorAttachments
     */
    public $payorAttachments;           // Class PayorAttachmentObj
    /**
     * @var DropInsertIndObj $dropInsertIndicators
     */
    public $dropInsertIndicators;       // Class DropInsertIndObj

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class InvoiceLineItemObj
{
    /**
     * @var string $invoiceLineNumber
     */
    public $invoiceLineNumber;
    /**
     * @var string $lineDescription
     */
    public $lineDescription;
    /**
     * @var string $linePaidValue
     */
    public $linePaidValue;
    /**
     * @var string $purchaseOrderItem
     */
    public $purchaseOrderItem;
    /**
     * @var string $partNumber
     */
    public $partNumber;
    /**
     * @var int $unitPrice
     */
    public $unitPrice;
    /**
     * @var int $quantity
     */
    public $quantity;
    /**
     * @var string $unitOfMeasure
     */
    public $unitOfMeasure;
    /**
     * @var int $lineValue
     */
    public $lineValue;
    /**
     * @var string $purchaseOrder
     */
    public $purchaseOrder;
    /**
     * @var string $taxCode
     */
    public $taxCode;
    /**
     * @var string $taxJurisdiction
     */
    public $taxJurisdiction;
    /**
     * @var int $lineFreightValue
     */
    public $lineFreightValue;
    /**
     * @var int $lineDiscountValue
     */
    public $lineDiscountValue;
    /**
     * @var int $lineAdjustmentValue
     */
    public $lineAdjustmentValue;
    /**
     * @var string $buyerField1
     */
    public $buyerField1;
    /**
     * @var string $buyerField2
     */
    public $buyerField2;
    /**
     * @var string $buyerField3
     */
    public $buyerField3;
    /**
     * @var string $buyerField4
     */
    public $buyerField4;
    /**
     * @var string $shortPayCode
     */
    public $shortPayCode;
    /**
     * @var string $shortPayText
     */
    public $shortPayText;
    /**
     * @var string $shortPayValue
     */
    public $shortPayValue;

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class InvoiceObj
{
    /**
     * @var string $invoiceDate
     */
    public $invoiceDate;
    /**
     * @var string $invoiceDueDate
     */
    public $invoiceDueDate;
    /**
     * @var string|null $supplierInvoiceRef
     */
    public $supplierInvoiceRef;
    /**
     * @var string|null $buyerInvoiceRef
     */
    public $buyerInvoiceRef;
    /**
     * @var int $paidValue
     */
    public $paidValue;
    /**
     * @var int $grossValue
     */
    public $grossValue;
    /**
     * @var int $netValue
     */
    public $netValue;
    /**
     * @var string $taxCode
     */
    public $taxCode;
    /**
     * @var string|int $taxValue
     */
    public $taxValue;
    /**
     * @var string|int $freightValue
     */
    public $freightValue;
    /**
     * @var string|int $discountValue
     */
    public $discountValue;
    /**
     * @var string|int $adjustmentValue
     */
    public $adjustmentValue;
    /**
     * @var string $buyerField1
     */
    public $buyerField1;
    /**
     * @var string $buyerField2
     */
    public $buyerField2;
    /**
     * @var string $buyerField3
     */
    public $buyerField3;
    /**
     * @var string $buyerField4
     */
    public $buyerField4;
    /**
     * @var string $shortPayCode
     */
    public $shortPayCode;
    /**
     * @var string $shortPayText
     */
    public $shortPayText;
    /**
     * @var string|int $shortPayValue
     */
    public $shortPayValue;
    /**
     * @var InvoiceLineItemObj[] $invoiceLineItem
     */
    public $invoiceLineItem;    // Array of InvoiceLineItemObj

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class SupplierAcctDetailsObj
{
    /**
     * @var PymtCheckDetailsObj $checkDetails
     */
    public $checkDetails;   // Class PymtCheckDetailsObj

    /**
     * @var string $accountId
     */
    public $accountId;

    public function __construct()
    {
        $this->checkDetails = new PymtCheckDetailsObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class PaymentDetailsObj
{
    /**
     * @var string $buyerOrgId
     */
    public $buyerOrgId;
    /**
     * @var string $supplierOrgId
     */
    public $supplierOrgId;
    /**
     * @var string $buyerPymtRefId
     */
    public $buyerPymtRefId;
    /**
     * @var string|int $paymentValue
     */
    public $paymentValue;
    /**
     * @var string $currencyCode
     */
    public $currencyCode;
    /**
     * @var string $paymentDueDate
     */
    public $paymentDueDate;
    /**
     * @var string $paymentMethod
     */
    public $paymentMethod;
    /**
     * @var AuditTrialDetailsObj $auditTrialDetails
     */
    public $auditTrialDetails;
    /**
     * @var string $overridePymtMtd
     */
    public $overridePymtMtd;
    /**
     * @var string $buyerField1
     */
    public $buyerField1;
    /**
     * @var string $buyerField2
     */
    public $buyerField2;
    /**
     * @var string $buyerField3
     */
    public $buyerField3;
    /**
     * @var string $buyerField4
     */
    public $buyerField4;

    /**
     * @var pymtDocAttachmentObj[] $pymtDocAttachments
     */
    public $pymtDocAttachments;

    /**
     * @var string $aboUserId
     */
    public $aboUserId;

    /**
     * @var BuyerAcctAcctDetailsObj $buyerAcctDetails
     */
    public $buyerAcctDetails;

    /**
     * @var SupplierAcctDetailsObj $supplierAcctDetails
     */
    public $supplierAcctDetails;

    /**
     * @var array $invoice
     */
    public $invoice;

    /**
     * @var string $defaultDomainCtrls
     */
    public $defaultDomainCtrls;

    public function __construct()
    {
        $this->auditTrialDetails = new AuditTrialDetailsObj();
        $this->buyerAcctDetails = new BuyerAcctAcctDetailsObj();
        $this->supplierAcctDetails = new SupplierAcctDetailsObj();
        $this->invoice = array();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class PaymentRequestInfoObj
{
    /**
     * @var CommonRequestContextObj $commonRequestContext
     */
    public $commonRequestContext;
    /**
     * @var PaymentDetailsObj $paymentDetails
     */
    public $paymentDetails;

    public function __construct()
    {
        $this->commonRequestContext = new CommonRequestContextObj();
        $this->paymentDetails = new PaymentDetailsObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class PaymentRequestDataObj
{
    /**
     * @var PaymentRequestInfoObj $data
     */
    public $data;    // Class PaymentRequestInfoObj

    public function __construct()
    {
        $this->data = new PaymentRequestInfoObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class PaymentRequestObj
{
    /**
     * @var PaymentRequestDataObj $processPaymentRequest
     */
    public $processPaymentRequest;    // Class PaymentRequestDataObj

    public function __construct()
    {
        $this->processPaymentRequest = new PaymentRequestDataObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class PaymentDetailsInfoObj
{
    /**
     * @var string $paymentMethod
     */
    public $paymentMethod;
    /**
     * @var string $buyerPymtRefId
     */
    public $buyerPymtRefId;

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class PaymentStatusInfoObj
{
    /**
     * @var CommonRequestContextObj $commonRequestContext
     */
    public $commonRequestContext;   // Class CommonRequestContextObj
    /**
     * @var string $organizationId
     */
    public $organizationId;         // Max Length 100 (mandatory)
    /**
     * @var string $associatedOrgId
     */
    public $associatedOrgId;
    /**
     * @var string $statusCategory
     */
    public $statusCategory;
    /**
     * @var PaymentDetailsInfoObj $paymentDetails
     */
    public $paymentDetails;

    public function __construct()
    {
        $this->commonRequestContext = new CommonRequestContextObj();
        $this->paymentDetails = new PaymentDetailsInfoObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class PaymentStatusDataObj
{
    /**
     * @var PaymentStatusInfoObj $data
     */
    public $data;    // Class PaymentStatusInfoObj

    public function __construct()
    {
        $this->data = new PaymentStatusInfoObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class PaymentStatusObj
{
    /**
     * @var PaymentStatusDataObj $getStatusRequest
     */
    public $getStatusRequest;    // Class PaymentStatusDataObj

    public function __construct()
    {
        $this->getStatusRequest = new PaymentStatusDataObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class SuppliersMatchContactInfoObj
{
    /**
     * @var string $companyName
     */
    public $companyName;
    /**
     * @var string $prefix
     */
    public $prefix;
    /**
     * @var string $firstName
     */
    public $firstName;
    /**
     * @var string $middleName
     */
    public $middleName;
    /**
     * @var string $lastName
     */
    public $lastName;
    /**
     * @var string $printAsName
     */
    public $printAsName;
    /**
     * @var string $addressLine1
     */
    public $addressLine1;
    /**
     * @var string $addressLine2
     */
    public $addressLine2;
    /**
     * @var string $city
     */
    public $city;
    /**
     * @var string $stateOrProvince
     */
    public $stateOrProvince;
    /**
     * @var string $zipCode
     */
    public $zipCode;
    /**
     * @var string $countryCode
     */
    public $countryCode;
    /**
     * @var string $countryName
     */
    public $countryName;
    /**
     * @var string $federalTaxId
     */
    public $federalTaxId;
    /**
     * @var string $primaryEmail
     */
    public $primaryEmail;
    /**
     * @var string $secondaryEmail
     */
    public $secondaryEmail;
    /**
     * @var string $secondaryURL
     */
    public $secondaryURL;
    /**
     * @var string $primaryURL
     */
    public $primaryURL;
    /**
     * @var string $primaryPhoneNumber
     */
    public $primaryPhoneNumber;
    /**
     * @var string $secondaryPhoneNumber
     */
    public $secondaryPhoneNumber;
    /**
     * @var string $mobileNumber
     */
    public $mobileNumber;
    /**
     * @var string $pager
     */
    public $pager;
    /**
     * @var string $fax
     */
    public $fax;

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class SuppliersMatchInfoObj
{
    /**
     * @var string $supplierId
     */
    public $supplierId;
    /**
     * @var string $supplierName
     */
    public $supplierName;
    /**
     * @var SuppliersMatchContactInfoObj $vendorDetails
     */
    public $vendorDetails;
    /**
     * @var SuppliersMatchContactInfoObj $vendorContactDetails
     */
    public $vendorContactDetails;
    /**
     * @var SuppliersMatchContactInfoObj $vendorPayToContactDetails
     */
    public $vendorPayToContactDetails;
    /**
     * @var SuppliersMatchContactInfoObj $vendorReturnToContactDetails
     */
    public $vendorReturnToContactDetails;

    public function __construct()
    {
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class UploadSuppliersMatchInfoObj
{
    /**
     * @var CommonRequestContextObj $commonRequestContext
     */
    public $commonRequestContext;   // Class CommonRequestContextObj
    /**
     * @var string $customerId
     */
    public $customerId;
    /**
     * @var SuppliersMatchInfoObj[] $suppliers
     */
    public $suppliers;              // Array of SuppliersMatchInfoObj objects

    /**
     * @param string $orgId
     */
    public function __construct($orgId)
    {
        $this->commonRequestContext = new CommonRequestContextObj();
        $this->customerId = $orgId;
        $this->suppliers = array();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class UploadSuppliersMatchDataObj
{
    /**
     * @var UploadSuppliersMatchInfoObj $data
     */
    public $data;    // Class PaymentStatusInfoObj

    /**
     * @param string $orgId
     */
    public function __construct($orgId)
    {
        $this->data = new UploadSuppliersMatchInfoObj($orgId);
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class UploadSuppliersMatchObj
{
    /**
     * @var UploadSuppliersMatchDataObj $uploadSuppliersRequest
     */
    public $uploadSuppliersRequest;    // Class PaymentStatusDataObj

    /**
     * @param string $orgId
     */
    public function __construct($orgId)
    {
        $this->uploadSuppliersRequest = new UploadSuppliersMatchDataObj($orgId);
    }
}


/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class SupplierMatchStatusObj
{
    /**
     * @var CommonRequestContextObj $commonRequestContext
     */
    public $commonRequestContext;   // Class CommonRequestContextObj
    /**
     * @var string $customerId
     */
    public $customerId;
    /**
     * @var string $supplierId
     */
    public $supplierId;

    /**
     * @param string $orgId
     */
    public function __construct($orgId)
    {
        $this->commonRequestContext = new CommonRequestContextObj();
        $this->customerId = $orgId;
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class SupplierMatchStatusDataObj
{
    /**
     * @var SupplierMatchStatusObj $data
     */
    public $data;    // Class SupplierMatchStatusObj

    /**
     * @param string $orgId
     */
    public function __construct($orgId)
    {
        $this->data = new SupplierMatchStatusObj($orgId);
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class GetSupplierMatchStatusObj
{
    /**
     * @var SupplierMatchStatusDataObj $getMatchStatusRequest
     */
    public $getMatchStatusRequest;    // Class SupplierMatchStatusDataObj

    /**
     * @param string $orgId
     */
    public function __construct($orgId)
    {
        $this->getMatchStatusRequest = new SupplierMatchStatusDataObj($orgId);
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class ACHPayersObj
{
    /**
     * @var CommonRequestContextObj $commonRequestContext
     */
    public $commonRequestContext;    // Class CommonRequestContextObj
    /**
     * @var string $organizationId
     */
    public $organizationId;
    /**
     * @var string $partnerAccountId
     */
    public $partnerAccountId;

    public function __construct()
    {
        $this->commonRequestContext = new CommonRequestContextObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class ACHPayersDataObj
{
    /**
     * @var ACHPayersObj $data
     */
    public $data;    // Class ACHPayersObj

    public function __construct()
    {
        $this->data = new ACHPayersObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class RetrievePayersObj
{
    /**
     * @var ACHPayersDataObj $retrievePayersRequest
     */
    public $retrievePayersRequest;    // Class AccountStatusDataObj

    public function __construct()
    {
        $this->retrievePayersRequest = new ACHPayersDataObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class RetrievePaymentLimitDetails
{
    /**
     * @var CommonRequestContextObj $commonRequestContext
     */
    public $commonRequestContext;    // Class CommonRequestContextObj
    /**
     * @var string $organizationId
     */
    public $organizationId;

    public function __construct()
    {
        $this->commonRequestContext = new CommonRequestContextObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class RetrievePaymentLimitDetailsDataObj
{
    /**
     * @var RetrievePaymentLimitDetails $data
     */
    public $data;

    public function __construct()
    {
        $this->data = new RetrievePaymentLimitDetails();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class RetrievePaymentLimitDetailsObj
{
    /**
     * @var RetrievePaymentLimitDetailsDataObj $retrievePaymentLimitDtlsRequest
     */
    public $retrievePaymentLimitDtlsRequest;

    public function __construct()
    {
        $this->retrievePaymentLimitDtlsRequest = new RetrievePaymentLimitDetailsDataObj();
    }
}


/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class ExceptionsObj
{
    /**
     * @var CommonRequestContextObj $commonRequestContext
     */
    public $commonRequestContext;
    /**
     * @var int $pageIndex
     */
    public $pageIndex;
    /**
     * @var string $lastRunTimestamp
     */
    public $lastRunTimestamp;

    public function __construct()
    {
        $this->commonRequestContext = new CommonRequestContextObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class ExceptionsDataObj
{
    /**
     * @var ExceptionsObj $data
     */
    public $data;    // Class ExceptionsObj

    public function __construct()
    {
        $this->data = new ExceptionsObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class ExceptionListObj
{
    /**
     * @var ExceptionsDataObj $getExceptionsRequest
     */
    public $getExceptionsRequest;    // Class ExceptionsDataObj

    public function __construct()
    {
        $this->getExceptionsRequest = new ExceptionsDataObj();
    }
}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> Narayanan <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class RESTAPIResponseCodes
{
    /**
     * @var string[] $enrollmentResponses
     */
    public static $enrollmentResponses = array(
        'SSEAPIEN001'   =>  'Internal Server Error  Internal Server error',
        'SSEAPIEN002'   =>  'Invalid Request Data.  If Data Quality Check Fails in the request data',
        'SSEAPIEN003'   =>  'Invalid Entity Relation. If hierarchy does not exists or hierarchy is not correct under Partner entity ID',
        'SSEAPIEN004'   =>  'Invalid Relationship between Partner Entity ID, Payment Entity ID and Organization ID. If Partner Entity ID, Payment Entity ID and Organization ID are not mapped correctly',
        'SSEAPIEN005'   =>  'Invalid Organization Name',
        'SSEAPIEN006'   =>  'Invalid Organization ID',
        'SSEAPIEN007'   =>  'Invalid Service code',
        'SSEAPIEN008'   =>  'Invalid Customer Fee Billing Indicator',
        'SSEAPIEN009'   =>  'Invalid Contact Detail - Invalid First Name. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN010'   =>  'Invalid Contact Detail - Invalid Middle Name. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN011'   =>  'Invalid Contact Detail - Invalid Last Name. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN012'   =>  'Invalid Contact Detail - Invalid Primary Phone Number',
        'SSEAPIEN013'   =>  'Invalid Contact Detail - Invalid Mobile Phone Number',
        'SSEAPIEN014'   =>  'Invalid Contact Detail - Invalid Extension Number',
        'SSEAPIEN015'   =>  'Invalid Contact Detail - Invalid Fax Number',
        'SSEAPIEN016'   =>  'Invalid Organization Address - Invalid Address Line 1. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN017'   =>  'Invalid Organization Address - Invalid Address Line 2. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN018'   =>  'Invalid Organization Address - Invalid Address Line 3. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN019'   =>  'Invalid Organization Address - Invalid Address Line 4. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN020'   =>  'Invalid Organization Address - Invalid City. Allowed characters are: alphabets and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN021'   =>  'Invalid Organization Address - Invalid Region Code. Allowed characters are: alphabets and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN022'   =>  'Invalid Organization Address - Invalid Country Name. Allowed characters are: alphabets and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN023'   =>  'Invalid Organization Address - Invalid Postal Code',
        'SSEAPIEN024'   =>  'Invalid value of Check Numbering Indicator',
        'SSEAPIEN025'   =>  'Invalid value of Check Print Service Indicator',
        'SSEAPIEN026'   =>  'Invalid value of Use Fast Forward Service Indicator',
        'SSEAPIEN027'   =>  'Check Delivery Indicator Details is invalid',
        'SSEAPIEN028'   =>  'Mail Vendor Method Code is invalid',
        'SSEAPIEN029'   =>  'Invalid Organization Status',
        'SSEAPIEN030'   =>  'Service cannot be unsubscribed',
        'SSEAPIEN031'   =>  'Invalid Payment Method: Payment Method configuration does not exist',
        'SSEAPIEN032'   =>  'Attachment ID already exists',
        'SSEAPIEN033'   =>  'Attachment ID is Empty',
        'SSEAPIEN034'   =>  'Invalid Attachment File',
        'SSEAPIEN035'   =>  'Attachment File is empty',
        'SSEAPIEN036'   =>  'Data Quality Check Failed  If Data Quality Check Fails',
        'SSEAPIEN037'   =>  'Organization Details cannot be Updated',
        'SSEAPIEN038'   =>  'Invalid Payment Entity ID',
        'SSEAPIEN039'   =>  'Invalid Service Code Configuration',
        'SSEAPIEN040'   =>  'Invalid Account Details',
        'SSEAPIEN041'   =>  'Invalid Partner Account ID',
        'SSEAPIEN042'   =>  'Invalid Partner Account ID Configuration',
        'SSEAPIEN043'   =>  'Invalid Partner Account ID: Must Be Unique Per Payment Method',
        'SSEAPIEN044'   =>  'Invalid Routing Number',
        'SSEAPIEN045'   =>  'Invalid Fraction Routing Number',
        'SSEAPIEN046'   =>  'Invalid Account Number',
        'SSEAPIEN047'   =>  'Invalid Request: Current Buyer Account Status Is Not Updatable',
        'SSEAPIEN048'   =>  'Invalid Bank Address - Invalid City. Allowed characters are: alphabets and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN049'   =>  'Invalid Bank Address - Invalid Region Code',
        'SSEAPIEN050'   =>  'Invalid Bank Address - Invalid Postal Code. PostalCode should have either 5 digits or field length has to be 10 with all digits except at position 6 which should be a hyphen',
        'SSEAPIEN051'   =>  'Invalid Account Type',
        'SSEAPIEN052'   =>  'Invalid Threshold Limit',
        'SSEAPIEN053'   =>  'Enable Threshold Flag Is Off',
        'SSEAPIEN054'   =>  'Invalid Threshold Limit',
        'SSEAPIEN055'   =>  'Invalid Threshold Detail',
        'SSEAPIEN056'   =>  'Invalid Check layout Image  Size/format of check layout image is invalid',
        'SSEAPIEN057'   =>  'Invalid Check Logo Image  Size/format of check logo image is invalid',
        'SSEAPIEN058'   =>  'Invalid Account ID  If Account id does not exist',
        'SSEAPIEN059'   =>  'Invalid Relationship between partner id and/or Partner Account ID',
        'SSEAPIEN060'   =>  'Invalid Bank Name.  Bank Name is Empty',
        'SSEAPIEN061'   =>  'Invalid Name On Account.  Name on Account is Empty',
        'SSEAPIEN062'   =>  'Invalid Signature1 Indicator And Signature1 Image',
        'SSEAPIEN063'   =>  'Invalid Company Name.  Company Name is Empty',
        'SSEAPIEN064'   =>  'Invalid Check Return AddressLine1.  Check Return AddressLine1 is Empty',
        'SSEAPIEN065'   =>  'Invalid Account Description.  Account Description is required',
        'SSEAPIEN066'   =>  'Invalid Expiration Date.  Expiration date is Invalid',
        'SSEAPIEN067'   =>  'Invalid Request: Account Details Can Not Be Updated',
        'SSEAPIEN068'   =>  'Invalid Organization ID  If Organization ID is blank',
        'SSEAPIEN069'   =>  'Invalid Bank Address - Invalid Address Line 1. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN070'   =>  'Invalid Card Number.  If card number is invalid or is empty',
        'SSEAPIEN071'   =>  'Invalid Enable Threshold Flag  If Enable Threshold is not valid',
        'SSEAPIEN072'   =>  'Invalid Signature1 File Type.  If Signature1 File Type is not valid',
        'SSEAPIEN073'   =>  'Invalid Signature2 File Type.  If Signature2 File Type is not valid',
        'SSEAPIEN074'   =>  'Invalid Signature Indicator: Must Be Unique Per Account',
        'SSEAPIEN077'   =>  'Invalid Request ID',
        'SSEAPIEN078'   =>  'Invalid Partner ID',
        'SSEAPIEN079'   =>  'Invalid Partner Name',
        'SSEAPIEN080'   =>  'Invalid Timestamp',
        'SSEAPIEN081'   =>  'Invalid Enrollment Action Type',
        'SSEAPIEN082'   =>  'Invalid Enrollment Category',
        'SSEAPIEN083'   =>  'CheckAcctDetails section must be provided if ServiceCode is Check CheckAcctDetails section must be provided if Subscribe ServiceCode is Check',
        'SSEAPIEN084'   =>  'Atleast one CardAcctDetails section must be provided if ServiceCode is Card',
        'SSEAPIEN085'   =>  'Invalid Organization ID - Duplicate Organization ID',
        'SSEAPIEN086'   =>  'Atleast one IWireAcctDetails section must be provided if ServiceCode is IWire',
        'SSEAPIEN087'   =>  'Atleast one AchAcctDetails section must be provided if ServiceCode is ACH',
        'SSEAPIEN088'   =>  'Invalid Contact Detail - Invalid Email ID',
        'SSEAPIEN089'   =>  'Service not supported',
        'SSEAPIEN090'   =>  'Invalid Associated Organization ID',
        'SSEAPIEN091'   =>  'Invalid Organization Information',
        'SSEAPIEN092'   =>  'Invalid Parent Organization ID',
        'SSEAPIEN093'   =>  'Invalid Tax ID',
        'SSEAPIEN094'   =>  'Invalid Contact Details',
        'SSEAPIEN095'   =>  'Invalid Contact Details - Invalid Salutation',
        'SSEAPIEN096'   =>  'Invalid Contact Details - Invalid Job Title',
        'SSEAPIEN097'   =>  'Invalid Contact Detail - Invalid Alternate Phone Number',
        'SSEAPIEN098'   =>  'Invalid Organization Address -Invalid Address Line 5. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN099'   =>  'Invalid Organization Address -Invalid Region Name. Only alphanumerals are allowed.',
        'SSEAPIEN100'   =>  'Invalid Organization Address -Invalid Country Code',
        'SSEAPIEN101'   =>  'Org Check Details section not passed',
        'SSEAPIEN102'   =>  'Invalid value of Supplier Notification Indicator',
        'SSEAPIEN103'   =>  'Delivery Indicator is Invalid',
        'SSEAPIEN104'   =>  'Mail Vendor Account Number is invalid',
        'SSEAPIEN105'   =>  'Hold Indicator is invalid',
        'SSEAPIEN106'   =>  'Invalid Payor Attachments',
        'SSEAPIEN107'   =>  'Account Details section not passed',
        'SSEAPIEN108'   =>  'Invalid Check Account Details -Upto 10 check account details are supported',
        'SSEAPIEN109'   =>  'Invalid Line of Business',
        'SSEAPIEN110'   =>  'Bank Address not passed  BankAddress section not passed in request',
        'SSEAPIEN111'   =>  'Invalid Bank Address - Invalid Address Line 2. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN112'   =>  'Invalid Bank Address - Invalid Address Line 3. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN113'   =>  'Invalid Bank Address - Invalid Address Line 4. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN114'   =>  'Invalid Bank Address - Invalid Address Line 5. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN115'   =>  'Invalid Bank Address - Invalid Region Name. Only alphanumerals are allowed.',
        'SSEAPIEN116'   =>  'Invalid Bank Address - Invalid Country Code',
        'SSEAPIEN117'   =>  'Invalid Bank Address - Invalid Country Name',
        'SSEAPIEN118'   =>  'Invalid Check Layout Indicator',
        'SSEAPIEN119'   =>  'Invalid Logo Indicator',
        'SSEAPIEN120'   =>  'Check Return Address section not passed',
        'SSEAPIEN121'   =>  'Return Address section not passed',
        'SSEAPIEN122'   =>  'Invalid Return Address - Invalid Company Name',
        'SSEAPIEN123'   =>  'Invalid Return Address - Invalid Address Line 1. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN124'   =>  'Invalid Return Address - Invalid Address Line 2. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN125'   =>  'Invalid Return Address - Invalid Address Line 3. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN126'   =>  'Invalid Return Address - Invalid Address Line 4. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN127'   =>  'Invalid Return Address - Invalid Address Line 5. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN128'   =>  'Invalid Return Address - Invalid City. Allowed characters are: alphanumerals and special characters [ ] ? \ / ( )  !  | $ : . - _ @ * % ~ #  ^  % ?  £ comma and space.',
        'SSEAPIEN129'   =>  'Invalid Return Address - Invalid Region Code. Only alphanumerals are allowed.',
        'SSEAPIEN130'   =>  'Invalid Return Address - Invalid Region Name',
        'SSEAPIEN131'   =>  'Invalid Return Address - Invalid Country Code',
        'SSEAPIEN132'   =>  'Invalid Return Address - Invalid Country Name',
        'SSEAPIEN133'   =>  'Invalid Return Address - Invalid Postal Code',
        'SSEAPIEN134'   =>  'Invalid Account Status',
        'SSEAPIEN135'   =>  'Organization Address not passed',
        'SSEAPIEN136'   =>  'Parent Organization not found',
        'SSEAPIEN137'   =>  'Associated Organization not found',
        'SSEAPIEN138'   =>  'Service already subscribed for Organization',
        'SSEAPIEN139'   =>  'Unsubscribed service not found',
        'SSEAPIEN140'   =>  'Account is inactive',
        'SSEAPIEN141'   =>  'Invalid relationship between Organization Id and Account ID',
        'SSEAPIEN142'   =>  'Organization is inactive',
        'SSEAPIEN143'   =>  'Invalid relationship between Organization Id, Associated Organization ID and Partner ID',
        'SSEAPIEN144'   =>  'Invalid Account - Duplicate Account ID',
        'SSEAPIEN145'   =>  'Invalid ContactId – ContactId not passed',
        'SSEAPIEN146'   =>  'Invalid Organization Short Name',
        'SSEAPIEN147'   =>  'Invalid Payor Attachment ID',
        'SSEAPIEN148'   =>  'Invalid Signature1 Indicator',
        'SSEAPIEN149'   =>  'Invalid Signature2 Indicator',
        'SSEAPIEN151'   =>  'Organization is In InProgress status',
        'SSEAPIEN152'   =>  'Organization Contact not found',
        'SSEAPIEN154'   =>  'Primary phone number is mandatory'
    );

    /**
     * @var string[] $statusResponses
     */
    public static $statusResponses = array(
        'SSEAPIST001'   =>  'Internal Server Error  Internal Server error',
        'SSEAPIST002'   =>  'Invalid Request Data.  If Data Quality Check Fails in the request data',
        'SSEAPIST003'   =>  'No record found for organization',
        'SSEAPIST004'   =>  'Invalid Page ID',
        'SSEAPIST005'   =>  'No record found for Account',
        'SSEAPIST006'   =>  'Invalid Binary Data Indicator',
        'SSEAPIST007'   =>  'Invalid Account Type',
        'SSEAPIST008'   =>  'Invalid Account ID',
        'SSEAPIST009'   =>  'Invalid Relationship',
        'SSEAPIST010'   =>  'Invalid Organization ID',
        'SSEAPIST011'   =>  'Invalid Buyer Account Status',
        'SSEAPIST012'   =>  'No record found for payment reference id',
        'SSEAPIST013'   =>  'Invalid payment method',
        'SSEAPIST014'   =>  'Invalid Buyer Payment Reference ID',
        'SSEAPIST015'   =>  'Invalid Request ID',
        'SSEAPIST016'   =>  'Invalid Partner ID',
        'SSEAPIST017'   =>  'Invalid Partner Name',
        'SSEAPIST018'   =>  'Invalid Timestamp',
        'SSEAPIST019'   =>  'Invalid Status Category',
        'SSEAPIST020'   =>  'Invalid Organization Category',
        'SSEAPIST021'   =>  'Invalid Associated Org ID',
        'SSEAPIST022'   =>  'Organization Info section not passed',
        'SSEAPIST023'   =>  'Account Details section not passed',
        'SSEAPIST024'   =>  'Payment Details section not passed',
    );

    /**
     * @var string[] $paymentResponses
     */
    public static $paymentResponses = array(
        'SSEAPIPY001'   =>  'Internal Server Error  Internal Server error',
        'SSEAPIPY002'   =>  'Buyer Organization not active',
        'SSEAPIPY003'   =>  'Supplier Organization not active',
        'SSEAPIPY004'   =>  'Buyer Account not active',
        'SSEAPIPY005'   =>  'Supplier Account not active',
        'SSEAPIPY006'   =>  'Invalid Payment Value',
        'SSEAPIPY007'   =>  'Invalid Payment Method',
        'SSEAPIPY008'   =>  'Invalid Currency Code',
        'SSEAPIPY009'   =>  'Invalid Buyer Payment Reference ID',
        'SSEAPIPY010'   =>  'Invalid Override Payment Method ',
        'SSEAPIPY011'   =>  'Invalid Request ID',
        'SSEAPIPY012'   =>  'Invalid Partner ID',
        'SSEAPIPY013'   =>  'Invalid Partner Name',
        'SSEAPIPY014'   =>  'Invalid Timestamp',
        'SSEAPIPY015'   =>  'Invalid Buyer Organization ID',
        'SSEAPIPY016'   =>  'Invalid Supplier Organization ID',
        'SSEAPIPY017'   =>  'Invalid Payment Doc Attachments - Upto 5 Payment doc attachments are supported',
        'SSEAPIPY018'   =>  'Invalid Payment Due Date',
        'SSEAPIPY019'   =>  'Invalid Payment Method',
        'SSEAPIPY020'   =>  'Invalid Buyer Account ID',
        'SSEAPIPY021'   =>  'Invalid Check Number',
        'SSEAPIPY022'   =>  'Invalid Check Date',
        'SSEAPIPY023'   =>  'Invalid Check Memo',
        'SSEAPIPY024'   =>  'Invalid Supplier Account ID',
        'SSEAPIPY025'   =>  'Invalid Vendor Name',
        'SSEAPIPY026'   =>  'Contact ID not found',
        'SSEAPIPY027'   =>  'Check Details not passed',
        'SSEAPIPY028'   =>  'Invalid Check Destination Address -Invalid Address Line 1',
        'SSEAPIPY029'   =>  'Invalid Check Destination Address -Invalid Address Line 2',
        'SSEAPIPY030'   =>  'Invalid Check Destination Address -Invalid Address Line 3',
        'SSEAPIPY031'   =>  'Invalid Check Destination Address -Invalid Address Line 4',
        'SSEAPIPY032'   =>  'Invalid Check Destination Address -Invalid Address Line 5',
        'SSEAPIPY033'   =>  'Invalid Check Destination Address -Invalid City',
        'SSEAPIPY034'   =>  'Invalid Check Destination Address -Invalid Region Code',
        'SSEAPIPY035'   =>  'Invalid Check Destination Address -Invalid Country Name',
        'SSEAPIPY036'   =>  'Invalid Check Destination Address -Invalid Postal Code',
        'SSEAPIPY037'   =>  'Invalid Check Destination Address -Invalid Region Name',
        'SSEAPIPY038'   =>  'Invalid Check Destination Address -Invalid Country Code',
        'SSEAPIPY039'   =>  'Invalid Contact ID',
        'SSEAPIPY040'   =>  'Invalid Contact Name',
        'SSEAPIPY041'   =>  'Invalid Contact Phone',
        'SSEAPIPY042'   =>  'Invalid Contact Email',
        'SSEAPIPY043'   =>  'Both ContactId and NewContact are passed',
        'SSEAPIPY044'   =>  'Delivery Indicator is Invalid',
        'SSEAPIPY045'   =>  'Mail Vendor Account Number is invalid',
        'SSEAPIPY046'   =>  'Invalid Delivery Method',
        'SSEAPIPY047'   =>  'Invalid Mail Vendor',
        'SSEAPIPY048'   =>  'Invalid Delivery Method association',
        'SSEAPIPY049'   =>  'Invalid Payment Doc Attachments – Duplicate attachment ID',
        'SSEAPIPY050'   =>  'Invalid Payment Doc Attachments',
        'SSEAPIPY051'   =>  'Payment Request already initiated',
        'SSEAPIPY052'   =>  'Buyer Organization not enrolled',
        'SSEAPIPY053'   =>  'Invalid Payor Attachments - Upto 5 payor attachments are supported',
        'SSEAPIPY054'   =>  'Payor Attachment ID already exists',
        'SSEAPIPY055'   =>  'Drop Insert Indicator already exists',
        'SSEAPIPY056'   =>  'Invalid Drop Insert Indicators - Upto 5 Drop Insert Indicator are supported',
        'SSEAPIPY057'   =>  'Invalid Handling SLA',
        'SSEAPIPY058'   =>  'Invalid Payor Attachment ID',
        'SSEAPIPY059'   =>  'Invalid Drop Insert Indicator',
        'SSEAPIPY060'   =>  'Invalid Invoice Date',
        'SSEAPIPY061'   =>  'Invalid Invoice Due Date',
        'SSEAPIPY062'   =>  'Invalid Supplier Invoice Number',
        'SSEAPIPY063'   =>  'Invalid Buyer Invoice Numbery',
        'SSEAPIPY064'   =>  'Invalid Paid Value',
        'SSEAPIPY065'   =>  'Invalid Gross Value',
        'SSEAPIPY066'   =>  'Invalid Net Value',
        'SSEAPIPY067'   =>  'Invalid Tax Code',
        'SSEAPIPY068'   =>  'Invalid Tax Value',
        'SSEAPIPY069'   =>  'Invalid Freight Value',
        'SSEAPIPY070'   =>  'Invalid Discount Value',
        'SSEAPIPY071'   =>  'Invalid Adjustment Value',
        'SSEAPIPY072'   =>  'Invalid Buyer Field',
        'SSEAPIPY073'   =>  'Invalid Short Pay Code',
        'SSEAPIPY074'   =>  'Invalid Short Pay Text',
        'SSEAPIPY075'   =>  'Invalid Short Pay Value',
        'SSEAPIPY076'   =>  'Invalid Invoice Line Number',
        'SSEAPIPY077'   =>  'Invalid Invoice Line Description',
        'SSEAPIPY078'   =>  'Invalid Invoice Line Paid Value',
        'SSEAPIPY079'   =>  'Invalid Purchase Order line',
        'SSEAPIPY080'   =>  'Invalid Part Number',
        'SSEAPIPY081'   =>  'Invalid Unit Price',
        'SSEAPIPY082'   =>  'Invalid Quantity',
        'SSEAPIPY083'   =>  'Invalid Unit of Measure',
        'SSEAPIPY084'   =>  'Invalid Line Value',
        'SSEAPIPY085'   =>  'Invalid Purchase Order',
        'SSEAPIPY086'   =>  'Invalid Invoice Line Tax Code',
        'SSEAPIPY087'   =>  'Invalid Invoice Line Tax Jurisdiction',
        'SSEAPIPY088'   =>  'Invalid Invoice Line Freight Value',
        'SSEAPIPY089'   =>  'Invalid Invoice Line Discount Value',
        'SSEAPIPY090'   =>  'Invalid Invoice Line Adjustment Value',
        'SSEAPIPY091'   =>  'Invalid Invoice Line Buyer Field',
        'SSEAPIPY092'   =>  'Invalid Invoice Line Short Pay Code',
        'SSEAPIPY093'   =>  'Invalid Invoice Line Short Pay Text',
        'SSEAPIPY094'   =>  'Invalid Invoice Line Short Pay Value',
        'SSEAPIPY095'   =>  'Supplier Organization not enrolled',
        'SSEAPIPY096'   =>  'Buyer Account not enrolled',
        'SSEAPIPY097'   =>  'Supplier Account not enrolled',
        'SSEAPIPY098'   =>  'Buyer not subscribed for service',
        'SSEAPIPY099'   =>  'Supplier not subscribed for service',
        'SSEAPIPY100'   =>  'Payment Details not passed',
        'SSEAPIPY101'   =>  'Buyer Account Details not passed',
        'SSEAPIPY102'   =>  'Supplier Account Details not passed',
        'SSEAPIPY103'   =>  'Buyer service and Supplier service does not match',
        'SSEAPIPY104'   =>  'Check supplementary details not found',
        'SSEAPIPY105'   =>  'Invalid Request Data',
        'SSEAPIPY106'   =>  'Invalid Invoice',
        'SSEAPIPY107'   =>  'Both ShortPayValue and ShortPayText must be passed together',
        'SSEAPIPY108'   =>  'Check number is already used for another payment request',
        'SSEAPIPY109'   =>  'Payment amount is greater than the bank account signature threshold amount specified',
        'SSEAPIPY110'   =>  'Internal error',
    );

    /**
     * @var string[] $uploadSuppliersResponses
     */
    public static $uploadSuppliersResponses = array(
        'SSEAPIUS0000'   =>  'Received Upload Suppliers Request',
        'SSEAPIUS1000'   =>  'Invalid Request ID',
        'SSEAPIUS1001'   =>  'Invalid Partner ID',
        'SSEAPIUS1002'   =>  'Invalid Partner Name',
        'SSEAPIUS1003'   =>  'Invalid Timestamp',
        'SSEAPIUS1004'   =>  'Invalid Customer ID',
        'SSEAPIUS1005'   =>  'Invalid Supplier ID',
    );

    /**
     * @var string[] $getMatchStatusResponses
     */
    public static $getMatchStatusResponses = array(
        'SSEAPIMS001'   =>  'Internal Server Error',
        'SSEAPIMS002'   =>  'Invalid Customer  ID',
        'SSEAPIMS003'   =>  'Invalid Request ID',
        'SSEAPIMS004'   =>  'Invalid Partner ID',
        'SSEAPIMS005'   =>  'Invalid Partner Name',
        'SSEAPIMS006'   =>  'Invalid Timestamp',
        'SSEAPIMS007'   =>  'Invalid Supplier ID',
        'SSEAPIMS008'   =>  'Supplier Id not found',
        'SSEAPIMS009'   =>  'Customer Id not found'
    );

}

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> V
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class AuditTrialDetailsObj
{
    /**
     * @var string $ipaddress
     */
    public $ipaddress;
    /**
     * @var string $payerEmailIds
     */
    public $payerEmailIds;

    public function __construct()
    {
    }
}




