CREATE INDEX IX_BASEGLTOTALS_DIMENSIONS ON BA<PERSON>GLTOTALS (CN<PERSON>#, ACCOUNT#, LOCATION#, BOOK<PERSON>, TIMEPERIOD, CURRENCY, DEPT#,
                                                         CUSTOMERDIMKEY, PROJECTDIMKEY, V<PERSON><PERSON><PERSON><PERSON><PERSON>KE<PERSON>, EMP<PERSON><PERSON><PERSON>EE<PERSON>MKE<PERSON>,
                                                         IT<PERSON><PERSON><PERSON>KEY, CLASSDIMKEY, CONT<PERSON><PERSON><PERSON>MKE<PERSON>, TAS<PERSON><PERSON>MKEY,
                                                         WA<PERSON>HOUSEDIMKEY, COSTTYPEDIMKEY, CUSTDIM1, CUSTDIM2, CUSTDIM3,
                                                         CUSTDIM4, CUSTDIM5, CUSTDIM6, CUSTDIM7, CUSTDIM8, CUSTDIM9,
                                                         CUSTDIM10, CUST<PERSON>M11, CUSTDIM12, CUSTDIM13, CUSTDIM14,
                                                         CUSTDIM15) TABLESPACE ACCTINDX ONLINE
/

CREATE INDEX IX_BASEGLTOTALS_ACCOUNT ON BASEGLTOTALS (CNY#, ACCOUNT#, TIMEPERIOD, CURRENCY, BOOK<PERSON>) TABLESPACE ACCTINDX ONLINE
/

DROP INDEX IX_BASEGLTOTAL_PROJECT
/

DROP INDEX IX_BASEGLTOTALS_DIM
/