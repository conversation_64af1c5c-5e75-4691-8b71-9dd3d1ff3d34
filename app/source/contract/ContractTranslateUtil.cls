<?php

/**
 * Class ContractTranslateUtil
 * Contains implementation for translate methods consumed by the Contract objects.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class ContractTranslateUtil
{
    /* @var EntityManager  $manager */
    private $manager;

    /**
     * @param EntityManager $manager
     */
    public function __construct($manager)
    {
        $this->manager = $manager;
    }

    /**
     * @param string $entity
     * @param int    $contractKey
     * @param int     $oldProjectKey
     * @param int     $notRecordNo
     * @return array
     */
    private static function queryContractProject($entity, $contractKey, $oldProjectKey, $notRecordNo)
    {
        $querySpec = [
            'selects' => ['RECORDNO'],
            'filters' => [[
                ['CONTRACTKEY', '=', $contractKey],
                ['PROJECTDIMKEY', '=', $oldProjectKey],
                ['RECORDNO', '!=', $notRecordNo],
            ]],
            'max' => 1
        ];
        $mgr = Globals::$g->gManagerFactory->getManager($entity);
        $results = $mgr->GetList($querySpec);
        return $results;
    }

    /**
     * Confirms that the valid contract and contract detail IDs are provided
     * and logs the proper error otherwise.
     *
     * @param array $values
     *
     * @return bool
     * @throws Exception
     */
    public function translateContractDetail(& $values)
    {
        $ok = true;
        if (!isArrayValueProvided($values, 'CONTRACTID')) {
            $msg ='Contract ID is required';
            Globals::$g->gErr->addError('CN-0647', __FILE__ . ':' . __LINE__, $msg,'','');
            $ok = false;
        } else {
            if (!isArrayValueProvided($values, 'LINENO')) {
                $msg = 'Contract line is not specified.';
                Globals::$g->gErr->addError('CN-0648', __FILE__ . ':' . __LINE__, $msg, '', '');
                $ok = false;
            } else {
                $cnDetMgr = Globals::$g->gManagerFactory->getManager('contractdetail');

                $querySpec = [
                    'selects' => [
                        'RECORDNO', 'LINENO', 'CONTRACTKEY', 'CONTRACTID',
                        'CONTRACT.CUSTOMERKEY', 'CONTRACT.CUSTOMERID',
                        'BEGINDATE', 'ENDDATE', 'STATE', 'CONTRACTSTATE'
                    ],
                    'filters' => [
                        [
                            ['CONTRACTID', '=', $values['CONTRACTID']],
                            ['LINENO', '=', $values['LINENO']],
                            (($values['PERIOD'] ?? '') == '') ?
                                ['PERIOD', 'ISNULL'] : ['PERIOD', '=', $values['PERIOD']],
                        ]
                    ]
                ];

                $resultSet = $cnDetMgr->GetList($querySpec);
                if (isset($resultSet[0])) {
                    $values['CONTRACTKEY'] = $resultSet[0]['CONTRACTKEY'];
                    $values['CONTRACT.STATE'] = $resultSet[0]['CONTRACTSTATE'];
                    $values['CONTRACTDETAILKEY'] = $resultSet[0]['RECORDNO'];
                    $values['CUSTOMERDIMKEY'] = $resultSet[0]['CONTRACT.CUSTOMERKEY'];
                    $values['CUSTOMERID'] = $resultSet[0]['CONTRACT.CUSTOMERID'];
                    $values['_CONTRACTDETAIL.BEGINDATE'] = $resultSet[0]['BEGINDATE'];
                    $values['_CONTRACTDETAIL.ENDDATE'] = $resultSet[0]['ENDDATE'];
                    $values['_CONTRACTDETAIL.STATE'] = $resultSet[0]['STATE'];
                }
            }
        }

        return $ok;
    }

    /**
     * Confirms that the valid contract ID is provided and logs the proper
     * error otherwise.
     *
     * @param array $values
     *
     * @return bool
     * @throws Exception
     */
    public function translateContract(&$values)
    {
        $ok = true;

        if (!isArrayValueProvided($values, 'CONTRACTID') && !isArrayValueProvided($values, 'CONTRACTKEY')) {
            $msg = 'Cannot identify contract';
            $corr = 'Contract ID or contract key must be provided to identify the contract.';
            Globals::$g->gErr->addError('CN-0649', __FILE__ . ':' . __LINE__, $msg, '', $corr);
            $ok = false;
        } else {
            if (isArrayValueProvided($values, 'CONTRACTDETAILLINENO')) {
                // the request came for contract detail.
                $values['LINENO'] = $values['CONTRACTDETAILLINENO'];
                $ok = $this->translateContractDetail($values);
            } else {
                if ($values['STATE'] === ContractDetailState::STATE_RENEWAL_FORECAST) {
                    $contract = $values[':forecastContract'];
                    unset($values[':forecastContract']);
                } else {
                    $select = [
                        'RECORDNO', 'CONTRACTID', 'CUSTOMERKEY', 'CUSTOMERID', 'LOCATIONID', 'TERMTYPE',
                        'BASECURR', 'CURRENCY', 'EXCHRATETYPE', 'EXCH_RATE_TYPE_ID',
                        'BEGINDATE', 'ENDDATE', 'STATE', 'STATUS',
                        'PRCLSTKEY', 'PRCLIST', 'RENEWALMACROKEY',
                        'TERM.VALUE', 'BILLINGFREQUENCY', 'ADVBILLBY', 'ADVBILLBYTYPE',
                        'RENEWAL', 'RENEWTERMLENGTH', 'RENEWTERMPERIOD', 'TIMESHEETAGGREGATION',
                        'SHIPTOKEY', 'SHIPTOCONTACTNAME', 'BILLTOKEY', 'BILLTOCONTACTNAME'
                    ];
                    if (!isArrayValueProvided($values, 'RECORDNO')) {
                        // We just need the CHILDCONTRACTID while adding a new contract detail
                        $select[] = 'CHILDCONTRACTID';
                    }
                    if (isArrayValueProvided($values, 'CONTRACTID')) {
                        $filters = [[['CONTRACTID', '=', $values['CONTRACTID']]]];
                    } else {
                        $filters = [[['RECORDNO', '=', $values['CONTRACTKEY']]]];
                    }
                    $params = array(
                        'selects' => $select,
                        'filters' => $filters
                    );
                    $contractMgr = Globals::$g->gManagerFactory->getManager('contract');
                    $result = $contractMgr->GetList($params);

                    if (!isset($result[0])) {
                        $msg = 'Contract is not valid.';
                        $corr = 'Pick a valid Contract ID';
                        Globals::$g->gErr->addError('CN-0650', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                        $ok = false;
                    } else {
                        $contract = $result[0];
                    }
                }
                if ($ok && isset($contract)) {
                    $values['CONTRACTID'] = $contract['CONTRACTID'] ?? null;
                    $values['CONTRACTKEY'] = $contract['RECORDNO'] ?? null;
                    $values['CUSTOMERDIMKEY'] = $contract['CUSTOMERKEY'] ?? null;
                    $values['CUSTOMERID'] = $contract['CUSTOMERID'] ?? null;
                    $values['TERM.VALUE'] = $contract['TERM.VALUE'] ?? null;
                    $values['CONTRACT.BASECURR'] = $contract['BASECURR'] ?? null;
                    $values['CONTRACT.CURRENCY'] = $contract['CURRENCY'] ?? null;
                    $values['CONTRACT.EXCHRATETYPE'] = $contract['EXCHRATETYPE'] ?? null;
                    $values['CONTRACT.EXCH_RATE_TYPE_ID'] = $contract['EXCH_RATE_TYPE_ID'] ?? null;
                    $values['CONTRACT.LOCATIONID'] = $contract['LOCATIONID'] ?? null;
                    $values['CONTRACT.BEGINDATE'] = $contract['BEGINDATE'] ?? null;
                    $values['CONTRACT.ENDDATE'] = $contract['ENDDATE'] ?? null;
                    $values['CONTRACT.PRCLSTKEY'] = $contract['PRCLSTKEY'] ?? null;
                    $values['CONTRACT.PRCLIST'] = $contract['PRCLIST'] ?? null;
                    $values['CONTRACT.STATE'] = $contract['STATE'] ?? null;
                    $values['CONTRACT.STATUS'] = $contract['STATUS'] ?? null;
                    $values['CONTRACT.BILLINGFREQUENCY'] = $contract['BILLINGFREQUENCY'] ?? null;
                    $values['CONTRACT.BILLTOKEY'] = $contract['BILLTOKEY'] ?? null;
                    $values['CONTRACT.BILLTOCONTACTNAME'] = $contract['BILLTOCONTACTNAME'] ?? null;
                    $values['CONTRACT.SHIPTOKEY'] = $contract['SHIPTOKEY'] ?? null;
                    $values['CONTRACT.SHIPTOCONTACTNAME'] = $contract['SHIPTOCONTACTNAME'] ?? null;
                    $values['CONTRACT'] = $contract;
                }
            }
        }
        return $ok;
    }

    /**
     * @param array    $values
     * @param array    $oldValues
     * @param string   $fieldToCheckInOldValues
     * @param string[] $fieldsToClear
     */
    private function maybeClearJournalFields (&$values, $oldValues, $fieldToCheckInOldValues, $fieldsToClear)
    {
        if (!isArrayValueProvided($oldValues, $fieldToCheckInOldValues)) {
            foreach ($fieldsToClear as $fieldToClear) {
                if (!isArrayValueProvided($oldValues, $fieldToClear)) {
                    unset ($values[$fieldToClear]);
                }
            }
        }
    }

    /**
     * @param array $values
     * @param array $oldValues
     *
     * @return bool
     */
    public function maybeClearRevenueJournalFields (&$values, $oldValues)
    {
        $ok = true;
        GetModulePreferences(Globals::$g->kCNid, $prefs);

        if (ContractUtil::isDisabledJournal1($prefs)) {
            $this->maybeClearJournalFields($values, $oldValues, 'REVENUETEMPLATENAME', ContractUtil::getRevenueJournalFields1());
        }
        if (ContractUtil::isDisabledJournal2($prefs)) {
            $this->maybeClearJournalFields($values, $oldValues, 'REVENUE2TEMPLATENAME', ContractUtil::getRevenueJournalFields2());
        }
        return $ok;
    }

    /**
     * @param array $values
     * @param array $oldValues
     *
     * @return bool
     */
    public function maybeClearExpenseJournalFields (&$values, $oldValues)
    {
        $ok = true;
        GetModulePreferences(Globals::$g->kCNid, $prefs);

        if (ContractUtil::isDisabledJournal1($prefs)) {
            $this->maybeClearJournalFields($values, $oldValues, 'TEMPLATENAME', ContractUtil::getExpenseJournalFields1());
        }
        if (ContractUtil::isDisabledJournal2($prefs)) {
            $this->maybeClearJournalFields($values, $oldValues, 'TEMPLATE2NAME', ContractUtil::getExpenseJournalFields2());
        }
        return $ok;
    }

    /**
     * Confirms that the valid item is selected for the mandatory ITEMID field.
     * @param array $values
     *
     * @return bool
     * @throws Exception
     */
    public function translateItem(& $values)
    {
        $ok = true;
        if (isArrayValueProvided($values, 'ITEMID')) {
            $arr = explode('--', $values['ITEMID']);

            $item = EntityManager::GetListQuick(
                'item',
                ['RECORDNO', 'COMPUTEFORSHORTTERM'],
                ['ITEMID' => $arr[0]]
            );

            if (!empty($item)) {
                $recordNo = $item[0]['RECORDNO'];
                $values['ITEM_COMPUTEFORSHORTTERM'] = $item[0]['COMPUTEFORSHORTTERM'];
            }

            if (isset($recordNo)) {
                $values['ITEMKEY'] = $recordNo;
            } else {
                $msg = 'Item is not valid.';
                $corr = 'Pick a valid Item ID';
                Globals::$g->gErr->addError('CN-0651', __FILE__ . ':' . __LINE__, $msg, '', $corr);
                $ok = false;
            }
        } else {
            $msg = 'Item is required.';
            $corr = 'Provide a valid Item ID';
            Globals::$g->gErr->addError('CN-0652', __FILE__ . ':' . __LINE__, $msg, '', $corr);
            $ok = false;
        }
        return $ok;
    }

    /**
     * Confirms that the valid location is selected for the mandatory
     * LOCATIONID field.
     *
     * @param array $values
     *
     * @return bool
     * @throws Exception
     */
    public function translateLocation(& $values)
    {
        $ok = true;
        if (isArrayValueProvided($values, 'LOCATIONID')) {
            $arr = explode('--', $values['LOCATIONID']);

            /* @var LocationManager $locationMgr */
            $locationMgr = Globals::$g->gManagerFactory->getManager('location');
            $recordNo = $locationMgr->GetRecordNo('LOCATIONID', $arr[0]);

            if (isset($recordNo)) {
                $values['LOCATIONKEY'] = $recordNo;
            } else {
                $ok = false;
            }
        } else if (IsMultiEntityCompany()) {
            $ok = false;
        }

        if (!$ok) {
            $msg = 'Location is not valid.';
            $corr = 'Pick a valid Location ID';
            Globals::$g->gErr->addError('CN-0653', __FILE__ . ':' . __LINE__, $msg, '', $corr);
        }

        return $ok;
    }

    /**
     * Confirms that the valid department is selected for the DEPARTMENTID field.
     *
     * @param array $values
     *
     * @return bool
     * @throws Exception
     */
    public function translateDepartment(& $values)
    {
        $ok = true;

        if (isArrayValueProvided($values, 'DEPARTMENTID')) {
            $arr = explode('--', $values['DEPARTMENTID']);

            /* @var DepartmentManager $departmentMgr */
            $departmentMgr = Globals::$g->gManagerFactory->getManager('department');
            $recordNo = $departmentMgr->GetRecordNo('DEPARTMENTID', $arr[0]);

            if (isset($recordNo)) {
                $values['DEPTKEY'] = $recordNo;
            } else if (!isArrayValueTrue($values, ':isCancelAction')) {
                // Throw this error if not a cancel action.
                $ok = false;
            }
        } else if (hasHiddenDepartment()) {
            $ok = false;
        }

        if (!$ok) {
            $msg = 'Department is not valid.';
            $corr = 'Pick a valid Department ID';
            Globals::$g->gErr->addError('CN-0654', __FILE__ . ':' . __LINE__, $msg, '', $corr);
        }
        return $ok;
    }

    /**
     * Confirms the valid selection for Item default fields (templates and MRR).
     *
     * @param array $values
     * @param array $itemDefaultsMap
     *
     * @return bool
     * @throws Exception
     */
    public function translateItemDefaults(& $values, $itemDefaultsMap)
    {
        $ok = true;
        
        $itemDefaultsSelectFields = array();
        foreach ( $itemDefaultsMap as $itemDefaults ) {
            if ( !array_key_exists($itemDefaults['field-name'], $values) ) {
                $itemDefaultsSelectFields[] = $itemDefaults['itemdefault-field'];
            }
        }

        if ( !empty($itemDefaultsSelectFields) ) {
            $filter = ['ITEMID' => $values['ITEMID']];
            $defaultTemplates = EntityManager::GetListQuick('item', $itemDefaultsSelectFields, $filter);
            $defaultTemplates = $defaultTemplates[0];
            foreach ( $itemDefaultsMap as $itemDefaults ) {
                if ( isArrayValueProvided($defaultTemplates, $itemDefaults['itemdefault-field']) ) {
                    $values[$itemDefaults['field-name']] = $defaultTemplates[$itemDefaults['itemdefault-field']];
                }
            }
        }

        foreach ($itemDefaultsMap as $itemDefaults) {
            if (isArrayValueProvided($values, $itemDefaults['field-name']) && $itemDefaults['entity'] !== null) {
                $recordNo = null;
                if (is_string($values[$itemDefaults['field-name']])) {
                    $arr = explode('--', $values[$itemDefaults['field-name']]);
                    /* @var ContractRevenueTemplateManager|ContractBillingTemplateManager|ContractExpenseTemplateManager $manager */
                    $manager = Globals::$g->gManagerFactory->getManager($itemDefaults['entity']);
                    if (($itemDefaults['field-name'] == 'REVENUETEMPLATENAME' || $itemDefaults['field-name'] == 'REVENUE2TEMPLATENAME')
                        && ContractDetailManager::isRevRecOnInvoice($values)
                    ) {
                        $arr[0] = ContractUtil::CONTRACT_REVREC_ON_INVOICE_TEMPLATE;
                    }
                    $recordNo = $manager->GetRecordNo('NAME', $arr[0]);
                }
                
                if (isset($recordNo)) {
                    $values[$itemDefaults['field-key']] = $recordNo;
                } else {
                    $msg = $itemDefaults['field-label'] . ' is not valid.';
                    $corr = 'Provide a valid ' . $itemDefaults['field-label'] . '.';
                    Globals::$g->gErr->addIAError('CN-0655', __FILE__ . ':' . __LINE__, $msg,
                                                  ['FIELD_LABEL' => $itemDefaults['field-label']], '', [],
                                                  $corr, ['FIELD_LABEL' => $itemDefaults['field-label']]);
                    $ok = false;
                }
            }else{
                $values[$itemDefaults['field-key']] = '';
            }
        }
        return $ok;
    }

    /**
     * resolve usage data to a contract detail record key
     *
     * @param string[] &$values
     * @param string[] $selectFields
     * @param string   $evgUsageOption  Filter for Evergreen quantity based billing based on Usage Creation Option
     *
     * @return array|array[]
     * @noinspection UnsupportedStringOffsetOperationsInspection
     */
    public function resolveToContractDetailKey (&$values, $selectFields, $evgUsageOption = '')
    {
        $cnDetMgr = Globals::$g->gManagerFactory->getManager('contractdetail');

        // build contract filter including renewed contracts in the logic
        $contractFilter = array (
            'operator' => 'or',
            'filters' => array(
                array('CONTRACT.CONTRACTID', '=', $values['CONTRACTID']),
// If we ever want to include the parent ID as part of the contractID filter try this
//                array(
//                    'RECORDNO',
//                    'CASE_CONDITION',
//                    array("exists (select 1 from contract pcn where pcn.cny# = contract.cny# and pcn.record# = contract.parentkey and pcn.contractid = '$values[CONTRACTID]') ")
//                ),
            ),
        );

        $excludeStates = [ ContractDetailState::STATE_RENEWAL_FORECAST ];
        $filterForEvgUsageAddToApplicableLine = $evgUsageOption === CNSetupManager::EVG_USAGE_OPTION_ADD_TO_APPLICABLE_CLOSED_LINE;
        $filterForEvgUsageAddToInProgressLine = $evgUsageOption === CNSetupManager::EVG_USAGE_OPTION_ADD_TO_IN_PROGRESS_LINE;
        if ( !$filterForEvgUsageAddToApplicableLine  ) {
            $excludeStates[] = ContractDetailState::STATE_CLOSED;
        } else {
            if (!empty($values['CONTRACTDETAILKEY'])) {
                $selectFields[] = '(select 1 from contractdetail where cny# = '.GetMyCompany().
                                  ' and renewedcontractdetailkey = '.$values['CONTRACTDETAILKEY'].') as ISRENEWED';
            }
        }
        $querySpec['selects'] = $selectFields;

        // translate contractid and detaillineno
        //$values['CONTRACTLINENO'] = '';
        if ( isArrayValueProvided($values, 'CONTRACTDETAILKEY') ) {

//            $querySpec['filters'][0][] = $contractFilter;
            $querySpec['filters'][0][] = array('RECORDNO', '=', $values['CONTRACTDETAILKEY']);

        } else if ( isArrayValueProvided($values, 'CONTRACTID') && isArrayValueProvided($values, 'CONTRACTLINENO') ) {

            $querySpec['filters'][0][] = $contractFilter;
            $querySpec['filters'][0][] = array('LINENO', '=', $values['CONTRACTLINENO']);
            $querySpec['filters'][0][] = ['STATE', 'NOT IN', $excludeStates];

        } else {

            /***
             * if we are not given specific contractdetailkey or contractdetail_lineno
             * then we need to do some matching logic to figure the contractdetail record
             *
             */

            // collect fields list to form a unique key to search in the incoming values array
            $searchFields = array(
                'CONTRACTID',
                'ITEMID',
                'LOCATIONID',
                'DEPARTMENTID',
            );
            $cnDetailFields = $cnDetMgr->GetDimensionFields();

            foreach ( $cnDetailFields as $row ) {
                $searchFields[] = $row['path'];
            }

            // now make these as filters for the query
            foreach ( $searchFields as $fld ) {
                if ( isArrayValueProvided($values, $fld) ) {
                    if ( $fld === 'CONTRACTID' ) {
                        $querySpec['filters'][0][] = $contractFilter;
                    } else {
                        $querySpec['filters'][0][] = array($fld, '=', $values[$fld]);
                    }
                }
            }

            $querySpec['selects'] = INTACCTarray_merge($querySpec['selects'], $searchFields);
            $querySpec['filters'][0][] = ['STATE', 'NOT IN', $excludeStates];
        }

        if ( $filterForEvgUsageAddToApplicableLine && !empty($values['USAGEDATE']) && empty($values['CONTRACTDETAILKEY']) ) {
            $querySpec2 = $querySpec;
            $querySpec['filters'][0][] = ['BEGINDATE', '<=', $values['USAGEDATE']];
            $querySpec['filters'][0][] = ['ENDDATE', '>=', $values['USAGEDATE']];
        }

        $cnDetails = $cnDetMgr->GetList($querySpec);

        if ( $filterForEvgUsageAddToApplicableLine && empty($cnDetails)  && isset($querySpec2) ) {
            // if no matching period is found, find the max period
            $querySpec3 = $querySpec2;
            $querySpec2['selects'] = ['MAX(contractdetail.PERIOD) as MAXPERIOD'];
            $querySpec2['groupby'] = ['LINENO'];
            $maxPeriodResult = $cnDetMgr->GetList($querySpec2);
            if (!empty($maxPeriodResult) && count($maxPeriodResult) === 1) {
                // if exactly one line is found (i.e. correct filter), return the max period line
                $querySpec3['filters'][0][] = ['PERIOD', '=', $maxPeriodResult[0]['MAXPERIOD']];
                $cnDetails = $cnDetMgr->GetList($querySpec3);
            }
        }

        $inProgressCnDetails = [];
        $lowPrecedenceStates = [ ContractManager::STATE_IN_CANCELLED ];
        if ($filterForEvgUsageAddToInProgressLine || empty($evgUsageOption)) {
            $lowPrecedenceStates[] = ContractDetailState::STATE_RENEWALONLY;
        }
        foreach ($cnDetails as $cnDetail) {
            if ( !in_array($cnDetail['STATE'], $lowPrecedenceStates) ) {
                $inProgressCnDetails[] = $cnDetail;
            }
        }

        // If there is a mix between In progress and non-In progress details, just return the In progress ones -- they take precedence
        // Otherwise, just return the non-In progress ones
        if (!empty($inProgressCnDetails)) {
            $cnDetails = $inProgressCnDetails;
        }
        return $cnDetails;
    }

    /**
     * Save project (and its predecessors that are billed with the project) with this contract ID
     *
     * @param IContractLineManager $mgr
     * @param array $values
     *
     * @return bool
     */
    public static function handleProject($mgr, &$values)
    {
        try {
            $ok = self::clearProjectLinkingIfNecessary($mgr, $values, $mgr->getOldValues());
        } catch (IAException $excp) {
            // TOOO: i18n - multiple possible messages
            Globals::$g->gErr->addError(ContractUtil::GENERIC_BL_SAVE_ERROR, __FILE__ . ':' . __LINE__, $excp->__toString());
            $ok = false;
        }
        if ($ok) {
            $projectObj = self::getExistingProjectObjFromValues($values);
            if ($projectObj !== null) {
                if (!isArrayValueProvided($projectObj, 'CONTRACTID')) {
                    $projMgr = Globals::$g->gManagerFactory->getManager('project');
                    $ok = $projMgr->setContractId($projectObj, $values['CONTRACTID']);
                }
            }
        }

        return $ok;
    }

    /**
     * @param IContractLineManager  $mgr
     * @param array                 $values
     * @param array                 $oldValues
     * 
     * @return bool
     * @throws IAException
     */
    public static function clearProjectLinkingIfNecessary($mgr, $values, $oldValues)
    {
        $ok = true;

        if (isArrayValueProvided($values, 'RECORDNO')) {

            // For existing detail, if the PROJECT is changed or removed, AND there is no other details using this project, remove contract from project
            if (isArrayValueProvided($oldValues, 'PROJECTDIMKEY')) {
                $oldProjectKey = $oldValues['PROJECTDIMKEY'];

                if (!isArrayValueProvided($values, 'PROJECTDIMKEY') || $values['PROJECTDIMKEY'] != $oldProjectKey) {

                    // Project dimension is cleared or changed.  Need to check other details/expenses that might have this project
                    $contractKey = $values['CONTRACTKEY'];
                    $recordNo = $values['RECORDNO'];
                    $notRecordNo = $mgr->getEntity() == 'contractdetail' ? $recordNo : 0;
                    $details = self::queryContractProject('contractdetail', $contractKey, $oldProjectKey, $notRecordNo);
                    if (!isset($details[0]['RECORDNO'])) {
                        $notRecordNo = $mgr->getEntity() == 'contractexpense' ? $recordNo : 0;
                        $expenses = self::queryContractProject('contractexpense', $contractKey, $oldProjectKey, $notRecordNo);
                        if (!isset($expenses[0]['RECORDNO'])) {
                            // There is no other detail/expense refers to this project, clear the project linking
                            $projMgr = Globals::$g->gManagerFactory->getManager('project');
                            $oldProjectObj = $projMgr->GetByRecordNo($oldProjectKey);
                            if (!isArrayValueProvided($oldProjectObj, 'PROJECTID')) {
                                // Should not happen really, since there is a fkey constraint
                                throw IAException::newIAException('CN-1013', 'Unable to retrieve project with record#: 
                                ' . $oldProjectKey, ['OLD_PROJECT_KEY' => $oldProjectKey]);
                            }
                            $oldProjectId = $oldProjectObj['PROJECTID'];
                            if (!isArrayValueProvided($oldProjectObj, 'CONTRACTID')) {
                                // Should not happen, since this project MUST be associated with this contract
                                throw IAException::newIAException('CN-1014', "About to clear the Contract ID of Project 
                                $oldProjectId. However it is already empty", ['OLD_PROJECT_ID' => $oldProjectId]);
                            }
                            if ($oldProjectObj['CONTRACTID'] != $values['CONTRACTID']) {
                                // Should not happen, since this project MUST be associated with this contract
                                throw IAException::newIAException('CN-1016', "About to clear the Contract ID of Project
                                 $oldProjectId. " .
                                    "However it is associated with Contract {$oldProjectObj['CONTRACTID']} instead of 
                                    {$values['CONTRACTID']}",
                                    [
                                        'OLD_PROJECT_ID' => $oldProjectId, '
                                         OLD_PROJECT_OBJ_CONTRACTID' => $oldProjectObj['CONTRACTID'],
                                        'CONTRACTID' => $values['CONTRACTID']
                                    ]);
                            }
                            $ok = $projMgr->setContractId($oldProjectObj, '');
                        }
                    }
                }
            }

        }
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return array|null
     * @throws IAException
     */
    public static function getExistingProjectObjFromValues(&$values)
    {
        $projectObj = null;
        if (!isArrayValueProvided($values, ':projectObj')) {
            if (isArrayValueProvided($values, 'PROJECTID')) {
                $projectId = $values['PROJECTID'];
                /** @var ProjectManager $mgr */
                $mgr = Globals::$g->gManagerFactory->getManager('project');
                $projectObj = $values[':projectObj'] = $mgr->get($projectId);
                if (!isArrayValueProvided($projectObj, 'PROJECTID')) {
                    // Should not happen really, since this should be called after translateDimensions which validates the project ID
                    throw IAException::newIAException('CN-1017', 'Unable to retrieve project with ID: ' . $projectId, ['PROJECT_ID' => $projectId]);
                }
            }
        } else {
            $projectObj = $values[':projectObj'];
        }

        return $projectObj;
    }

    /**
     * Confirms that the valid item is selected for the mandatory ITEMID field.
     * @param array $values
     *
     * @return bool
     * @throws Exception
     */
    public function translateUsageLineType(&$values)
    {
        $ok = true;
        if ($values['BILLINGMETHOD'] === ContractBillingMethodTypes::CONTRACT_BILLING_METHOD_QTY_BASED) {
            if (!isArrayValueProvided($values, 'USAGELINETYPE')) {
                // this is the default value of usage line type, if from api this value is not coming then default will assigned
                // Also force it to Variable for Rec on Invoice line
                $values['USAGELINETYPE'] = ContractBillingMethodTypes::CONTRACT_QTY_BILLING_TYPE_VARIABLE;
            }
            if (!isArrayValueProvided($values, 'COMMITTEDUSAGEEXCESS') && ContractBillingMethodTypes::isCommittedQuantityType($values['USAGELINETYPE'])) {
                // this is the default value of USAGECONSUMPEXCESS, if from api this value is not coming then default will assigned
                $values['COMMITTEDUSAGEEXCESS'] = ContractCommittedQuantityExcessPolicy::BILL_ON_EXCESS_USAGE;
            }
            if (ContractDetailManager::isRevRecOnInvoice($values) && !ContractBillingMethodTypes::isVariableQuantityType($values['USAGELINETYPE'])) {
                Globals::$g->gErr->addIAError('CN-0679',__FILE__ . ':' . __LINE__);
                $ok = false;
            }
        } else {
            unset($values['USAGELINETYPE']);
        }

        return $ok;
    }
}

interface IContractLineManager
{
    public function getEntity();
    public function getOldValues();
}
