openapi: 3.0.0
info:
  title: construction-payroll-employee-tax-classification
  description: construction.payroll-employee-tax-classification API
  version: '1.0'
  contact:
    name: <PERSON><PERSON><PERSON><PERSON>
    email: karth<PERSON><PERSON>.<EMAIL>
tags:
  - name: Payroll employee tax classification
    description: Construction payroll employee tax classification object used for reporting.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/construction/payroll-employee-tax-classification:
    get:
      summary: List payroll employee tax classifications
      description: 'Returns a collection with a key, ID, and link for each payroll employee tax classification.'
      tags:
        - Payroll employee tax classification
      operationId: list-construction-payroll-employee-tax-classification
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of payroll-employee-tax-classification objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of payroll employee tax classification:
                  value:
                    'ia::result':
                      - key: '3'
                        id: '3'
                        href: /objects/construction/payroll-employee-tax-classification/3
                      - key: '5'
                        id: '5'
                        href: /objects/construction/payroll-employee-tax-classification/5
                      - key: '18'
                        id: '18'
                        href: /objects/construction/payroll-employee-tax-classification/18
                      - key: '19'
                        id: '19'
                        href: /objects/construction/payroll-employee-tax-classification/19
                      - key: '21'
                        id: '21'
                        href: /objects/construction/payroll-employee-tax-classification/21
                      - key: '25'
                        id: '25'
                        href: /objects/construction/payroll-employee-tax-classification/25
                    'ia::meta':
                      totalCount: 6
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
    post:
      summary: Create a payroll employee tax classification
      description: Creates a new payroll employee tax classification.
      tags:
        - Payroll employee tax classification
      operationId: create-construction-payroll-employee-tax-classification
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/construction-payroll-employee-tax-classification'
                - $ref: '#/components/schemas/construction-payroll-employee-tax-classificationRequiredProperties'
            examples:
              Creates a payroll employee tax classification:
                value:
                  payrollEmployee:
                    key: '49'
                  taxId: IL-SIT
                  taxCategoryId: SIT
                  classificationCode: SG
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                title: New payroll-employee-tax-classification
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                New payroll employee tax classification:
                  value:
                    'ia::result':
                      id: '25'
                      key: '25'
                      href: /objects/construction/payroll-employee-tax-classification/25
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
  '/objects/construction/payroll-employee-tax-classification/{key}':
    parameters:
      - name: key
        description: System-assigned unique key for the payroll employee tax classification.
        in: path
        required: true
        schema:
          type: string
        example: '25'
    get:
      summary: Get a payroll employee tax classification
      description: Returns detailed information for a specified payroll employee tax classification.
      tags:
        - Payroll employee tax classification
      operationId: get-construction-payroll-employee-tax-classification-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the payroll-employee-tax-classification
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/construction-payroll-employee-tax-classification'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the payroll employee tax classification:
                  value:
                    'ia::result':
                      id: '25'
                      key: '25'
                      payrollEmployee:
                        id: '49'
                        key: '49'
                        href: /objects/construction/payroll-employee/49
                      taxId: IL-SIT
                      taxCategoryId: SIT
                      classificationCode: SG
                      audit:
                        createdDateTime: '2025-02-12T07:59:23Z'
                        modifiedDateTime: '2025-02-12T07:59:23Z'
                        createdByUser:
                          key: '134'
                          href: /objects/company-config/user/134
                        modifiedByUser:
                          key: '134'
                          href: /objects/company-config/user/134
                      href: /objects/construction/payroll-employee-tax-classification/25
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update a payroll employee tax classification
      description: Updates an existing payroll employee tax classification by setting field values. Any fields not provided remain unchanged.
      tags:
        - Payroll employee tax classification
      operationId: update-construction-payroll-employee-tax-classification-key
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/construction-payroll-employee-tax-classification'
                - type: object
                  properties:
                    payrollEmployee:
                      readOnly: true
            examples:
              Updates a payroll employee tax classification:
                value:
                  taxId: IL-SITE-PA
                  taxCategoryId: PA-EIT
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated payroll-employee-tax-classification
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Updated payroll employee tax classification:
                  value:
                    'ia::result':
                      id: '25'
                      key: '25'
                      href: /objects/construction/payroll-employee-tax-classification/25
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete a payroll employee tax classification
      description: Deletes a payroll employee tax classification.
      tags:
        - Payroll employee tax classification
      operationId: delete-construction-payroll-employee-tax-classification-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    construction-payroll-employee-tax-classification:
      $ref: ../models/construction.payroll-employee-tax-classification.s1.schema.yaml
    construction-payroll-employee-tax-classificationRequiredProperties:
      type: object
      required:
        - payrollEmployee
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml