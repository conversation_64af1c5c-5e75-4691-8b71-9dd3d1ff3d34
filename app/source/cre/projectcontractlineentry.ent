<?php

/**
 * Entity for the Project Contract Line Entry object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2021 Sage Intacct Inc.
 *
 * This document contains trade secret data that belongs to Sage Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

global $gRecordNoFormat, $gCurrencyFormatTenDec, $gDecimalFormat, $gDateType, $gEstimateWFType;
global $gWhenCreatedFieldInfo, $gWhenModifiedFieldInfo, $gCreatedByFieldInfo, $gModifiedByFieldInfo;

$kSchemas['projectcontractlineentry'] = array(

    'children' => array(
        'projectcontractline' => array(
            'fkey' => 'pclkey',
            'invfkey' => 'record#',
            'table' => 'projectcontractline',
            'join' => 'inner'
        ),
        'location' => array(
            'fkey' => 'locationkey',
            'invfkey' => 'record#',
            'table' => 'locationmst',
            'join' => 'outer'
        ),
        'department' => array(
            'fkey' => 'deptkey',
            'invfkey' => 'record#',
            'table' => 'departmentmst',
            'join' => 'outer'
        ),
        'project' => array(
            'fkey' => 'projectdimkey',
            'invfkey' => 'record#',
            'table' => 'project',
            'join' => 'outer'
        ),
        'task' => array(
            'fkey' => 'taskdimkey',
            'invfkey' => 'record#',
            'table' => 'task',
            'join' => 'outer'
        ),
        'pjestimate' => array(
            'fkey' => 'projectdimkey',
            'invfkey' => 'projectkey',
            'table' => 'pjestimatemst',
            'join' => 'outer',
            'filter' => " pjestimate.isprimary(+) = 'T' ",
        ),
    ),
    'object' => array(
        'RECORDNO',
        'PROJECTCONTRACTLINEKEY',
        'PROJECTCONTRACTLINEID',
        'RECORDTYPE',
        'WFTYPE',
        'LOCATIONKEY',
        'LOCATIONID',
        'LOCATIONNAME',
        'DEPARTMENTKEY',
        'DEPARTMENTID',
        'DEPARTMENTNAME',
        'QTY',
        'EUOM',
        'UNITPRICE',
        'PRICE',
        'PRICEMARKUPPERCENT',
        'PRICEMARKUPAMOUNT',
        'LINEPRICE',
        'MEMO',
        'PRICEEFFECTIVEDATE',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'PROJECTCONTRACTLINEKEY' => 'pclkey',
        'PROJECTCONTRACTLINEID' => 'projectcontractline.projectcontractlineid',
        'RECORDTYPE' => 'recordtype',
        'WFTYPE' => 'wftype',
        'LOCATIONKEY' => 'locationkey',
        'LOCATIONID' => 'location.location_no',
        'LOCATIONNAME' => 'location.name',
        'DEPARTMENTKEY' => 'deptkey',
        'DEPARTMENTID' => 'department.dept_no',
        'DEPARTMENTNAME' => 'department.title',
        'QTY' => 'qty',
        'EUOM' => 'euom',
        'UNITPRICE' => 'unitprice',
        'PRICE' => 'price',
        'PRICEMARKUPPERCENT' => 'pricemarkup_percent',
        'PRICEMARKUPAMOUNT' => 'pricemarkup_amount',
        'LINEPRICE' => 'lineprice',
        'MEMO' => 'memo',
        'PRICEEFFECTIVEDATE' => 'pricedate',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
    ),
    'nexus' => array(
        'projectcontractline' => array(
            'object' => 'projectcontractline',
            'relation' => ONE2MANY,
            'field' => 'PROJECTCONTRACTLINEKEY'
        ),
        'location' => array(
            'object' => 'location',
            'relation' => ONE2MANY,
            'field' => 'LOCATIONID',
            'dbalias' => 'location',
        ),
        'department' => array(
            'object' => 'department',
            'relation' => ONE2MANY,
            'field' => 'DEPARTMENTID',
            'dbalias' => 'department',
        ),
    ),
    'publish' => array(
        'PROJECTCONTRACTLINEKEY',
        'PROJECTCONTRACTLINEID',
        'RECORDTYPE',
        'WFTYPE',
        'LOCATIONID',
        'LOCATIONNAME',
        'DEPARTMENTID',
        'DEPARTMENTNAME',
        'QTY',
        'EUOM',
        'UNITPRICE',
        'PRICE',
        'PRICEMARKUPPERCENT',
        'PRICEMARKUPAMOUNT',
        'LINEPRICE',
        'MEMO',
        'PRICEEFFECTIVEDATE',
        'WHENMODIFIED',
        'WHENCREATED',
        'MODIFIEDBY',
        'CREATEDBY',
    ),

    'sqldomarkup' => true,

    'sqlmarkupfields' => array(
        'WHENMODIFIED',
        'WHENCREATED'
    ),

    'customerp' => [
        'SLTypes' => [
            CUSTOMERP_SMARTLINKCLICK,
            CUSTOMERP_SMARTLINKVALIDATE,
            CUSTOMERP_SMARTLINKFETCH,
            CUSTOMERP_SMARTLINKWORKFLOW
        ],
        'SLEvents' => [
            CUSTOMERP_EVENT_ADD,
            CUSTOMERP_EVENT_SET,
            CUSTOMERP_EVENT_ADDSET,
            CUSTOMERP_EVENT_DELETE,
            CUSTOMERP_EVENT_CLICK
        ],
        'AllowCF' => true,
    ],

    'auditcolumns' => true,

    'module' => 'pa',

    'autoincrement' => 'RECORDNO',

    'description' => 'IA.ENTRY_FOR_A_PROJECT_CONTRACT_LINE_DESC',
    'printas' => 'IA.PROJECT_CONTRACT_LINE_ENTRY',
    'pluralprintas' => 'IA.PROJECT_CONTRACT_LINE_ENTRIES',
    'upsertEntries' => true,

    'vid' => 'RECORDNO',

    'table' => 'crdetail',

    'renameable' => true,

    'parententity' => 'projectcontractline',

    'allowDDS' => true,

    'pairedFields' => array(
        'LOCATIONID' => 'LOCATIONNAME',
        'DEPARTMENTID' => 'DEPARTMENTNAME',
    ),

    'primaryfield' => 'PRICEEFFECTIVEDATE',

    'hasdimensions' => true,
    'followgldimensions' => false,

    'api' => array(
        'GET_BY_GET' => true,
        'PERMISSION_MODULES' => array('pa'),
        'PERMISSION_READ' => 'lists/projectcontractlineentry/view',
        'PERMISSION_CREATE' => 'NONE',
        'PERMISSION_UPDATE' => 'NONE',
        'PERMISSION_DELETE' => 'lists/projectcontractlineentry/delete',
    ),

    'dbfilters' => [
        ['recordtype', '=', 'cl'],
    ],

    'fieldinfo' => array(
        array(
            'path' => 'RECORDNO',
            'fullname' => 'IA.RECORD_NUMBER',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 15,
                'format' => $gRecordNoFormat
            ),
            'hidden' => true,
            'readonly' => true,
            'desc' => 'IA.RECORD_NUMBER',
            'noapiadd' => true,
            'id' => 1,
        ),
        array(
            'path' => 'PROJECTCONTRACTLINEKEY',
            'fullname' => 'IA.PROJECT_CONTRACT_LINE_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 15,
                'format' => $gRecordNoFormat
            ),
            'renameable' => true,
            'derived' => true,
            'readonly' => true,
            'desc' => 'IA.PROJECT_CONTRACT_LINE_KEY',
            'noapiadd' => true,
            'noapiset' => true,
            'id' => 2,
        ),
        array(
            'path' => 'PROJECTCONTRACTLINEID',
            'fullname' => 'IA.PROJECT_CONTRACT_LINE_ID',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 40,
            ),
            'renameable' => true,
            'desc' => 'IA.PROJECT_CONTRACT_LINE_ID',
            'noapiadd' => true,
            'noapiset' => true,
            'id' => 3,
        ),
        array(
            'path' => 'RECORDTYPE',
            'fullname' => 'IA.RECORD_TYPE',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'size' => 2
            ),
            'default' => 'cl',
            'readonly' => true,
            'derived' => true,
            'noapiadd' => true,
            'noapiset' => true,
            'hidden' => true,
            'desc' => 'IA.RECORD_TYPE',
            'id' => 201,
        ),
        array(
            'path' => 'WFTYPE',
            'fullname' => 'IA.WORKFLOW_TYPE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'enum',
                'validlabels' => array_values(array_filter($gEstimateWFType['validlabels'], function ($value) {
                    $excludeList = ['IA.APPROVED_CHANGE', 'IA.PENDING_CHANGE'];
                    return !in_array($value, $excludeList, true);
                })),
                'validvalues' => array_values(array_filter($gEstimateWFType['validvalues'], function ($value) {
                    $excludeList = ['approved change', 'pending change'];
                    return !in_array($value, $excludeList, true);
                })),
                '_validivalues' => array_values(array_filter($gEstimateWFType['_validivalues'], function ($value) {
                    $excludeList = ['A', 'P'];
                    return !in_array($value, $excludeList, true);
                })),
            ),
            'required' => true,
            'default' => 'original',
            'readonly' => false,
            'desc' => 'IA.WORKFLOW_TYPE',
            'id' => 4,
        ),
        array(
            'path' => 'LOCATIONKEY',
            'fullname' => 'IA.LOCATION_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 15,
                'format' => $gRecordNoFormat
            ),
            'renameable' => true,
            'derived' => true,
            'hidden' => true,
            'readonly' => true,
            'desc' => 'IA.LOCATION_KEY',
            'noapiadd' => true,
            'noapiset' => true,
            'id' => 5,
        ),
        array(
            'path' => 'LOCATIONID',
            'fullname' => 'IA.LOCATION',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'location',
                'maxlength' => 20,
            ),
            'renameable' => true,
            'desc' => 'IA.LOCATION',
            'noapiadd' => true,
            'noapiset' => true,
            'id' => 6,
        ),
        array(
            'path' => 'LOCATIONNAME',
            'fullname' => 'IA.LOCATION_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 40,
            ),
            'renameable' => true,
            'readonly' => true,
            'derived' => true,
            'desc' => 'IA.LOCATION_NAME',
            'noapiadd' => true,
            'noapiset' => true,
            'id' => 7,
        ),
        array(
            'path' => 'DEPARTMENTKEY',
            'fullname' => 'IA.DEPARTMENT_KEY',
            'type' => array(
                'ptype' => 'integer',
                'type' => 'integer',
                'maxlength' => 15,
                'format' => $gRecordNoFormat
            ),
            'renameable' => true,
            'derived' => true,
            'hidden' => true,
            'readonly' => true,
            'desc' => 'IA.DEPARTMENT_KEY',
            'noapiadd' => true,
            'noapiset' => true,
            'id' => 11,
        ),
        array(
            'path' => 'DEPARTMENTID',
            'fullname' => 'IA.DEPARTMENT',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'text',
                'entity' => 'departmentpick',
                'maxlength' => 20,
            ),
            'renameable' => true,
            'desc' => 'IA.DEPARTMENT',
            'id' => 12,
        ),
        array(
            'path' => 'DEPARTMENTNAME',
            'fullname' => 'IA.DEPARTMENT_NAME',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 40,
            ),
            'renameable' => true,
            'readonly' => true,
            'derived' => true,
            'desc' => 'IA.DEPARTMENT_NAME',
            'noapiadd' => true,
            'noapiset' => true,
            'id' => 13,
        ),
        array(
            'path' => 'QTY',
            'fullname' => 'IA.QUANTITY',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 30,
                'size' => 30,
            ),
            'desc' => 'IA.QUANTITY',
            'noformat' => true,
            'id' => 16,
        ),
        array(
            'path' => 'EUOM',
            'fullname' => 'IA.EXTERNAL_UOM',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 200,
            ),
            'desc' => 'IA.EXTERNAL_UOM',
            'id' => 17,
        ),
        array(
            'path' => 'UNITPRICE',
            'fullname' => 'IA.UNIT_PRICE',
            'type' => array(
                'ptype' => 'currency',
                'type' => 'decimal',
                'maxlength' => 30,
                'format' => $gCurrencyFormatTenDec,
                'size' => 30,
            ),
            'desc' => 'IA.UNIT_PRICE',
            'id' => 20,
        ),
        array(
            'path' => 'PRICE',
            'fullname' => 'IA.PRICE',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18
            ),
            'desc' => 'IA.PRICE',
            'id' => 21,
        ),
        array(
            'path' => 'PRICEMARKUPPERCENT',
            'fullname' => 'IA.PRICE_MARKUP_PERCENT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 5,
                'format' => $gDecimalFormat,
                'size' => 9
            ),
            'desc' => 'IA.PRICE_MARKUP_PERCENT',
            'id' => 22,
        ),
        array(
            'path' => 'PRICEMARKUPAMOUNT',
            'fullname' => 'IA.PRICE_MARKUP_AMOUNT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18
            ),
            'desc' => 'IA.PRICE_MARKUP_AMOUNT',
            'id' => 23,
        ),
        array(
            'path' => 'LINEPRICE',
            'fullname' => 'IA.LINE_PRICE',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18
            ),
            'readonly' => true,
            'desc' => 'IA.LINE_PRICE',
            'noapiadd' => true,
            'noapiset' => true,
            'id' => 24,
        ),
        array(
            'path' => 'MEMO',
            'fullname' => 'IA.MEMO',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 2000,
            ),
            'desc' => 'IA.MEMO',
            'id' => 25,
        ),
        array(
            'path' => 'PRICEEFFECTIVEDATE',
            'fullname' => 'IA.PRICE_EFFECTIVE_DATE',
            'type' => $gDateType,
            'desc' => 'IA.PRICE_EFFECTIVE_DATE',
            'required' => true,
            'id' => 29,
        ),
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
    ),
);


