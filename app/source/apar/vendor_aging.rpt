<?
/**
*    FILE:            vendor_aging.rpt
*    AUTHOR:            rpn
*    DESCRIPTION:    rpt file for vendor aging filter
*
*    (C) 2000, Intacct Corporation, All Rights Reserved
*
*    This document contains trade secret data that belongs to Intacct
*    Corporation and is protected by the copyright laws.  Information
*    herein may not be used, copied or disclosed in whole or in part
*    without prior written consent from Intacct Corporation.
*/

$where="MODULEKEY='3.AP' and property like 'AGP%' and value is not null ";
$order='PROPERTY asc';
$_reportingBook = Request::$r->_reportingBook;
//$ageperiodMap = GetObjectMap('modulepref', 'property', 'value', $where, $order);
// changed the above map creation so as to make the drop down list box in the wizard to have
// value and text as same
$ageperiodMap = GetObjectMap('modulepref', 'value', 'value', $where, $order);
if (empty($ageperiodMap)) {
    $ageperiodMap['0-30,31-60,61-90,91-120'] = '0-30,31-60,61-90,91-120';
}

$agingperiodlabels = [];
$agingperiodvalues = [];

foreach($ageperiodMap  as $key => $value) {
    $agingperiodlabels[] = $key;
    $agingperiodvalues[] = $value;
}

$basedondatelabels = [ 'IA.BILL_DATE', 'IA.DUE_DATE', ];
$basedondatevalues = [ 'Invoicedate', 'Duedate', ];

$sortbyvalues = [ 'VENDORID', 'NAME', 'AMOUNT', 'AMOUNT_DESC' ];
$sortbylabels = [ 'IA.VENDOR_ID', 'IA.VENDOR_NAME', 'IA.AMOUNT', 'IA.AMOUNT_DESC' ];

$groupbyvalues = [ 'None', 'VENDORID', 'NAME', 'VENDTYPE', 'PAYMENTPRIORITY', 'DUEDATE', 'GLPOSTDATE' ];
$groupbylabels = [
    'IA.NONE', 'IA.VENDOR_ID', 'IA.VENDOR_NAME', 'IA.VENDOR_TYPE', 'IA.PAYMENT_PRIORITY', 'IA.DUE_DATE',
    'IA.GL_POSTING_DATE'
];

if(IsCompanyAccrual() || IsOperatingMultipleBooks()) {
    $basedondatelabels[] = 'IA.GL_POSTING_DATE';
    $basedondatevalues[] = 'GLPostdate';

    $sortbylabels[] = 'IA.GL_POSTING_DATE';
    $sortbyvalues[] = 'GLPOSTDATE';

    $sortbylabels[] = 'IA.GL_POSTING_DATE_DESC';
    $sortbyvalues[] = 'GLPOSTDATE_DESC';
}

$defexchtype = '';

/**
 * @var ExchangeRateTypesManager $exchMgr     WI-38714
 */
$exchMgr = Globals::$g->gManagerFactory->getManager('exchangeratetypes');
$exchtype = $exchMgr->GetDefaultExchangeRateType(true);
$defexchtype = $exchtype[0]['NAME'];

$mcmesubscribed = IsMCMESubscribed();

$ctx = GetContextLocation();

$kSchemas["vendor_aging"] = [
    'schema' => [
        [
            'REPORTASOF' => 'IA.REPORT_AS_OF',
            'BASEDON' => 'IA.BASED_ON',
            'FROMVENDORID' => 'IA.FROM_VENDOR',
            'TOVENDORID' => 'IA.TO_VENDOR',
            'AGINGPERIODS' => 'IA.AGING_PERIODS',
            'SORTBY' => 'IA.SORT_BY',
            'REPORTTYPE' => 'IA.REPORT_TYPE',
            'SHOWVENDORSWITHZEROBALANCE' => 'IA.SHOW_VENDORS_WITH_ZERO_BALANCE',
            'SHOWPAIDINVOUTSTANDINGRETAINAGE' => 'IA.SHOW_FULLY_PAID_BILLS_WITH_OUTSTANDING',
        ]
    ],
    'individualreport' => [
        'LOCATION' => [ 'default' => false ],
        'DEPARTMENT' => [ 'default' => false ],
    ],
    'promptonrun' => [
        'LOCATION' => [ 'default' => false ],
        'DEPARTMENT' => [ 'default' => false ],
    ],
    'fieldgroup' => [
        'FIELDGRPRANGE' => [
            'fields' => [
                [
                    'fullname' => '',
                    'type' => [
                        'ptype' => 'radio',
                        'type' => 'radio',
                        'validlabels' => ['IA.RANGE'],
                        'validvalues' => ['Range'],
                    ],
                    'path' => 'MULTIPLEFILTERGRPTYPE',
                    'nolabel' => true,
                    'default' => 'Range',
                    'onchange_js' => "handleMultiGroupChange(this);",
                ],
                [
                    'fullname' => 'IA.FROM_VENDOR',
                    'type' => [
                        'ptype' => 'ptr',
                        'type' => 'ptr',
                        'entity' => 'vendor',
                        'addlPickFields' => ['NAME'],
                        'maxlength' => 20,
                        'format' => $gEntityIDFormat,
                    ],
                    'desc' => 'IA.FROMVENDORID',
                    'path' => 'FROMVENDORID',
                    'noedit' => true,
                    'nonew' => true,
                    'noview' => true,
                    'renameable' => true,
                    'onchange' => "SetToValue(this, 'TOVENDORID'); handleMultiGroupChange(this);",
                    'id' => 930
                ],
                [
                    'fullname' => 'IA.TO_VENDOR',
                    'type' => [
                        'ptype' => 'ptr',
                        'type' => 'ptr',
                        'entity' => 'vendor',
                        'addlPickFields' => ['NAME'],
                        'maxlength' => 20,
                        'format' => $gEntityIDFormat,
                    ],
                    'desc' => 'IA.TOVENDORID',
                    'path' => 'TOVENDORID',
                    'noedit' => true,
                    'nonew' => true,
                    'noview' => true,
                    'renameable' => true,
                    'onchange' => 'handleMultiGroupChange(this);',
                    'id' => 931
                ],
                [
                    'fullname' => 'IA.INCLUDE_CHILD_VENDORS',
                    'type' => Globals::$g->gBooleanType,
                    'desc' => 'IA.INCLUDE_CHILD_VENDORS',
                    'path' => 'INCLUDECHILDENTITY',
                ],
            ]
        ],
        'FIELDGRPMULTIPLE' => [
            'fields' => [
                [
                    'fullname' => '',
                    'type' => [
                        'ptype' => 'radio',
                        'type' => 'radio',
                        'validlabels' => ['IA.MULTIPLE_VENDORS'],
                        'validvalues' => ['Multi'],
                    ],
                    'groupmember' => true,
                    'path' => 'MULTIPLEFILTERGRPTYPE',
                    'nolabel' => true,
                    'onchange_js' => "handleMultiGroupChange(this);",
                ],
                [
                    'fullname' => '',
                    'type' => [
                        'ptype' => 'multitext',
                        'type' => 'multitext',
                        'showpopup' => true,
                        'size' => 32,
                        'maxlength' => 500,
                    ],
                    'path' => 'MULTIPLEFILTER_DISP',
                    'nolabel' => true,
                    'form' => 'main',
                    'disabled' => true,
                    'onchange_js' => "handleMultiGroupChange(this);",
                ],
                [
                    'fullname' => '',
                    'desc' => '',
                    'type' => [
                        'ptype' => 'href',
                        'type' => 'href',
                    ],
                    'path' => 'MULTIPLELINK',
                    'nolabel' => true,
                    'hrefimage' => '<img src="' . IALayoutManager::getCSSButtonPath("pick.gif") . '" border="0" alt="pick" align="left" />',
                    'hreftxt' => 'IA.SELECT_VENDORS',
                    'onclick' => "document.forms[0]._currentlayer.value='';toggleMultipleEntitySection(true);",
                ],
            ]
        ],
        'FIELDGRPGROUP' => [
            'fields' => [
                [
                    'fullname' => '',
                    'type' => [
                        'ptype' => 'radio',
                        'type' => 'radio',
                        'validlabels' => ['IA.VENDOR_GROUP'],
                        'validvalues' => ['Group'],
                    ],
                    'groupmember' => true,
                    'path' => 'MULTIPLEFILTERGRPTYPE',
                    'nolabel' => true,
                    'onchange_js' => "handleMultiGroupChange(this);",
                ],
                [
                    'fullname' => '',
                    'type'     => [
                        'ptype' => 'ptr',
                        'type' => 'ptr',
                        'entity' => "vendorgroup",
                        'maxlength' => 20,
                        'pick_url' => 'picker.phtml?.privateButton=1&.showprivate=1',
                    ],
                    'desc'     => 'IA.VENDOR_GROUP',
                    'path'     => 'VENDORGROUP',
                    'noview'   => true,
                    'noedit'   => true,
                    'nonew'    => true,
                    'onchange' => 'handleMultiGroupChange(this);',
                ],
            ]
        ],
    ],
    'fieldinfo' => [
        'userprefs' => true,
        'lines' => [
            [
                'title' => 'IA.TIME_PERIOD',
                'fields' => [
                    [
                        'fullname' => 'IA.AGING_PERIODS',
                        'type' => [
                            'type' => 'enum',
                            'ptype' => 'enum',
                            'validvalues' => $agingperiodvalues,
                            '_validivalues' => [],
                            'validlabels' => $agingperiodlabels, 
                            'default' => $agingperiodvalues[0],
                            'value' => $agingperiodvalues[0],
                        ],
                        'default' => $agingperiodvalues[0],
                        'value' => $agingperiodvalues[0],
                        'path' => 'AGINGPERIODS'
                    ],
                    [
                        'fullname' => 'IA.REPORT_AS_OF',
                        'type' => [
                            'ptype' => 'radio',
                            'type' => 'radio',
                            'maxlength' => 1,
                            'validlabels' => [ 'IA.TODAY', 'IA.SELECT_DATE' ],
                            'validvalues' => [ 'Current', 'Single' ],
                        ],
                        'default' => 'Current',
                        'validvalues' => [],
                        'desc' => 'IA.REPORT_AS_OF',
                        'path' => 'REPORTASOF',
                        'layout' => 'landscape',
                        'onchange_js' => 'handleReportAsOfChange(this);',
                    ],
                    [
                        'fullname' => 'IA.DATE_TO_USE',
                        'type' => [
                            'ptype' => 'date',
                            'type' => 'date',
                            'maxlength' => 12,
                        ],
                        'desc' => 'IA.SELECTED_DATE',
                        'path' => 'SELECTEDDATE',
                        'value' => GetCurrentDate(),
                        'onchange' => 'handleSelectedDateChange(this);',
                    ],
                    [
                        'fullname' => 'IA.BASED_ON',
                        'type' => [
                            'ptype' => 'radio',
                            'type' => 'radio',
                            'maxlength' => 1,
                            'validlabels' => $basedondatelabels,
                            'validvalues' => $basedondatevalues,

                        ],
                        'default' => 'Invoicedate',
                        'validvalues' => [],
                        'desc' => 'IA.BASED_ON',
                        'path' => 'BASEDON',
                        'layout' => 'landscape',
                    ],
                ],
            ],
            [
                'title' => 'IA.FILTERS',
                'fields' => [
                    [
                        'fullname' => 'IA.VENDOR_SELECTION',
                        '_func' => 'FieldSetLayout',
                        'title' => '',
                        'columns' => [
                            [
                                'fullname' => 'IA.GRPRANGE',
                                'type' => ['type' => 'fieldgroup'],
                                'path' => 'FIELDGRPRANGE',
                            ],
                            [
                                'fullname' => 'IA.GRPMULTIPLE',
                                'type' => ['type' => 'fieldgroup'],
                                'path' => 'FIELDGRPMULTIPLE',
                            ],
                            [
                                'fullname' => 'IA.VENDOR_GROUP',
                                'type' => ['type' => 'fieldgroup'],
                                'path' => 'FIELDGRPGROUP',
                            ],
                        ],
                    ],
                    [
                        'fullname' => 'IA.VENDOR_TYPE',
                        'type' => [
                            'ptype' => 'ptr',
                            'entity' => 'vendtype',
                            'type' => 'text',
                            'size' => '20'
                        ],
                        'path' => 'VENDORTYPE',
                        'noview' => true,
                        'noedit' => true,
                        'nonew' => true
                    ],
                    $gCurrencyFilter,
                    [
                        'fullname' => 'IA.EXCLUDE_CREDIT_CARD_TRANSACTIONS_FROM_REPORT',
                        'type' => [
                            'ptype' => 'boolean',
                            'type' => 'char',
                            'validvalues' => $gBooleanValues,
                            '_validivalues' => $gBooleanIValues,
                        ],
                        'default' => 'false',
                        'desc' => 'IA.EXCLUDE_CREDIT_CARD_TRANSACTIONS_FROM_REPORT',
                        'path' => 'EXCLUDE_CC_TXN',
                    ],
                    [
                        'fullname' => 'IA.EXCHANGE_RATE_TYPE',
                        'type' => [
                            'ptype' => 'ptr',
                            'type' => 'ptr',
                            'entity' => 'exchangeratetypesall',
                            'pick_url' => 'picker.phtml?.hidecustom=1',
                            'comboCacheKey' => 'hidecustom',
                            'maxlength' => 40,
                        ],
                        'hidecustom' => true,
                        'default' => $defexchtype,
                        'assist' => 'fat',
                        'path' => 'EXCHRATETYPE',
                        'noview' => true,
                        'noedit' => true,
                        'nonew' => true,
                        'multicurrency_filter' => true,
                    ],
                    $gLocationPick,
                    $gDepartmentPick,
                    $gLocationSubFilter,
                ],
            ],
            [
                'title' => 'IA.FORMAT',
                'fields' => [
                    [
                        'fullname' => 'IA.SORT_BY',
                        'type' => [
                            'type' => 'enum',
                            'ptype' => 'enum',
                            'validvalues' => $sortbyvalues,
                            '_validivalues' => [],
                            'validlabels' => $sortbylabels,
                            'default' => 'VENDORID',
                        ],
                        'default' => 'VENDORID',
                        'path' => 'SORTBY',
                    ],
                    [
                        'fullname' => 'IA.GROUP_BY',
                        'type' => [
                            'type' => 'enum',
                            'ptype' => 'enum',
                            'validvalues' => $groupbyvalues,
                            '_validivalues' => [],
                            'validlabels' => $groupbylabels,
                        ],
                        'default' => 'None',
                        'path' => 'GROUPBY',
                    ],
                    [
                        'fullname' => 'IA.REPORT_TYPE',
                        'type' => [
                            'ptype' => 'radio',
                            'type' => 'radio',
                            'maxlength' => 1,
                            'validlabels' => [ 'IA.SUMMARY', 'IA.DETAIL' ],
                            'validvalues' => [ 'Summary', 'Detail' ],
                        ],
                        'default' => 'Summary',
                        'validvalues' => [],
                        'desc' => 'IA.SUMMARY',
                        'path' => 'REPORTTYPE',
                        'layout' => 'landscape',
                    ],
                    [
                        'fullname' => 'IA.SHOW_VENDORS_WITH_ZERO_BALANCE',
                        'type' => [
                            'ptype' => 'radio',
                            'type' => 'radio',
                            'maxlength' => 1,
                            'validlabels' => [ 'IA.YES', 'IA.NO' ],
                            'validvalues' => [ 'yes', 'no' ],
                        ],
                        'default' => 'no',
                        'validvalues' => [],
                        'desc' => 'IA.YES',
                        'path' => 'SHOWVENDORSWITHZEROBALANCE',
                        'layout' => 'landscape',
                    ],
                    [
                        'fullname' => 'IA.SHOW_FULLY_PAID_BILLS_WITH_OUTSTANDING',
                        'type' => [
                            'ptype' => 'radio',
                            'type' => 'radio',
                            'maxlength' => 1,
                            'validlabels' => [ 'IA.YES', 'IA.NO' ],
                            'validvalues' => [ 'yes', 'no' ],
                        ],
                        'default' => 'no',
                        'validvalues' => [],
                        'desc' => 'IA.YES',
                        'path' => 'SHOWPAIDINVOUTSTANDINGRETAINAGE',
                        'layout' => 'landscape',
                        'apretainage_filter' => true,
                    ],
                    [
                        'fullname' => 'IA.PAGE_ORIENTATION',
                        'type' => [
                            'ptype' => 'radio',
                            'type' => 'radio',
                            'validlabels' => [ 'IA.PORTRAIT', 'IA.LANDSCAPE' ],
                            'validvalues' => [ 'P', 'L' ],
                        ],
                        'layout' => 'landscape',
                        'path' => 'ORIENTATION',
                        'default' => 'L',
                    ],
                    [
                        'path' => 'DATE_COLUMNS',
                        'fullname' => 'IA.DATES_TO_DISPLAY',
                        'type' => [
                            'type' => 'multipick',
                            'ptype' => 'multipick',
                            'validlabels' => ['IA.GL_POSTING_DATE','IA.BILL_DATE','IA.DUE_DATE',],
                            'validvalues' => [
                                'DisplayGLPostingDate',
                                'DisplayTxnDate',
                                'DisplayDueDate',
                            ],
                            'delimiter' => '#~#',
                        ],
                        'fromlabel' => 'IA.AVAILABLE',
                        'tolabel' => 'IA.SELECTED',
                        'rowcount' => 3,
                        'needhidden' => true,
                        'default' => 'DisplayTxnDate#~#DisplayDueDate#~#DisplayGLPostingDate',
                    ],
                    [
                        'fullname' => '',
                        'type' => [
                            'ptype' => 'multitext',
                            'type' => 'multitext',
                            'size' => 32,
                            'maxlength' => 500,
                        ],
                        'path' => 'MULTIPLEFILTER',
                        'nolabel' => true,
                        'form' => 'main',
                    ],
                ],
            ],
        ],
    ],
    'controls' => [
        kShowHTML,
        kShowPDF,
        kShowExcel,
        kShowCSV,
        kShowBackground,
        kEmail,
        kAddtoDashboard,
        kMemorize,
    ],
    'layout' => 'frame',
    'customjs' => 'APARAgeReporter.js',
    'popupfilterflds' => [ '_reportingBook' ],
    'layoutproperties' => $gInvRptLayoutProperties,
    'printas' => 'IA.VENDOR_AGING_REPORT',
    'module' => 'ap',
    'helpfile' => 'Vendor_Aging_Report',
];

if ( $mcmesubscribed && ! GetContextLocation() ) {
    $repCurrFilter = $gReportingCurrencyFilter;
    $kSchemas['vendor_aging']['onloadjs'] = "toggleRevalType();";
    $repCurrFilter['onchange'] = "toggleRevalType();";
    $reportArr = [
        'title' => 'IA.REPORTING_CURRENCY',
        'fields' => [
            $repCurrFilter,
            [
                'fullname' => 'IA.CONVERT_CURRENCY_FROM',
                'type' => [
                    'ptype' => 'radio',
                    'type' => 'radio',
                    'maxlength' => 1,
                    'validlabels' => [ 'IA.BASE_CURRENCY', 'IA.TRANSACTION_CURRENCY' ],
                    'validvalues' => [ 'base', 'trans' ],

                ],
                'default' => 'base',
                'desc' => 'IA.CONVERT_CURRENCY_FROM',
                'path' => 'REVALUATIONTYPE',
                'layout' => 'landscape',
            ],
        ],
    ];
    array_unshift($kSchemas["vendor_aging"]["fieldinfo"]["lines"], $reportArr);
}

if ( $mcmesubscribed && $_reportingBook != '' ) {
    $kSchemas['vendor_aging']['fieldinfo']['lines'][0]['fields'][0]['default'] = GetBookCurrency($_reportingBook);
}

$kSchemas['vendor_aging']['noreportloccheck'] = true;


