ia::definition:
  methodPermissions:
    GET:
      - pa/lists/employeeposition/view
    POST:
      - pa/lists/employeeposition/create
    PATCH:
      - pa/lists/employeeposition/edit
    DELETE:
      - pa/lists/employeeposition/delete
s1:
  hash: '0'
  type: rootObject
  systemViews:
    systemfw1:
      revision: s1
      hash: '0'
    systemfw2:
      revision: s1
      hash: '0'
  uiMetadataHash: '0'