<?php
require_once "Menu.cls";

/**
 * Class ShortcutsBean
 */
class ShortcutsBean extends Bean
{

    /**
     * @var string $layout
     */
    var $layout;

    /**
     * @param array $_params
     */
    function __construct($_params=null)
    {
        parent::__construct($_params);
    }

    /**
     * @return string
     */
    function calcShortcutName()
    {
        return "NotImplemented!!!";
    }

    function RenderBody() 
    {
        print "<table width='100%' border='0' cellpadding='0' cellspacing='0'>";

        $shortcut = $this->calcShortcutName();

        $this->layout = GetMyLayoutType();
        $forceGetScript = ($this->layout == 'C') ? false : true;

        $m =  new Menu();
        $m->InitializeMenu($shortcut, $forceGetScript);
        $mnu = $m->Build();
        foreach ( $mnu as $section ) {
            $title = $section['title'];
            if ( !empty($section['menuitems']) ) {
            ?>
            <TR>
              <TD>
             <TABLE CLASS="Box1" CELLPADDING="0" CELLSPACING="0" BORDER="0" WIDTH="100%"><TR><TD>
              <TABLE CLASS="Box2" CELLPADDING="0" CELLSPACING="0" BORDER="0" WIDTH="100%">
            <TR>
             <TD VALIGN="middle" WIDTH="184" CLASS="COMP_SUB_HEADER2" nowrap><?=$title?></TD>
            </TR>
            <TR>
            <?
                                $this->renderSection($section, $shortcut);
            ?>
            </TR>
             </TABLE>
              </TD></TR>
             </TABLE>
              </TD>
            </TR>
            <?
            } // end of 'if'
        }
        print "</table>";
    }

    /**
     * @param array  $section
     * @param string $shortcut
     */
    function renderSection($section, $shortcut = "" )
    {
    ?>
        <TD style="white-space:nowrap;">
    <?

    foreach( $section['menuitems'] as $itmKey => $itm ) {
        $this->renderItem($itm['src'], $itmKey, $shortcut);
    }


    ?>
     </TD>
    <?
    }

    /**
     * @param string $url
     * @param string $itm
     * @param string $shortcut
     */
    function renderItem($url, $itm, /** @noinspection PhpUnusedParameterInspection */ $shortcut)
    {
        $url=CallUrl($url);
        if ( false !== strpos($url, 'frameset') ) {
            $url = str_replace(".op", ".navop", $url);
            $tartop = "target=_top";
        }
        else if ( $this->layout=='C' && isl_strstr($url, 'iaflash') ) {

            // Strongly believing that this can happen only if the module is not configured
            // This block is to replace module_welcome with framset for classic layouts
        
            $url = str_replace("iaflash.phtml?.id=3&.mod=", "frameset.phtml?.fo=", $url);
            $tartop = "target=_top";
        }
        ?>
      <A HREF="<?=$url?>" CLASS=DashPanelLeft <?= /** @noinspection PhpUndefinedVariableInspection */
      $tartop?> ><?=$itm?></A>
        <?
    }
}

