<?php
/**
 * Manager for the details of a Smart Event Job
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Intacct Corporation, All Rights Reserved
 */

/**
 * Class SmartEventJobDetailsManager
 */
class SmartEventJobDetailsManager extends OwnedObjectManager
{
    const SMARTEVENTJOBNO = 'SMARTEVENTJOBNO';
    const EXECUTIONSTATE = 'EXECUTIONSTATE';
    const SMARTEVENTID = 'SMARTEVENTID';
    const COMMENTS = 'COMMENTS';
}