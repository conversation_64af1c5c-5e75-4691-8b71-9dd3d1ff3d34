<?php
namespace test\source\dm\import\platform\sheets;
use APIConstants;
use FF_ENUM_FieldType;
use NIS_ConvertorHandler;
use NIS_ObjectConfigurations;
use NIS_SheetsConfiguration;
use NIS_TransformHandler;
use PHPUnit\Framework\TestCase;

/**
 * Class NIS_SheetsConfigurationTest
 */
class NIS_SheetsConfigurationTest extends TestCase
{
    public function testSheetsConfigurationIsInitializedCorrectly()
    {
        $objectConfigMock = $this->createMock(NIS_ObjectConfigurations::class);
        $objectConfigMock->method('getObjectMedata')->willReturn([]);
        $objectConfigMock->method('hasCustomFields')->willReturn([]);
        $objectConfigMock->method('getObjectApiVersion')->willReturn(\NIS_Helper::API_VERSION);

        $sheetsConfig = new NIS_SheetsConfiguration($objectConfigMock);

        $this->assertInstanceOf(NIS_ConvertorHandler::class, $sheetsConfig->getConvertor());
        $this->assertInstanceOf(NIS_TransformHandler::class, $sheetsConfig->getTransformer());
        $this->assertEmpty($sheetsConfig->getCustomFields());
    }

    public function testGetSheetsReturnsCorrectSheets()
    {
        $objectConfigMock = $this->createMock(NIS_ObjectConfigurations::class);
        $objectConfigMock->method('getObjectSheets')->willReturn([
            [
                'fields' => [['type' => FF_ENUM_FieldType::BOOLEAN->value, 'key' => 'field1']],
                'filters' => [],
                'convertor' => NIS_ConvertorHandler::class,
                'transformer' => NIS_TransformHandler::class,
                'customFields' => [],
                'access' => [],
                'orderFunction' => null,
                'uiName' => 'Add lines',
                'slug' => 'create'
            ]
        ]);
        $objectConfigMock->method('getObjectMedata')->willReturn([]);
        $objectConfigMock->method('getObjectApiVersion')->willReturn(\NIS_Helper::API_VERSION);


        $sheetsConfig = new NIS_SheetsConfiguration($objectConfigMock);
        $sheets = $sheetsConfig->getSheets();

        $this->assertCount(1, $sheets);
        $this->assertEquals('Add lines', $sheets[0]->getName());
        $this->assertEquals(APIConstants::API_OPERATION_CREATE, $sheets[0]->getSlug());
    }

    public function testGetAllColumnsSheetReturnsCorrectSheet()
    {
        $objectConfigMock = $this->createMock(NIS_ObjectConfigurations::class);
        $objectConfigMock->method('getObjectMedata')->willReturn([]);
        $objectConfigMock->method('getObjectApiVersion')->willReturn(\NIS_Helper::API_VERSION);


        $sheetsConfig = new NIS_SheetsConfiguration($objectConfigMock);
        $sheet = $sheetsConfig->getAllColumnsSheet();

        $this->assertEquals(APIConstants::API_OPERATION_CREATE, $sheet->getSlug());
    }
}
