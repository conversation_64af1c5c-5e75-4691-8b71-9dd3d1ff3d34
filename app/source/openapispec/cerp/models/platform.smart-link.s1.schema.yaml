title: platform-smart-link
x-mappedTo: smartlinkclickapi
type: object
description: A Smart link click is a conditional link that's embedded in a form object. The link can be set up to appear only if certain criteria are met.
properties:
  key:
    type: string
    description: System-assigned key for the smart-link.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: Smart link ID, same as key.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  href:
    type: string
    description: Endpoint for the smart-link.
    readOnly: true
    example: /objects/platform/smart-link/23
  packageName:
    type: string
    description: Package name - added when smart link is created using package install
    x-mappedTo: PACKAGENAME
    readOnly: true
    example: Open Purchase Orders
  documentTypes:
    type: array
    description: Document type
    x-mappedTo: DOCTYPE
    x-delimiter: '#~#'
    example: [ 'Sales Order', 'Sales Invoice', 'Sales Return' ]
    items:
      type: string
  name:
    type: string
    description: The name of Smart link clicks.
    x-mappedTo: LABEL
    example: Credit Limit
  smartLinkId:
    type: string
    description: Smart link ID is the unique ID for Smart link click.
    x-mappedTo: SMARTLINKID
    example: CREDIT_LIMIT
  description:
    type: string
    description: Help the user to understand the purpose of the Smart link click and how to use it.
    x-mappedTo: DESCRIPTION
    example: This is a smart link for credit limit
  targetURL:
    type: string
    description: The destination web address or URL that the user will be directed when they click the Smart link click.
    x-mappedTo: TARGET
    example: 'http://{!CUSTOMER.WEBSITE!}'
  condition:
    type: string
    description: An argument that determines whether the Smart link click will appear in the interface.
    x-mappedTo: CONDITION
    example: '{!CUSTOMER.WEBSITE!} != ""'
  targetWindow:
    type: object
    description: Target window configuration
    properties:
      page:
        type: string
        description: Creates new page to display your fetch
        x-mappedTo: NEWPAGE
        example: Account
      section:
        type: string
        description: The group/section to which you want to fetch should be listed on the object page.
        x-mappedTo: FIELDSET
        example: Tax ID information
      height:
        type: integer
        description: The vertical dimension of the window in pixels.
        x-mappedTo: HEIGHT
        example: 200
      width:
        type: integer
        description: The horizontal dimension of the window in pixels.
        x-mappedTo: WIDTH
        example: 300
      showScrollbar:
        type: boolean
        description: Marking the content as scrollable is necessary if it is not fit within the height provided.
        x-mappedTo: SHOWSCROLLBAR
        x-mappedToType: string
        example: false
        default: false
  page:
    type: string
    description: The existing page to display your link
    x-mappedTo: EXISTINGPAGE
    example: vendor
  objectName:
    $ref: ../../common/models/smart-link-object-names.s1.schema.yaml
  status:
    $ref: ../../common/models/status.s1.schema.yaml