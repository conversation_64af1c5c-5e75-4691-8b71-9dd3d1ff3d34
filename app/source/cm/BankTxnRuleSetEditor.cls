<?php

class BankTxnRuleSetEditor extends FormEditor
{

    /**
     * @param array $_params
     */
    public function __construct($_params = [])
    {
        parent::__construct($_params);
    }

    /**
     * I18N. Collection of tokens used primarily in JavaScript.
     *
     * @return string[]
     */
    protected function getFormTokens() : array
    {
        $this->textTokens[] = "IA.MULTIPLE_RECORDS_DELETE_CONFIRMATION";
        $this->textTokens[] = "IA.ERROR_PROCESSING_REQUEST";
        $this->textTokens[] = "IA.SELECT_ATLEAST_ONE_RECORD";
        $this->textTokens[] = "IA.NEW_RULE";
        return parent::getFormTokens();
    }

    /**
     * Map the ajax action to a PHP function
     *
     * @param string $cmd the Ajax action requested
     *
     * @return bool false if command not found
     */
    protected function runAjax($cmd)
    {
        $ok = true;
        $response = [];
        switch ($cmd) {
            case 'populaterules':
                $response = $this->populateRules();
                break;
            case 'populateaccountstoselect':
                $response = $this->populateAccountsToSelect();
                break;
            case 'getnewruleurl':
                $response = $this->getNewRuleUrl();
                break;
            case 'getexistingruleurl':
                $response = $this->getExistingRuleUrl();
                break;
            default:
                $ok = parent::runAjax($cmd);
        }

        if ( $response ) {
            echo json_encode($response);
        }

        return $ok;
    }

    /**
     * Method to populate and transform the values for the view.
     *
     * @param array $obj
     *
     * @return bool
     *
     * @throws Exception
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        $errorRecoveryTime = Request::$r->_errorTimeStamp;
        if ( ! isset($errorRecoveryTime) || $errorRecoveryTime == '' ) {
            $this->sortRuleMaps($obj);

            $banktxnrulesetmanager = Globals::$g->gManagerFactory->getManager('banktxnruleset');

            $banktxnrulesetmanager->populateAccounts($obj);
        }

        $this->enableDisableFields($obj);
        $this->showHideField($obj);
        // Populate ruleset performance log
        $this->populateRulesetPerformanceLogURL($obj);
        return parent::mediateDataAndMetadata($obj);
    }

    /**
     * Populates the rule set performance log url.
     *
     * @param array $obj
     */
    private function populateRulesetPerformanceLogURL(&$obj)
    {
        $rulesetPerfLog = GetOperationId('cm/lists/banktxnrulerun');
        $rulesetPerfUrl
            = 'lister.phtml?.op=' . $rulesetPerfLog . '&.rulesetKey=' . $obj['RECORDNO']
            . '&.popup=1&.ydialog=1&.sess=' . Session::getKey();
        $obj['RULEPERFURL'] = $rulesetPerfUrl;
    }

    /**
     * @param array $obj
     */
    protected function enableDisableFields ($obj)
    {
        $action = Request::$r->_do;
        $view = $this->getView();
        if ( $action != $this->kCreateAction ) {
            // Make fin id field read only on edit
            $fields = [];
            // populate the primary account options options
            if ( $view->findComponents([ 'path' => 'RULESETID' ], EditorComponentFactory::TYPE_FIELD, $fields) ) {
                if ( isset($fields[0]) ) {
                    $fields[0]->setProperty('readonly', true);
                }
            }

            $fields = [];
            if ( $view->findComponents([ 'path' => 'ACCOUNTTYPE' ], EditorComponentFactory::TYPE_FIELD, $fields) ) {
                if ( isset($fields[0]) && $obj['ACCOUNTCOUNT'] != 0) {
                    $fields[0]->setProperty('readonly', true);
                }
            }

            // Show rule perf link on view/edit only
            $fields = [];
            if ( $view->findComponents([ 'path' => 'RULEPERF' ], EditorComponentFactory::TYPE_FIELD, $fields) ) {
                if ( isset($fields[0]) && $obj['ACCOUNTCOUNT'] != 0) {
                    $fields[0]->setProperty('hidden', false);
                }
            }
        }
    }

    /**
     * @param array $obj
     */
    protected function showHideField($obj)
    {
        $this->showHideCurrencyField($obj);
        $this->hideLocationField();
    }

    /**
     * This is a hook functions for subclases to add the dynamic metadata into the current layout.
     * At the time this function is called, the data, state and view objects are not available.
     * The subclass must operate on the given params structure.
     *
     * @param array $params the metadata
     */
    protected function buildDynamicMetadata(&$params)
    {
        $action = Request::$r->{$this->kAction};
        if ( !$action ) {
            $action = $this->kDefaultVerbActions[Request::$r->_do];
            if ( $action == $this->kWantViewAction ) {
                $this->showHideSelectCheckBox($params);
            }
        }


    }

    /**
     * @param array $params
     */
    private function showHideSelectCheckBox($params)
    {
        $grid = array();
        self::findElements($params, array('path' => 'ACCOUNTS'), EditorComponentFactory::TYPE_GRID, $grid);
        if ( isset($grid[0]) ) {
            $grid[0]['enableSelect'] = false;
            unset($grid[0]['selectColumn']);
        }
        $grid = array();;
        self::findElements($params, array('path' => 'RULEMAPS'), EditorComponentFactory::TYPE_GRID, $grid);
        if ( isset($grid[0]) ) {
            $grid[0]['enableSelect'] = false;
            unset($grid[0]['selectColumn']);
        }
    }

    /**
     * @param array $obj
     */
    private function showHideCurrencyField($obj)
    {
        if ($obj['ACCOUNTTYPE'] != BankTxnRuleUtil::ACCOUNT_TYPE_BANK) {
            $view = $this->getView();
            $grid = array();
            $view->findComponents(array('path' => 'MAPACCOUNTS'), EditorComponentFactory::TYPE_GRID, $grid);
            if (isset($grid) && isset($grid[0])) {
                $entryGridObj = &$grid[0];
                $accountcurrency = array();
                $entryGridObj->findComponents(array('path' => 'ACCOUNTCURRENCY'),
                                              EditorComponentFactory::TYPE_GRID_COLUMN,
                                              $accountcurrency);
                if ($accountcurrency && $accountcurrency[0]) {
                    $accountcurrency[0]->setProperty('hidden', true);
                }
            }

            $grid = array();
            $view->findComponents(array('path' => 'ACCOUNTS'), EditorComponentFactory::TYPE_GRID, $grid);
            if (isset($grid) && isset($grid[0])) {
                $entryGridObj = &$grid[0];
                $accountcurrency = array();
                $entryGridObj->findComponents(array('path' => 'ACCOUNTCURRENCY'),
                                              EditorComponentFactory::TYPE_GRID_COLUMN,
                                              $accountcurrency);
                if ($accountcurrency && $accountcurrency[0]) {
                    $accountcurrency[0]->setProperty('hidden', true);
                }
            }
        }
    }

    /**
     * Hides the location field at entity.
     */
    private function hideLocationField()
    {
        if (GetContextLocation()) {
            $view = $this->getView();
            $grid = array();
            $view->findComponents(array('path' => 'MAPACCOUNTS'), EditorComponentFactory::TYPE_GRID, $grid);
            if (isset($grid) && isset($grid[0])) {
                $entryGridObj = &$grid[0];
                $location = [];
                $entryGridObj->findComponents(array('path' => 'ACCTLOCATION'),
                    EditorComponentFactory::TYPE_GRID_COLUMN,
                    $location);
                if ($location && $location[0]) {
                    $location[0]->setProperty('hidden', true);
                }
            }

            $grid = array();
            $view->findComponents(array('path' => 'ACCOUNTS'), EditorComponentFactory::TYPE_GRID, $grid);
            if (isset($grid) && isset($grid[0])) {
                $entryGridObj = &$grid[0];
                $location = array();
                $entryGridObj->findComponents(array('path' => 'ACCTLOCATION'),
                    EditorComponentFactory::TYPE_GRID_COLUMN,
                    $location);
                if ($location && $location[0]) {
                    $location[0]->setProperty('hidden', true);
                }
            }
        }
    }

    /**
     * @param array $obj
     */
    private function sortRuleMaps(&$obj)
    {
        $ruleMaps = $obj['RULEMAPS'];

        if (is_array($ruleMaps)) {
            uasort($ruleMaps, function ($a, $b) {
                return $a['RULEORDER'] <=> $b['RULEORDER'];
            });
            unset ($obj['RULEMAPS']);
            foreach ($ruleMaps as $ruleMap) {
                $ruleMap['RULENAME'] = isl_htmlspecialchars($ruleMap['RULENAME']);
                $obj['RULEMAPS'] [] = $ruleMap;
            }
        }
    }
    /**
     * @return array
     */
    // private function deleteMap ()
    // {
    //     $rulemaprecords = Request::$r->{'RULEMAP_RECORDS'};
    //     $ruleSetKey = Request::$r->{'RULESETKEY'};
    //     $banktxnrulemapmanager = Globals::$g->gManagerFactory->getManager('banktxnrulemap');
    //     $ok = $banktxnrulemapmanager->deleteMap($rulemaprecords, $ruleSetKey);
    //     $response ['ERROR'] = !$ok;
    //     $response ['MSG'] = $ok ? " Rules are successfully removed" : " Error removing rules ";
    //     return $response;
    // }

    /**
     * @return array
     */

    private function getNewRuleUrl()
    {
        $url = 'editor.phtml?.do=create' . '&.r=' . '&.op=' . GetOperationId('cm/lists/banktxnrule/create') .
               '&.popup=1' . '&.add=Add&_action=new';

        return [ 'URL' => $url ];
    }

    /**
     * @return array
     */
    private function getExistingRuleUrl ()
    {
        $ruleKey = Request::$r->{'RULEKEY'};
        $url = BankTxnRuleUtil::getExistingRuleUrl($ruleKey);
        return ['URL' => $url];

    }
    /**
     * @return array
     */
    private function populateRules()
    {
        $ruleSetKey = Request::$r->{'RULESETKEY'};
        $existingdata = Request::$r->{'EXISTINGDATA'};
        $banktxnrulemanager = Globals::$g->gManagerFactory->getManager('banktxnrule');
        $response = $banktxnrulemanager->getAvailableRules($ruleSetKey);
        $response = $this->reduceDataBeforeSend($existingdata, $response, 'RULEKEY', 'RECORDNO');
        $softDeletedRules = $this->checkForSoftDeletedItems(BankTxnRuleUtil::RULEMAP,
                                        $ruleSetKey,
                                        $existingdata,
                                        'RULEKEY',
                                        'RULEKEY');
        $response = array_merge($response, $softDeletedRules);
        if ( empty ($response) ) {
            $response ['ERROR'] = true;
            $response ['MSG'] = I18N::getSingleToken('IA.NO_RULES_AVAILABLE_TO_ADD');
        }

        return $response;
    }

    /**
     * @return array
     */
    private function populateAccountsToSelect()
    {
        $ruleSetKey = Request::$r->{'RULESETKEY'};
        $accountType = Request::$r->{'ACCOUNTTYPE'};
        $existingdata = Request::$r->{'EXISTINGDATA'};
        $banktxnrulemanager = Globals::$g->gManagerFactory->getManager('banktxnruleset');
        $response = $banktxnrulemanager->populateAccountsToSelect($ruleSetKey, $accountType);
        $response = $this->reduceDataBeforeSend($existingdata, $response, 'RECORDNO', 'RECORDNO');
        $softDeletedRules = $this->checkForSoftDeletedItems(BankTxnRuleUtil::ACCOUNT,
                                                            $ruleSetKey,
                                                            $existingdata,
                                                            'RECORDNO',
                                                            'RECORDNO',
                                                            $accountType);
        $response = array_merge($response, $softDeletedRules);
        if ( empty ($response) ) {
            $response ['ERROR'] = true;
            $response ['MSG'] = I18N::getSingleToken('IA.NO_ACCOUNTS_AVAILABLE_TO_ADD');
        }

        return $response;
    }

    /**
     * @param array  $existingData
     * @param array  $data
     *
     * @param string $existingDataKey
     * @param string $dataKey
     *
     * @return array
     */
    private function reduceDataBeforeSend($existingData, $data, $existingDataKey, $dataKey)
    {
        // Translate the rule type
        if (!empty($data)) {
            foreach ($data as &$rule) {
                if (!empty($rule['RULETYPE'])) {
                    // Translate the rule type
                    $ruleTypeLabel = ($rule['RULETYPE'] === BankTxnRuleUtil::CREATE_RULE) ?
                        BankTxnRuleUtil::CREATE_RULE_LABEL : BankTxnRuleUtil::MATCH_RULE_LABEL;
                    $rule['RULETYPELABEL'] = I18N::getSingleToken($ruleTypeLabel);
                    $rule['RULETYPE'] = ($rule['RULETYPE'] == BankTxnRuleUtil::CREATE_RULE) ?
                        BankTxnRuleUtil::RULEATTR_CREATE : BankTxnRuleUtil::RULEATTR_MATCH;
                    $rule['RULENAME'] = isl_htmlspecialchars($rule['RULENAME']);
                }
            }
        }

        $countExistingData = is_countable($existingData) ? count($existingData) : 0;
        if ( $countExistingData > 0 ) {
            $existingData = ( resultSetToMapByColumn($existingData, $existingDataKey) );
            $response = [];
            foreach ( $data as $d ) {
                if ( ! isset($existingData[$d[$dataKey]]) ) {
                    $response [] = $d;
                }
            }
        } else {
            $response = $data;
        }

        return $response;
    }

    /**
     * @param string $type
     * @param int    $ruleSetKey
     * @param array  $existingData
     * @param string $existingDataKey
     * @param string $dataKey
     *
     * @param null|string   $accountType
     *
     * @return array
     */
    public function checkForSoftDeletedItems($type, $ruleSetKey, $existingData, $existingDataKey, $dataKey, $accountType = null)
    {
        $banktxnrulemanager = Globals::$g->gManagerFactory->getManager('banktxnruleset');
        $softDeletedItems = [];
        if ($type == BankTxnRuleUtil::RULEMAP) {
            $obj = $banktxnrulemanager->get($ruleSetKey);
            if (!empty($obj)) {
                $softDeletedItems = $obj[$type];
                $countExistingData = is_countable($existingData) ? count($existingData) : 0;
                if ($countExistingData > 0 ) {
                    $softDeletedItems = [];
                    $existingData = ( resultSetToMapByColumn($existingData, $existingDataKey) );
                    foreach ( $obj[$type] as $d ) {
                        if ( ! isset($existingData[$d[$dataKey]]) ) {
                            $softDeletedItems [] = $d;
                        }
                    }
                }
            }
        } else {
            $associatedAccounts = $banktxnrulemanager->getAccounts($ruleSetKey, $accountType, BankTxnRuleUtil::BELONGS_TO);
            $softDeletedItems = $associatedAccounts;
            $countExistingData = is_countable($existingData) ? count($existingData) : 0;
            if ( $countExistingData > 0 ) {
                $softDeletedItems = [];
                $existingData = ( resultSetToMapByColumn($existingData, $existingDataKey) );
                foreach ($associatedAccounts as $d) {
                    if ( ! isset($existingData[$d[$dataKey]]) ) {
                        $softDeletedItems [] = $d;
                    }
                }
            }
        }

        return $softDeletedItems;
    }


    /**
     * Returns the list of Javascript files to include in the page.
     *
     * @return array the list of Javascript files to include
     */
    protected function getJavaScriptFileNames()
    {
        include_once 'js_common.inc';
        $jsFiles[] = '../resources/js/banktxnruleset.js';

        return $jsFiles;
    }

    /**
     * Hook for subclasses to prepare the object during a save action
     * At the time of the call the object is in business form
     *
     * @param array &$obj the object (in and out)
     *
     * @return bool
     */
    protected function prepareObjectForSave(&$obj)
    {
        $ok = parent::prepareObjectForSave($obj);
        if ( $ok ) {
            $this->prepareObject($obj);
        }

        return $ok;
    }

    /**
     * Hook for subclasses to prepare the object during a create action
     * At the time of the call the object is in business form
     *
     * @param array &$obj the object (in and out)
     *
     * @return bool
     */
    protected function prepareObjectForCreate(&$obj)
    {
        $ok = parent::prepareObjectForCreate($obj);
        if ( $ok ) {
            $this->prepareObject($obj);
        }

        return $ok;
    }

    /**
     * @param array $obj
     */
    private function prepareObject(&$obj)
    {
        $ruleorder = 0;
        if (!empty($obj['RULEMAPS'])) {
            foreach ($obj['RULEMAPS'] as &$rulemap) {
                if (!$rulemap['RULEKEY']) {
                    $rulemap['RULEKEY'] = $rulemap['RECORDNO'];
                }
                unset ($rulemap['RULESTATUS']);
                unset ($rulemap['RECORDNO']);
                $rulemap['RULEORDER'] = ++$ruleorder;
            }
        }
    }

    /**
     * Reset data conditionally
     *
     * @param array $obj
     *
     * @return bool
     */
    protected function prepareObjectForCopyNew(&$obj)
    {
        $obj['RULESETID'] = '';
        unset ($obj['ACCOUNTS']);
        parent::prepareObjectForCopyNew($obj);
        return true;
    }


    /**
     * @param string $state
     *
     * @return array|null
     */

    public function getStandardButtons($state)
    {
        $buttons = parent::getStandardButtons($state);
        switch ($state) {
            case Editor_ShowNewState:
                $this->updateSaveButtons($buttons, "clearGridFiltersOnSave('new')");
                break;
            case Editor_ShowEditState:
                $this->updateSaveButtons($buttons, "clearGridFiltersOnSave('edit')");
                break;
            default :
                break;
        }
        return $buttons;
    }


    /**
     * @param array $buttons
     * @param string|null $jsCodeValue
     *
     * @return void
     */
    private function updateSaveButtons(&$buttons, $jsCodeValue)
    {
        foreach ($buttons as $key => $value) {
            if ($value['id'] === 'savebuttid' || $value['id'] === 'saveandnewbuttid') {
                if (array_key_exists('jsCode', $value)) {
                    $buttons[$key]['jsCode'] .= ";".$jsCodeValue;
                } else {
                    $buttons[$key]['jsCode'] = ";".$jsCodeValue;
                }
            }
        }
    }

    /**
     *
     * @return bool
     */
    protected function CanPrint() : bool
    {
        // hide "Print To" button
        return false;
    }


    /**
     * Cleanup the grid values before it reaches the manager
     *
     * @param EditorGrid    $grid      the grid
     * @param EntityManager $entityMgr the grid's object manager
     * @param array         $_obj      the transaction data
     *
     * @return bool
     */
    protected function innerCleanupLineItems($grid, $entityMgr, &$_obj)
    {
        $path = $grid->getProperty('path');
        if ($path !== 'RULEMAPS' && $path !== 'MAPACCOUNTS') {
            parent::innerCleanupLineItems($grid, $entityMgr, $_obj);
        }

        return true;
    }

}
