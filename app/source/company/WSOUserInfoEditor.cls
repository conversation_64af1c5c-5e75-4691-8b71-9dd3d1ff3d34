<?php
/**
 *  FILE:           WSOUserInfoEditor.cls
 *  AUTHOR:         cprodan
 *  DESCRIPTION:    Web services Only User Info Editor
 *
 *  (C)2000, Intacct Corporation, All Rights Reserved
 *
 *  Intacct Corporation Proprietary Information.
 *  This document contains trade secret data that belongs to Intacct
 *  corporation and is protected by the copyright laws. Information herein
 *  may not be used, copied or disclosed in whole or part without prior
 *  written consent from Intacct Corporation.
 */

/**
 * Class WSOUserInfoEditor
 */
class WSOUserInfoEditor extends UserInfoEditor
{
    /**
     * WSOUser specific tokens
     * @var string[]
     */
    protected $additionalTokens = [
        'IA.WEB_SERVICES_USER_INFORMATION',
    ];
    
    /**
     * @param array $params
     */
    protected function buildDynamicMetadata(&$params)
    {
        parent::buildDynamicMetadata($params);

        $matches = [];
        self::findElements($params, [ 'id' => 'MFATab' ], EditorComponentFactory::TYPE_PAGE, $matches);
        if ( $matches ) {
            $matches[0]['hidden'] = true;
        }

        $matches = [];
        self::findElements($params, [ 'id' => 'SSOTab' ], EditorComponentFactory::TYPE_PAGE, $matches);
        if ( $matches ) {
            $matches[0]['hidden'] = true;
        }

        $matches = [];
        self::findElements(
            $params, [ 'path' => 'MFA_NOTRUST' ], EditorComponentFactory::TYPE_FIELD,
            $matches
        );
        $matches[0]['hidden'] = true;
    }

    /**
     * @param array $obj Initial params
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        parent::mediateDataAndMetadata($obj);

        $view = $this->getView();

        if ( $view ) {
            $userType = [];
            $view->findComponents([ 'path' => 'USERTYPE' ], EditorComponentFactory::TYPE_FIELD, $userType);

            if ( isset($userType[0]) && $userType[0] != '' ) {
                $type = $userType[0]->getProperty('type');
                if ( GetMyApp() !== 'C' ) {
                    $type['validlabels'] = array_values(array_diff($type['validlabels'], ['IA.CRM']));
                    $type['validvalues'] = array_values(array_diff($type['validvalues'], ['CRM user']));
                    $type['_validivalues'] = array_values(array_diff($type['_validivalues'], ['S']));
                }

                $affiliation = Profile::getProperty('AFFILIATION');
                $returnArrayAccountant = getAffiliationTypeValues("Accountant");
                if ( $affiliation === $returnArrayAccountant['type'] ) {
                    $type['validlabels'] = array_values(array_diff($type['validlabels'], ['IA.PAYMENT_APPROVER']));
                    $type['validvalues'] = array_values(array_diff($type['validvalues'], ['payment approver']));
                    $type['_validivalues'] = array_values(array_diff($type['_validivalues'], ['A']));
                }

                $userType[0]->setProperty('type', $type);
            }
            return true;
        }

        return false;
    }

    /**
     * getMetadataKeyName - override base method
     *
     * @param string[] $params object values
     *
     * @return string
     */
    protected function getMetadataKeyName(&$params)
    {
        return "userinfo_form.pxml";
    }

    /**
     * massageRawMetadata - overrid of base method to make custom top level changes
     *
     * @param array $top top level object values
     */
    protected function massageRawMetadata(&$top)
    {
        $top['entity'] = 'wsouserinfo';
        $top['helpfile'] = 'Adding_Editing_and_Viewing_User_Information';
        $top['title'] = GT($this->textMap, 'IA.WEB_SERVICES_USER_INFORMATION');
    }

    /**
     * To disable print button
     *
     * @return bool
     */
    protected function CanPrint()
    {
        return false;
    }
}
