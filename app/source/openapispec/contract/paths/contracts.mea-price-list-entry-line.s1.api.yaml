openapi: 3.0.0
info:
  title: contracts-mea-price-list-entry-line
  description: contracts.mea-price-list-entry-line API
  version: '1.0'
  contact:
    name: ni<PERSON> kumar
    email: <EMAIL>
tags:
  - name: MEA price list entry lines
    description: An MEA price list entry line contains the standalone selling price, or fair value price, associated with an item when it's included in a multiple-element arrangement. You can enter a fair value price as an amount, percentage, or as a price range. You can also use a price range with a price entered as an amount.
servers:
  - url: 'http://localhost:3000'
paths:
  /objects/contracts/mea-price-list-entry-line:
    get:
      summary: List MEA price list entry lines
      description: |
        Returns up to 100 MEA price list entry lines from the collection with a key, ID, and link for each line. This operation is mostly for use in testing; use the query service to find MEA price list entry lines that meet specific criteria and to specify the properties that you want in the response.
      tags:
        - MEA price list entry lines
      operationId: list-contracts-mea-price-list-entry-line
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: List of mea-price-list-entry-line objects
                properties:
                  'ia::result':
                    type: array
                    items:
                      $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata-pages.schema.yaml
              examples:
                List of MEA price list entry lines:
                  value:
                    'ia::result':
                      - key: '574'
                        id: '574'
                        href: /objects/contracts/mea-price-list-entry-line/574
                      - key: '513'
                        id: '513'
                        href: /objects/contracts/mea-price-list-entry-line/513
                      - key: '514'
                        id: '514'
                        href: /objects/contracts/mea-price-list-entry-line/514
                      - key: '515'
                        id: '515'
                        href: /objects/contracts/mea-price-list-entry-line/515
                    'ia::meta':
                      totalCount: 4
                      start: 1
                      pageSize: 100
                      next: null
                      previous: null
        '400':
          $ref: '#/components/responses/400error'
  /objects/contracts/mea-price-list-entry-line/{key}:
    parameters:
      - name: key
        description: System-assigned unique key for an MEA price list entry line.
        in: path
        required: true
        example: '574'
        schema:
          type: string
    get:
      summary: Get an MEA price list entry line
      description: Returns detailed information for a specified MEA price list entry line.
      tags:
        - MEA price list entry lines
      operationId: get-contracts-mea-price-list-entry-line-key
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Details of the mea-price-list-entry-line
                properties:
                  'ia::result':
                    $ref: '#/components/schemas/contracts-mea-price-list-entry-line'
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Details of the MEA price list entry line:
                  value:
                    'ia::result':
                      id: '574'
                      key: '574'
                      meaPriceListEntry:
                        id: '516'
                        key: '516'
                        href: /objects/contracts/mea-price-list-entry/516
                      startDate: '2022-08-01'
                      amountOrPercent: '10.00'
                      markUp: '0'
                      markDown: '0'
                      upperLimit: '0'
                      lowerLimit: '0'
                      memo: null
                      audit:
                        modifiedDateTime: '2025-01-16T23:12:22Z'
                        createdDateTime: '2024-05-13T18:22:36Z'
                        createdByUser:
                          key: '1'
                          href: /objects/company-config/user/1
                        createdBy: '1'
                        modifiedByUser:
                          key: '109'
                          href: /objects/company-config/user/109
                        modifiedBy: '109'
                      href: /objects/contracts/mea-price-list-entry-line/574
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    patch:
      summary: Update an MEA price list entry line
      description: Updates an existing MEA price list entry line by setting field values. Any fields not provided remain unchanged.
      tags:
        - MEA price list entry lines
      operationId: update-contracts-mea-price-list-entry-line-key
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/contracts-mea-price-list-entry-line'
            examples:
              Updates an MEA price list entry line:
                value:
                  startDate: '2022-08-02'
                  amountOrPercent: '10.00'
                  markUp: '0'
                  markDown: '0'
                  upperLimit: '0'
                  lowerLimit: '0'
                  memo: null
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                title: Updated mea-price-list-entry-line
                properties:
                  'ia::result':
                    $ref: ../../common/models/object-reference.schema.yaml
                  'ia::meta':
                    $ref: ../../common/models/metadata.schema.yaml
              examples:
                Reference to updated MEA price list entry line:
                  value:
                    'ia::result':
                      id: '574'
                      key: '574'
                      href: /objects/contracts/mea-price-list-entry-line/574
                    'ia::meta':
                      totalCount: 1
                      totalSuccess: 1
                      totalError: 0
        '400':
          $ref: '#/components/responses/400error'
    delete:
      summary: Delete an MEA price list entry line
      description: Deletes an MEA price list entry line.
      tags:
        - MEA price list entry lines
      operationId: delete-contracts-mea-price-list-entry-line-key
      responses:
        '204':
          description: No Content
        '400':
          $ref: '#/components/responses/400error'
components:
  schemas:
    contracts-mea-price-list-entry-line:
      $ref: ../models/contracts.mea-price-list-entry-line.s1.schema.yaml
  responses:
    400error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: ../../common/models/error-response.schema.yaml
