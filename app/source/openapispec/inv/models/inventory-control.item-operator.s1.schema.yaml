type: object
title: itemOperator
readOnly: true
properties:
  itemOperator:
    type: object
    properties:
      operator:
        type: string
        description: Item operator
        x-mappedTo: ITEMOPERATOR
        enum:
          - equals
          - notEqualTo
          - lessThan
          - lessThanOrEqual
          - greaterThan
          - greaterThanOrEqual
          - contains
          - doesNotContain
          - startsWith
          - endsWith
          - includes
          - excludes
        x-mappedToValues:
          - Equals
          - Not equal to
          - Less than
          - Less than or equal
          - Greater than
          - Greater than or equal
          - Contains
          - Does not contain
          - Starts with
          - Ends with
          - Includes
          - Excludes
        example: equals
      value:
        type: string
        description: Item operator values
        x-mappedTo: ITEMVALUES
        example: Hammer