<?php

/**
 * Class AMEXBuyerAccount
 */
class AMEXBuyerAccount extends AMEXAccount
{

    /**
     * @param string $service
     */
    public function __construct($service = '')
    {
        parent::__construct($service);
    }

    /**
     * @param AMEXBankAccountDetails|AMEXCardAccountDetails|string $requestObject
     * @param string                                               $httpMethod
     * @param string                                               $apiName
     * @param string                                               $uri
     * @param string                                               $objectID
     * @param string                                               $service
     *
     * @return AMEXBuyerAccountAPIRequest
     */
    protected function getRequestObject($requestObject, $httpMethod, $apiName, $uri, $objectID, $service)
    {
        return new AMEXBuyerAccountAPIRequest(
            $requestObject,
            $httpMethod,
            $apiName,
            $uri,
            $objectID,
            $service
        );
    }
}