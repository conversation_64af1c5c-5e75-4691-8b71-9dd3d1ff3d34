--liquibase formatted sql
--changeset ganapati.mogri:2311.0.20230821.107270.001 runOnChange:false logicalFilePath:A2311.0.20230821.107270.001__customer_visibility_record_no_column.sql

---Update customerkey and vendorkey values for existing records
---Populate customerkey column values for existing records
DECLARE
    TYPE new_table IS RECORD (RECORD# NUMBER);
    TYPE ptdtypetable IS TABLE OF new_table INDEX BY BINARY_INTEGER;
    ptddata ptdtypetable;

    CURSOR ptd_cur IS SELECT CNY.RECORD# FROM COMPANY CNY;

BEGIN
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
    OPEN ptd_cur;

-- First loop for CUSTOMERKEY update
--ODA
LOOP
FETCH ptd_cur BULK COLLECT INTO ptddata;
        EXIT WHEN ptddata.count = 0;

        FORALL indx IN 1..ptddata.count

            UPDATE ODA O
            SET O.CUSTOMERKEY = (
                SELECT C.RECORD#
                FROM CUSTOMER C
                WHERE C.CUSTOMERID = O.OBJECTID
                  AND O.OBJECTTYPE = 'CUSTOMER'
                  AND ROWNUM = 1
                  AND C.CNY# = O.CNY#
                  AND C.CNY# = ptddata(indx).RECORD#
            )
            WHERE O.CNY# = ptddata(indx).RECORD#
              AND EXISTS (
                    SELECT 1
                    FROM CUSTOMER C
                    WHERE C.CUSTOMERID = O.OBJECTID
                      AND O.OBJECTTYPE = 'CUSTOMER'
                      AND C.CNY# = O.CNY#
                      AND C.CNY# = ptddata(indx).RECORD#
                      AND ROWNUM = 1
                );
            COMMIT;
END LOOP;

CLOSE ptd_cur;
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/

--MODA
DECLARE
    TYPE new_table IS RECORD (RECORD# NUMBER);
    TYPE ptdtypetable IS TABLE OF new_table INDEX BY BINARY_INTEGER;
    ptddata ptdtypetable;

    CURSOR ptd_cur IS SELECT CNY.RECORD# FROM COMPANY CNY;

BEGIN
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
    OPEN ptd_cur;

-- First loop for CUSTOMERKEY update
LOOP
FETCH ptd_cur BULK COLLECT INTO ptddata;
        EXIT WHEN ptddata.count = 0;

        FORALL indx IN 1..ptddata.count

            UPDATE MODA M
            SET M.CUSTOMERKEY = (
                SELECT C.RECORD#
                FROM CUSTOMER C
                WHERE C.CUSTOMERID = M.OBJECTID
                  AND M.OBJECTTYPE = 'CUSTOMER'
                  AND ROWNUM = 1
                  AND C.CNY# = M.CNY#
                  AND C.CNY# = ptddata(indx).RECORD#
            )
            WHERE M.CNY# = ptddata(indx).RECORD#
              AND EXISTS (
                    SELECT 1
                    FROM CUSTOMER C
                    WHERE C.CUSTOMERID = M.OBJECTID
                      AND M.OBJECTTYPE = 'CUSTOMER'
                      AND C.CNY# = M.CNY#
                      AND C.CNY# = ptddata(indx).RECORD#
                      AND ROWNUM = 1
                );
            COMMIT;
END LOOP;

CLOSE ptd_cur;
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/

--OLA
DECLARE
    TYPE new_table IS RECORD (RECORD# NUMBER);
    TYPE ptdtypetable IS TABLE OF new_table INDEX BY BINARY_INTEGER;
    ptddata ptdtypetable;

    CURSOR ptd_cur IS SELECT CNY.RECORD# FROM COMPANY CNY;

BEGIN
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
    OPEN ptd_cur;

-- First loop for CUSTOMERKEY update
LOOP
FETCH ptd_cur BULK COLLECT INTO ptddata;
        EXIT WHEN ptddata.count = 0;

        FORALL indx IN 1..ptddata.count

            UPDATE OLA O
            SET O.CUSTOMERKEY = (
                SELECT C.RECORD#
                FROM CUSTOMER C
                WHERE C.CUSTOMERID = O.OBJECTID
                  AND O.OBJECTTYPE = 'CUSTOMER'
                  AND ROWNUM = 1
                  AND C.CNY# = O.CNY#
                  AND C.CNY# = ptddata(indx).RECORD#
            )
            WHERE O.CNY# = ptddata(indx).RECORD#
              AND EXISTS (
                    SELECT 1
                    FROM CUSTOMER C
                    WHERE C.CUSTOMERID = O.OBJECTID
                      AND O.OBJECTTYPE = 'CUSTOMER'
                      AND C.CNY# = O.CNY#
                      AND C.CNY# = ptddata(indx).RECORD#
                      AND ROWNUM = 1
                );
            COMMIT;
END LOOP;

CLOSE ptd_cur;
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/

--MOLA
DECLARE
    TYPE new_table IS RECORD (RECORD# NUMBER);
    TYPE ptdtypetable IS TABLE OF new_table INDEX BY BINARY_INTEGER;
    ptddata ptdtypetable;

    CURSOR ptd_cur IS SELECT CNY.RECORD# FROM COMPANY CNY;

BEGIN
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
    OPEN ptd_cur;

-- First loop for CUSTOMERKEY update
LOOP
FETCH ptd_cur BULK COLLECT INTO ptddata;
        EXIT WHEN ptddata.count = 0;

        FORALL indx IN 1..ptddata.count

            UPDATE MOLA M
            SET M.CUSTOMERKEY = (
                SELECT C.RECORD#
                FROM CUSTOMER C
                WHERE C.CUSTOMERID = M.OBJECTID
                  AND M.OBJECTTYPE = 'CUSTOMER'
                  AND ROWNUM = 1
                  AND C.CNY# = M.CNY#
                  AND C.CNY# = ptddata(indx).RECORD#
            )
            WHERE M.CNY# = ptddata(indx).RECORD#
              AND EXISTS (
                    SELECT 1
                    FROM CUSTOMER C
                    WHERE C.CUSTOMERID = M.OBJECTID
                      AND M.OBJECTTYPE = 'CUSTOMER'
                      AND C.CNY# = M.CNY#
                      AND C.CNY# = ptddata(indx).RECORD#
                      AND ROWNUM = 1
                );
            COMMIT;
END LOOP;

CLOSE ptd_cur;
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/


DECLARE
    TYPE new_table IS RECORD (RECORD# NUMBER);
    TYPE ptdtypetable IS TABLE OF new_table INDEX BY BINARY_INTEGER;
    ptddata ptdtypetable;

    CURSOR ptd_cur IS SELECT CNY.RECORD# FROM COMPANY CNY;

BEGIN
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
    OPEN ptd_cur;
-- Second loop for VENDORKEY update
--ODA
LOOP
FETCH ptd_cur BULK COLLECT INTO ptddata;
        EXIT WHEN ptddata.count = 0;

        FORALL indx IN 1..ptddata.count

            UPDATE ODA O
            SET O.VENDORKEY = (
                SELECT V.RECORD#
                FROM VENDOR V
                WHERE V.VENDORID = O.OBJECTID
                  AND O.OBJECTTYPE = 'VENDOR'
                  AND ROWNUM = 1
                  AND V.CNY# = O.CNY#
                  AND V.CNY# = ptddata(indx).RECORD#
            )
            WHERE O.CNY# = ptddata(indx).RECORD#
              AND EXISTS (
                    SELECT 1
                    FROM VENDOR V
                    WHERE V.VENDORID = O.OBJECTID
                      AND O.OBJECTTYPE = 'VENDOR'
                      AND V.CNY# = O.CNY#
                      AND V.CNY# = ptddata(indx).RECORD#
                      AND ROWNUM = 1
                );
            COMMIT;
END LOOP;

CLOSE ptd_cur;
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/

--MODA
DECLARE
    TYPE new_table IS RECORD (RECORD# NUMBER);
    TYPE ptdtypetable IS TABLE OF new_table INDEX BY BINARY_INTEGER;
    ptddata ptdtypetable;

    CURSOR ptd_cur IS SELECT CNY.RECORD# FROM COMPANY CNY;

BEGIN
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
    OPEN ptd_cur;

-- Second loop for VENDORKEY update
LOOP
FETCH ptd_cur BULK COLLECT INTO ptddata;
        EXIT WHEN ptddata.count = 0;

        FORALL indx IN 1..ptddata.count

            UPDATE MODA M
            SET M.VENDORKEY = (
                SELECT V.RECORD#
                FROM VENDOR V
                WHERE V.VENDORID = M.OBJECTID
                  AND M.OBJECTTYPE = 'VENDOR'
                  AND ROWNUM = 1
                  AND V.CNY# = M.CNY#
                  AND V.CNY# = ptddata(indx).RECORD#
            )
            WHERE M.CNY# = ptddata(indx).RECORD#
              AND EXISTS (
                    SELECT 1
                    FROM VENDOR V
                    WHERE V.VENDORID = M.OBJECTID
                      AND M.OBJECTTYPE = 'VENDOR'
                      AND V.CNY# = M.CNY#
                      AND V.CNY# = ptddata(indx).RECORD#
                      AND ROWNUM = 1
                );
            COMMIT;
END LOOP;

CLOSE ptd_cur;
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/

--OLA
DECLARE
    TYPE new_table IS RECORD (RECORD# NUMBER);
    TYPE ptdtypetable IS TABLE OF new_table INDEX BY BINARY_INTEGER;
    ptddata ptdtypetable;

    CURSOR ptd_cur IS SELECT CNY.RECORD# FROM COMPANY CNY;

BEGIN
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
    OPEN ptd_cur;

-- Second loop for VENDORKEY update
LOOP
FETCH ptd_cur BULK COLLECT INTO ptddata;
        EXIT WHEN ptddata.count = 0;

        FORALL indx IN 1..ptddata.count

            UPDATE OLA O
            SET O.VENDORKEY = (
                SELECT V.RECORD#
                FROM VENDOR V
                WHERE V.VENDORID = O.OBJECTID
                  AND O.OBJECTTYPE = 'VENDOR'
                  AND ROWNUM = 1
                  AND V.CNY# = O.CNY#
                  AND V.CNY# = ptddata(indx).RECORD#
            )
            WHERE O.CNY# = ptddata(indx).RECORD#
              AND EXISTS (
                    SELECT 1
                    FROM VENDOR V
                    WHERE V.VENDORID = O.OBJECTID
                      AND O.OBJECTTYPE = 'VENDOR'
                      AND V.CNY# = O.CNY#
                      AND V.CNY# = ptddata(indx).RECORD#
                      AND ROWNUM = 1
                );
            COMMIT;
END LOOP;

CLOSE ptd_cur;
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/

--MOLA
DECLARE
    TYPE new_table IS RECORD (RECORD# NUMBER);
    TYPE ptdtypetable IS TABLE OF new_table INDEX BY BINARY_INTEGER;
    ptddata ptdtypetable;

    CURSOR ptd_cur IS SELECT CNY.RECORD# FROM COMPANY CNY;

BEGIN
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
    OPEN ptd_cur;

-- Second loop for VENDORKEY update
LOOP
FETCH ptd_cur BULK COLLECT INTO ptddata;
        EXIT WHEN ptddata.count = 0;

        FORALL indx IN 1..ptddata.count

            UPDATE MOLA M
            SET M.VENDORKEY = (
                SELECT V.RECORD#
                FROM VENDOR V
                WHERE V.VENDORID = M.OBJECTID
                  AND M.OBJECTTYPE = 'VENDOR'
                  AND ROWNUM = 1
                  AND V.CNY# = M.CNY#
                  AND V.CNY# = ptddata(indx).RECORD#
            )
            WHERE M.CNY# = ptddata(indx).RECORD#
              AND EXISTS (
                    SELECT 1
                    FROM VENDOR V
                    WHERE V.VENDORID = M.OBJECTID
                      AND M.OBJECTTYPE = 'VENDOR'
                      AND V.CNY# = M.CNY#
                      AND V.CNY# = ptddata(indx).RECORD#
                      AND ROWNUM = 1
                );
            COMMIT;
END LOOP;

CLOSE ptd_cur;
    DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/

---REMOVE UNIQUE CONSTRAINTS
---ODA CUSTOMERKEY
---Deleting wrong unique key constraint
DECLARE
  v_constraint_exists NUMBER;
BEGIN
  -- Check if the constraint exists
  SELECT COUNT(*)
  INTO v_constraint_exists
  FROM all_constraints
  WHERE table_name = 'ODA' AND constraint_name = 'UQ_ODA_CUSTOMERKEY' AND owner = sys_context('userenv', 'current_schema');

  -- If the constraint exists, drop it
  IF v_constraint_exists > 0 THEN
    EXECUTE IMMEDIATE 'ALTER TABLE ODA DROP CONSTRAINT UQ_ODA_CUSTOMERKEY DROP INDEX';
  END IF;
END;
/

---MODA CUSTOMERKEY
DECLARE
  v_constraint_exists NUMBER;
BEGIN
  -- Check if the constraint exists
  SELECT COUNT(*)
  INTO v_constraint_exists
  FROM all_constraints
  WHERE table_name = 'MODA' AND constraint_name = 'UQ_MODA_CUSTOMERKEY' AND owner = sys_context('userenv', 'current_schema');

  -- If the constraint exists, drop it
  IF v_constraint_exists > 0 THEN
    EXECUTE IMMEDIATE 'ALTER TABLE MODA DROP CONSTRAINT UQ_MODA_CUSTOMERKEY DROP INDEX';
  END IF;
END;
/


---MOLA CUSTOMERKEY
DECLARE
  v_constraint_exists NUMBER;
BEGIN
  -- Check if the constraint exists
  SELECT COUNT(*)
  INTO v_constraint_exists
  FROM all_constraints
  WHERE table_name = 'MOLA' AND constraint_name = 'UQ_MOLA_CUSTOMERKEY' AND owner = sys_context('userenv', 'current_schema');

  -- If the constraint exists, drop it
  IF v_constraint_exists > 0 THEN
    EXECUTE IMMEDIATE 'ALTER TABLE MOLA DROP CONSTRAINT UQ_MOLA_CUSTOMERKEY DROP INDEX';
  END IF;
END;
/


---OLA CUSTOMERKEY
DECLARE
  v_constraint_exists NUMBER;
BEGIN
  -- Check if the constraint exists
  SELECT COUNT(*)
  INTO v_constraint_exists
  FROM all_constraints
  WHERE table_name = 'OLA' AND constraint_name = 'UQ_OLA_CUSTOMERKEY' AND owner = sys_context('userenv', 'current_schema');

  -- If the constraint exists, drop it
  IF v_constraint_exists > 0 THEN
    EXECUTE IMMEDIATE 'ALTER TABLE OLA DROP CONSTRAINT UQ_OLA_CUSTOMERKEY DROP INDEX';
  END IF;
END;
/


---MODA VENDORKEY
DECLARE
  v_constraint_exists NUMBER;
BEGIN
  -- Check if the constraint exists
  SELECT COUNT(*)
  INTO v_constraint_exists
  FROM all_constraints
  WHERE table_name = 'MODA' AND constraint_name = 'UQ_MODA_VENDORKEY' AND owner = sys_context('userenv', 'current_schema');

  -- If the constraint exists, drop it
  IF v_constraint_exists > 0 THEN
    EXECUTE IMMEDIATE 'ALTER TABLE MODA DROP CONSTRAINT UQ_MODA_VENDORKEY DROP INDEX';
  END IF;
END;
/

---ODA VENDORKEY
DECLARE
  v_constraint_exists NUMBER;
BEGIN
  -- Check if the constraint exists
  SELECT COUNT(*)
  INTO v_constraint_exists
  FROM all_constraints
  WHERE table_name = 'ODA' AND constraint_name = 'UQ_ODA_VENDORKEY' AND owner = sys_context('userenv', 'current_schema');

  -- If the constraint exists, drop it
  IF v_constraint_exists > 0 THEN
    EXECUTE IMMEDIATE 'ALTER TABLE ODA DROP CONSTRAINT UQ_ODA_VENDORKEY DROP INDEX';
  END IF;
END;
/


---MOLA VENDORKEY
DECLARE
  v_constraint_exists NUMBER;
BEGIN
  -- Check if the constraint exists
  SELECT COUNT(*)
  INTO v_constraint_exists
  FROM all_constraints
  WHERE table_name = 'MOLA' AND constraint_name = 'UQ_MOLA_VENDORKEY' AND owner = sys_context('userenv', 'current_schema');

  -- If the constraint exists, drop it
  IF v_constraint_exists > 0 THEN
    EXECUTE IMMEDIATE 'ALTER TABLE MOLA DROP CONSTRAINT UQ_MOLA_VENDORKEY DROP INDEX';
  END IF;
END;
/

---OLA VENDORKEY
DECLARE
  v_constraint_exists NUMBER;
BEGIN
  -- Check if the constraint exists
  SELECT COUNT(*)
  INTO v_constraint_exists
  FROM all_constraints
  WHERE table_name = 'OLA' AND constraint_name = 'UQ_OLA_VENDORKEY' AND owner = sys_context('userenv', 'current_schema');

  -- If the constraint exists, drop it
  IF v_constraint_exists > 0 THEN
    EXECUTE IMMEDIATE 'ALTER TABLE OLA DROP CONSTRAINT UQ_OLA_VENDORKEY DROP INDEX';
  END IF;
END;
/