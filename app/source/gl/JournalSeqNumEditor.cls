<?php
/**
 * Editor for journal sequence numbering
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */

/**
 * Editor class for JournalSeqNumEditor object
 */
class JournalSeqNumEditor extends FormEditor
{
    /**
     * Constructor.
     *
     * @param string[] $_params values array for init params
     */
    public function __construct($_params = array())
    {
        parent::__construct($_params);
        AT($this->textTokens, "IA.ENTER_NUMERIC_VALUE");
        AT($this->textTokens, "IA.ERROR_CORRECT_ANY_ISSUES");

        if ( IsMultiEntityCompany() && GetContextLocation() == '' ) {
            SetReportViewContext();
        }
    }

    /**
     * mediateDataAndMetadata.
     *
     * @param string[] $obj values array
     *
     * @return bool
     */
    protected function mediateDataAndMetadata(&$obj)
    {
        $view = $this->getView();
        $state = $this->getState();
        if ($state == Editor_ShowEditState) {
            $matches = array();
            $view->findComponents(array('path' => 'NEXTSEQNUM'), EditorComponentFactory::TYPE_FIELD, $matches);
            if ($matches[0]) {
                $matches[0]->setProperty('readonly', 'true');
            }
        }

        return parent::mediateDataAndMetadata($obj);
    }

    /**
     * mediateDataAndMetadata.
     *
     * @return array
     */
    protected function getJavaScriptFileNames()
    {
        $jsfiles = array('../resources/js/journalseqnum.js');

        return $jsfiles;
    }

    /**
     * Map the ajax action to a PHP function
     *
     * @param string $cmd the Ajax action requested
     *
     * @return bool false if command not found
     */
    protected function runAjax($cmd)
    {
        $ok = true;
        switch ($cmd) {
            case 'getNextNumber':
                $this->getNextNumber();
                break;
            default:
                $ok = parent::runAjax($cmd);
                break;
        }

        return $ok;
    }

    /**
     * @return JournalSeqNumManager
     */
    public function getEntityMgr()
    {
        assert($this->entityMgr instanceof JournalSeqNumManager);
        return $this->entityMgr;
    }

    /**
     * The function returns the nextseqnum associated for the given seqnum
     * json encoded string for the ajax call
     */
    private function getNextNumber()
    {
        $seqnum = Request::$r->{'seqnum'};
        $lineno = Request::$r->{'lineno'};
        $entryMgr = Globals::$g->gManagerFactory->getManager('SEQNUM');

        $params = array(
            'selects' => array(
                'nextval', 'use_rollover', 'record#'
            ),
            'filters' => array(
                array(
                    array('TITLE', '=', $seqnum)
                )
            ),
        );

        $result = $entryMgr->GetList($params);
        $nextVal = $result[0]['NEXTVAL'];
        
        if($result[0]['USE_ROLLOVER'] === 'true') {
            $nextVal = $entryMgr->PeekNextSequenceFiscalYear($seqnum);
        }
        
        if (!empty($nextVal)) {
            $result = array(
                'value' => $nextVal,
                'lineno' => $lineno,
            );
        } else {
            $result = array(
                'errors' => "seqnum is not available for the journal ",
                'lineno' => $lineno
            );
        }
        echo json_encode($result);
    }



    /**
     * Return the list of globals to have for the editor
     *
     * @return array the list of globals
     */
    protected function getEditorGlobals()
    {
        $vars = parent::getEditorGlobals();
        if ( $vars == null ) {
            $vars = array();
        }
         $vars['IS_MCME'] = IsMCMESubscribed();
        return $vars;
    }
}
