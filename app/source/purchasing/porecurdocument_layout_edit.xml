<?xml version='1.0' encoding='UTF-8'?>
<ROOT assoc="T" >
    <entity>porecurdocument</entity>
    <pages>
        <page assoc="T" >
            <title>IA.HEADER</title>
            <fields>
				<field assoc="T">
					<path>WHENCREATED</path>
					<required>0</required>
					<hidden>1</hidden>
				</field>
				<field assoc="T" >
					<path>SOURCEDOCUMENT</path>
					<hidden>1</hidden>
				</field>
                <field>DOCPARID</field>
                <field>DOCID</field>
				<field>CUSTVENDID</field>
                <field>CUSTVENDNAME</field>
                <field>PONUMBER</field>
                <field>VENDORDOCNO</field>
				<field assoc="T" >
					<path>RECURDUEDATE</path>
				</field>
                <field>TERM.NAME</field>
			    <field assoc="T">
					<path>WHENDUE</path>
					<hidden>true</hidden>
					<required>false</required>
				</field>
                <field>MESSAGE</field>
				<field>CONTRACTID</field>
				<field>CONTRACTDESC</field>
                <field>WAREHOUSE.LOCATIONID</field>
				<field>DEPARTMENT</field>
				<field>LOCATION</field>
                <field>SHIPVIA</field>
				<field>SHIPTOKEY</field>
				<field>BILLTOKEY</field>
		<SinglelineLayout assoc="T"  key="hbox" >
		    <fullname>IA.PAY_TO_CONTACT</fullname>
		    <required>true</required>
		    <columns>
			<column assoc="T" >
			    <path>BILLTO.CONTACTNAME</path>
			</column>
			<column assoc="T" >
			    <path>DUMMYSPACE</path>
			</column>
		    </columns>
		    <_func>SinglelineLayout</_func>
		</SinglelineLayout>
		<SinglelineLayout assoc="T"  key="hbox" >
		    <fullname>IA.RETURN_TO_CONTACT</fullname>
		    <required>true</required>
		    <columns>
			<column assoc="T" >
			    <path>SHIPTO.CONTACTNAME</path>
			</column>
			<column assoc="T" >
			    <path>DUMMYSPACE</path>
			</column>
		    </columns>
		    <_func>SinglelineLayout</_func>
		</SinglelineLayout>
		<field>SUPDOCID</field>
		<field>STATUS</field>
           </fields>
        </page>
        <page assoc="T" >
        <title>IA.LINE_ITEMS</title>
        <fields>
            <field>VIRTUAL.CUSTOMER</field>
            <field>VIRTUAL.TERM</field>
		    <field assoc="T">
				<path>PRCLINENUM</path>
				<hidden>1</hidden>
			</field>
			<field>BASECURR</field>
			<field>CURRENCY</field>
			<field>EXCHRATEDATE</field>
			<field>EXCHRATETYPES.NAME</field>
			<field>EXCHRATE</field>
            <MultilineLayout assoc="T"  key="field" >
            <path>ENTRIES</path>
            <title>IA.ENTRY_ROWS</title>
			<togglecheckbox>1</togglecheckbox>
            <columns>
                 <vbox assoc="T"  key="column" >
                    <_func>vbox</_func>
                    <_args>
                        <_arg assoc="T" >
                            <path>LINESELECT</path>
	                     </_arg>
                      </_args>
                </vbox>
                <vbox assoc="T"  key="column" >
                    <_func>vbox</_func>
                    <_args>
                        <_arg assoc="T" >
                            <path>ITEMID</path>
                            <size>12</size>
			    <autofill>1</autofill>
                            <fullname>IA.ITEM</fullname>
                        </_arg>
                        <_arg assoc="T" >
			<path>WAREHOUSEAVAIL</path>
			<autofill>1</autofill>
                        <fullname>IA.WAREHOUSE</fullname>
                    </_arg>
                    </_args>
                </vbox>
                <vbox assoc="T"  key="column" >
                    <_func>vbox</_func>
                    <_args>
                        <_arg assoc="T" >
                            <path>ITEMDESC</path>
                            <size>45</size>
			    <autofill>1</autofill>
                            <fullname>IA.DESCRIPTION</fullname>
                        </_arg>
                        <_arg assoc="T" >
                        <path>MEMO</path>
                        <size>45</size>
                        <fullname>IA.MEMO</fullname>
                    </_arg>
                    </_args>
                </vbox>
                <vbox assoc="T"  key="column" >
                    <_func>vbox</_func>
                    <_args>
                        <_arg assoc="T" >
                            <path>UIQTY</path>
			    <autofill>1</autofill>
                            <fullname>IA.QUANTITY</fullname>
                        </_arg>
                        <_arg assoc="T" >
                        <path>UNITFACTOR</path>
			<autofill>1</autofill>
                        <fullname>IA.UNIT</fullname>
                    </_arg>
                    </_args>
                </vbox>
      	      <column assoc="T" >
                <path>AVAILLINK</path>
	      </column>
		  <column assoc="T">
					<path>FORM1099</path>
			</column>
	       <vbox assoc="T"  key="column" >
                    <_func>vbox</_func>
                    <_args>
                        <_arg assoc="T" >
                           <path>UIPRICE</path>
			   <autofill>1</autofill>
			   <fullname>IA.PRICE</fullname>
                        </_arg>
                    </_args>
                  </vbox>
		  <vbox assoc="T"  key="column" >
                    <_func>vbox</_func>
                    <_args>
                        <_arg assoc="T" >
                           <path>UIVALUE</path>
			   <totaled>1</totaled>
			   <autofill>1</autofill>
			   <fullname>IA.EXTENDED_PRICE</fullname>
                        </_arg>
                    </_args>
                  </vbox>
    	    <column assoc="T" >
                <path>ITEMCURRENT</path>
                <size>1</size>
                <hidden>1</hidden>
                <fullname>IA.CURRENT_ITEM</fullname>
            </column>
       	    <column assoc="T" >
                <path>UNIT</path>
                <size>1</size>
                <hidden>1</hidden>
                <fullname>IA.UNIT_TEXT</fullname>
            </column>
       	    <column assoc="T" >
                <path>WAREHOUSE.LOCATION_NO</path>
                <size>1</size>
                <hidden>1</hidden>
                <fullname>IA.WARE_HOUSE_TEXT_TEXT</fullname>
            </column>
	    	<column assoc="T" >
                <path>PRODLINE</path>
                <size>1</size>
                <hidden>1</hidden>
                <fullname>IA.PRODUCT_LINE</fullname>
            </column>
            <column assoc="T" >
                <path>ITEM.TAXABLE</path>
                <size>1</size>
                <hidden>1</hidden>
                <fullname>IA.IS_TAXABLE</fullname>
            </column>
            <column assoc="T" >
                <path>ITEM.TAXGROUP.RECORDNO</path>
                <size>30</size>
                <hidden>1</hidden>
                <fullname>IA.TAX_GROUP</fullname>
            </column>
            <column assoc="T" >
                <path>ITEM.UOMGRPKEY</path>
                <hidden>1</hidden>
            </column>
	    <column assoc="T" >
                <path>ITEMGLGROUP</path>
                <size>30</size>
                <hidden>1</hidden>
                <fullname>IA.TAX_GROUP</fullname>
            </column>
            </columns>
            <_func>MultilineLayout</_func>
        </MultilineLayout>
        </fields>
    </page>

    <page assoc="T">
		<title>IA.SCHEDULE</title>
		<fields>
			<field assoc="T">
				<path>VIRTUAL.CUSTOMER</path>
			</field>
			<field assoc="T">
				<path>VIRTUAL.TERM</path>
			</field>
			<field assoc="T">
				<path>STARTDATE</path>
			</field>
			<field assoc="T">
				<path>NEXTEXECDATE</path>
			</field>
			<field assoc="T">
				<path>LASTEXECDATE</path>
			</field>
			<field assoc="T">
				<path>EXECCOUNT</path>
				<fullname>IA.EXECUTION_COUNT</fullname>
			</field>
			<field assoc="T">
				<path>ENDDATE</path>
			</field>
			<field assoc="T">
				<path>ENDING</path>
			</field>
			<field assoc="T">
				<path>OCCUR</path>
			</field>
			<field assoc="T">
				<path>MODENEW</path>
			</field>
			<field assoc="T">
				<path>INTERVAL</path>
				<hidden>1</hidden>
			</field>
			<field assoc="T">
				<path>INTERVAL1</path>
			</field>
			<field assoc="T">
				<path>INTERVAL2</path>
			</field>
			<field assoc="T">
				<path>INTERVAL3</path>
			</field>
			<field assoc="T">
				<path>INTERVAL4</path>
			</field>
			<field assoc="T">
				<path>EOM</path>
				<hidden>1</hidden>
			</field>
			<field assoc="T">
				<path>REPEATBY</path>
				<hidden>1</hidden>
			</field>
			<field>SCHEDULEDCONTACTEMAIL</field>
			<!--
			<field>SCHEDULEDRETRYLIMITCOUNT</field>
			<field>SCHEDULEDALLOWRETRY</field>
			-->
		</fields>
	</page>
		
    </pages>
    <helpfile>Adding_Editing_and_Viewing_Recurring_Templates_for_Purchase_Orders</helpfile>
</ROOT>
