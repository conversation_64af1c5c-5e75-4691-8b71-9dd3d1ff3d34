<?
try {
    include 'pt_setupHeader.inc';

    $emptyOrSectionClass = Pt_SetupComponents::$emptyOrSectionClass;
    $boldOrEmpty = Pt_SetupComponents::$boldOrEmpty;
    $emptyOrBtnPrimaryClass = Pt_SetupComponents::$emptyOrBtnPrimaryClass;
    $rbs_roundedTableOrEmpty = Pt_SetupComponents::$rbs_roundedTableOrEmpty;

    $tokens = [ "IA.ERROR.ERROR_PLEASE_PROVIDE_A_FILE_TO_IMPORT", "IA.ERROR.ERROR_PLEASE_PROVIDE_A_FILE_IN_XML_FORMAT",
                "IA.ERROR.ERROR_INVALID_DATA_REVIEW_ERROR_MESSAGES", "IA.INSTALL_OR_UPDATE_APPLICATION", "IA.INSTALL",
                "IA.CANCEL", "IA.UPLOAD_APPLICATION_XML_FILE", "IA.PLEASE_UPLOAD_XML_FILE_WITH_APPLICATION_DATA_IF",
                "IA.APPLICATION_XML_FILE" ];
    $placeholderTokens = [];
    $textMap = getIntlTextMap($tokens, $placeholderTokens);
?>

<script language="JavaScript">
function rbf_checkInput() {
    with (document.theForm) {

        rbf_clearErrors();
        var hasError = false;

        var fileName = importFile.value;
        if (fileName == null || fileName == "") {
            rbf_activateError('importFile','<?= GT($textMap, "IA.ERROR.ERROR_PLEASE_PROVIDE_A_FILE_TO_IMPORT"); ?>');
            hasError=true;
        }
        else {
            var extension = fileName.substring(fileName.lastIndexOf('.')+1).toLowerCase();
            if (extension != "xml") {
                rbf_activateError('importFile','<?= GT($textMap, "IA.ERROR.ERROR_PLEASE_PROVIDE_A_FILE_IN_XML_FORMAT"); ?>');
                hasError=true;
            }
        }

        if (hasError) {
            showInfoMessage('<?= GT($textMap, "IA.ERROR.ERROR_INVALID_DATA_REVIEW_ERROR_MESSAGES"); ?>',true);
            return false;
        }

        //act.value = 'appInstall';
    }
    return true;
}

function rbf_doCancel() {
    with ( document.theForm ) {
        action = 'pt_applications.phtml';
        act.value = '';
    }

    return true;
}
</script>

<form action='pt_appInstall4.phtml' method='post' name='theForm' enctype="multipart/form-data"
      onSubmit='rbf_disableAllButtons();rbf_showLoadingMessage();'>
<?=Pt_WebUtil::hidden()?>
<input type="hidden" id="hlp" name="hlp" value="Custom_applications">
<input type='hidden' name='act' value=''>

<table class="rbs_mainComponentTable" cellpadding=0 cellspacing=0>
<tr>
    <td>

        <?= Pt_SetupComponents::$rbs_roundedTableTopOrEmpty ?>
        <table class='<?= Pt_SetupComponents::$rbs_silverNoborderTableOrEmpty . $emptyOrSectionClass ?>' cellpadding=0 cellspacing=0>
            <tr>
                <td class='center'>
                    <table>
                        <tr>
                            <td>&nbsp;</td>
                            <td class='rbs_PageTopicWide'><?= GT($textMap, "IA.INSTALL_OR_UPDATE_APPLICATION"); ?>&nbsp;&nbsp;&nbsp;</td>
                            <td class='rbs_recordActionCol' nowrap>
                                <input type="submit"
                                       class='<?= $boldOrEmpty . $emptyOrBtnPrimaryClass ?>'
                                       value=" <?= GT($textMap, "IA.INSTALL"); ?> " onClick='return rbf_checkInput()'>&nbsp;&nbsp;
                                <input type="submit" class="<?= $emptyOrBtnPrimaryClass ?>"
                                       value=" <?= GT($textMap, "IA.CANCEL"); ?> " onClick='return rbf_doCancel()'>&nbsp;&nbsp;
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <?= Pt_SetupComponents::$rbs_roundedTableBottomOrEmpty; ?>

        <table height='10' class='wide'>
            <tr>
                <td></td>
            </tr>
        </table>

        <?= Pt_WebUtil::getContainerBegin(Pt_SetupComponents::$setupOrEmptyClass . $emptyOrSectionClass); ?>
        <?= Pt_WebUtil::getThinSectionTop(GT($textMap, "IA.UPLOAD_APPLICATION_XML_FILE"), true) ?>

        <table class='rbs_componentContentTable' cellpadding=0 cellspacing=0>
            <tr>
                <td colspan=2 class='rbs_grayDetailHTMLcol'>
                    <?= GT($textMap, "IA.PLEASE_UPLOAD_XML_FILE_WITH_APPLICATION_DATA_IF"); ?>
                </td>
            </tr>

            <tr>
                <td colspan=2 height='10'></td>
            </tr>

            <tr>
                <td id='importFile_label' class='rbs_rightLabelRequired'><?= GT($textMap, "IA.APPLICATION_XML_FILE"); ?></td>
                <td id='importFile_field' class='rbs_leftDataColWide'>
                    <input size='30' type="file" name="importFile" value=''><br><span class='rbs_alertMsg'
                                                                                      id='importFile_error'></span>
                </td>
            </tr>

        </table>

        <?= Pt_WebUtil::getContainerEnd(''); ?>

        <table height='<?= Pt_SetupComponents::$isQuixote ? "10" : "14" ?>' class='wide'>
            <tr>
                <td></td>
            </tr>
        </table>

        <?= Pt_SetupComponents::$rbs_roundedTableTopOrEmpty ?>
        <table class="<?= Pt_SetupComponents::$rbs_lightsilverTableOrEmpty . $emptyOrSectionClass ?>">
            <tr>
                <td class='center'>
                    <table>
                        <tr>
                            <td>&nbsp;</td>
                            <td class='rbs_PageTopicWide'>&nbsp;&nbsp;&nbsp;</td>
                            <td class='rbs_recordActionCol' nowrap>
                                <input type="submit"
                                       class='<?= $boldOrEmpty . $emptyOrBtnPrimaryClass ?>'
                                       value=" <?= GT($textMap, "IA.INSTALL"); ?> " onClick='return rbf_checkInput()'>&nbsp;&nbsp;
                                <input type="submit" class="<?= $emptyOrBtnPrimaryClass ?>"
                                       value=" <?= GT($textMap, "IA.CANCEL"); ?> " onClick='return rbf_doCancel()'>&nbsp;&nbsp;
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <?= Pt_SetupComponents::$rbs_roundedTableBottomOrEmpty ?>

    </td>
</tr>

<?= Pt_SetupComponents::$trHeight10OrEmpty ?>
</table>
</form>

<script language='JavaScript'>
    document.theForm.importFile.focus();
    rbf_addOnLoadMethod(rbf_initializeValidationField("importFile"));
    // 38437: Firefox is restoring the button disabled state when restoring form fields on a go-back navigation
    // (this is *not* a bfcache issue) so just force all the buttons to be enabled when we load the page
    rbf_addOnLoadMethod(rbf_enableAllButtons);
</script>
<?
include 'pt_setupFooter.inc';
}
catch (Exception $ex) {
    error($ex);
}
