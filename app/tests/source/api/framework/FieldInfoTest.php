<?php
namespace tests\source\api\framework;
require_once 'APITestHelper.cls';

/**
 * Class FieldInfoTest
 *
 * <AUTHOR>
 * @copyright Copyright (C)2022 Sage Intacct Corporation, All Rights Reserved
 */
class FieldInfoTest extends \unitTest\core\UnitTestBaseContext
{

    /**
     * @dataProvider _testValueConversionDataProvider
     *
     * @param string      $type
     * @param mixed|null  $value
     * @param mixed|null  $apiValue
     * @param string|null $mappedToType
     * @param array       $enums
     * @param array       $mappedToVales
     *
     * @throws \APIException
     */
    public function testValueConversion(string $type, $value, $apiValue, string $mappedToType = null,
                                        array $enums = [], array $mappedToVales = [])
    {
        print "== ent type:$mappedToType == value:" . json_encode($value) .
              " ==> api type:$type == api value:" . json_encode($apiValue) . "\n";
        $fieldName = 'testField';
        $descr = [
            \APIConstants::API_MAPPED_TO_KEY      => $fieldName,
            \APIConstants::API_TYPE_KEY           => $type,
        ];
        if ( ! empty($mappedToType) ) {
            $descr[\APIConstants::API_MAPPED_TO_TYPE_KEY] = $mappedToType;
        }
        if ( ! empty($enums) ) {
            $descr[\APIConstants::API_ENUM_KEY] = $enums;
            if ( ! empty($mappedToVales) ) {
                $descr[\APIConstants::API_MAPPED_TO_VALUES_KEY] = $mappedToVales;
            }
        }
        $fieldInfo = new \FieldInfo($fieldName, $descr);

        // var_dump($value);
        $entValue = $value;
        $fieldInfo->validateAndAdjustEntValue($value);
        // var_dump($value);
        if ($type === \APIConstants::API_OBJECT_TYPE && $entValue === []) {
            $this->assertIsNotArray($value);
            $this->assertTrue(json_encode($apiValue) === json_encode($value), "Expected:". json_encode($apiValue) . ", got: " . json_encode($value));
            // skip API->ENT conversion as it will be an array from json_decode
        } else {
            $this->assertTrue($apiValue === $value, "Expected:". json_encode($apiValue) . ", got: " . json_encode($value));
            $fieldInfo->validateAndAdjustAPIValue($value, $fieldName);
            // var_dump($value);
            $this->assertTrue($entValue === $value);
        }
    }

    /**
     * @dataProvider _testEnumConversionDataProvider
     *
     * @param string      $type
     * @param mixed|null  $entValue
     * @param mixed|null  $apiValue
     * @param mixed|null  $defaultEntValue
     * @param array       $enums
     * @param string|null $mappedToType
     * @param array       $mappedToValues
     *
     * @throws \APIException
     */
    public function testEnumConversion(string $type, $entValue, $apiValue, $defaultEntValue, array $enums,
                                       string $mappedToType = null, array $mappedToValues = [])
    {
        print "== ent type:$mappedToType == value:" . json_encode($entValue) .
              " ==> api type:$type == value:" . json_encode($apiValue) . "\n";
        $fieldName = 'testField';
        $descr = [
            \APIConstants::API_MAPPED_TO_KEY      => $fieldName,
            \APIConstants::API_TYPE_KEY           => $type,
            \APIConstants::API_MAPPED_TO_TYPE_KEY => $mappedToType,
        ];

        $descr[\APIConstants::API_ENUM_KEY] = $enums;
        if ( ! empty($mappedToValues) ) {
            $descr[\APIConstants::API_MAPPED_TO_VALUES_KEY] = $mappedToValues;
        }
        $fieldInfo = new \FieldInfo($fieldName, $descr);

        // var_dump($value);
        $value = $entValue;
        $fieldInfo->validateAndAdjustEntValue($value);
        //var_dump($value);
        $this->assertTrue($apiValue === $value, json_encode($value));

        //validate with default-data true
        // var_dump($value);
        $value = $entValue;
        if (!empty($value)) {
            $value = ""; // should test an empty string instead
        }
        $fieldInfo->validateAndAdjustEntValue($value, false, true);
        //var_dump($value);
        $this->assertTrue($defaultEntValue === $value, json_encode($value));
    }

    /**
     * @dataProvider _testPercentToDecimalAPI2EntConversionDataProvider
     * @param string $apiValue
     * @param string $entValue
     * @param string $format
     *
     * @return void
     * @throws \APIException
     * @throws \APIInternalException
     */
    public function testPercentToDecimalAPI2EntConversion(string $apiValue, string $entValue, string $format)
    {
        $fieldName = 'testField';
        $descr = [
            \APIConstants::API_MAPPED_TO_KEY => $fieldName,
            \APIConstants::API_TYPE_KEY => \APIConstants::API_TYPE_STRING,
            \APIConstants::API_FORMAT_KEY => $format,
            \APIConstants::API_MAPPED_TO_DECIMAL_PERCENT_KEY => true,
        ];
        $fieldInfo = new \FieldInfo($fieldName, $descr);

        $value = $apiValue;
        $fieldInfo->validateAndAdjustAPIValue($value, $fieldName);
        $this->assertEquals($entValue, $value);
    }

    /**
     * @dataProvider _testPercentToDecimalEnt2APIConversionDataProvider
     *
     * @param string $entValue
     * @param string $apiValue
     * @param string $format
     *
     * @return void
     * @throws \APIException
     */
    public function testPercentToDecimalEnt2APIConversion(string $entValue, string $apiValue, string $format)
    {
        $fieldName = 'testField';
        $descr = [
            \APIConstants::API_MAPPED_TO_KEY => $fieldName,
            \APIConstants::API_TYPE_KEY => \APIConstants::API_TYPE_STRING,
            \APIConstants::API_FORMAT_KEY => $format,
            \APIConstants::API_MAPPED_TO_DECIMAL_PERCENT_KEY => true,
        ];
        $fieldInfo = new \FieldInfo($fieldName, $descr);

        $value = $entValue;
        $this->assertTrue($fieldInfo->needsEntValueConversion());
        $fieldInfo->validateAndAdjustEntValue($value);
        $this->assertEquals($apiValue, $value);
    }

    /**
     * @return array
     */
    public function _testValueConversionDataProvider(): array
    {
        // type, ent value, api value, $mappedToType (optional), enum (optional), mappedToValues(optional)
        return [
            ["string", false, "false", "boolean"],
            ["string", true, "true", "boolean"],
            ["boolean", "false", false, "string"],
            ["boolean", "true", true, "string"],
            ["boolean", "no", false, "string", ["true", "false"], ["yes", "no"]],
            ["boolean", "yes", true, "string", ["true", "false"], ["yes", "no"]],
            ["string", 0, "0", "integer"],
            ["string", -5, "-5", "integer"],
            ["integer", "0", 0, "string"],
            ["integer", "-5", -5, "string"],
            ["string", 1.5, "1.5", "number"],
            ["integer", "123456789123456789", 123456789123456789, "string"],
            ["array", [], []],
            ["object", [], (object)[]],
            ["object", null, null],
        ];
    }

    /**
     * @return array
     */
    public function _testEnumConversionDataProvider(): array
    {
        // type, ent value, api value, expected api value for default data conversion, enum, mappedToType (optional), mappedToValues (optional)
        return [
            ["string", "", null, null, ["value1", null], "string", ["v1", ""]], // will result in null either regular or default conversion
            ["string", null, null, null, ["value1", null], "string", ["v1", ""]], // will result in null either regular or default conversion
            ["string", "v1", "value1", "", ["value1", "value2"], "string", ["v1", "v2"]], // should not fail converting default
        ];
    }

    /**
     * @return array
     */
    public function _testPercentToDecimalAPI2EntConversionDataProvider() : array
    {
        return [
            ['12.12345678', '0.**********', \APIConstants::API_PERCENT_PRECISION_8_FORMAT_NAME],
            ['.12', '0.**********', \APIConstants::API_PERCENT_PRECISION_8_FORMAT_NAME],
            ['12.34', '0.1234', \APIConstants::API_PERCENT_PRECISION_2_FORMAT_NAME],
        ];
    }

    /**
     * @return array
     */
    public function _testPercentToDecimalEnt2APIConversionDataProvider() : array
    {
        return [
            ['0.**********', '12.12345678', \APIConstants::API_PERCENT_PRECISION_8_FORMAT_NAME],
            ['.0012', '0.12000000', \APIConstants::API_PERCENT_PRECISION_8_FORMAT_NAME],
            ['.1234', '12.34', \APIConstants::API_PERCENT_PRECISION_2_FORMAT_NAME],
        ];
    }

    /**
     * Uncomment to test execution time
     * @return void
     */
    public function testExecutionTime()
    {
        $this->assertTrue(true);

        // $t1 = $this->microtime_float();
        // for ($i = 0; $i < 1000000; $i++) {
        //     // test something
        // }
        // $t2 = $this->microtime_float();
        // print "Time1: " . ($t2 - $t1) . "\n";
        //
        // $t1 = $this->microtime_float();
        // for ($i = 0; $i < 1000000; $i++) {
        //     // test something else
        // }
        // $t2 = $this->microtime_float();
        // print "Time2: " . ($t2 - $t1) . "\n";
    }

    /**
     * @return float
     */
    private function microtime_float()
    {
        [$usec, $sec] = explode(" ", microtime());
        return ((float)$usec + (float)$sec);
    }

    public function testFractionConversion()
    {
        $fieldName = 'testField';
        $descr = [
            \APIConstants::API_MAPPED_TO_KEY => $fieldName,
            \APIConstants::API_TYPE_KEY => \APIConstants::API_TYPE_NUMBER,
            \APIConstants::API_MAPPED_TO_TYPE_KEY => \APIConstants::API_TYPE_STRING,
        ];
        $fieldInfo = new \FieldInfo($fieldName, $descr);

        $value = ".1";
        $fieldInfo->validateAndAdjustEntValue($value);
        // var_dump($value);
        $this->assertEquals(0.1, $value);

        $value = "-.1";
        $fieldInfo->validateAndAdjustEntValue($value);
        // var_dump($value);
        $this->assertEquals(-0.1, $value);
    }

    /**
     * @dataProvider __testFieldInfoDerivedElementsDataProvider
     *
     * @param string      $definition
     * @param string      $expectedModel
     * @param string|null $erroCode
     * @param array       $errorParams
     *
     * @throws \APIInternalException
     */
    public function testFieldInfoDerivedElements(string $definition, string $expectedModel, string $erroCode = null,
                                                 array $errorParams = [])
    {
        $field = json_decode($definition, true);
        $field["mappedTo"] = "XYZ";
        $field["type"] = "string";
        // print_r($field);
        try {
            $fieldInfo = new \FieldInfo("name", $field);
            // print_r($field);
            if (empty($erroCode)) {
                $this->assertNotNull($fieldInfo);
                unset($field["mappedTo"]);
                unset($field["type"]);
                $this->assertEquals(json_decode($expectedModel, true), $field);
            } else {
                $this->fail("Failed to catch $erroCode");
            }
        } catch (\Exception $e) {
            if (empty($erroCode)) {
                $this->fail($e);
            } else {
                if ( $e instanceof \APIException ) {
                    $expectedMsg = \APIErrorMessages::buildMessage($erroCode, $errorParams );
                    $this->assertStringContainsStringIgnoringCase($expectedMsg, $e->getAPIError()->getMessage());
                } else {
                    $this->fail($e->getMessage());
                }
            }
        }
    }

    /**
     * @return array
     */
    public function __testFieldInfoDerivedElementsDataProvider(): array
    {
        return [
            ['{ "readOnly" : true}',
             '{
                "readOnly": true,
                "writeOnly": false,
                "required": false,
                "nullable": true,
                "mutable": false
            }'],
            ['{ "mutable" : false, "required" : true}',
             '{
                "readOnly": false,
                "writeOnly": false,
                "required": true,
                "nullable": false,
                "mutable": false   
            }'],
            ['{}',
             '{
                "readOnly": false,
                "writeOnly": false,
                "required": false,
                "nullable": true,
                "mutable": true   
            }'],
            ['{"nullable": false}',
             '{
                "readOnly": false,
                "writeOnly": false,
                "required": false,
                "nullable": false,
                "mutable": true   
            }'],
            ['{"mutable" : true, "readOnly" : true}', '', \APIErrorMessages::INTERNAL_SERVER_ERROR_API_SCHEMA_ISSUE_0329, [
                "FIELD_NAME" => 'name',
                "PROPERTY1" => \APIConstants::API_READ_ONLY_KEY,
                "PROPERTY2" => \APIConstants::API_MUTABLE_KEY,
            ]],
            ['{"nullable" : true, "required" : true}', '', \APIErrorMessages::INTERNAL_SERVER_ERROR_API_SCHEMA_ISSUE_0329, [
                "FIELD_NAME" => 'name',
                "PROPERTY1" => \APIConstants::API_REQUIRED_KEY,
                "PROPERTY2" => \APIConstants::API_NULLABLE_KEY,
            ]],
        ];
    }

    /**
     * @dataProvider _testEnt2ApiFormatConversionDataProvider
     *
     * @param mixed  $entValue
     * @param string $apiValue
     * @param string $format
     *
     * @return void
     * @throws \APIException
     */
    public function testEnt2ApiFormatConversion($entValue, string $apiValue, string $format)
    {
        $fieldName = 'testField';
        $descr = [
            \APIConstants::API_MAPPED_TO_KEY => $fieldName,
            \APIConstants::API_TYPE_KEY => \APIConstants::API_TYPE_STRING,
            \APIConstants::API_FORMAT_KEY => $format,
        ];
        $fieldInfo = new \FieldInfo($fieldName, $descr);

        $value = $entValue;
        $fieldInfo->validateAndAdjustEntValue($value);
        $this->assertEquals($apiValue, $value);
    }

    public function _testEnt2ApiFormatConversionDataProvider() : array
    {
        return [
            [.12, '0.12', \APIConstants::API_DECIMAL_PRECISION_FORMAT_NAME_PREFIX . '2'],
            [.1, '0.100', \APIConstants::API_DECIMAL_PRECISION_FORMAT_NAME_PREFIX . '3'],
            [5.1, '5.100', \APIConstants::API_DECIMAL_PRECISION_FORMAT_NAME_PREFIX . '3'],
            [.12, '0.12', \APIConstants::API_PERCENT_PRECISION_2_FORMAT_NAME],
            [.1, '0.10', \APIConstants::API_PERCENT_PRECISION_2_FORMAT_NAME],
            [5.1, '5.10', \APIConstants::API_PERCENT_PRECISION_2_FORMAT_NAME],
            [.12345, '0.12345000', \APIConstants::API_PERCENT_PRECISION_8_FORMAT_NAME],
            [5.12345, '5.12345000', \APIConstants::API_PERCENT_PRECISION_8_FORMAT_NAME],
        ];
    }

    /**
     * @dataProvider _testValidateTypeValueProvider
     *
     * @param string     $type
     * @param array|null $enums
     * @param mixed|null $value
     * @param string     $exceptionCode
     * @param bool       $nullable
     * @param bool       $includeTypeInMsgParams
     *
     * @throws \APIException
     */
    public function testValidateTypeValue(string $type, ?array $enums, $value, string $exceptionCode, bool $nullable = true,
                                          bool $includeTypeInMsgParams = false)
    {
        $fieldName = 'testField';
        $descr = [
            \APIConstants::API_MAPPED_TO_KEY => $fieldName,
            \APIConstants::API_TYPE_KEY => $type,
            \APIConstants::API_ENUM_KEY => $enums,
        ];
        if (!$nullable) {
            $descr[\APIConstants::API_NULLABLE_KEY] = false;
        }
        $this->validateFieldValue($fieldName, $descr, $value, $exceptionCode,
                                  $includeTypeInMsgParams ?
                                      ["FIELD" => $fieldName, "TYPE" => gettype($value), "EXPECTED_TYPE" => $type] :
                                      ["FIELD" => $fieldName]);
    }

    /**
     * @return array
     */
    public function _testValidateTypeValueProvider(): array
    {
        return [
            // type, enum, api value, exception, nullable, includeTypeInMsgParams
            ['string', ['A', 'B'], null, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0033], // cannot be null
            ['string', [null, 'A', 'B'], null, ''],
            ['string', [null, 'A', 'B'], null, \APIErrorMessages::INTERNAL_SERVER_ERROR_API_SCHEMA_ISSUE_0325, false], // Invalid nullable field definition
            ['string', [null, 'A', 'B'], '', \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0027, true, false],
            ['boolean', null, null, ''], // probably odd but if no enum, null is ok
            ['boolean', null, '', \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0024, true, true], // is not of expected type
            ['boolean', null, true, ''],
            ['boolean', null, null, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0033, false], // cannot be null
            ['string', null, '', \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0033, false],
            ['string', null, null, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0033, false],
            ['integer', null, 1.5, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0024, true, true],
            ['integer', null, -.5, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0024, true, true],
        ];
    }

    /**
     * @dataProvider _testValidateDateProvider
     *
     * @param string      $format
     * @param string|null $value
     * @param string|null $convertedValue
     * @param string      $exceptionCode
     * @param bool        $isFilter
     *
     * @param bool        $isBadDate
     *
     * @throws \APIException
     */
    public function testValidateDateTime(string $format, $value, $convertedValue, string $exceptionCode, bool $isFilter, bool $isBadDate = false)
    {
        $fieldName = 'testField';
        $descr = [
            \APIConstants::API_MAPPED_TO_KEY => $fieldName,
            \APIConstants::API_TYPE_KEY => 'string',
            'format' => $format,
        ];
        $fieldInfo = new \FieldInfo($fieldName, $descr);

        $originalValue = $value;
        $isDate = $value === null? false : (substr_count($value, '-') > 1);
        $hasOffset = $value === null? false : (substr_count($value, '-') === 3 || strpos('+', $value) > 0);

        $arrayData = [
            "FIELD" => $fieldName,
        ];
        if ($isBadDate) {
            $arrayData["FORMAT"] = $format;
        } else {
            $arrayData["PATTERN"] = $this->getPatternFromFormatName($format);
        }
        $exception = $exceptionCode ? \APIErrorMessages::buildMessage($exceptionCode, $arrayData) : null;

        try {
            // print $value . " ";
            if (empty(\APIQueryUtil::$knownAPIDateNames)) {
                \APIQueryUtil::setKnownDateNames();
            }
            $fieldInfo->validateAndAdjustAPIValue($value, $fieldName, \APIConstants::API_QUERY,
                ($isFilter? \APIQueryUtil::$knownAPIDateNames : []), ($isFilter? \APIQueryUtil::$knownFVDateNames : []));
            // print $value . "\n";
            if ($exception) {
                $APIErrorCollector = \APIErrorCollectorUtil::getInstance();
                //throw error if there is any error
                if ($APIErrorCollector->hasAPIErrors()) {
                    throw ( new \APIException() )->setAPIError($APIErrorCollector->getAPIErrors()[0]);
                }
                $this->fail("Expected exception: $exception");
            }

            $this->assertEquals($convertedValue, $value);

            // print $originalValue . " ";
            if (empty($originalValue) || $isDate) {
                // test conversion back if it was a null or a date
                $fieldInfo->validateAndAdjustEntValue($value);
                if (!$hasOffset) {
                    $this->assertEquals($originalValue, $value);
                } else {
                    // test that it has 'Z' at the end
                    $this->assertTrue(endsWith($value, 'Z'));
                }
                // print $value . "\n";
            }

        } catch (\APIException $e) {
            $this->assertStringContainsStringIgnoringCase($exception, $e->getAPIError()->getMessage(), "Unexpected resul");
        }
    }

    /**
     * @return array|array[]
     */
    public function _testValidateDateProvider(): array
    {
        return [
            // format, value, covertedValue, exception code, isFilter, is a bad date
            ['date', '2020-01-01', '01/01/2020', '', false], //0
            ['date-time', '2020-01-01T01:01:01Z', '01/01/2020 01:01:01', '', false], //1
            ['date', '2020-01-01', '01/01/2020', '', true], //2 - filter
            ['date-time', '2020-01-01T08:01:01Z', '01/01/2020 08:01:01', '', true], //3 - filter
            [ 'date', 'xxx', null, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0026, true],//4 - filter - 'does not match pattern'
            [ 'date-time', 'xxx', null, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0026, true],//5 - filter - 'does not match pattern'
            [ 'date', 'xxx', null, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0026, false],//6 - 'does not match pattern'
            [ 'date-time', 'xxx', null, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0026, false],//7 - 'does not match pattern'
            [ 'date', '02-02-2000', null, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0026, false],//8 - 'does not match pattern'
            ['date', null, null, '', false], //9
            [ 'date-time', '02/02/2000 01:01:01', null, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0026, false],//10 - 'does not match pattern'
            [ 'date', '2020-13-01', null, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0025, false, true],// 11 - is not a valid for format [date]
            ['date-time', null, null, '', false], //12
            [ 'date', '2020-02-30', null, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0025, false, true],// 13 - is not a valid for format [date]
            ['date-time', '2020-01-01T08:01:01-07', '01/01/2020 15:01:01', '', false], //14 - offset
        ];
    }

    /**
     * @dataProvider _testValidateFormatValueProvider
     *
     * @param string     $type
     * @param string     $format
     * @param mixed|null $value
     * @param string     $exceptionCode
     *
     * @throws \APIException
     */
    public function testValidateFormatValue(string $type, string $format, $value, string $exceptionCode)
    {
        $fieldName = 'testField';
        $descr = [
            \APIConstants::API_MAPPED_TO_KEY => $fieldName,
            \APIConstants::API_TYPE_KEY => $type,
            \APIConstants::API_FORMAT_KEY => $format,
        ];
        $pattern = $this->getPatternFromFormatName($format);
        $this->validateFieldValue($fieldName, $descr, $value, $exceptionCode,
            ($exceptionCode === \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0024 ?
                ["FIELD" => $fieldName, "TYPE" => gettype($value), "EXPECTED_TYPE" => $type] :
                ($exceptionCode === \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0025 ?
                    ["FIELD" => $fieldName, "FORMAT" => $format] :
                    ["FIELD" => $fieldName, "PATTERN" => $pattern]
                )
            )
        );
    }

    /**
     * @return array
     */
    public function _testValidateFormatValueProvider(): array
    {
        return [
            // type, format, value, exception code ('does not match pattern')
            ['string', \APIConstants::API_DECIMAL_PRECISION_FORMAT_NAME_PREFIX . '2', '9.**********', \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0026],
            ['string', \APIConstants::API_DECIMAL_PRECISION_FORMAT_NAME_PREFIX . '2', 9.**********, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0024],
            ['string', \APIConstants::API_DECIMAL_PRECISION_FORMAT_NAME_PREFIX . '2', '-9.96', ''],
            ['string', \APIConstants::API_DECIMAL_PRECISION_FORMAT_NAME_PREFIX . '2', '-9.9', ''],
            ['string', \APIConstants::API_DECIMAL_PRECISION_FORMAT_NAME_PREFIX . '2', '-9', ''],
            ['string', \APIConstants::API_DECIMAL_PRECISION_FORMAT_NAME_PREFIX . '2', '.96', ''],
            ['string', \APIConstants::API_DECIMAL_PRECISION_FORMAT_NAME_PREFIX . '2', '.9', ''],
            ['string', \APIConstants::API_DECIMAL_PRECISION_FORMAT_NAME_PREFIX . '2', -9.96, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0024],
            ['string', \APIConstants::API_DECIMAL_PRECISION_FORMAT_NAME_PREFIX . '2', ' ', \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0025],

            [ 'string', \APIConstants::API_PERCENT_PRECISION_2_FORMAT_NAME, '9.**********', \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0026],
            [ 'string', \APIConstants::API_PERCENT_PRECISION_2_FORMAT_NAME, '0.96', ''],
            [ 'string', \APIConstants::API_PERCENT_PRECISION_2_FORMAT_NAME, '10.96', ''],
            [ 'string', \APIConstants::API_PERCENT_PRECISION_2_FORMAT_NAME, 9.**********, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0024],
            [ 'string', \APIConstants::API_PERCENT_PRECISION_2_FORMAT_NAME, 0.96, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0024],

            [ 'string', \APIConstants::API_PERCENT_PRECISION_8_FORMAT_NAME, '9.123456789', \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0026],
            [ 'string', \APIConstants::API_PERCENT_PRECISION_8_FORMAT_NAME, '0.96123456', ''],
            [ 'string', \APIConstants::API_PERCENT_PRECISION_8_FORMAT_NAME, '10.96123456', ''],
            [ 'string', \APIConstants::API_PERCENT_PRECISION_8_FORMAT_NAME, 9.123456789, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0024],
            [ 'string', \APIConstants::API_PERCENT_PRECISION_8_FORMAT_NAME, 0.123456789, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0024],
        ];
    }

    /**
     * @dataProvider _testValidatePatternValueProvider
     *
     * @param string     $type
     * @param string     $pattern
     * @param mixed|null $value
     * @param string     $exceptionCode
     *
     * @throws \APIException
     */
    public function testValidatePatternValue(string $type, string $pattern, $value, string $exceptionCode)
    {
        $fieldName = 'testField';
        $descr = [
            \APIConstants::API_MAPPED_TO_KEY => $fieldName,
            \APIConstants::API_TYPE_KEY => $type,
            \APIConstants::API_PATTERN_KEY => $pattern,
        ];
        $this->validateFieldValue($fieldName, $descr, $value, $exceptionCode, ["FIELD" => $fieldName, "PATTERN" => $pattern]);
    }

    /**
     * @return array
     */
    public function _testValidatePatternValueProvider(): array
    {
        return [
            // type, pattern, value, exception code ('does not match pattern')
            ['number', '/^[0-9]+$/', 999999999.99, \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0026], //0
            ['string', '/^[0-9]+$/', '-9', \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0026], //1
            ['number', '/^([0-9]){1,30}$/', 999999999, ''], //2
            ['string', '/^([0-9]){1,30}$/', 'A', \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0026], //3
            ['string', '/^[0-9]+$/', '01234567899', ''], //4
        ];
    }

    /**
     * @dataProvider _testValidateArrayItemTypeValueProvider
     *
     * @param string|null $itemType
     * @param string|null $format
     * @param array|null  $value
     * @param string      $exceptionCode
     * @param string|null $errorType
     *
     * @throws \APIException
     */
    public function testValidateArrayItemTypeValue($itemType, $format, $value, string $exceptionCode, string $errorType = null)
    {
        $fieldName = 'testField';
        $descr = [
            \APIConstants::API_MAPPED_TO_KEY => $fieldName,
            \APIConstants::API_TYPE_KEY => \APIConstants::API_TYPE_ARRAY,
        ];

        $errorParams = [];
        if ($format) {
            $descr[\APIConstants::API_ITEMS_KEY][\APIConstants::API_FORMAT_KEY] = $format;
            if ($exceptionCode) {
                $pattern = $this->getPatternFromFormatName($format);
                $errorParams = ["FIELD" => $fieldName, "PATTERN" => $pattern];
            }
        }
        if ($itemType) {
            $descr[\APIConstants::API_ITEMS_KEY][\APIConstants::API_TYPE_KEY] = $itemType;
            if ($exceptionCode && empty($errorParams)) { // do not override if format error was built
                $errorParams = ["FIELD" => $fieldName, "TYPE" => $errorType, "EXPECTED_TYPE" => $itemType, "LEVEL" => 1 ];
            }
        } // default for both nulls

        $this->validateFieldValue($fieldName, $descr, $value, $exceptionCode, $errorParams);
    }

    /**
     * @return array
     */
    public function _testValidateArrayItemTypeValueProvider(): array
    {
        return [
            // type, pattern, value, exceptionCode, errorType
            [null,  null, ["99.999999999", 'xx'], '', null], //0 null is a string
            ['number',  null, [99.999999999, 'xx'], \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0039, 'string'],//1 - 'is not of expected type'
            ['number',  null, [99.999999999, -1], '', null], //2
            ['string',  'decimal-precision-2', ["99.999999999", -1], \APIErrorMessages::BAD_REQUEST_INVALID_REQUEST_0026, null],//3 - 'does not match pattern'
        ];
    }

    public function testInArrayConvertion()
    {
        $handler = \RegistryLoader::getInstance("5-test-beta2")->getHandler("test/__testtest");
        $APIErrorCollector = \APIErrorCollectorUtil::getInstance();

        // 1-level array with enums - match should be found
        $fieldName = 'arrayField8';
        $fieldInfo = $handler->getObjectFieldInfo($fieldName);
        $value = "A_A_A";
        $found = $fieldInfo->findMatchingEnumMappedToInArray($value, $fieldName);
        $this->assertEquals("A_1", $found);
        $this->assertFalse($APIErrorCollector->hasAPIErrors(), json_encode($APIErrorCollector->getAPIErrors()));

        // same but with a bad value - should return null and collect
        $value = "A_A_B";
        $found = $fieldInfo->findMatchingEnumMappedToInArray($value, $fieldName);
        $this->assertNull($found);
        $this->assertTrue($APIErrorCollector->hasAPIErrors());
        $APIErrorCollector->clearAPIErrors();

        // 1-level array with no enums - should return null  but no errors
        $fieldName = 'arrayField7';
        $fieldInfo = $handler->getObjectFieldInfo($fieldName);
        $value = "A_A_A";
        $found = $fieldInfo->findMatchingEnumMappedToInArray($value, $fieldName);
        $this->assertNull($found);
        $this->assertFalse($APIErrorCollector->hasAPIErrors(), json_encode($APIErrorCollector->getAPIErrors()));

        // 2-level array with enums - should find enum
        $fieldName = 'arrayField4';
        $fieldInfo = $handler->getObjectFieldInfo($fieldName);
        $value = "B_B";
        $found = $fieldInfo->findMatchingEnumMappedToInArray($value, $fieldName);
        $this->assertEquals("B", $found);
        $this->assertFalse($APIErrorCollector->hasAPIErrors(), json_encode($APIErrorCollector->getAPIErrors()));
    }

    /**
     * Test removing mappedTo from the definition
     */
    public function testUnsetMappedTo()
    {
        $array1 = [
            "mappedTo" => "ARRAY11",
            "type"     => "array",
            "items"    => [
                "enum" => [
                    "level1",
                    "level2",
                    "level3"
                ],
                "mappedToValues" => [
                    "level1",
                    "level2",
                    "level3"
                ],
                'type' => 'string'
            ]
        ];

        // remove mappedTo
        \FieldInfo::unsetRecursive($array1);

        foreach ( $array1 as $key => $value ) {
            // if key starts with mappedTo fail test
            if ( str_starts_with($key, 'mappedTo') ) {
                $this->fail('mappedTo should not be present');
            }
            if ( is_array($value) ) {
                foreach ( $value as $subKey => $subValue ) {
                    if ( is_array($subValue) ) {
                        // if key starts with mappedTo fail test
                        if ( str_starts_with($key, 'mappedTo') ) {
                            $this->fail('mappedTo should not be present');
                        }
                    }
                }
            }
        }

        // success
        $this->assertTrue(true);
    }

    /**
     * @param string     $fieldName
     * @param array      $descr
     * @param mixed|null $value
     * @param string     $exceptionCode
     * @param array      $params
     *
     * @throws \APIException
     */
    private function validateFieldValue(string $fieldName, array $descr, $value, string $exceptionCode, array $params = []) : void
    {
        $exception = ($exceptionCode)? \APIErrorMessages::buildMessage($exceptionCode, $params) : null;
        $APIErrorCollector = \APIErrorCollectorUtil::getInstance();
        $APIErrorCollector->clearAPIErrors();
        try {
            $fieldInfo = new \FieldInfo($fieldName, $descr);
            $fieldInfo->validateAndAdjustAPIValue($value, $fieldName, 'create');
            if ( $exception ) {
                //throw error if there is any error
                if ( $APIErrorCollector->hasAPIErrors() ) {
                    throw ( new \APIException() )->setAPIError($APIErrorCollector->getAPIErrors()[0]);
                }
                $this->fail("Expected exception: $exception");
            } else {
                $this->assertFalse($APIErrorCollector->hasAPIErrors(), json_encode($APIErrorCollector->getAPIErrors()));
            }
        } catch ( \APIException $e ) {
            $this->assertStringContainsStringIgnoringCase($exception, $e->getAPIError()
                                                                        ->getMessage(), "Unexpected result");
        }
    }

    /**
     * @param string $format
     *
     * @return mixed
     */
    private function getPatternFromFormatName(string $format)
    {
        if (startsWith($format, \APIConstants::API_DECIMAL_PRECISION_FORMAT_NAME_PREFIX)) {
            $precision = substr($format, strlen(\APIConstants::API_DECIMAL_PRECISION_FORMAT_NAME_PREFIX));
            $pattern = str_replace('N', $precision, \APIConstants::API_DECIMAL_PRECISION_PATTERN_TEMPLATE);
        } else if (array_key_exists($format, \APIConstants::API_GLOBAL_FORMATS)) {
            $patternName = \APIConstants::API_GLOBAL_FORMATS[$format];
            global $$patternName;
            $pattern = $$patternName;
        } else {
            $pattern = \APIConstants::API_DATE_TIME_FORMATS[$format] ?? ( \APIConstants::API_GENERIC_FORMATS[$format] ?? null);
        }

        return $pattern;
    }
}