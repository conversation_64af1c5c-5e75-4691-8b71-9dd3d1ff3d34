<?

$kBS_Queries = array(
    'QRY_BS_ADD_MONTH_TO_DUE_DATE' => array(
        'QUERY' => "
update company2
	set duedate=add_months(duedate,1)
	where cny#=?
",
        'ARGTYPES' => array('integer'),
    ),
    'QRY_BS_UPDATE_DUE_DATE' => array(
        'QUERY' => "
update company2
	set duedate= ?
	where cny#=?
",
        'ARGTYPES' => array('text','integer'),
    ),

    'QRY_BS_GET_DUEDATE_AND_CYCLEDATE' => array(
        'QUERY' => "
select duedate,billingdate from company2 
	where cny#=?
",
        'ARGTYPES' => array('integer'),
    ),

    'QRY_BS_GET_ALL_CPA_BILLABLE' => array(
        'QUERY' => "
select
    c2.cny# as c2cny#, ca.externcnykey as ac2cny#, c2.duedate  as c2duedate,
    c.title as ctitle, ac.title as actitle, ac.databaseid dbid
from
	company c, schemamap ac,
	company2 c2, externassoc ca
where
	c.record# = c2.cny# and
	c2.cny#=ca.cny# and
	c2.billparent='T' and
	ca.externcnykey = ac.cny# and
	c.status='T' and
    c.moved = 'F' and
	ca.type = 'P' and
	(c.type!='demo' or c.type is null)
",
        'ARGTYPES' => array(),
    ),

    'QRY_BS_FIND_ALL_COMPANIES_THAT_SHOULD_BE_BILLED_TODAY' => array(
        'QUERY' => "
select * 
from company, company2
where
	status='T' and
    moved = 'F' and
	(type!='demo' or type is null) and
	company.record#=company2.cny# and
	(company2.duedate between (sysdate - 10) and sysdate) and
	(company2.term = 'Sept1stPrice' or company2.term = 'Nov1stPrice' or company2.term = 'Feb04Price')
	order by company.record#",
    'ARGTYPES' => array(),
    ),
    'QRY_BS_GET_ALL_COMPANY_IDS' => array(
        'QUERY' => "
select company.record#
from
	company, company2
where
	status='T' and
    moved = 'F' and
	(type!='demo' or type is null) and
	company.record#=company2.cny# and
	(company2.term = 'Sept1stPrice' or company2.term = 'Nov1stPrice'  or company2.term = 'Feb04Price')
 and
	company2.cny# in (select cny# from customers10 union select cny# from cpas)",
        'ARGTYPES' => array(),
    ),
    'QRY_BS_GET_CYCLE_DAY' => array(
        'QUERY' => 'select company2.billingdate from company2 where cny#=?',
        'ARGTYPES' => array('integer'),
    ),
    'QRY_BS_IS_CPA_COMPANY' => array(
        'QUERY' => 'select cny# from (select cny# from practicecompany where practicetype = \'C\' union  select cny#
			from cpas) where cny# =?',
        'ARGTYPES' => array('integer')
    ),
    'QRY_BS_IS_MC_ENTITY' => array(
        'QUERY' => 'select practicecompany.cny# from globalpracticecompany practicecompany,externassoc 
			where practicecompany.cny# = externassoc.externcnykey and practicecompany.practicetype = \'M\' and externassoc.cny# =? and externassoc.type = \'P\'
			union select practicecompany.cny# from practicecompany where practicecompany.practicetype = \'M\' and practicecompany.cny# =?',
        'ARGTYPES' => array('integer','integer')
    ),
    'QRY_BS_GET_COMPANY_TITLES' => array(
        'QUERY' => 'select record# cny#,title from company where record# in (?)',
        'ARGTYPES' => array('integer')
    ),

    'QRY_BS_GET_NUMBER_OF_CPAS' => array(
        'QUERY' => 'select count(*) as cnt from cpas',
        'ARGTYPES' => array()
    ),


    'QRY_BS_GET_NUMBER_OF_CLIENTS' => array(
        'QUERY' => '
		select count(*) as cnt from
			( select customers10.cny# from customers10, externassoc where
			  customers10.cny# = externassoc.cny# and externassoc.type = \'P\' )
		',
        'ARGTYPES' => array()
    ),


    'QRY_BS_GET_NUMBER_OF_DIRECTS' => array(
        'QUERY' => '
		select count(*) as cnt from
		   (select cny# from customers10 where not (cny#  in (select customers10.cny#
		   from customers10, externassoc
		   where customers10.cny# = externassoc.cny# and externassoc.type = \'P\')))
		',
        'ARGTYPES' => array()
    ),


    'QRY_BS_GET_NUMBER_OF_CLIENT_USERS' => array(
        'QUERY' => '
		select count(*) as cnt from (select unique bssubscription.cny#, username
		from bssubscription, externassoc, schemamap epracticec, company clientc
		where
			bssubscription.cny# = externassoc.cny#  and
			externassoc.cny# = clientc.record# and
			externassoc.externcnykey = epracticec.cny# and
			externassoc.type = \'P\' and
			bssubscription.cny# in (select customers10.cny# from
			customers10, externassoc where
			customers10.cny# = externassoc.cny# and externassoc.type = \'P\')
		)
		',
        'ARGTYPES' => array()
    ),


    'QRY_BS_GET_NUMBER_OF_DIRECT_USERS' => array(
        'QUERY' => '
		select count(*) as cnt from (
			select unique bssubscription.cny#, username
			from bssubscription
			where
				bssubscription.cny# in
				(select cny# from customers10 where not (cny#  in (select
				customers10.cny# from customers10, externassoc where
				customers10.cny# = externassoc.cny# and externassoc.type = \'P\')))
			)
		',
        'ARGTYPES' => array()
    ),


    'QRY_BS_GET_NUMBER_OF_ACTIVE_DIRECTS' => array(
        'QUERY' => "
		select count(*) as cnt from (
		select title, count(*)  from prrecord ,company 
		where company.record# = prrecord.cny# and
			cny# in (select cny# from customers10 where not (cny#  in (select
				customers10.cny# from customers10, externassoc
			  	where customers10.cny# = externassoc.cny# and externassoc.type = \'P\'))) and
			created <  whencreated and
			whencreated > add_months(sysdate, -1)
			group by title
			having count(*) > 10
			order by 2 desc
		)
		",
        'ARGTYPES' => array()
    ),


    'QRY_BS_GET_NUMBER_OF_ACTIVE_CLIENTS' => array(
        'QUERY' => "
		select count(*) as cnt from (
		select title, count(*)  from prrecord ,company 
		where company.record# = prrecord.cny# and
			cny# in ( select customers10.cny# from customers10, externassoc
				where customers10.cny# = externassoc.cny# and externassoc.type = \'P\' ) and
			created <  whencreated and
			whencreated > add_months(sysdate, -1)
			group by title
			having count(*) > 10
			order by 2 desc
		)
		",
        'ARGTYPES' => array()
    ),


    'QRY_BS_GET_CPA_REVENUE' => array(
        'QUERY' => "
		select sum(totalatcreation) as total from bsorderlineitem
		where
			to_char(invoiced,'mm/dd/yyyy') = to_char(sysdate,'mm/dd/yyyy') and
			cny# in (select cny# from cpas )
		",
        'ARGTYPES' => array()
    ),


    'QRY_COMPANY2_GET_TERM' => array(
        'QUERY'=> "select term from company2 where cny# = ?",
        'ARGTYPES' => array('integer')
    ),



    'QRY_CNY_TO_CUSTOMERID' => array(
        'QUERY' => "select title from company where record# = ?",
        'ARGTYPES' => array('integer')
        ),

    'QRY_IS_MCONSOLE_PARENT' => array(
        'QUERY' => "select PRACTICETYPE from PRACTICECOMPANY where CNY# = ?",
        'ARGTYPES' => array('integer')
        )
);
