<?php
/**
 * Smart Event Dispatcher
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Intacct Corporation, All Rights Reserved
 */

require_once 'CustomERP.inc';

final class SmartEventDispatcher extends FIFODispatcher
{
    use SmartEventTrait;

    const SE_TRACKER_FNAME = '/tmp/setracker.log';

    /**
     * Return the dispatcher type (the value to store in DB, singe char)
     *
     * @return string
     */
    public function getType() : string
    {
        return FIFODispatcher::DISPATCHER_TYPES['smarteventjobqueue'];
    }

    /**
     * @return string
     */
    public function getConfigSectionName(): string
    {
        return 'SMART_EVENT_DISPATCHER';
    }

    /**
     * @param DBSchemaInfo $dbInfo
     *
     * @return int
     */
    public function getDBBandwidth(DBSchemaInfo $dbInfo): int
    {
        return $dbInfo->getSmartEventWorkflowBandwidth();
    }

    /**
     * @param DBSchemaInfo $dbInfo
     *
     * @return int
     */
    public function getDBTimeLimit(DBSchemaInfo $dbInfo): int
    {
        return $dbInfo->getSmartEventWorkflowTimeLimit();
    }

    /**
     * @return string
     */
    public function getControlLockPrefix(): string
    {
        return 'seDispatch';
    }
    
    /**
     * @param $dbsToRunOn
     * @param $companiesList
     *
     * @return array|bool
     */
    public function getJobList($dbsToRunOn, $companiesList) : bool|array
    {
        $jobList = parent::getJobList($dbsToRunOn, $companiesList);
        logToFileInfo($this->getLogPrefix() . 'Job list: ' . json_encode($jobList));
        return $jobList;
    }
}