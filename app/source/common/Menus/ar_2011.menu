<?  

//	FILE:			ar.menu
//	AUTHOR:			<PERSON>
//	DESCRIPTION:	Menus for AR application
//
//	(C)2002, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//


$ar_menu = array(
    'Tasks' => array(
        'Lists' => array('menutype' => 'heading'),
        'Customers' => array('script' => 'lister.phtml', 'key' => 'ar/lists/customer','renameable' => 1),
        'Projects' => array('script' => 'lister.phtml', 'key' => 'ar/lists/project','renameable' => 1),
        'Custom Views' => array(),

        'Transactions' => array('menutype' => 'heading'),
        'Invoices' => array('script' => 'lister.phtml?.recordtype=ri&.it=arinvoice', 'key' => 'ar/lists/arinvoice'),
        'Recurring Invoices' => array('script' => 'lister.phtml?.recordtype=ri&.it=arrecurinvoice', 'key' => 'ar/lists/arrecurinvoice'),
        'Deposits' => array('script' => 'lister.phtml', 'key' => 'ar/lists/deposit'),
        'Manual Deposits' => array('script' => 'lister.phtml', 'key' => 'ar/lists/arquickdeposit'),
        'Account Adjustments' => array('script' => 'lister.phtml?.recordtype=ra', 'key' => 'ar/lists/aradjustment'),
        'Electronic Payments' => array('script' => 'lister.phtml', 'key' => 'ar/lists/epaymentlog'),
        'Advances' => ['script' => 'lister.phtml', 'key' => 'ar/lists/aradvance'],
        'Posted Payments' => array('script' => 'lister.phtml', 'key' => 'ar/lists/arpostedpayment'),
        'Activities' => array('menutype' => 'heading'),
        'Receive a Payment' => array('script' => 'receivepayment.phtml', 'key' => 'ar/activities/applypayments'),
        'Apply Penalties' => array('script' => 'popupreporteditor.phtml', 'key' => 'ar/activities/penalties'),
        'Print/Email' => array('popupmenus' => array (
            'Invoices' => array('script' => 'popupreporteditor.phtml', 'key' => 'ar/activities/printinvoices'),
            'Statements' => array('script' => 'popupreporteditor.phtml', 'key' => 'ar/activities/statements_report'),
        )),
        'Manage Subledger' => array('popupmenus' => array (
                'Summary List' => array('script' => 'lister.phtml', 'key' => 'ar/activities/arbatch'),
                'Payment Summaries' => array('script' => 'lister.phtml', 'key' => 'ar/activities/arpaymentbatch'),
        'Open Subledger' => array('script' => 'manage_prbatch.phtml?.prtype=r&.praction=open', 'key' => 'ar/activities/open_batches'),
        'Close Subledger' => array('script' => 'manage_prbatch.phtml?.prtype=r&.praction=close', 'key' => 'ar/activities/close_batches'),
        )), 
        'spacer3' => array('menutype' => 'separator'),
        'Manage Revenue Schedules'    => array('script' => 'editor.phtml?.done='.insertDone('iaflash.phtml?.id=3&.mod=ar'), 'key' => 'ar/activities/revrecschedules'),
        'spacer4' => array('menutype' => 'separator'),
       
    ),
    'Views' => array(
    ),
    'Reports' => array(
        'My Stored Reports' => array('script' => 'lister.phtml', 'key' => 'ar/reports/reportstore'),
        'Memorized Reports' => array('script' => 'lister.phtml', 'key' => 'ar/lists/memorizedreports','nolistadd' => true),
        'Custom Reports' => array(),
        'Registers' => array('popupmenus' => array (
            'Sales Register' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/salesregister'),
            'Receipts Register' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/receiptsregister'),
        )),
        'Customer List Report' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/entitylist','renameable' => 1),
        'Customer Aging' => array('popupmenus' => array (
            'Customer Aging Reports' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/customeraging_report','renameable' => 1),
        'Customer Aging Graph' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/aragegraph','renameable' => 1),
        ), 'renameable' => 1 ),

        'AR Ledger' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/arledger'),
        'Invoices Analysis' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/invoicesanalysis'),
        'Invoices Analysis Graph' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/invoicesanalysisgraph'),
        'AR Recurring Report' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/arrecur'),
        'AR Open Items Revaluation Report' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/arreval', 'mcpkey' => 'ar/reports/arreval'),
        'Reclassification Report' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/arreclass'),
        'Recurring Transaction Status' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/recurtxnaroestatus'),
        'Recurring Transaction Forecast' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/recurtxnforecast'),
        'Sales Tax Report' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/salestax'),
        
        'Deferred Reports' => array('popupmenus' => array (
            'Deferred Revenue Forecast' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/defrevforecast'),
            'Deferred Revenue Forecast Graph' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/defrevforecastgraph'),
            'Deferred Revenue Details' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/defrevenue'),
            'Deferred Revenue Revaluation Report' => array('script' => 'reporteditor.phtml', 'key' => 'ar/reports/deferrevenuerevalue'),
        )),
    ),
    'Setup' => array(
        'Configure Accounts Receivable' => array('moduleconf' => 'ar'),

        'Setup Lists' => array('menutype' => 'heading'),
        'Customer Types' => array('script' => 'lister.phtml', 'key' => 'ar/lists/custtype','renameable' => 1),
        'Customer Visibility' => array('script' => 'lister.phtml', 'key' => 'ar/lists/customervisibility','renameable' => 1, 'entity' => 'customer'),
            'Customer Groups' => array('script' => 'lister.phtml', 'key' => 'ar/lists/customergroup','renameable' => 1),
        'Customer Bank Accounts' => array('script' => 'lister.phtml', 'key' => 'ar/lists/customerbankaccount','renameable' => 1),
        'Customer Card Accounts' => array('script' => 'lister.phtml', 'key' => 'ar/lists/customercreditcard','renameable' => 1),
        'Project Types' => array('script' => 'lister.phtml', 'key' => 'ar/lists/projecttype','renameable' => 1),
        'Project Groups' => array('script' => 'lister.phtml', 'key' => 'ar/lists/projectgroup','renameable' => 1),
        'Project Status' => array('script' => 'lister.phtml', 'key' => 'ar/lists/projectstatus','renameable' => 1),
        'AR Terms' => array('script' => 'lister.phtml', 'key' => 'ar/lists/arterm'),
        'Shipping Methods' => array('script' => 'lister.phtml', 'key' => 'ar/lists/shipmethod'),
        'AR Account Labels' => array('script' => 'lister.phtml', 'key' => 'ar/lists/araccountlabel'),
        'Account Label Tax Groups' => array('script' => 'lister.phtml', 'key' => 'ar/lists/acctlabeltaxgroup'),
        'Tax Authority' => array('script' => 'lister.phtml', 'key' => 'ar/lists/taxauthority'),
        'Tax Detail' => array('script' => 'lister.phtml', 'key' => 'ar/lists/taxdetail'),
        'Tax Schedule' => array('script' => 'lister.phtml', 'key' => 'ar/lists/taxschedule'),
        'Tax Schedule Map' => array('script' => 'lister.phtml', 'key' => 'ar/lists/artaxschedmap'),
        'Revenue Recognition Category' => array('script' => 'lister.phtml', 'key' => 'gl/lists/revreccategory'),    
        'Revenue Recognition Templates' => array('script' => 'lister.phtml', 'key' => 'ar/lists/revrectemplate'),
        'Bill Back Templates'=>array('script' => 'lister.phtml', 'key' => 'ar/lists/billbacktemplate','renameable' => 1),

    /*
    'Summary Invoice Entry' => array('script' => 'fastentry_pref.phtml?.key='.URLCleanParams::insert('.key', 'invoice').'&.mod=4.AR', 'key' => 'ar/quickadds/billfastentry'),
    'Adjust Account' => array('script' => 'editor.phtml?.recordtype=ra&.mod=ar&.done='.insertDone('iaflash.phtml?.id=3&.mod=ar'), 'key' => 'ar/activities/aradjustment/create'),
    */        
    ),
    'Shortcuts' => array(
        'Customer' => array('script' => 'editor.phtml?.done='.insertDone('iaflash.phtml?.id=3&.mod=ar'), 'key' => 'ar/quickadds/customer/create','renameable' => 1),
        'Invoice' => array('script' => 'editor.phtml?.done='.insertDone('iaflash.phtml?.id=3&.mod=ar'), 'key' => 'ar/quickadds/arinvoice/create'),
        'Recurring Invoice' => array('script' => 'editor.phtml?.done='.insertDone('iaflash.phtml?.id=3&.mod=ar'), 'key' => 'ar/quickadds/arrecurinvoice/create'),
        'Invoice Batch' => array('script' => 'edit_prbatch.phtml?.recordtype=ri&.done='.insertDone('iaflash.phtml?.id=3&.mod=ar'), 'key' => 'ar/quickadds/invoicebatch/create'),
        'Quick Deposit' => array('script' => 'editor.phtml?.done='.insertDone('iaflash.phtml?.id=3&.mod=ar'), 'key' => 'ar/lists/arquickdeposit/create'),
        'Account Label' => array('script' => 'editor.phtml?.done='.insertDone('iaflash.phtml?.id=3&.mod=ar'), 'key' => 'ar/quickadds/araccountlabel/create'),
    ),
);


