--OWNER_CONTACT:<EMAIL>

--liquibase formatted sql
--changeset object:1 runOnChange:true logicalFilePath:R__VIEW_V_GLACCTGRPMAP.sql

CREATE OR REPLACE FORCE VIEW v_glacctgrpmap (cny#,record#,acct_no,title,acctgrpkey,acctgrpname) AS
select ac.cny#, ac.record#, ac.acct_no, ac.title, grp.record# as acctgrpkey,
    grp.NAME  as acctgrpname
from BASEACCOUNT ac, GLACCTGRP grp, GLACCTGRPRANGES rng
where  grp.Howcreated = 'U'
    and    ac.cny#  = grp.cny#
        and    grp.CNY# = rng.CNY#
        and    grp.RECORD# = rng.PARENTKEY
        and    ac.ACCT_NO BETWEEN rng.RANGEFROM AND rng.RANGETO
union
(
select  glaccount.cny#, glaccount.record#, glaccount.acct_no,
glaccount.TITLE,
        to_number(null), to_char(null)
from    BASEACCOUNT glaccount
minus
select ac.cny#, ac.record#, ac.acct_no, ac.title, to_number(null), to_char(null)
from BASEACCOUNT ac, GLACCTGRP grp, GLACCTGRPRANGES rng
where  grp.Howcreated = 'U'
    and    ac.cny#  = grp.cny#
        and    grp.CNY# = rng.CNY#
        and    grp.RECORD# = rng.PARENTKEY
        and    ac.ACCT_NO BETWEEN rng.RANGEFROM AND rng.RANGETO)



 
 
 
 
 ;