-- set operation country on companies

UPDATE companypref SET value = 'United States' WHERE property = 'OPCOUNTRY' AND (value IS NULL OR value = '')
/

INSERT INTO companypref (cny#, record#, status, property, value)
SELECT
    c.record#,
    (COALESCE((SELECT MAX(record#) FROM companypref WHERE cny# = c.record#), 0) + 1),
    'T',
    'OPCOUNTRY',
    'United States'
FROM company c
LEFT JOIN companypref cp ON cp.cny# = c.record# AND cp.property = 'OPCOUNTRY'
WHERE cp.value IS NULL
/
