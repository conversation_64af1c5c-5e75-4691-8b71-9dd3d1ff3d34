<?php
/**
 * SubjectAreaLister.cls contains the implementation of SubjectAreaLister
 *
 * <AUTHOR> Rusu <<EMAIL>>
 * @copyright 2016 Intacct Corporation, All Rights Reserved
 */

/**
 * Lister class for SubjectArea entity
 */
class SubjectAreaLister extends NLister
{

    /* @var bool $canDelete */
    private $canDelete;
    /* @var bool $canRun */
    private $canRun;
    /* @var bool $canEdit */
    private $canEdit;


    /**
     * @param array $params
     */
    function __construct($params = [])
    {
        $additionalTokens = [
            ['id' => "IA.INTERACTIVE_REPORT_AREAS"],
            ['id' => "IA.NAME"],
            ['id' => "IA.APPLICATION"],
            ['id' => "IA.DESCRIPTION"],
        ];
        I18N::addTokens($additionalTokens);
        $kTokenMap = I18N::getText();
        parent::__construct(
            INTACCTarray_merge(
                [
                    'entity' => 'subjectarea',
                    'title' => $kTokenMap["IA.INTERACTIVE_REPORT_AREAS"],
                    'fields' => array('NAME', 'APPLICATION' ,'DETAILS'),
                    'fieldlabels' => [$kTokenMap["IA.NAME"], $kTokenMap["IA.APPLICATION"], $kTokenMap["IA.DESCRIPTION"]],
                    'sortcolumn' => 'NAME:a',
                    'edit' => 'editor.phtml',
                    'linkinfo' => array(
                        'select' => array(
                            'opID' => 'co/lists/subjectarea',
                            ),
                        ),
                    ],
                $params
            )
        );

        $this->canDelete = false;
        $this->canRun = false;
        $this->canEdit = false;
    }

    /**
     * Return a list of entities
     *
     * @param array  $querySpec a structure used to build the custom query
     * @param string $querytype unused; for compatibility with parent method
     *
     * @return array list of entities
     */
    function getList($querySpec, $querytype = 'normal')
    {
        $mgr = $this->entityMgr;
        /** @var SubjectAreaManager $mgr */
        $reportAreas = $mgr->getList($querySpec);

        //only sorting by name
        $orders = $querySpec['orders'][0][1];
        if ($orders != null) {
            if ($orders == 'asc') {
                usort($reportAreas, function($a, $b) {
                    return strcmp($a['NAME'], $b['NAME']);
                });
            } else if (substr($orders, 0, 4) == 'desc') {
                usort($reportAreas, function($a, $b) {
                    return -1 * strcmp($a['NAME'], $b['NAME']);
                });
            }
        }
        return $reportAreas;
    }

    /**
     * Return the number of entities
     *
     * @param array $querySpec a structure used to build the custom query
     *
     * @return int number of entities
     */
    function GetCount($querySpec)
    {
        return count($this->getList($querySpec));
    }

    /**
     * No filters needed
     *
     * @return string
     */
    function getFilterButtons()
    {
        $ret = '<filterbutton><nope/></filterbutton>';
        return $ret;

    }

    /**
     * This function creates the advanced checkboxes and sets its state like Include inactive and Display hierarchy and
     * Include private. Include One-Time is only created in calcOTURL in customerLister and vendorLister only
     *
     * @return string
     */
    function genAdvancedOptions()
    {
        return '';
    }



    /**
     * Override parent
     *
     * @param int   $i    row index
     * @param array $vals input params
     *
     * @return array
     */
    function calcViewUrlParms($i, $vals)
    {
        I18N::addToken("IA.VIEW");
        $kTokenMap = I18N::getText();
        $t = &$this->table;
        $thisRow = &$t[$i];

        $urlargs = &$vals['urlargs'];
        $urlargs['.it'] = '';
        $urlargs['.op'] = GetOperationId('cerp/lists/subjectarea/view');
        $urlargs['.do'] = '';
        $urlargs['.subjectarea'] = urlencode($thisRow['ID']);
        $urlargs['.subjectareaType'] = urlencode($thisRow['TYPE']);
        $vals['dst'] = 'editor.phtml';
        $vals['text'] = $kTokenMap["IA.VIEW"];
        $vals['tip'] = 'view';

        return parent::calcViewUrlParms($i, $vals);
    }

    /**
     * Overrides the parent method.
     *
     * @return string
     */
    function genGlobs()
    {
        $ret = parent::genGlobs();
        $ret .= "<g name='.customRA'>" . Request::$r->_customRA . "</g>";

        return $ret;
    }

    /**
     * @return string
     */
    function genTopPanel()
    {
        $ret = "<b id='iacr'/>";
        $ret .= parent::genTopPanel();

        return $ret;
    }



    /**
     * User cannot add subject areas
     *
     * @return string
     */
    function calcAddUrl()
    {
        return '';
    }

    /**
     * @return string
     */
    function calcBodyIsland() {
        return "<script>if (top && top.Pace) { top.Pace.stop(); }</script>";
    }


    /**
     * Generates delete url parameters
     *
     * @param int   $i    row number
     * @param array $vals report info
     *
     * @return array
     */
    function calcDeleteUrlParms($i, $vals)
    {
        if ( $this->canDelete ) {
            if ( $this->table[$i]['CANDELETE'] ) {
                return parent::calcDeleteUrlParms($i, $vals);
            }
        }

        return [];
    }

    /**
     * Override parent
     *
     * @param int   $i    row index
     * @param array $vals input params
     *
     * @return array
     */
    function calcEditUrlParms($i, $vals)
    {
        I18N::addToken("IA.EDIT");
        $kTokenMap = I18N::getText();
        if ( $this->canEdit ) {
            $t = &$this->table;
            $thisRow = &$t[$i];
            if ($thisRow['TYPE'] == 'Standard') {
                return [];
            }
            $urlargs = &$vals['urlargs'];
            $urlargs['.it'] = '';
            $urlargs['.op'] = GetOperationId('cerp/lists/subjectarea/edit');
            $urlargs['.do'] = 'edit';
            $urlargs['.subjectarea'] = urlencode($thisRow['ID']);
            $vals['dst'] = 'editor.phtml';
            $vals['text'] = $kTokenMap["IA.EDIT"];
            $vals['tip'] = 'edit';

            return parent::calcEditUrlParms($i, $vals);
        } else {
            return [];
        }
    }

    /**
     * Override to change the required values
     */
    function BuildTable()
    {
        if ( OBIEEInstance::getInstance()->isUnderMaintenance() ) {
            throw new Exception('The Interactive Custom Report Writer component is under maintenace.'
                                . '  Please retry your request later.');
        }
        parent::BuildTable();

        $flds = $this->_params['_fields'];
        $fldnames = $this->_params['_fieldlabels'];

        $flds = array_diff($flds, [ 'CANDELETE' ]);
        $flds[] = 'RUN';
        $fldnames[] = '';
        $this->SetOutputFields($flds, $fldnames);
    }

    /**
     * @return string
     */
    /*
    function genAllButtons()
    {
        $iaOp = GetOperationId("cerp/lists/subjectarea");
        $ret = [];

        if (Request::$r->_customRA === "true") {
            $ret = parent::genAllButtons();
            if (CheckAuthorization($iaOp, 1)) {
                $text = "Standard report areas";
                $tip = "Standard report areas";
                $href = str_replace('&', '&amp;', CallUrl('lister.phtml?.op=' . $iaOp
                                                          . '&.customRA=' . urlencode('false')));
                $iacr_url = "<a href='" . $href . "' " . HREFUpdateStatus($tip) . " onclick=''>" . $text . "</a>";
                $ret .= "<b id='iacr'>" . $iacr_url . "</b>";
            }
        } else {
            if ( CheckAuthorization($iaOp, 1) ) {
                $ret = "<b id='done'>" . $this->calcDoneUrl() . "</b>";
                $text = "Custom report areas";
                $tip = "Custom report areas";
                $href = str_replace('&', '&amp;', CallUrl('lister.phtml?.op=' . $iaOp
                                                          . '&.customRA=' . urlencode('true')));
                $iacr_url = "<a href='" . $href . "' " . HREFUpdateStatus($tip) . " onclick=''>" . $text . "</a>";
                $ret .= "<b id='iacr'>" . $iacr_url . "</b>";
                $ret .= "<b id='export' type='menu'>" . $this->calcExportUrl() . "</b>";

            }
        }

        return $ret;
    }
    */
}
