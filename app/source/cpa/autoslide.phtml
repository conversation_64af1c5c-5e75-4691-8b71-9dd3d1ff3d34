<?php
//=============================================================================
//
//	FILE:            autoslide.phtml
//	AUTHOR:          dwilks
//	DESCRIPTION:     performs an automatic slide
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================

require_once 'util.inc';

// to prevent JS/SQL injections
Request::FilterRequest();
InitBase();
if (!InitProfile()) {
    Fwd("noauthorization.phtml?.op=", $_SERVER['HTTP_REFERER']);
}
InitGlobals();

$tokens = [
    ['id' => 'IA.INTACCT'],
    ['id' => 'IA.NO_PAGE_SPECIFIED'],
];
$textMap = I18N::getTokensForArray($tokens);

// Won't return if successful
Autoslide::processPage($_GET);

?>
<html>
<head><TITLE><?= GT($textMap, "IA.INTACCT"); ?></TITLE></head>
<body>
<?= GT($textMap, "IA.NO_PAGE_SPECIFIED"); ?>
</body>
</html>
