<?php

/**
 * Metadata for the CSV Import of Change Request object
 *
 * <AUTHOR>
 * @copyright 2025 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
class csvimport_changerequest extends csvimport_base
{
    /**
     * @param array $args
     */
    public function __construct($args = [])
    {
        $entity = 'changerequest';

        parent::__construct($entity);

        require_once 'csv_metadata_changerequest.inc';

        // Initialize the class variables
        /** @noinspection PhpUndefinedVariableInspection */
        $this->imeta = $imeta;
        /** @noinspection PhpUndefinedVariableInspection */
        $this->imetagroom = $imetagroom;

        /** @noinspection PhpUndefinedVariableInspection */
	    $this->initializeFieldPrefixMap($imetaFieldPrefixMap);
        /** @noinspection PhpUndefinedVariableInspection */
        $this->objkey = $imetakeys['objkey'];
        $this->csvkey = $imetakeys['csvkey'];

        // current entity manager
        $gManagerFactory = Globals::$g->gManagerFactory;
        $this->entMgr = $gManagerFactory->getManager($entity, true);

        // Merge the meta
        $this->MergeMeta();
    }

    /**
     * Override function for writing the error file
     *
     * @param array $obj the data
     * @param resource $errfp the file pointer
     * @param int $noheaders true if no header else false
     */
    public function _WriteToImportErrorFile($obj, $errfp, $noheaders = 1)
    {
        $this->_writeUTF8Bom($errfp);
        foreach ($obj as $values) {
            $csvString = ArrayToCSVString($values, [], $noheaders);
            fwrite($errfp, $csvString);
        }
    }

    /**
     * Check to see if we should stop reading rows (true) or not (false)
     *
     * @param array $row
     *
     * @return bool
     */
    protected function checkStopReadCSVTransaction($row)
    {
        $sequenceid = GetPreferenceForProperty(Globals::$g->kPAid, 'CHANGEREQUEST_SEQUENCEID');
        if (!empty($sequenceid)) {
            if ($row['PROJECTID']) {
                return true;
            }
            else {
                return false;
            }
        }
        else {
            // Treat same project ID as part of previous
            return $this->checkIDChangeReadCSVTransaction($row, 'CHANGEREQUESTID');
        }
    }

    /**
     * Override function of the base to get file instead of row contents
     *
     * @param array $obj
     *
     * @return bool
     */
    function _GetNextImportObject(&$obj)
    {
        return $this->_GetImportCSVTransaction($this->impFP, $this->objMap, $obj);
    }

    /**
     * @param array $rowArray
     *
     * @return bool
     *
     * @throws IAException
     */
    function Import($rowArray)
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        // Start a transaction. Everything has to succeed or we will rollback all of it.
        $source = __METHOD__;
        XACT_BEGIN($source);

        // Build the data array into the format entity manager expect
        $changeRequest = array();
        $ok = $ok && $this->ConstructRecord($rowArray, $changeRequest);

        // upsert constructed changerequest records..
        $changeRequest['CRENTRIES'] = $changeRequest['ENTRIES'];
        unset($changeRequest['ENTRIES']);
        list($changeRequestId) = explode('--', $changeRequest['CHANGEREQUESTID']);
        $existingRecordNo = null;

        if ($changeRequestId != '') {
            $params = array(
                'selects' => array('RECORDNO', 'CHANGEREQUESTID'),
                'filters' => array(
                    array(
                        array('CHANGEREQUESTID', '=', $changeRequestId),
                    )
                )
            );
            $myList = $this->entMgr->GetList($params);
            $existingRecordNo = $myList[0]['RECORDNO'];
        }

        if ($existingRecordNo) {
            // a change request with this id already exists, so it's an error
            $gErr->addIAError(
                number: 'CRE-3400',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: ['CHANGEREQUESTID' => $changeRequestId]
            );
            $ok = false;
        }

        foreach($changeRequest['CRENTRIES'] as &$entry) {
            // if COST and PRICE have no value, ChangeRequestManager expects them to not be set, rather than set with blank value
            // so if empty, unset them. this will cause ChangeRequestManager to do the right thing, which of course is a Sage value
            if (empty($entry['COST'])) {
                unset($entry['COST']);
            }
            if (empty($entry['PRICE'])) {
                unset($entry['PRICE']);
            }
        }

        $ok = $ok && $this->entMgr->add($changeRequest);

        if (!$ok) {
            $gErr->addIAError(
                number: 'CRE-3401',
                source: __FILE__ . ':' . __LINE__,
                desc1PHs: ['CHANGEREQUESTID' => $changeRequest['CHANGEREQUESTID']]
            );
            XACT_ABORT($source);
        } else {
            XACT_COMMIT($source);
        }

        return $ok;
    }

    /**
     * @param array $rowArray
     * @param array $changeRequest
     *
     * @return bool
     */
    protected function ConstructRecord(&$rowArray, &$changeRequest)
    {
        $ok = true;
        $changeRequest = array();

        foreach ($rowArray as $row) {
            $tmpChangeRequest = array();

            foreach ($this->imeta as $key => $value) {
                if ($this->imetagroom[$value]) {
                    $function = $this->imetagroom[$value];
                    $row[$value] = $this->$function($row[$value]);
                }
                $tmpChangeRequest[$key] = $row[$value];
            }
	    $structured = $this->entMgr->FlatToStructured($tmpChangeRequest);
            if ($this->entMgr->hasSequence()) {
                unset($tmpChangeRequest['CHANGEREQUESTID']);
            }
            $items = INTACCTarray_merge($structured['CHANGEREQUESTENTRY'], $structured['CRENTRIES']);
            $structured['ENTRIES'] = array();

            // Get the header
            if ( !empty($row['CHANGEREQUESTID'])) {
                $changeRequest = $structured;
            }

            // Get the line item
            if ( !empty($row['CHANGEREQUESTENTRY_PROJECTID']) ) {
                $changeRequest['ENTRIES'][] = $items;
            }
        }

        return $ok;
    }

    /**
     * @param string $entity
     */
    function MergeMetaForDimensions($entity)
    {
        parent::MergeMetaForDimensions('changerequest');
        //  Customer should not be included in import template since it is derived from project
        unset($this->imeta['CRENTRIES.CUSTOMERID']);
    }
}