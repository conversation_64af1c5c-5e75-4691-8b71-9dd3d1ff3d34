<?php

/**
 * picker class for project contract pick
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Sage Intacct Inc., All Rights Reserved
 *
 */

class ProjectContractpickPicker extends NPicker
{

    /**
     * @param array $params intialization parameters
     */
    function __construct($params = array())
    {
        $nparams = array(
            'entity' => 'projectcontractpick',
            'pickfield' => 'PICKID',
            'fields' => array('PICKID', 'TOTALRETAINAGEHELD', 'ORIGINALPRICE'),
        );
        parent::__construct($nparams);
    }
}
