/**
 * Utility fucntions for documenteditor.
 */

function doEdit(rec,it,editop,sess,done,ydialog) {
	baseTransferAction(); // disable the buttons
	url = "editor.phtml?.r="+rec+"&.it="+it+"&.do=edit&.op="+editop+"&.dt="+dt+"&.sess="+sess+"&.done="+done+"&.ydialog="+ydialog;
	location.href = url;
}

function GetOwnerDocumentForm(layer, indx) {
	var form = '';
	if (is.nav) {
		form = document.getElementById(layer).ownerDocument.forms[indx];
	} else {
		form = document.getElementById(layer).ownerDocument.forms[indx+1];				
	}
	return form;
}

function GetDuplicateObjectVal() {
	var layer = 'Layer0';
	
	var hdrForm = GetOwnerDocumentForm(layer, 0);
	
	var docparid = hdrForm.elements['_obj__DOCPARID'].value;
	var docno = trimString(hdrForm.elements['_obj__DOCNO'].value);

	if(docno == '' || docno == 'New'){
		return false;
	}
	
	var qrequest = new QRequest;
	var url = 'qrequest.phtml?.function=GetDuplicateDocObject&.handler=QValidator&.sess='+sess
		+'&.otherparams=docparid,docno&.docparid='+escape(docparid)+'&.docno='+encodeURIComponent(docno.encrypt());
	var updateFunc = "RespProcesserDuplicateobject";
	var updateArgs = "";
	qrequest.quickRequest(url, updateFunc, updateArgs, true);
}

function RespProcesserDuplicateobject(m_response){
	var objVal = StripXMLData(m_response.getElementsByTagName('isduplicate'));
	var lineObj = document.getElementById('dupdocno');
	if(objVal == 1){
		lineObj.style.display = 'block';
		lineObj.style.visibility = 'visible';
		baseSetText(lineObj, 'Warning! Document Number already used.');
		lineObj.style.color = 'red';
	}else{
		lineObj.style.display = 'none';
		lineObj.style.visibility = 'hidden';
		baseSetText(lineObj, '');
	}
}

function DisableExchRateType(){
	
	if(overrideExchRateType=='false'){
		var layer = document.forms[0]._currentlayer.value;
		var lineitemForm = GetOwnerDocumentForm(layer, 1);
		lineitemForm.elements['_obj__EXCHRATETYPES__NAME'].disabled = true;
	}
	
}


function DisableProrateButtons(){
	var layer = 'Layer1';
	var lineitemForm = GetOwnerDocumentForm(layer, 0);
	
	for(index=0;;index++){
		computeForShortTermName=lineitemForm.elements['_obj__ENTRIES('+index+')__ITEM__COMPUTEFORSHORTTERM'];
		if(!computeForShortTermName) {
			break;
		}
		if(computeForShortTermName.value=='true'){
			prorateButton=document.getElementById('_obj__ENTRIES('+index+')__PRORATEbuttid');
			if(prorateButton){
				prorateButton.disabled=true;
			}
		}
	}
}

// Disable price and value column if the item type is Standard
function DisableCostFields(){
	var layer = 'Layer1';
	var lineitemForm = GetOwnerDocumentForm(layer, 1);
	
	if(transferin || transferout){
		for(index=0;;index++){
			costmethod = lineitemForm.elements['_obj__ENTRIES('+index+')__COST_METHOD'];
			if(!costmethod) {
				break;
			}
			if(costmethod.value == 'S'){
				itemPrice = lineitemForm.elements['_obj__ENTRIES('+index+')__UIPRICE'];
				helperItemTotal = document.getElementsByName('_helper___obj__ENTRIES('+index+')__UIVALUE')[0];
				if(itemPrice){
					itemPrice.disabled=true;
				}
				if(helperItemTotal){
					helperItemTotal.disabled=true;
				}
			}
		}
	}
}

function SetDefaultDocNo() {
	
	var layer = 'Layer0';
	var headerForm = GetOwnerDocumentForm(layer, 0);
	docno = headerForm.elements['_obj__DOCNO'];
	
	if (docno.value == 'New') {
		docno.value = '';
	}
	docno.focus();
	docno.select();
}

function StripXMLData(response) {
	if (response.length) {
		var nodevalue = new Array();
		for (var i = 0; i < response.length; i++) {
			if (response[i].firstChild)
			{
				nodevalue[i] = new Array(response[i].firstChild.data);
			} else {
				nodevalue[i] = '';
			}
		}
	}
	return nodevalue;
}

function LaunchSmartlink(smartlinkkey, vidval, height, width, resizable, showaddressbar, showtoolbar, showmenubar, showstatusbar, showscrollbar) {
	var params = '';
	params += (height != '') ? ',height=' + height : '';
	params += (width != '') ? ',width=' + width : '';
	params += (resizable != '') ? ',resizable=' + resizable : '';
	params += (showaddressbar == true) ? ',location=yes' : ',location=no';
	params += (showtoolbar == true) ? ',toolbar=yes' : ',toolbar=no';
	params += (showmenubar == true) ? ',menubar=yes' : ',menubar=no';
	params += (showstatusbar == true) ? ',status=yes' : ',status=no';
	params += (showscrollbar == true) ? ',scrollbars=yes' : ',scrollbars=no';
	var URL = "smartlinklaunch.phtml?.sess="+sess+"&.smartlinkkey=" + smartlinkkey + "&.op="+max_dummyid+"&.vid=" + encodeURIComponent(vidval) + "&.dt="+dt;
	window.open(URL, '', params);
}

function FetchSmartlink(smartlinkkey, vidval) {
	var URL = "smartlinklaunch.phtml?.sess="+sess+"&.smartlinkkey=" + smartlinkkey + "&.op="+max_dummyid+"&.vid=" + encodeURIComponent(vidval) + "&.dt="+dt;
	return URL;
}

function getDivText(currType, mode) {
	var currTypeArr;
	var docTypeArr;
	var label;
	var index;

	if (mode == 'Copy') {
		docTypeArr = doctypesCopy;
		label ='Copy';
	} else {
		docTypeArr = doctypes;
		label ='Convert';
	}

	var numRec = docTypeArr.length;
	var hrefHTML = '';

	for (i=0; i<numRec; i++) {
		if (docTypeArr[i] != null && currType == docTypeArr[i][0]){
			index = i;
		}
	}

	currTypeArr = docTypeArr[index];
	var currTypeNum = currTypeArr.length;

	if (currTypeNum > 1) {
		hrefHTML = '<TABLE BORDER=\"1\" CELLSPACING=\"0\" CELLPADDING=\"1\"><tr><td><table>';
	}

	for (i=1; i<currTypeNum; i++) {
		if (currTypeArr[i] != null) {
			dt = currTypeArr[i];
			hrefHTML +=
				'<A CLASS="Result2"  HREF="editor.phtml?.op='+op+'&.sess='+sess+'&.dt='+escape(dt)+'&.copymode='+mode+'&.r='+escape(docid)+'&.done='+escape(done)+'"'+
				'onmouseover=\'window.status="'+label+' to '+dt+'";return true;\''+
				'onmouseout=\'window.status=""; return true;\''+
				'><font size=1>'+dt+'</font></A><BR>'
		}
	}
	if (hrefHTML) {
		hrefHTML += '</table></td></tr></table>';
	}
	return hrefHTML;
}

var onMousedLayer = '';
function showMenu (evt, currType, layer, obj, mode) {

	var hrefHTML = getDivText(currType, mode);

	if (!hrefHTML) {
	  alert('No recall information found for '+currType);
	  return true;
	}

	menuExpand = document.getElementById("convertMenu"+layer);
	if (!menuExpand) return false;
	menuExpand.style.left = baseGetObjectLeft(obj) + obj.offsetWidth;
	menuExpand.style.top = baseGetObjectTop(obj);
	menuExpand.style.position = 'absolute';
	menuExpand.style.visibility = 'visible';
	menuExpand.innerHTML = hrefHTML;
	return false;
}

function hideMenu() {
	if (onMousedLayer == 'set') {
		return;
	}

	var layer = document.forms[0]._currentlayer.value;
	onMousedLayer = '';

	menuExpand = document.getElementById('convertMenu'+layer);
	if (!menuExpand) return false;
	menuExpand.style.visibility = 'hidden';
}

function CustomizeURL() {
	
	if (arguments.length == 1) {
		//alert('CustomizeURL needs at least one argument.  USAGE: CustomizeURL(script,args...)');
		return false;
	}
	
	var layer = document.forms[0]._currentlayer.value;
	
	var delim = '?';	
	var base = arguments[0];
	if (base.indexOf(delim)>0) { delim = '&'; }	

	var url = '';
	for(var i = 1; i < arguments.length; i++) {
		url += ((i == 1) ? '' : '&') + arguments[i];
	}
	
	var hdrForm		 = GetOwnerDocumentForm(layer, 0);
	var lineitemForm = GetOwnerDocumentForm(layer, 1);	
	
	var custvendObj = hdrForm.elements['_obj__CUSTVENDID'];	
	var dt			= hdrForm.elements['_obj__DOCPARID'].value;
	var ID 			= (mod != 'inv') ? custvendObj.value : '' ;
	var trxCurr		= (ismcpEnabled) ? lineitemForm.elements['_obj__CURRENCY'].value : '';
	
	var t_arr = ID.split("--");
	ID = t_arr[0];

	//Appending url pickers for restricted objects
	var restrictedObjLoc = document.getElementById('hasRestrictedLoc');
	var restrictedObjDept = document.getElementById('hasRestrictedDept');
	
	if(restrictedObjLoc.value == '1' || restrictedObjDept.value == '1'){
		url += '&.objectType=VENDOR'+'&.objectLocId='+ID;
	}
	
	// to restrict project dimension
	if (url.indexOf('.it=project')!= -1 && custvendObj && custvendObj != '' && mod == 'so')
	{	
		url += '&.objectType=CUSTOMER'+'&.objectVal='+ID;
	}

	var finalurl = base + delim + url + '&.dt=' + dt + '&.id=' + ID + '&.cmod=' + mod + '&.trxcurr='+trxCurr;
	return finalurl;
}

function AutoPopulateDocno(docno){
	baseSetText(document.getElementById("VIRTUAL.DOCNOLayer1"), docno);
	baseSetText(document.getElementById("VIRTUAL.DOCNOLayer2"), docno);
}

function AutoPopulateDate(date){
	
	baseSetText(document.getElementById("VIRTUAL.WHENCREATEDLayer1"), date);
	baseSetText(document.getElementById("VIRTUAL.WHENCREATEDLayer2"), date);
	
	// do nothing for inventory.
	if (mod=='inv') {return true;}
	
	//var state = document.forms[0]._state.value;
	//var layer = document.forms[0]._currentlayer.value;
	
	//if (state=='showedit'){
	//	layer = 'Layer0';
	//}
	layer = 'Layer0';
	
	var page = GetLayerDoc(layer);

	var term = page.forms[layer+'_form'].elements['_obj__TERM__NAME'].value;	
	var docpar = page.forms[layer+'_form'].elements['_obj__DOCPARID'].value;

	//default duedate to be same as creationdate to begin with.
	var duedate = page.forms[layer+'_form'].elements['_obj__WHENDUE'];
	duedate.value = date;

	if (term == '' || !term_arr[term]) { return true; }

	if (date != '' && docparamsArr[docpar]["DOCCLASS"] == 'Invoice') {
		var termObj = term_arr[term];				
		duedate.value = CalculateDueDateEOM([termObj["MNTH"],termObj["DAY"]],date);
	}
}


function AutoPopulateCustVendID(custvendid, autofill){

	//layer = document.forms[0]._currentlayer.value;
	var layer = 'Layer0';
	var page = GetLayerDoc('Layer0');
	
	if (!custvendid) {
		page.forms[layer+'_form'].elements['_obj__TERM__NAME'].value = "";
		return true;
	}
	
	
	var hdrForm = GetOwnerDocumentForm(layer, 0);
	var lineitemForm = GetOwnerDocumentForm(layer, 1);
	
	var docpar		= hdrForm.elements['_obj__DOCPARID'].value;	
	var date		= hdrForm.elements['_obj__WHENCREATED'].value;
	var shippingMethodObj	= hdrForm.elements['_obj__SHIPVIA'];
	var currencyObj	= lineitemForm.elements['_obj__CURRENCY'];
	
	var t_arr = custvendid.split("--");
	var custvendid = t_arr[0];
	
	var strLoc = "custvendinfo.phtml?.sess="+sess+"&.custvendid="+escape(custvendid)+"&.date="+escape(date)+"&.docpar="+escape(docpar)+"&.mod="+mod;	
	
	var output = baseLoadXMLHTTP(strLoc, true);	
	var nodes = output.getElementsByTagName("entity");

	if (nodes.item(0).getAttribute("invalid")) {
		alert("No such "+custvendTitle);
		hdrForm.elements['_obj__CUSTVENDID'].select();
		return false;
	}

	var onhold = nodes.item(0).getAttribute("onhold");
	// Check if vendor is ON HOLD
	if ( onhold == 'T' && onholdcheck != 'NOCHECK' && !WarnOnHold()) {		
		if (autofill == 'false') {
			history.go(-1);
		} else {
			hdrForm.elements['_obj__CUSTVENDID'].value = '';
			return false;
		}
	}

	term = nodes.item(0).getAttribute("term");
	checkform1099 = nodes.item(0).getAttribute("form1099");
	duedate = nodes.item(0).getAttribute("duedate");
	shipto = nodes.item(0).getAttribute("shipto");
	billto = nodes.item(0).getAttribute("billto");
	custvend = nodes.item(0).getAttribute("entityval");
	crlimit = nodes.item(0).getAttribute("creditlimit");
	entitydue = nodes.item(0).getAttribute("totaldue");
	custmessage = nodes.item(0).getAttribute("message");
	custCurrency = nodes.item(0).getAttribute("currency");
	var shippingMethod = nodes.item(0).getAttribute("shippingmethod");
	var allow_1099_override = nodes.item(0).getAttribute("allow1099override");
	
    if (autofill == 'true') {

		if(mod == 'so' && useVSOE == 'true')
		{
			vsoePriceListName = nodes.item(0).getAttribute("vsoepricelistname");
			if(vsoePriceListName == '')
			{
				alert('Warning: Either there is no default vsoe price list defined, or the default vsoe price list for this customer is inactive.');
			}
			//recurring template does have not field VSOEPRICELIST
			if(typeof(page.forms['Layer1_form'].elements['_obj__VSOEPRICELIST']) != 'undefined') {
			    page.forms['Layer1_form'].elements['_obj__VSOEPRICELIST'].value = vsoePriceListName;		
			}
		}

		document.forms[0].elements['_entid'].value = custvend;
		document.forms[0].elements['_mod'].value = mod;
		document.forms[0].elements['_dt'].value = docpar;

		page.forms[layer+'_form'].elements['_obj__CUSTVENDID'].value = custvend;
        
        // We have to update the header dimension value so we populate the correct one at the line level 
        // after selecting an item. Let's figure out the primary dimension first
        var dimId = '';
        if ( mod == 'so' ) {
            dimId = 'CUSTOMERID';
        } else if ( mod == 'po' ) {
            dimId = 'VENDORID';
        }
        
        // Populate the new primary dimension value
        var dimIDField = document.forms[_form].elements['_obj__HEADER_' + dimId];
        if ( dimIDField && dimId ) {
            dimIDField.value = custvend;
        }
        
		k = 0;
		f1099 = page.forms[_form].elements['helper__obj__ENTRIES('+k+')__FORM1099'];
	
		while(f1099){
            if (checkform1099 != '' && allow_1099_override == 'N') {
                f1099.checked = true;
				page.forms[_form].elements['_obj__ENTRIES('+k+')__FORM1099'].value = 'true';
				f1099.disabled = true;
            }
			else if(checkform1099 != '') {
				f1099.checked = true;
				page.forms[_form].elements['_obj__ENTRIES('+k+')__FORM1099'].value = 'true';
				f1099.disabled = false;
			} else {
				f1099.checked = false;
				page.forms[_form].elements['_obj__ENTRIES('+k+')__FORM1099'].value = 'false';
				f1099.disabled = true;
			}
			k++;
			f1099 = page.forms[_form].elements['helper__obj__ENTRIES('+k+')__FORM1099'];
		}

		page.forms[layer+'_form'].elements['_obj__TERM__NAME'].value = term;
		if (page.forms[layer+'_form'].elements['_obj__WHENDUE']) {
			page.forms[layer+'_form'].elements['_obj__WHENDUE'].value = duedate;
		}
		if (page.forms[layer+'_form'].elements['_obj__MESSAGE'] && mod=='so') {
			page.forms[layer+'_form'].elements['_obj__MESSAGE'].value = custmessage;
		}
		

		page.forms[layer+'_form'].elements['_obj__SHIPTO__CONTACTNAME'].value = shipto;
		page.forms[layer+'_form'].elements['_obj__BILLTO__CONTACTNAME'].value = billto;

		// On change of ship to
		baseSendUIEvent(page.forms[layer+'_form'].elements['_obj__SHIPTO__CONTACTNAME'], 'change');
		
		var virtualCustVendIDLayer1 = document.getElementById("VIRTUAL.CUSTVENDIDLayer1");  
		if (virtualCustVendIDLayer1) {
			baseSetText(virtualCustVendIDLayer1, custvend);
		}
		
		var virtualCustVendIDLayer2 = document.getElementById("VIRTUAL.CUSTVENDIDLayer2"); 
		if (virtualCustVendIDLayer2) {
			baseSetText(virtualCustVendIDLayer2, custvend);
		}		

		if (bt && bt==false){			
			document.forms[layer+'_form'].elements['_obj__BILLTO__CONTACTNAME'].disabled=true;
		}
		
		if (st && st==false){
			document.forms[layer+'_form'].elements['_obj__SHIPTO__CONTACTNAME'].disabled=true;
		}
		
		if(ismcpEnabled && custCurrency != ''){
			currencyObj.value = custCurrency;
			UpdateExchangeRate(sess, qop, baseCurrency);
		}
		if (shippingMethod != '') {            
			shippingMethodObj.value = shippingMethod;
		}
	}
	if (fromRecurDocument) {		
		PopulateLayer();
	}
}

function disableVSOEPriceListField()
{
	page = GetLayerDoc('Layer0');
	if(page.forms['Layer1_form'].elements['_obj__VSOEPRICELIST']) {	
		page.forms['Layer1_form'].elements['_obj__VSOEPRICELIST'].disabled=true; 
	}
	return true;
}

function ShowWarningOnLowQty(docpar) {
	return (docparamsArr[docpar]["WARNONLOWQTY"] == 'true') ? true : false;
}

function ShowWarningOnLowCrLimit() {
	
	var layer = document.forms[0]._currentlayer.value;
	var hdrForm = GetOwnerDocumentForm(layer, 0);
	var docpar	= hdrForm.elements['_obj__DOCPARID'].value;
	
	return (docparamsArr[docpar]["CREDITLIMITCHECK"] == 'true') ? true : false;
}

function AutoPopulatedocparID(docpar){
	
	if (!docpar) return;
	
	var layer = document.forms[0]._currentlayer.value;
	
	if (mod != 'inv') {
		var hdrForm = GetOwnerDocumentForm(layer, 0);		
		document.forms[0].elements['_entid'].value = hdrForm.elements['_obj__CUSTVENDID'].value;
	}
		
	document.forms[0].elements['_action'].value = 'refresh';
	document.forms[0].elements['_mod'].value = mod;
	document.forms[0].elements['_dt'].value = docpar;

	if(BeforeSubmit()){
		document.forms[0].submit();
	}
	
}

function RefreshOnSourceTransSelect(mode, source, submitFlag) {
	
	if (!source) return true;

	var submitForm = document.forms[0];
	var layer = submitForm._currentlayer.value;	
	var custvendid = '';
	
	var hdrForm = GetOwnerDocumentForm(layer, 0);
	
	if (mod != 'inv') {
		var custvendid	= hdrForm.elements['_obj__CUSTVENDID'].value;
		var t_arr = custvendid.split("--");
		custvendid = t_arr[0];
		submitForm.elements['_entid'].value = custvendid;
	} 
	
	submitForm.elements['_copymode'].value = mode;
	submitForm.elements['_dt'].value = hdrForm.elements['_obj__DOCPARID'].value;
	submitForm.elements['_mod'].value = mod;
	submitForm.elements['_date'].value = hdrForm.elements['_obj__WHENCREATED'].value;
	submitForm.elements['.r'].value = source;

	submitForm.elements['_action'].value = 'new';
	submitForm.elements['_state'].value = 'init';
	
	if (BeforeSubmit() && submitFlag) {
		WarnOnSaveDocument();
		submitForm.submit();
	}	
}

function ReCalculateClick(_layer,_form,_mlinename) {
	
	document.forms[0].elements['_action'].value='refresh';
	document.forms[0]._currentlayer.value = _layer;
	document.forms[0]._currentmline_name.value = _mlinename;
	
	if(BeforeSubmit()){
		document.forms[0].submit();
	}

	return false;
}


function AvailableClick(_layer, _form, linenum) {
	
	var _form = (_form == null) ? 0 : _form;
	var page = GetLayerDoc(_layer);

	var itemId = page.forms[_form].elements['_obj__ENTRIES('+linenum+')__ITEMID'].value;
	
	if(itemId == null || itemId == ''){
		alert('Please select an Item first.');
		return false;
	}
	
	var avail = 'T';
	
	var rec1 = AutoFillLookUpID(itemId, false);	
	
	// In case of kits, the item components needs to be exploded and retrieved from backend, so force to get the item detail fetch.
	if (!rec1 || (rec1.itemtype=='K' && dt != 'Disassemble Kits') || dt == 'Build Kits') {
		var rec1 = ItemDetailFetch(itemId,avail);
	}
	
	iskit = '';
	if((rec1.itemtype=='K' && dt != 'Disassemble Kits') || dt == 'Build Kits') {
		itemId = rec1.itemid;
		iskit = '&_KIT='+avail;
	}
	
	url = 'reportor.phtml?.sess='+sess+'&.op='+invstatusop+'&.type=_html&_obj__PRODUCTLINEID=&_obj__FROMWAREHOUSEID='
		+'&_obj__TOWAREHOUSEID=&_obj__FROMITEMID='+encodeURIComponent(itemId.encrypt())
		+'&_obj__TOITEMID='+encodeURIComponent(itemId.encrypt())+iskit;
	Launch(url,'mywindow','800','200');

}

function KitAlloClick(_layer, _form, linenum) {
	
	_form = (_form == null) ? 0 : _form;
	var page = GetLayerDoc(_layer);

	var prefix = "UI";
	if(ismcpEnabled){
		prefix = "TRX_";
	}

	var itemId = page.forms[_form].elements['_obj__ENTRIES('+linenum+')__ITEMID'];
	var itemTrxPriceField = page.forms[_form].elements['_obj__ENTRIES('+linenum+')__'+prefix+'PRICE'];
	var itemRetailPriceField = page.forms[_form].elements['_obj__ENTRIES('+linenum+')__'+'RETAILPRICE'];

	
	var lineTotal = 0;
	if(itemTrxPriceField && itemTrxPriceField.value && itemTrxPriceField.value!='') {
		lineTotal = itemTrxPriceField.value;
	}

	if(itemId.value==null || itemId.value==''){
		alert('Please select an Item first.');
		return false;
	}
	
	var avail = 'T';
	
	var rec1 = AutoFillLookUpID(itemId.value, false);
	if (!rec1) {
		var rec1 = ItemDetailFetch(itemId.value,avail);
	}	

	var itemtype = rec1.itemtype;
	var itemName = itemId.value.split('--')[0];

	//readtime of the form
	var readtime = document.getElementsByName('.readtime')[0].value;
	
	//transaction date
	var page0 = GetLayerDoc('Layer0');
	var trxDate = page0.forms['Layer0_form'].elements['_obj__WHENCREATED'].value;

	//vsoe price list
	var pListName = page.forms['Layer1_form'].elements['_obj__VSOEPRICELIST'].value;
	
	if (useVSOE == 'true' && (pListName == null || pListName == '')) {
		alert('You must pick a VSOE price list first.');
		return false;
	}
	
	var createdFrom = page0.forms['Layer0_form'].elements['_obj__CREATEDFROM'].value;

	//allocKey that can uniquely identify this allocation
	var allokey = sess + readtime + linenum + itemName;	
	// when converting we do not want changing the price or VSOE pricelist to reset fulfillment status
	if (createdFrom == '' || docmode != 'create') {
	    allokey += pListName;
	    if(lineTotal != 0) {
	        allokey += RoundCurrency(lineTotal);
	    }
	}
	allokey =  escape(allokey);
	
	var url = '';
	if(docmode == 'create') {
		url = 'editor.phtml?.sess='+sess+'&.op='+createKitAlloOp+'&.do=edit&.r='+escape(itemName)+'&.allomode='+docmode+'&.allokey='+allokey+'&.it=vsoekitallocation&.itemName='+escape(itemName)+'&.trxDate='+escape(trxDate)+'&.pListName='+escape(pListName)+'&.uiprice='+lineTotal;
	} else if(docmode == 'edit') {
		docid = document.getElementsByName('_obj__DOCID')[0].value;
		url = 'editor.phtml?.sess='+sess+'&.op='+createKitAlloOp+'&.do=edit&.r='+escape(itemName)+'&.docid='+escape(docid)+'&.lineno='+linenum+'&.allomode='+docmode+'&.allokey='+allokey+'&.it=vsoekitallocation&.itemName='+escape(itemName)+'&.trxDate='+escape(trxDate)+'&.pListName='+escape(pListName)+'&.uiprice='+lineTotal;
	} else {
		docid = document.getElementsByName('_obj__DOCID')[0].value;
		url = 'editor.phtml?.sess='+sess+'&.op='+viewKitAlloOp+'&.do=view&.r='+escape(itemName)+'&.docid='+escape(docid)+'&.lineno='+linenum+'&.allomode='+docmode+'&.allokey='+allokey+'&.it=vsoekitallocation&.itemName='+escape(itemName)+'&.uiprice='+lineTotal;
	}
	
	//baseModalDialog(url, "", 'vsoeallowindow', 0, 1000, 600, false);
	Launch(url,'vsoeallowindow','1000','600');
}

function LaunchReport(_layer, _form, linenum, reporttype) {
	_form = (_form == null) ? 0 : _form;
	var page = GetLayerDoc(_layer);

	var itemId = page.forms[_form].elements['_obj__ENTRIES('+linenum+')__ITEMID'];

	if(itemId.value==null || itemId.value==''){
		alert('Please select an Item first.');
		return false;
	}

	var whsekey = '';
	if(docmode!='view'){
		whsekey = page.forms[_form].elements['_obj__ENTRIES('+linenum+')__WAREHOUSE__LOCATION_NO'];
	}else{
		whsekey = document.getElementById('_obj__ENTRIES('+linenum+')__WAREHOUSE__LOCATION_NO');
	}

	//splitting warehouse id from warehouseid-name to compare
	whsekey = whsekey.value.split('--');
	whsekey = whsekey[0];
	
	var url = '';
	if (reporttype == 'picklist') {
		var url = 'reportor.phtml?.sess='+sess+'&.op='+phyinventoryop+'&.type=_html'
            +'&_obj__FROMWAREHOUSEID='+encodeURIComponent(whsekey.encrypt())
            +'&_obj__TOWAREHOUSEID='+encodeURIComponent(whsekey.encrypt())
            +'&_obj__FROMITEMID='+encodeURIComponent(itemId.value.encrypt())
            +'&_obj__TOITEMID='+encodeURIComponent(itemId.value.encrypt())
            +'&_obj__REPORTGROUPINGS='+'Warehouse'.encrypt()+'&_obj__REPORTON='+'ITEMID'.encrypt();
	} else if (reporttype == 'cost') {
		url = 'reportor.phtml?.sess='+sess+'&.op='+itemcogsop+'&.type=_html'
            +'&_obj__FROMWAREHOUSEID='+encodeURIComponent(whsekey.encrypt())
            +'&_obj__TOWAREHOUSEID='+encodeURIComponent(whsekey.encrypt())
            +'&_obj__FROMDOCID='+encodeURIComponent(newdocid.encrypt())
            +'&_obj__TODOCID='+encodeURIComponent(newdocid.encrypt())
            +'&_obj__FROMITEMID='+itemId.value.encrypt()
            +'&_obj__TOITEMID='+itemId.value.encrypt();
	}
	
	if (url != '') {
		Launch(url,'mywindow','1000','400');
	}
}

function HdrPickListReport(_layer, _form, linenum) {
	_form = (_form == null) ? 0 : _form;
	var page = GetLayerDoc(_layer);				

	var docid = page.forms[_form].elements['_obj__DOCID'].value;
	var url = 'reportor.phtml?.sess='+sess+'&.op='+pickinglistop+'&.type=_pdf' 
		+'&_obj__FROMDOCID='+encodeURIComponent(docid.encrypt())
		+'&_obj__TODOCID='+encodeURIComponent(docid.encrypt());
	Launch(url,'mywindow','800','200');
}

function PickListReport(_layer, _form, linenum) {
	LaunchReport(_layer, _form, linenum, 'picklist');
}

/* This function construct the link to drill down to Inventory Costing Report which shows the cost distribution for this line item */
function CostReportClick(_layer, _form, linenum) {
	LaunchReport(_layer, _form, linenum, 'cost');
}

//function SLBInfoClick(_layer, _form, linenum)
function SLBInfoClick(_layer, _form, linenum) {
	_form = (_form == null) ? 0 : _form;
	var page = GetLayerDoc(_layer);

	var itemId = page.forms[_form].elements['_obj__ENTRIES('+linenum+')__ITEMID'];
	
	var itemid = itemId.value.split("--")[0];
	
	if(itemid==null || itemid==''){
		alert('Please select an Item first.');
		return false;
	}
	
	if(docmode != 'view'){
		var unit = page.forms[_form].elements['_obj__ENTRIES('+linenum+')__UNIT'];
		var whsekey = page.forms[_form].elements['_obj__ENTRIES('+linenum+')__WAREHOUSE__LOCATION_NO'];
	} else {
		var unit = document.getElementsByName('_obj__ENTRIES('+linenum+')__UNIT')[0];
		var whsekey = document.getElementsByName('_obj__ENTRIES('+linenum+')__WAREHOUSE__LOCATION_NO')[0];
	}

	var whseid = whsekey.value.split("--")[0];
	
	var page0 = GetLayerDoc('Layer0');
	var date = page0.forms['Layer0_form'].elements['_obj__WHENCREATED'].value;
		
	var avail = 'T';
	
	var recitem = AutoFillLookUpID(itemid, false);
	
	if (!recitem || recitem.itemtype == 'K') {		
		var recitem = ItemDetailFetch(itemid,avail);		
	}
	
	id = recitem.itemid.split('--');
	recitemid = id[0];

	var kititems = recitemid;
	var itemtype = recitem.itemtype;
	var isserialized = recitem.isserialized;

	var quantity 	= page.forms[_form].elements['_obj__ENTRIES('+linenum+')__UIQTY'];
		
	var ufactor = 1;
	if (recitem) {
		ufactor = getUnitFactor(recitem, unit.value);
	}
	
	// If we cannot find an unit factor default it to 1.
	if (!ufactor || ufactor == 0) {
		ufactor = 1;
	}
	
	if (itemtype == 'I'){		
		
		if(isserialized){
			
			var newquantity = quantity.value * ufactor;
						
			var _do= (docmode == 'view') ? 'view' : '';

			if (docmode != 'create') {
				inquantity = page.forms[_form].elements['_inquantity('+linenum+')'];
				if(inquantity && parseInt(newquantity) < parseInt(inquantity.value)) {
					newquantity = inquantity.value;
				}
			}
			
			linenum = parseInt(linenum);
			
			url = 'editor.phtml?.sess='+escape(sess)+'&.op='+escape(docentrytrackop)+'&.inquantity='+escape(newquantity)+'&.initemid='+escape(itemid)+'&.kitid='+escape(itemid)+'&.kititems='+escape(kititems)+'&.popup=1&.itemtype='+escape(itemtype)+'&.docid='+escape(newdocid)+'&.docmode='+escape(docmode)+'&.do='+escape(_do)+'&.date='+escape(date)+'&.whsekey='+escape(whseid)+'&.in_out='+escape(in_out)+'&.itemlineno='+escape(linenum)+'&.docparRecno='+escape(docparRecno)+'&.parentid='+escape(parentid)+'&.isreturn='+escape(isReturn)+'&.isbackorder='+escape(isBackOrder)+'&.type=_html';

			Launch(url,'mywindow','900','400');
			
		} else {
			alert("This Item '" + recitem.itemid + "'  is not set for tracking (Serialization, Lot Tracking or Bin Tracking) information");
			return false;
		}
		
	} else if (itemtype == 'K') {
		
		var namelist = kititems.replace(/'/g,"");
		items = namelist.split(",");
			
		var isKitSerialized = false;
		// Check if anyone of the item in the kit is serialized.		
		for (var m = 0; m < items.length; m++) {
			rec1  = ItemDetailFetch(items[m], avail);			
			if(rec1.isserialized) { isKitSerialized = true; }			
		}
		
		
		if(isKitSerialized == false){
			alert("None of the component for this kit is set for tracking (Serialization, Lot Tracking or Bin Tracking) information");
		} else {
			// this code is for calculating the conversion factor for the line item.
			reckit = ItemDetailFetch(itemid,'C');

			var newquantity = quantity.value * ufactor;
			
			// this code is for calculating the conversion factor for the line item.
			//if(newdocid.length==0){	newdocid = ' ';	}
			
			url = 'editor.phtml?.sess='+escape(sess)+'&.op='+escape(docentrytrackkitop)+'&.inquantity='+escape(newquantity)+'&.kitid='+escape(itemid)+'&.popup=1&.itemtype='+escape(itemtype)+'&.docid='+escape(newdocid)+'&.docmode='+escape(docmode)+'&.date='+escape(date)+'&.whsekey='+escape(whseid)+'&.in_out='+escape(in_out)+'&.itemlineno='+escape(linenum)+'&.docparRecno='+escape(docparRecno)+'&.parentid='+escape(parentid)+'&.isreturn='+escape(isReturn)+'&.isbackorder='+escape(isBackOrder)+'&.type=_html';
			 
			LaunchNew(url,'mywindow','900','400');
		}
	}
}


function LaunchNew(aUrl, aName, aWidth, aHeight){
	aUrl = aUrl+ (aUrl.indexOf('?')> 0 ? '&' : '?') + '.sess='+sess;
	//var top = aWidth > 10 ? 100 + aName.length * 10 : 1000;
	//var helpwinparam =  'width=' + aWidth + ',height=' + aHeight +',toolbar=no,location=no,directories=no,status=yes,menubar=no,scrollbars=yes,resizable=yes,dependent,left='+top+',top='+top;
	baseModalDialog(aUrl, "", aName, LaunchNewResume, aWidth, aHeight, false);
}

function LaunchNewResume(popup) {
	if (popup == null) {return;}
	if (popup.opener == null ) {popup.opener = self;}
	// if this is a visible window make sure it goes on top
	if (aWidth > 10) {
		popup.location.href=aUrl;
		popup.focus();
	}
}

function Launch(aUrl, aName, aWidth, aHeight)
{
	aUrl = aUrl+ (aUrl.indexOf('?')> 0 ? '&' : '?') + '.sess='+sess;
	if (window.PAGE_LAYOUT_TYPE && PAGE_LAYOUT_TYPE === 'Q' && typeof showQxDialog === 'function') {
		showQxDialog('',aUrl,'none','form','url','');
	} else {
		var top = aWidth > 10 ? 100 + aName.length * 10 : 1000;
		var helpwinparam = 'width=' + aWidth + ',height=' + aHeight + ',toolbar=no,location=no,directories=no,status=yes,menubar=no,scrollbars=yes,resizable=yes,dependent,left=' + top + ',top=' + top;
		var popup = window.open(aUrl, aName, helpwinparam);
		if (popup == null) {
			return;
		}
		if (popup.opener == null) {
			popup.opener = self;
		}
		// if this is a visible window make sure it goes on top
		if (aWidth > 10) {
			//popup.location.href=aUrl;
			popup.focus();
		}
	}
}

function SuggestPriceClick(_layer, _form, linenum) {
	_form = (_form == null) ? 0 : _form;
	var page = GetLayerDoc(_layer);
	
	page.forms[_form].elements['_obj__PRCLINENUM'].value = linenum;
	document.forms[0].elements['_action'].value='refresh';
	document.forms[0]._currentlayer.value = _layer;
	
	if(BeforeSubmit()){
		document.forms[0].submit();
	}
	return false;
}

function AutoFillLookUpID(id, inc_whse) {
	var oldId;
	var rec;
	if (oldId == id) return rec;

	//idlen = id.length;
	//var numRec = itemdensearr.length;
	
	//splitting item id from itemid-name to compare
	id = id.split('--');
	itemID = id[0];
	
	var rec = itemdensearr[itemID];
	
	if (rec) {
		if (rec.price == null) {
			rec.price = 0;
		}
		
		if (rec.uomgrpname == null) {
			rec.uomgrpname = defaultuomgrp;
		}
	
		if (inc_whse) {
			var numWhse = 0;
			if (whsedensearr) {
				numWhse = whsedensearr.length;
			}
		
			var whseqtys = [];	
			var initialQty = 0;
			
			if (!qtydensearr || qtydensearr == '') {  // it is an increase
				initialQty = '';
			}
		
			for (i=0; i<numWhse; i++) {
				whseqtys[i] = {
						"warehousekey" : unreplaceXMLEscapeCharacters(whsedensearr[i]), 
						"qtyleft" : initialQty 
					};		
			}
			
			
			for (j=0; j<numWhse; j++) {				
				//splitting warehouse id from warehouseid-name to compare
				whid = whseqtys[j].warehousekey.split('--');
				whid = whid[0];		
				
				itemwhseqty = qtydensearr[itemID+"--"+whid];

				if (itemwhseqty && itemwhseqty.warehousekey != null && (itemwhseqty.warehousekey == whid || itemwhseqty.warehousekey == whseqtys[j].warehousekey)) {
					// To maintain the whse--name we need to retain it from previous value.
					whsekey = whseqtys[j].warehousekey;
					whseqtys[j] = itemwhseqty;			
					whseqtys[j].warehousekey = whsekey;
				}
			}
			rec.wh = whseqtys;
		}		
	}
	
	return rec;	
}

function AutoFillLookUpDesc(desc) {
	var oldDesc;
	var rec;
	if (oldDesc == desc) return rec;

	desclen = desc.length;
	numRec = itemdensearr.length;

	for (var i=0; i<numRec; i++) {
		if (itemdensearr[i] != null && itemdensearr[i][1].substring(0,desclen).toLowerCase() == desc.toLowerCase()){
			//return itemdensearr[i];
			return AutoFillLookUpID(itemdensearr[i][0], false);
		}
	}

	//Item Not Found, fetch using description
}

function DocumentOnLoad() {

	SetupSavedItems('Layer1','Layer1_form'); 
	SetTabDispAttributes();
	if(ismcpEnabled){
		DisableExchRateType();
	}
	
	switch (mod)
	{
		case 'inv':
				InvDocumentOnLoad();
				break;	
		case 'so':
				SODocumentOnLoad();				
				break;
		case 'po':
				PODocumentOnLoad();
				break;			
		default :
				break;
	}

	// check for duplicate when a transaction is converted
	if(document.forms[1].elements['_obj__DOCNO'].isContentEditable){
		GetDuplicateObjectVal();
	}

	//If its duplicate then block the transaction
	if(document.forms[0]._kNoDupl.value == 'true') {
		var message = 'An Invoice in Accounts Receivable with the same number already exists. Please use a different number';
		if (mod == 'po') {
			message = 'A Bill in Accounts Payable with the same '+custvendTitle+' Reference Number already exists. Please use a different number';
		} 
		alert(message);
		document.forms[1].elements['_obj__VENDORDOCNO'].focus();
		document.forms[0]._kNoDupl.value = '';
	}

	//If there is a warning then show the warning and decide to continue or not based on the response
	if(document.forms[0]._kNoWarn.value == 'true') {
		var message = 'An Invoice in Accounts Receivable with the same number already exists. Do you want to use the same number?';
		if (mod == 'po') {
			message = 'A Bill in Accounts Payable with the same '+custvendTitle+' Reference Number already exists. Do you want to use the same number?';
		} 

		if (confirm(message)) {
			var ok = BeforeSubmit(); 
			if (!ok)
			{
				return ok;
			}
			state = document.forms[0]._state.value;
			action = 'save';
			if (state == 'shownew') {
				action = 'create';
			}
			document.forms[0]._action.value = action;
			document.forms[0]._kNoWarn.value = 'true'; 
			document.forms[0].submit();
		} else {
			document.forms[0]._kNoWarn.value = '';
		}
	}
	
	if (mod != 'inv') {
		if (st == 'false') {
			document.forms[1].elements['_obj__SHIPTO__CONTACTNAME'].disabled=true;
		}
		if (bt == 'false') {
			document.forms[1].elements['_obj__BILLTO__CONTACTNAME'].disabled=true;
		}
	}
	
	return true;
}

function InvDocumentOnLoad() {
	DisableCostFields();
    state = document.forms[0]._state.value;
	//Get the cost of item only if the document is transfer out and when it is in copy mode. 
	//And if the transfer out document is being converted from any document then get the cost of the item from its parent document.
	if ((transferin || transferout ) && parentid && state=='shownew' && (copymode=='Copy' || copymode=='Update'))
	{
		GetCost_InvTransInOutConvertCopy();
	
	}
	return true;
}

function SODocumentOnLoad() {
	if (itemsHaveStartEndDate == true) {
		DisableProrateButtons();
	}

	if (multivisibility == true) {
		if(document.forms[1].elements['_obj__CUSTVENDID'].value != '') {
			FilterRestrictions('customer', document.forms[1].elements['_obj__CUSTVENDID'].value, 'locationpick', sess, objop);
			FilterRestrictions('customer', document.forms[1].elements['_obj__CUSTVENDID'].value, 'departmentpick', sess, objop);
		}
	}

	if (restrictproject)
	{
		if(document.forms[1].elements['_obj__CUSTVENDID'].value != '') {
			RestrictProjectDimension('_obj__CUSTVENDID','CUSTOMER','ENTRIES',projectop)
		}

	}

	var salecon = document.forms[0].elements['.salecon'].value;
	if (copymode != '' || salecon=='T') {
		AutoPopulateCustVendID(document.forms[0].elements['_obj__CUSTVENDID'].value, 'false');
	}

	return true;
}

function PODocumentOnLoad() {
	if (multivisibility == true) {
		if(document.forms[1].elements['_obj__CUSTVENDID'].value != '') {
			FilterRestrictions('vendor', document.forms[1].elements['_obj__CUSTVENDID'].value, 'locationpick', sess, objop);
			FilterRestrictions('vendor', document.forms[1].elements['_obj__CUSTVENDID'].value, 'departmentpick', sess, objop);
		}
	}
	
	if (copymode != '') {
		AutoPopulateCustVendID(document.forms[0].elements['_obj__CUSTVENDID'].value, 'false');
	}

	return true;
}

function SetupSavedItems(_layer, _form) {
	layer = document.forms[0]._currentlayer.value;
	//var rec = 0; //rec is global variable here.
	
	if (in_out == 'Decrease') {
		LoadQuantityDenseArr();
	} else {
		qtydensearr = '';
	}
	
	custvendid = '';
	billtocontact='';
	shiptocontact='';
	currency='';
	if(ismcpEnabled){
		prefix = "TRX_";
	}else{
		prefix = "UI";
	}
	
	var headerform		= GetOwnerDocumentForm(layer, 0);	
	var lineitemform	= GetOwnerDocumentForm(layer, 1);
		
	if (mod != 'inv') {
		custvendid = headerform.elements['_obj__CUSTVENDID'].value;
		billtocontact = headerform.elements['_obj__BILLTO__CONTACTNAME'].value;
		shiptocontact = headerform.elements['_obj__SHIPTO__CONTACTNAME'].value;
	}

	date = headerform.elements['_obj__WHENCREATED'].value;
	docpar = headerform.elements['_obj__DOCPARID'].value;
	if(ismcpEnabled){
		currency = lineitemform.elements['_obj__CURRENCY'].value;
	}	

	t_arr = custvendid.split("--");
	custvendid = t_arr[0];
	custvendname = t_arr[1];


	_form = (_form == null) ? 0 : _form;
	page = GetLayerDoc(_layer);
	exchRate = GetExchangeRate(_layer, _form);
	//ITEMID = 0; DESC = 1; TAX = 2; PRODLINE = 3; BASEUOM = 4; PURUOM = 5; SALUOM=6; PRECISION=7; PRICE = 8; WH = 9; ITEMTYPE = 10; QTYCHECK=11;

	multi_line_name = 'ENTRIES';
	
	numrec = page.forms[_form].elements['_obj__' + multi_line_name + '_numofrows_top'].value;
	
	for (var m=0; m<numrec; m++) {
		itemId 			= page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__ITEMID'];
		itemTaxable 	= page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__ITEM__TAXABLE'];
		helperItemUnit 	= page.forms[_form].elements['helper__obj__'+multi_line_name+'('+m+')__UNITFACTOR'];
		itemProdLine 	= page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__PRODLINE'];
		itemUnitText 	= page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__UNIT'];

		itemUnit 		= page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__UNITFACTOR'];
		itemWarehouseText = page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__WAREHOUSE__LOCATION_NO'];
		item_wh_qty 	= page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__WAREHOUSEAVAIL'];
		helper_item_wh_qty = page.forms[_form].elements['helper__obj__'+multi_line_name+'('+m+')__WAREHOUSEAVAIL'];
		itemPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__'+prefix+'PRICE'];
		listPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__RETAILPRICE'];
		
		itemTotal = page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__'+prefix+'VALUE'];

		if(ismcpEnabled){
			basePrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__UIPRICE'];
			baseTotal = page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__UIVALUE'];
		}else{
			basePrice='';
			baseTotal='';
		}

		itemQuantity = page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__UIQTY'];

		if (itemId.value != '') {
			
			var item = itemId.value.i_slice(0, '--');
			
			rec = AutoFillLookUpID(item, true);
			
			rec.itemid = rec.itemid+'--'+rec.name;

			if (itemUnitText.value == '') itemUnitText.value = itemUnit.value;
			
			itm_uom = uom_arr[escape(rec.uomgrpname)];
				
			if (itm_uom) { 
				var uomlength = itm_uom.length;
				var unitSelected = false;
				var defModUnitIndx = 0;
				var defModUnit = '';
				
				for (var j=uomlength ; j >= 0; j--) {
					if (itm_uom[j] == null) continue;		
					
					if (itemUnitText.value == itm_uom[j]['UNIT']) {
						newoption =  new Option(itm_uom[j]['UNIT'], itm_uom[j]['CONVFACTOR'],true,true);					
						itemUnit.value = itm_uom[j]['CONVFACTOR'];
						itemUnitText.value = itm_uom[j]['UNIT'];
						unitSelected = true;
					}else{
						newoption =  new Option(itm_uom[j]['UNIT'], itm_uom[j]['CONVFACTOR'],false,false);
					}
					
					// Capture the module level unit so that we can default to it in case where the uom group is changed for the item.
					if (itm_uom[j]['DEFMODUNIT'] == true) {
						defModUnitIndx = j;
						defModUnit = itm_uom[j]['UNIT'];
					}
					
					helperItemUnit.options[j] = newoption;
				}
							
				// when the uom group for the item is changed, we have to default the selected unit to the module level unit.
				// Also we need to update the unitText to the default module unit, so that edit and save works.
				if (!unitSelected) {
					helperItemUnit.options[defModUnitIndx].selected = true;
					itemUnitText.value = defModUnit;
				}
			}
			
			if(listPrice){ 
				degree	= (rec && rec.precision != '' && rec.precision != null) ? rec.precision : app_precision;
				if(copymode == 'Copy' || copymode == 'Update'){
					PriceCheck(custvendid, billtocontact, shiptocontact, item, itemProdLine.value, itemQuantity.value, date, docpar, degree, currency,'', _layer, _form, '', m, degree, 'RespProcesser_ListPrice');
				}else{
					listPrice.value = RoundCurrency(listPrice.value,degree);
				}
			}			

			num_wh = rec.wh.length;
			
			bNonInvItem = (rec.itemtype == 'NI' || rec.itemtype == 'NP' || rec.itemtype == 'NS' ) ? true : false ;

			if( !bNonInvItem ){
					if (itemWarehouseText.value == '') itemWarehouseText.value = item_wh_qty.value;
					//Shakil-- don't know how it retrieves value like rec[WH][0][0].value(throws error), changing it rec[WH][0][0]
					if (itemWarehouseText.value == '' && rec.wh[0] && rec.wh[0].warehousekey) itemWarehouseText.value = rec.wh[0].warehousekey;
					for (var j=num_wh ; j >= 0; j--) {
						if (rec.wh[j] == null) continue;
						if (itemWarehouseText.value == rec.wh[j].warehousekey) {
							newoption =  new Option(rec.wh[j].warehousekey, rec.wh[j].qtyleft,true,true);
							item_wh_qty.value = rec.wh[j].qtyleft;
							itemWarehouseText.value = rec.wh[j].warehousekey;
						}else{
							newoption =  new Option(rec.wh[j].warehousekey, rec.wh[j].qtyleft,false,false);
						}
						helper_item_wh_qty.options[j] = newoption;
					}
			}
			else {
				itemWarehouseText.value = '';
				helper_item_wh_qty.options.selectedIndex = -1;
				helper_item_wh_qty.disabled = true;
			}
			
			degree	= (rec && rec.precision != null && rec.precision != '') ? rec.precision : app_precision;
			
			itemPrice.value =  RoundCurrency(itemPrice.value,degree);
			
			itemTotal.value =  CalcTotal(itemQuantity.value, itemPrice.value);

			// need to check bug#19077
			if(baseTotal && itemQuantity){
				if(baseCurrency == currency){
					baseTotal.value = itemTotal.value;
				}else{
					baseTotal.value =  RoundCurrency(itemTotal.value * exchRate);
				}
			}
			if(basePrice && itemQuantity){
				if(baseCurrency == currency){
					basePrice.value = itemPrice.value;
				}else{
					basePrice.value = RoundCurrency((itemTotal.value * exchRate)/itemQuantity.value, degree);
				}
			}

			itemProdLine.value = rec.prodline;
			if (rec.tax == 'T') itemTaxable.value = 'true';
		}

		// formatting the controls
		// right aligning the price and extended price fields
		try{
			itemPrice.style.textAlign = "right";
			itemTotal.style.textAlign = "right";
			if(basePrice){
				basePrice.style.textAlign = "right";
			}
			if(baseTotal){
				baseTotal.style.textAlign = "right";
			}
			if(listPrice){
				listPrice.style.textAlign = "right";
			}
		}catch(err){	}
	}
}

function CalcTotal(itemQuantity, itemPrice){
	var itemTotal = 0;
	var factor = 1;

	if(itemQuantity < 0 || itemPrice < 0) {			
		if(itemQuantity < 0){
			factor = -1  * factor;
		}
		if(itemPrice < 0){
			factor = -1 * factor;
		}
		itemQuantity = Math.abs(itemQuantity);
		itemPrice	 = Math.abs(itemPrice);

		itemTotal = RoundCurrency(eval(itemQuantity  * itemPrice ).toFixed(8))
		itemTotal = factor * itemTotal;
	}else {
		itemTotal =  RoundCurrency(eval(itemQuantity * itemPrice).toFixed(8));
	}
	return itemTotal;
}

function PriceCheck(custvendid, billtocontact, shiptocontact, itemid,prodline,qty,date,docpar, degree, currency,whse, _layer, _form, from, rownum, fieldid, functionCall) {

	var qrequest = new QRequest;
	var url = 'qrequest.phtml?.function=FetchPrice&.handler=QRequest&.entity=item&.sess=' + sess
        + '&.otherparams=custvendid,billtocontact,shiptocontact,itemid,prodline,qty,currency,whse,date,docpar,mod'
		+ '&.custvendid=' + escape(custvendid) + '&.billtocontact=' + escape(billtocontact.encrypt())
        + '&.shiptocontact=' + escape(shiptocontact) + '&.itemid=' + escape(itemid) + '&.prodline=' + escape(prodline)
        + '&.qty=' + escape(qty) + '&.currency=' + escape(currency) + '&.whse=' + escape(whse) + '&.date='+escape(date)
        + '&.docpar='+escape(docpar) + '&.mod=' + escape(mod);

	var updateFunc = functionCall;

	var updateArgs = "'"+_layer+"','"+_form+"','"+from+"','"+rownum+"','"+fieldid+"'";

	qrequest.quickRequest(url, updateFunc, updateArgs, false);
}

function ItemDetailFetch(itemid,avail) {
			
	var itemid = itemid.split('--')[0];
	
	if (!itemid || itemid == '') {
		return false
	}
	
	var strLoc = "itemDetailfetch.phtml?.sess="+sess+"&.itemid="+escape(itemid)+"&.avail="+escape(avail)+"&.tags=1&.mod="+mod;

	var output = baseLoadXMLHTTP(strLoc, true);
	var nodes = output.getElementsByTagName("item");
	
	var itemObj = new item();
	
	if (nodes.length > 0) {
		var itemid = nodes.item(0).getAttribute("itemid");
		
		if (itemid) {
			itemObj.itemid = itemid;
			itemObj.itemtype = nodes.item(0).getAttribute("itemtype");
			itemObj.enablesno = nodes.item(0).getAttribute("enablesno");
			itemObj.enablelot = nodes.item(0).getAttribute("enablelot");
			itemObj.enablebin = nodes.item(0).getAttribute("enablebin");
			itemObj.enableexpiration = nodes.item(0).getAttribute("enableexpiration");
			itemObj.stdunit = nodes.item(0).getAttribute("stdunit");
			itemObj.punit = nodes.item(0).getAttribute("punit");
			itemObj.punit_factor = nodes.item(0).getAttribute("punit_factor");
			itemObj.sunit = nodes.item(0).getAttribute("sunit");
			itemObj.sunit_factor = nodes.item(0).getAttribute("sunit_factor");
			itemObj.isserialized = nodes.item(0).getAttribute("isserialized");
		}
	}
	
	return itemObj;
}

// this is the item object structure that will be used for all UI logic purpose in the front end.
function item() {
	
	this.itemid = '';
	this.name = '';
	this.itemtype = '';
	this.enablesno = 'F';
	this.enablelot = 'F';
	this.enablebin = 'F';
	this.enableexpiration = 'F';
	this.stdunit = '';
	this.punit = '';
	this.punit_factor = 1;
	this.sunit = '';
	this.sunit_factor = 1;
	this.isserialized = false;
	this.taxable = 'F';		
	this.prodline = '';	
	this.precision = 0;
	this.price = 0;
	this.qtycheck = '';
	this.listprice = 0;
	this.quantity = 0;
	this.memo = '';
	this.sfdcpricing = '';
	this.servicestartdate = '';
	this.serviceenddate = '';
	this.hasstartenddate = '';
	this.termperiod = '';
	this.totalperiod = '';
	this.computeforshortterm = '';
	this.itemIdvalue = '';
	this.itemname = '';
	this.renewalmacroid = '';
	this.enablesno = '';
	this.wh = '';
	this.standardcost = 0;
	this.cost_method = '';
	this.invavailwhse = '';
	this.uomgrpkey = '';
}

function getUnitFactor(recitem, unit) 
{
	var unitfactor = 1;
	if (unit == recitem.punit){
		unitfactor = recitem.punit_factor;
	} else if(unit == recitem.sunit){
		unitfactor = recitem.sunit_factor;
	}
	return unitfactor;
};

function ItemFetch(custvendid, billtocontact, shiptocontact, itemid, prodline, qty, date, docpar, whse, currency, _layer, _form, from, rownum ,fieldid, functionCall ) {
	
	date = ReformatDate(date, USERDATEFORMAT, STANDARDFORMAT)

	var qrequest = new QRequest;
	var url = 'qrequest.phtml?.function=FetchItem&.handler=QRequest&.entity=item&.sess=' + sess
        + '&.otherparams=custvendid,billtocontact,shiptocontact,itemid,prodline,qty,whse,currency,date,docpar,mod,from'
        + '&.custvendid=' + escape(custvendid) + '&.billtocontact=' + escape(billtocontact.encrypt())
        + '&.shiptocontact=' + escape(shiptocontact) + '&.itemid=' + iaEscape(itemid) + '&.prodline=' + escape(prodline)
        + '&.qty=' + escape(qty) + '&.whse=' + escape(whse) + '&.currency=' + escape(currency)
        + '&.date=' + escape(date) + '&.docpar=' + escape(docpar) + '&.mod='+escape(mod) + '&.from=' + escape(from);

	var updateFunc = functionCall;
	var updateArgs = "'"+_layer+"','"+_form+"','"+from+"','"+rownum+"','"+fieldid+"'";
    //window.prompt(url,url);
	
	qrequest.quickRequest(url, updateFunc, updateArgs, false);
}

function LoadQuantityDenseArr() {
	
	var qrequest = new QRequest;
	qrequest.setUseResponseText(true);
	var url = 'qrequest.phtml?.function=fetchQuantityTotals&.handler=QRequest&.noxmlheader=1&.entity=item&.sess='+sess;

	var updateFunc = 'RespProcesser_QuantityTotals';
	//var updateArgs = "'"+_layer+"','"+_form+"','"+from+"','"+rownum+"','"+fieldid+"'";
	var updateArgs = '';
    //window.prompt(url,url);
	
	qrequest.quickRequest(url, updateFunc, updateArgs, false);
}

function RespProcesser_QuantityTotals(m_response) {	
	qtydensearr = JSON.parse(m_response);	
}

function ShiptoFetch(shipto) {
	if(enableoverridetax != 'true') {
		// Overried tax is not enabled, no processing required
		return;
	}

	_form = 'Layer1_form';
	page = GetLayerDoc('Layer1');

	var itemids = '';
	var hasitems = false;
	for(rownum=0;;rownum++){
		itemId = page.forms['Layer1_form'].elements['_obj__ENTRIES('+rownum+')__ITEMID'];

		// Double quote (") separated items, as it is not allowed in itemid string
		//alert(itemId.value);

		if(!itemId) break;
				
		if(rownum == 0) {
			itemids += itemId.value.i_slice(0, '--');
		} else {
			itemids += '"'+itemId.value.i_slice(0, '--');
		}

		if(itemId.value != '') hasitems = true;
	}
	//alert(itemids);

	if(!shipto || shipto.value == '' || !hasitems) {
		// Disable all taxable check box
		for(rownum=0;;rownum++){
			helperOverrideTax = page.forms[_form].elements['helper__obj__ENTRIES('+rownum+')__OVERRIDETAX'];
			overrideTax = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__OVERRIDETAX'];

			if(!overrideTax) break;
			
			// Disble
			itemTaxable.value = 'false';
			overrideTax.disabled = true;
			overrideTax.value = 'false';
			helperOverrideTax.checked=false;
			helperOverrideTax.disabled = true;
		}
		return;
	};

	// Show waring
	alert("Taxability of line items will be computed again.");

	var shiptocontact = document.all[0].ownerDocument.forms[1].elements['_obj__SHIPTO__CONTACTNAME'].value;
	//alert(shiptocontact);

	var docpar	= document.all[0].ownerDocument.forms[1].elements['_obj__DOCPARID'].value;

	var qrequest = new QRequest;
	var url = 'qrequest.phtml?.function=FetchShipto&.handler=QRequest&.entity=item&.sess='+sess+'&.otherparams=shiptocontact,itemids,docpar,mod&.shiptocontact='+escape(shiptocontact)+'&.itemids='+escape(itemids)+'&.docpar='+escape(docpar)+'&.mod='+escape(mod);
	
	var updateFunc = 'RespProcesser_Shipto';
	var updateArgs = "";
    //window.prompt(url,url);
	qrequest.quickRequest(url, updateFunc, updateArgs, false);
}

function RespProcesser_Shipto(m_response) {
	
	_form = 'Layer1_form';
	page = GetLayerDoc('Layer1');

	nodes = m_response.getElementsByTagName("lineitem");
	//alert('length:'+nodes.length);

	for (line_num=0; line_num<nodes.length; line_num++) {
		itemid = nodes.item(line_num).getAttribute("itemid");
		//alert('itemid:'+itemid);
		
		//alert('_obj__ENTRIES('+line_num+')__ITEMID');
		var itemIdField = page.forms['Layer1_form'].elements['_obj__ENTRIES('+line_num+')__ITEMID'];
		
		if(itemid != '' && itemIdField && itemid == itemIdField.value.i_slice(0, '--')) {
			taxcapable = nodes.item(line_num).getAttribute("taxcapable");
			//alert('taxcapable:'+taxcapable);

			taxable = nodes.item(line_num).getAttribute("taxable");
			//alert('taxable:'+taxable);

			helperOverrideTax = page.forms[_form].elements['helper__obj__ENTRIES('+line_num+')__OVERRIDETAX'];
			overrideTax = page.forms[_form].elements['_obj__ENTRIES('+line_num+')__OVERRIDETAX'];

			// Set taxable check box properties
			overridetaxField = page.forms['Layer1_form'].elements['_obj__ENTRIES('+line_num+')__OVERRIDETAX'];
			//alert('overridetaxField:'+overridetaxField.checked);

			if(taxcapable == 'T') {
				helperOverrideTax.disabled = false;
			
				if(taxable == 'T') {
					itemTaxable.value = 'true';
					overrideTax.value = 'true';
					helperOverrideTax.checked=true;
				}
				else {
					itemTaxable.value = 'false';
					overrideTax.value = 'false';
					helperOverrideTax.checked=false;
				}
			} else {
				itemTaxable.value = 'false';
				overrideTax.disabled = true;
				overrideTax.value = 'false';
				helperOverrideTax.checked=false;
				helperOverrideTax.disabled = true;
			}
		}
	}
}

function GetExchangeRate(_layer, _form){
	//_form 	= (_form == null) ? 0 : _form;
	//page 	= GetLayerDoc(_layer);
	//layer 	= document.forms[0]._currentlayer.value;

	layer				= document.forms[0]._currentlayer.value;
	var lineitemform 	= GetOwnerDocumentForm(layer, 1);
	var exchRate=1;

	if(ismcpEnabled){		
		//var currency = page.forms[_form].elements['_obj__CURRENCY'].value;
		//if (page.forms[_form].elements['_obj__EXCHRATE']) {
		//	exchRate = page.forms[_form].elements['_obj__EXCHRATE'].value;
		//}

		currency		= lineitemform.elements['_obj__CURRENCY'].value;
		if(lineitemform.elements['_obj__EXCHRATE']) {
			exchRate = lineitemform.elements['_obj__EXCHRATE'].value;
		}

		if(baseCurrency == currency || exchRate==''){
			exchRate = 1;
		}
	}
	return exchRate;
}

function RespProcesser_ItemID(_layer, _form, from, rownum, fieldid, m_response){
	
	_form 	= (_form == null) ? 0 : _form;
	page 	= GetLayerDoc(_layer);
	layer 	= document.forms[0]._currentlayer.value;
	
	// Perform the validation upfront.
	if (!m_response) {
		return false;
	}
	
	nodes = m_response.getElementsByTagName("item");
	
	itemid = nodes.item(0).getAttribute("itemid");
		
	if(!itemid) {
		return false;
	}

	if (fieldid != '') {
		fromarr = fieldid.split('(');
		if (fromarr[1]) {
			anothersplit = fromarr[1].split(')');
			rownum = anothersplit[0];
		}

		fromarr = fieldid.split('__');
		from = fromarr[(fromarr.length)-1];
	}
	
	error = nodes.item(0).getAttribute("error");
	if(error != null)
	{
		//alert vsoe restriction. nested kit with top level component level posting not allowed if vsoe enabled.
		alert('Error : ' + error);

		//reset itemid to null
		itemId = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__ITEMID'];
		itemId.value = '';
		return false;
	}
	// Perform the validation upfront.
	
		
	multi_line_name = 'ENTRIES';
	
	if(ismcpEnabled){
		basePrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UIPRICE'];
		baseTotal = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UIVALUE'];
		prefix 	= "TRX_";
	} else {
		basePrice='';
		baseTotal='';
		prefix 	= "UI";
	}

	var costmethod = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__COST_METHOD'];
	var enablesno_elem = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__ENABLESNO'];

	var itemCurrentId = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__ITEMCURRENT'];
	var itemUnitText = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UNIT'];
	var itemWarehouseText = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__WAREHOUSE__LOCATION_NO'];
	var itemId = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__ITEMID'];
	var itemTaxable = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__ITEM__TAXABLE'];
	var itemProdLine = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__PRODLINE'];
	var itemDesc =  page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__ITEMDESC'];
	var itemPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__'+prefix+'PRICE'];
	var listPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__RETAILPRICE'];
	var itemUnit = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UNITFACTOR'];
	var itemTotal = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__'+prefix+'VALUE'];
	var itemMemo = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__MEMO'];
		
	var uomGrpKey = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__ITEM__UOMGRPKEY'];

	var helperItemTotal = page.forms[_form].elements['_helper___obj__'+multi_line_name+'('+rownum+')__'+prefix+'VALUE'];
	var helperItemUnit = page.forms[_form].elements['helper__obj__'+multi_line_name+'('+rownum+')__UNITFACTOR'];
	var itemQuantity = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UIQTY'];
	var item_wh_qty = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__WAREHOUSEAVAIL'];
	var helper_item_wh_qty = page.forms[_form].elements['helper__obj__'+multi_line_name+'('+rownum+')__WAREHOUSEAVAIL'];
	var avail_link = document.getElementsByName('helper__obj__'+multi_line_name+'('+rownum+')__AVAILLINK');
	var pick_link = document.getElementsByName('helper__obj__'+multi_line_name+'('+rownum+')__PICKLIST');

	var discPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__DISCOUNTPERCENT'];
	var locTaxOverrideCheck = docparamsArr[docpar]["LOCOVERRIDE"];
	var itemLocation = null;
	if (page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__LOCATION']) {
		itemLocation = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__LOCATION'];
	}

	if (page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__DEPARTMENT']) {
		itemDept = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__DEPARTMENT'];
	}

	if (page.forms[_form].elements['_obj__TERM__NAME']) {
		termName = page.forms[_form].elements['_obj__TERM__NAME'];
	}
	duedate = page.forms[_form].elements['_obj__WHENDUE'];
	whencreated  = page.forms[_form].elements['_obj__WHENCREATED'];
	exchRate = GetExchangeRate(_layer, _form);
		
	AppendItemData(nodes, itemid);
	
	if (itemid && itemid != '') {
		itemId.value = itemid;
	}
	
	rec = AutoFillLookUpID(itemId.value, true);
	
	if ((mod != 'so') && rec.itemtype=='K') {
		itemId.value='';
		itemCurrentId.value='';
		return;
	}
	
	//if item's extended description is blank, then use itemname for Description field in line entries
	if (rec.name==''){
		rec.name = rec.itemname;
	}

	if(discPrice){
		discPrice.value = '';
	}
	
	//When combo boxes are disabled, autofilling the item name with id
	var pos = itemid.indexOf('--');
	if(pos==-1){
		itemId.value = itemid + '--' +rec.itemname;
	}	
	rec.itemid = itemId.value;
		
	if (rec == null) {
		alert('No match for item found.');
		if (from == 'ITEMID') {
			itemId.focus();
			itemId.select();
		}else{
			itemDesc.focus();
			itemDesc.select();
		}
		return false;
	}
	
	if(renewalsEnabled == true && itemsHaveStartEndDate == true) {		
        if(rec.hasstartenddate == 'T'){
            var RenewalMacro = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__RENEWALMACRO'];
            if (RenewalMacro){
                RenewalMacro.value = rec.renewalmacroid;
            }
        }
	}
	
	if (locTaxOverrideCheck && itemLocation) {
        var locationNoVal = nodes.item(0).getAttribute("warehouselocation");
        if (locationNoVal && itemLocation.value == '') {
            itemLocation.value = locationNoVal;
        }
    }	
	
	if(mod == 'so' && itemsHaveStartEndDate == true) {	    
	  
		var itemTermValues = new Array();
		itemTermValues['D'] = 'Days';
		itemTermValues['W'] = 'Weeks';
		itemTermValues['M'] = 'Months';
		itemTermValues['Y'] = 'Years';
		var startDate 	  = document.forms[1].elements['_obj__WHENCREATED'].value;
		var termStartDate = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__REVRECSTARTDATE'];
		var termEndDate   = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__REVRECENDDATE'];
		var itemTerm      = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__ITEMTERM'];
		var termPeriod    = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__TERMPERIOD'];
	
		var sc_termStartDate = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__SC_REVRECSTARTDATE'];
		var sc_termEndDate   = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__SC_REVRECENDDATE'];
		if(itemId.value && rec.hasstartenddate == 'T') {
		  var endDate;
		  itemTerm.value      = itemTermValues[rec.termperiod];
		  termPeriod.value    = rec.totalperiod;
		  //var strLoc = fetchRenewalTermurl+'&itemlabel='+iaEncodeUR
			// IComponent(rec.itemid)+'&startdate='+ReformatDate(startDate, USERDATEFORMAT, STANDARDFORMAT);
		  //var output = baseLoadXMLHTTP(strLoc, true);
		  //endDate = ReformatDate(output, STANDARDFORMAT, USERDATEFORMAT);
		  var endDate = ReformatDate(
              doAjax(fetchRenewalTermurl + '&itemlabel=' + iaEncodeURIComponent(rec.itemid.encrypt())
              + '&startdate='+ReformatDate(startDate, USERDATEFORMAT, STANDARDFORMAT)), STANDARDFORMAT, USERDATEFORMAT);
		  termStartDate.value = startDate;
		  termEndDate.value   = endDate;
		  if(sc_termStartDate){
			sc_termStartDate.value = startDate;
		  }
		  if(sc_termEndDate){
			sc_termEndDate.value   = endDate;
		  }
		}else{
		  itemTerm.value      = '';
		  termPeriod.value    = '';
		  termStartDate.value = '';
		  termEndDate.value   = '';
		  if(sc_termStartDate){
			sc_termStartDate.value = '';
		  }
		  if(sc_termEndDate){
			sc_termEndDate.value   = '';
		  }
	  }
	
		var prorate = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__PRORATE'];
		//to make the prorate button hidden when the page is refreshed / calculated
		var itemComputerForShortTerm = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__ITEM__COMPUTEFORSHORTTERM'];
		if(rec.computeforshortterm == 'T' && rec.hasstartenddate == 'T'){
			if(itemComputerForShortTerm){
				itemComputerForShortTerm.value = "true";
			}
			if(prorate){
			  prorate.disabled = true;
			}
		}else{
			if(itemComputerForShortTerm){
				itemComputerForShortTerm.value = "false";
			}
			if(prorate){
				prorate.disabled = false;
			}
		}
	}
	degree = (rec && rec.precision != null && rec.precision != '') ? rec.precision : app_precision;

	if (priceFetchDisabled && from == 'ITEMDESC') {
		rec.price = itemPrice.value;
	}
	
	//disable cost field and populate it with standard cost if costing method is standard
	if(costmethod){
		costmethod.value = rec.cost_method;
	}
	if (enablesno_elem) {
		enablesno_elem.value = rec.enablesno;
	}
	
	if(transferin || transferout){
		if(rec.cost_method == 'S' && rec.itemtype != 'SK'){
			rec.price = rec.standardcost;
			if(itemPrice)itemPrice.disabled = true;
			if(helperItemTotal)helperItemTotal.disabled = true;
		}else{
			if(itemPrice)itemPrice.disabled = false;
			if(helperItemTotal)helperItemTotal.disabled = false;
		}
	}

	itemCurrentId.value = rec.itemid;
	itemId.value = rec.itemid;
	
	page0 = GetLayerDoc('Layer0');
	default_whse = page0.forms['Layer0_form'].elements['_obj__WAREHOUSE__LOCATIONID'].value;

	if (page0.forms['Layer0_form'].elements['_obj__LOCATION']) {
		default_loc = page0.forms['Layer0_form'].elements['_obj__LOCATION'].value;
	}

	if (page0.forms['Layer0_form'].elements['_obj__DEPARTMENT']) {
		default_dept = page0.forms['Layer0_form'].elements['_obj__DEPARTMENT'].value;
	}
	
	// Moved to separate function so it can be overridden in PSADocumentEditor
	RespProcesser_ItemID_SetDesc(itemDesc,itemPrice,rec,degree,listPrice,page,_form,rownum);
	itemProdLine.value = rec.prodline;
	
	if (page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__LOCATION'] && default_loc) {
		// Moved to separate function so it can be overridden in PSADocumentEditor
		RespProcesser_ItemID_SetLocation(default_loc,itemLocation,page,_form,rownum);
	}
	if (page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__DEPARTMENT'] && default_dept) {
		// Moved to separate function so it can be overridden in PSADocumentEditor
		RespProcesser_ItemID_SetDept(default_dept,itemDept,page,_form,rownum);
	}
	
	
	uomdetails = nodes.item(0).getElementsByTagName("uomdetail");
	
	if (uomdetails) {
		
		uomlist = uomdetails.item(0).getElementsByTagName("uom");
		
		if (uomlist) {
			
			helperItemUnit = clrOptions(helperItemUnit);			
			var selectedindx = 0;
			
			for (var i = 0; i < uomlist.length; i++) {			
			
				unitname 	= uomlist.item(i).getAttribute("unit");
				unitrec  	= uomlist.item(i).getAttribute("rec");
				unitfactor 	= uomlist.item(i).getAttribute("factor");

				newBaseOptionName = new Option(unitname, unitfactor,true,true);
				
				if (uomlist.item(i).getAttribute("sel")) {				
					selectedindx = i;
					itemUnit.value = unitfactor;
					itemUnitText.value = unitname;
					
					itemPrice.value = RoundCurrency(rec.price * unitfactor, degree);
				}
				
				helperItemUnit.options.add(newBaseOptionName, i+1);
				
			}			
			
			helperItemUnit.options[selectedindx].selected = true;
		}
		
		uomGrpKey.value = rec.uomgrpkey;
	}
			
	
	// Moved to separate function so it can be overridden in PSADocumentEditor
	RespProcesser_ItemID_SetQuantity(itemQuantity,itemPrice,itemUnit,rec,degree,listPrice,page,_form,rownum)

	// Adding logic to populate Item details for SFDC Pricing companies
	if(rec.sfdcpricing == '1'){
		itemQuantity.value = rec.quantity ? rec.quantity : 1;
		
		//List Price come in picture once Line Item Discount Feature will be checked in.
		if(listPrice) listPrice.value = rec.listprice;
		
		if(mod == 'so' && itemsHaveStartEndDate == true){
			if(rec.service_start_date && rec.service_start_date != '' && termStartDate){
				termStartDate.value = rec.service_start_date;
			}
			if(rec.service_end_date && rec.service_end_date != '' && termEndDate){
				termEndDate.value = rec.service_end_date;
			}
		}
		itemMemo.value = rec.memo;
	}
	
	itemTotal.value =  RoundCurrency(itemQuantity.value * itemPrice.value);
	
	if(baseTotal){
		if(baseCurrency == currency){
			baseTotal.value = itemTotal.value;
		}else{
			baseTotal.value =  RoundCurrency(itemTotal.value * exchRate);
		}
	}
	
	if(basePrice){
		if(baseCurrency == currency){
			basePrice.value = itemPrice.value;
		}else{
			basePrice.value = RoundCurrency((itemTotal.value * exchRate)/itemQuantity.value, degree);
		}
	}

	RefreshMultilineTotal(this,_layer,_form,'_obj__'+multi_line_name,prefix+'VALUE');
	quantity = itemUnit.value * itemQuantity.value;
	
	var bNonInvItem = (rec.itemtype=='NI' || rec.itemtype=='NP' || rec.itemtype=='NS') ? true : false ;

	if( bNonInvItem )
	{
		// Disable the avail link.
		baseDisableAnchor(avail_link[0], true);
		if(pick_link[0]){
			baseDisableAnchor(pick_link[0], true);
		}

		itemWarehouseText.value = '';
		helper_item_wh_qty.options.selectedIndex = -1;
		helper_item_wh_qty.disabled = true;
		
	} else {
		
		var num_wh = rec.wh.length;
		
		helper_item_wh_qty.disabled = false;

		var whseid = '';
		var whseqty = '';
		var defaultwhseindx = 0;
				
		for (var j=num_wh ; j >= 0; j--) {
			if (rec.wh[j] == null) continue;

			whseid  = rec.wh[j].warehousekey.i_slice(0, '--');
			whseqty = rec.wh[j].qtyleft;
						
			if (default_whse != '' && default_whse == whseid) {
				defaultwhseindx = j;
				//newoption =  new Option(whseid, whseqty,true,true);
				item_wh_qty.value = whseqty;
				itemWarehouseText.value = rec.wh[j].warehousekey;
				
			} 
			//else {
				newoption =  new Option(rec.wh[j].warehousekey, whseqty);
			//}
			
			helper_item_wh_qty.options[j] = newoption;
			
		}
		
		helper_item_wh_qty.options[defaultwhseindx].selected = true;
		
		if (item_wh_qty.value == '') {
			item_wh_qty.value = rec.wh[0].qtyleft;
			itemWarehouseText.value = rec.wh[0].warehousekey;
		}
		
		if (wareselmethod=='Warehouse with Available Inventory' && rec.invavailwhse != '' && default_whse == ''){			
			default_whse = rec.invavailwhse;			
		}  else if (wareselmethod=='Use the default warehouse' && upref_docpar_whse != '' && default_whse == ''){			 
			default_whse = upref_docpar_whse;			 
		}
		
		default_whse = default_whse.i_slice(0, '--');
		
		if (default_whse!='')
		{
			var op = '';
			for(i=0; i<helper_item_wh_qty.options.length; i++){
								
				op = baseGetText(helper_item_wh_qty.options[i]);				
				op = op.i_slice(0, '--');
				
				if (default_whse==op){
					index = i;
					itemWarehouseText.value = baseGetText(helper_item_wh_qty.options[i]);
					break;
				}
			}
			helper_item_wh_qty.selectedIndex = i;
		}

		// Enable the avail link.
		baseDisableAnchor(avail_link[0], false);
		if(pick_link[0]){
			baseDisableAnchor(pick_link[0], false);
		}
	}

	// Overried tax is enabled
	if(enableoverridetax == 'true') {
		taxcapable = nodes.item(0).getAttribute("taxcapable");
		//alert('taxcapable:'+taxcapable);

		helperOverrideTax = page.forms[_form].elements['helper__obj__'+multi_line_name+'('+rownum+')__OVERRIDETAX'];	
		overrideTax = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__OVERRIDETAX'];		

		//alert('taxable:'+rec[TAX]);

		if (taxcapable =='T') {
			helperOverrideTax.disabled = false;
			if(rec.tax == 'T') {
				itemTaxable.value = 'true';
				overrideTax.value = 'true';
				helperOverrideTax.checked=true;
			} else {
				itemTaxable.value = 'false';
				overrideTax.value = 'false';
				helperOverrideTax.checked=false;
			}
		} else {
			itemTaxable.value = 'false';
			overrideTax.disabled = true;
			overrideTax.value = 'false';
			helperOverrideTax.checked=false;
			helperOverrideTax.disabled = true;
		}
	}


	if (!rec.tax) itemTaxable.value = 'false';
	
	WarnforWarehouseQty(rec, item_wh_qty, quantity, itemWarehouseText.value);
			
	if (itemQuantity.type == 'hidden' && page.forms[_form].elements['_helper__'+'_obj__'+multi_line_name+'('+rownum+')__UIQTY']) {
		itemQuantityHlp = page.forms[_form].elements['_helper__'+'_obj__'+multi_line_name+'('+rownum+')__UIQTY'];
	}
	else  {
		// Moved to separate function so it can be overridden in PSADocumentEditor
		RespProcesser_ItemID_SetQuantityFocus(itemQuantity,page,_form,rownum);
	}
}

// Separate function so it can be overridden in PSADocumentEditor
function RespProcesser_ItemID_SetDesc(itemDesc,itemPrice,rec,degree,listPrice,page,_form,rownum) {
	//alert('RespProcesser_ItemID_SetDesc');	
	itemDesc.value = rec.name;	
	itemPrice.value = RoundCurrency(rec.price * rec.std_convrate,degree);
	if(listPrice){
		listPrice.value = itemPrice.value;
	}
}

// Separate function so it can be overridden in PSADocumentEditor
function RespProcesser_ItemID_SetLocation(default_loc,itemLocation,page,_form,rownum) {
	//alert('RespProcesser_ItemID_SetLocation');
	itemLocation.value	= default_loc; 
}

// Separate function so it can be overridden in PSADocumentEditor
function RespProcesser_ItemID_SetDept(default_dept,itemDept,page,_form,rownum) {
	//alert('RespProcesser_ItemID_SetDept');
	itemDept.value = default_dept; 
}

// Separate function so it can be overridden in PSADocumentEditor
function RespProcesser_ItemID_SetQuantity(itemQuantity,itemPrice,itemUnit,rec,degree,listPrice,page,_form,rownum) {
	//alert('RespProcesser_ItemID_SetQuantity');
	//alert('RespProcesser_ItemID_SetQuantity');
	itemQuantity.value = 1;
	
	var unitfactor = 1;
	if (itemUnit) {
		unitfactor = itemUnit.value;
	}
	if (!unitfactor || unitfactor == '') {
		unitfactor = 1;
	}	
	itemPrice.value = RoundCurrency(unitfactor * rec.price,degree);	
	if(listPrice){
		listPrice.value = itemPrice.value;		
	}
}

function RespProcesser_ItemID_SetQuantityFocus(quantityObject,page,_form,rownum) {
	//alert('RespProcesser_ItemID_SetQuantityFocus');
	quantityObject.focus();
	quantityObject.select();
}

function RespProcesser_ProratePrice(_layer, _form, from, rownum, fieldid, m_response){
	//alert('RespProcesser_ProratePrice');
	_form = (_form == null) ? 0 : _form;
	page = GetLayerDoc(_layer);

	layer = document.forms[0]._currentlayer.value;

	if(ismcpEnabled){
		prefix = "TRX_";
	}else{
		prefix = "UI";
	}

	multi_line_name = 'ENTRIES';

	var itemId = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__ITEMID'];
	rec = AutoFillLookUpID(itemId.value, false);
	var price = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__'+prefix+'PRICE'];
	var uivalue = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__'+prefix+'VALUE'];
	var uiqty = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__UIQTY'].value;
	var degree = (rec && rec.precision != null && rec.precision != '') ? rec.precision : app_precision;

	var termStartDate = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__REVRECSTARTDATE'];
	var termEndDate   = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__REVRECENDDATE'];

	var proratePrice  = document.getElementsByName('_obj__ENTRIES('+rownum+')__PRORATEPRICE')[0];

	var suggestedPrice = 0;
	nodes = m_response.getElementsByTagName("price");
	suggestedPrice = nodes.item(0).getAttribute("num");

	salesprice = price.value;
	if(proratePrice.value == 'true' && termStartDate.value!='' && termEndDate.value != ''){		
		var st_date  =  ReformatDate(termStartDate.value, USERDATEFORMAT, STANDARDFORMAT);
		var end_date =  ReformatDate(termEndDate.value, USERDATEFORMAT, STANDARDFORMAT);
		
		var qryPrice = (isLineDiscountOnSuggestedPrice=='T') ? suggestedPrice : salesprice;
		
		//var strLoc = fetchproratepriceURL+'&itemlabel='+iaEncodeURIComponent(itemId.value)+'&startdate='+st_date+'&enddate='+end_date+'&suggestedprice='+qryPrice;		
		//var proratePrice = baseLoadXMLHTTP(strLoc, true);
		
		if (isLineDiscountOnSuggestedPrice=='T')
		{
			var proratePrice = doAjax(fetchproratepriceURL+'&itemlabel='+iaEncodeURIComponent(itemId.value.encrypt())
				+'&startdate='+st_date+'&enddate='+end_date+'&suggestedprice='+suggestedPrice+'&.dt='
				+iaEncodeURIComponent(dt));
		}else{
			var proratePrice = doAjax(fetchproratepriceURL+'&itemlabel='+iaEncodeURIComponent(itemId.value.encrypt())
				+'&startdate='+st_date+'&enddate='+end_date+'&suggestedprice='+salesprice+'&.dt='
				+iaEncodeURIComponent(dt));
		}
		
		var discPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__DISCOUNTPERCENT'];
	
		var itmid = itemId.value.i_slice(0, '--');		
		
		if (rec.sunit != rec.stdunit && rec.sunit != '' ) {
			proratePrice = proratePrice * rec.sunit_factor;
		}
		
		price.value = RoundCurrency(proratePrice, degree);
		uivalue.value =  RoundCurrency(proratePrice * uiqty);
		
		discountProratePrice[rownum] = {
			'itemkey' : itmid,
			'prprice' : price.value
		}
		if (discPrice && discPrice.value)
		{
			price.value =   calcDiscount(discPrice.value,price.value,degree);
		}
	} else {
		price.value = suggestedPrice;
		if (rec.sunit != rec.stdunit && rec.sunit != '' ) {
			price.value = suggestedPrice * rec.sunit_factor;
		}
		uivalue.value =  RoundCurrency(price.value * uiqty);
	}
	if(ismcpEnabled){
		baseSendUIEvent(price, 'change');
	}
	RefreshMultilineTotal(this,_layer,_form,'_obj__ENTRIES',prefix+'VALUE');
}

function RespProcesser_Quantity(_layer, _form, from, rownum, fieldid, m_response){

	_form = (_form == null) ? 0 : _form;
	page = GetLayerDoc(_layer);

	layer = document.forms[0]._currentlayer.value;
	
	if(ismcpEnabled){
		prefix = "TRX_";
	}else{
		prefix = "UI";
	}
	
	multi_line_name = 'ENTRIES';

	itemId = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__ITEMID'];
	helperItemUnit = page.forms[_form].elements['helper__obj__'+multi_line_name+'('+rownum+')__UNITFACTOR'];
	itemQuantity = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UIQTY'];
	item_wh_qty = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__WAREHOUSEAVAIL'];
	itemPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__'+prefix+'PRICE'];
	listPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__RETAILPRICE'];
	itemUnit = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UNITFACTOR'];
	itemTotal = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__'+prefix+'VALUE'];
	helperItemTotal = page.forms[_form].elements['helper__obj__'+multi_line_name+'('+rownum+')__'+prefix+'VALUE'];
	itemWarehouseText = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__WAREHOUSE__LOCATION_NO'];
  	var proratePrice  = document.getElementsByName('_obj__ENTRIES('+rownum+')__PRORATEPRICE')[0];
	var discPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__DISCOUNTPERCENT'];
    //If the price is Prorated, then do not fetch it again
    var prorated = (proratePrice && proratePrice.value=='true')?true:false;
	exchRate = GetExchangeRate(_layer, _form);
	rec = AutoFillLookUpID(itemId.value, false);

	qty = helperItemUnit.value * itemQuantity.value;
	degree = (rec && rec.precision != null && rec.precision != '') ? rec.precision : app_precision;
	
	if (!priceFetchDisabled && !prorated ) {
		if (qty == 0) {
			rec.price = (itemPrice.value != null) ? itemPrice.value : 0;
		} else {
			nodes = m_response.getElementsByTagName("price");
			price = nodes.item(0).getAttribute("num");
				
			rec.price = (degree) ? RoundCurrency(price,degree) : RoundCurrency(price);
		}

		if (rec.price != 0 || itemPrice.value == 0) {
			itemPrice.value = RoundCurrency(rec.price * helperItemUnit.value, degree);
			if(listPrice){
				listPrice.value = itemPrice.value;
			}
		}
	}

	if(discPrice && discPrice.value && listPrice && itemPrice){
			var item = itemId.value.i_slice(0, '--');
			
			if (proratePrice.value=='true' && discountProratePrice[rownum] && discountProratePrice[rownum]['itemkey']==item){
				var discountPrice =   calcDiscount(discPrice.value,discountProratePrice[rownum]['prprice'],degree);
			}else{
				var discountPrice =   calcDiscount(discPrice.value,listPrice.value,degree);
			}
		itemPrice.value = RoundCurrency(discountPrice , degree);
	}

	unitfactor = itemUnit.value ? itemUnit.value : 1;
	quantity = unitfactor * itemQuantity.value;

	itemTotal.value =  RoundCurrency(itemQuantity.value * itemPrice.value);

	if (transferin || transferout){
		
	/* nodes = m_response.getElementsByTagName("price") will get assigned only when priceFetchDisabled = 0 (with the condition if (!priceFetchDisabled && !prorated ) above)
	   priceFetchDisabled becomes 1 when the document is being converted from another (with the condition $this->priceFetchDisabled = ($recalled != '' && $copy == 'Update') ? 1 : 0 in DocumentEditor.cls)
	   Hence, if the document converted is Inventory Transfer Out document then it never gets nodes, so we are getting nodes here */

		nodes = m_response.getElementsByTagName("price");
		price = nodes.item(0).getAttribute("num");
		costmethod = nodes.item(0).getAttribute("costmethod");
		itemtype = nodes.item(0).getAttribute("itemtype");
		
		if(costmethod== 'S' && itemtype != 'SK'){
			itemTotal.value =  RoundCurrency(quantity * price);
			itemPrice.value = RoundCurrency(price,degree);
			itemPrice.disabled = true;			  
		    if(helperItemTotal)helperItemTotal.disabled = true;
		}else{
			itemPrice.disabled = false;			
		    if(helperItemTotal)helperItemTotal.disabled = true;
		}
		
	}

	if(baseTotal){
		if(baseCurrency == currency){
			baseTotal.value = itemTotal.value;
		}else{
			baseTotal.value =  RoundCurrency(itemTotal.value * exchRate);
		}
	}
	if(basePrice){
		if(baseCurrency == currency){
			basePrice.value = itemPrice.value;
		}else{
			basePrice.value = RoundCurrency((itemTotal.value * exchRate)/itemQuantity.value, degree);
		}
	}
	itemQuantity.value = RoundCurrency(itemQuantity.value, 4);
	RefreshMultilineTotal(this,_layer,_form,'_obj__ENTRIES',prefix+'VALUE');

	WarnforWarehouseQty(rec, item_wh_qty, quantity, itemWarehouseText.value);
}


function RespProcesser_Warehouse(_layer, _form, from, rownum, fieldid, m_response){
		
	multi_line_name = 'ENTRIES';

	if(ismcpEnabled){
			prefix = "TRX_";
	}else{
			prefix = "UI";
	}
	

	var itemWarehouseText = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__WAREHOUSE__LOCATION_NO'];
	var helper_item_wh_qty = page.forms[_form].elements['helper__obj__'+multi_line_name+'('+rownum+')__WAREHOUSEAVAIL'];
	var itemUnit = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UNITFACTOR'];
	var itemQuantity = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UIQTY'];
	var item_wh_qty = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__WAREHOUSEAVAIL'];

	var nodes = m_response.getElementsByTagName("item");
	var itemid = nodes.item(0).getAttribute("itemid");

	if(itemid=='') {
		return;
	}
	
	AppendItemData(nodes, itemId.value);

	itemWarehouseText.value = helper_item_wh_qty.options[helper_item_wh_qty.options.selectedIndex].text;
	var itemLocation = null;
	if (page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__LOCATION']) {
		itemLocation = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__LOCATION'];
	}
	
	if (itemLocation) {
        var locationNoVal = nodes.item(0).getAttribute("warehouselocation");
        if (locationNoVal && itemLocation.value == '') {
            itemLocation.value = locationNoVal;
        }
    }
    
	quantity = itemUnit.value * itemQuantity.value;

	rec = AutoFillLookUpID(itemId.value, true);
	degree = (rec && rec.precision != null && rec.precision != '') ? rec.precision : app_precision;
	
	WarnforWarehouseQty(rec, item_wh_qty, quantity, itemWarehouseText.value);
	
	//disable cost field and populate it with standard cost if costing method is standard
	if(transferin || transferout){
		if(rec.cost_method == 'S' && rec.itemtype != 'SK'){
			itemPrice.value = RoundCurrency(rec.standardcost,degree);
			itemPrice.disabled = true;
			itemTotal.value =  RoundCurrency(rec.quantity * rec.standardcost);
		}else{
			itemPrice.disabled = false;
		}
	}
	RefreshMultilineTotal(this,_layer,_form,'_obj__'+multi_line_name,prefix+'VALUE');

}

function RespProcesser_UnitFactor(_layer, _form, from, rownum, fieldid, m_response){
	_form = (_form == null) ? 0 : _form;
	page = GetLayerDoc(_layer);

	layer = document.forms[0]._currentlayer.value;
	
	if(ismcpEnabled){
		prefix = "TRX_";
	}else{
		prefix = "UI";
	}

	
	//Constants
	//ITEMID = 0; DESC = 1; TAX = 2; PRODLINE = 3; BASEUOM = 4; PURUOM = 5; SALUOM=6; PRECISION = 7; PRICE = 8; WH = 9, ITEMTYPE = 10, QTYCHECK=11;
	//Shakil -- Added extra constants
	//LISTPRICE=16; QUANTITY=17; MEMO=18; SFDCPRICING=19; SERVICE_START_DATE =20; SERVICE_END_DATE =21;
	multi_line_name = 'ENTRIES';

	itemId = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__ITEMID'];
	helperItemUnit = page.forms[_form].elements['helper__obj__'+multi_line_name+'('+rownum+')__UNITFACTOR'];
	itemQuantity = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UIQTY'];
	item_wh_qty = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__WAREHOUSEAVAIL'];
	itemPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__'+prefix+'PRICE'];
	listPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__RETAILPRICE'];
	itemUnitText = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UNIT'];
	itemUnit = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UNITFACTOR'];
	itemTotal = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__'+prefix+'VALUE'];
	helperItemTotal = page.forms[_form].elements['helper__obj__'+multi_line_name+'('+rownum+')__'+prefix+'VALUE'];
	itemWarehouseText = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__WAREHOUSE__LOCATION_NO'];
	exchRate = GetExchangeRate(_layer, _form);

	rec = AutoFillLookUpID(itemId.value, true);
	degree = (rec && rec.precision != null && rec.precision != '') ? rec.precision : app_precision;

	if (rec && !priceFetchDisabled) {
		qty = helperItemUnit.value * itemQuantity.value;
		
		if (qty == 0) {
			rec.price = (itemPrice.value != null) ? itemPrice.value : 0;
		} else {
			nodes = m_response.getElementsByTagName("price");
			price = nodes.item(0).getAttribute("num");
				
			rec.price = (degree) ? RoundCurrency(price,degree) : RoundCurrency(price);
		}

		if (rec.price != 0 || itemPrice.value == 0) {
			itemPrice.value = RoundCurrency(itemUnit.value * rec.price, degree);
			if(listPrice){
				listPrice.value = itemPrice.value;
			}
		/*
		}else{
			if(sfdcpricing){
				alert(rec);
				//				alert(itemUnit.value); alert(itemPrice.value); alert(degree);
				//				baseratio =
				//				itemPrice.value = RoundCurrency(itemUnit.value * itemPrice.value, degree);
			}
		*/
		}
		
	} else {
		oldUnitText = itemUnitText.value;
		
		for (i=0;i < helperItemUnit.options.length;i++) {
			if (oldUnitText == helperItemUnit.options[i].text) {
				oldUnitValue = helperItemUnit.options[i].value;				
			}
		}
		
		newUnitValue = helperItemUnit.options[helperItemUnit.options.selectedIndex].value;
		itemPrice.value = RoundCurrency(itemPrice.value * (newUnitValue/oldUnitValue), degree);
		
		//itemQuantity.value = itemQuantity.value/(newUnitValue/oldUnitValue);
		if(listPrice){
			listPrice.value = itemPrice.value;
		}
	}

	itemUnitText.value = helperItemUnit.options[helperItemUnit.options.selectedIndex].text;	
	quantity = itemUnit.value * itemQuantity.value;
	itemTotal.value =  itemQuantity.value * itemPrice.value;

	if(baseTotal){
		if(baseCurrency == currency){
			baseTotal.value = itemTotal.value;
		}else{
			baseTotal.value = RoundCurrency(itemTotal.value * exchRate);
		}
	}
	if(basePrice){
		if(baseCurrency == currency){
			basePrice.value = itemPrice.value;
		}else{
			basePrice.value = RoundCurrency((itemTotal.value * exchRate)/itemQuantity.value, degree);
		}
	}
	
	if(transferin || transferout){
		costmethod = rec.cost_method;
		itemtype = rec.itemtype;

		if(costmethod == 'S' && itemtype != 'SK'){
			
			itemTotal.value = RoundCurrency(quantity * rec.price);						
		    itemPrice.value = RoundCurrency((itemTotal.value/quantity),degree);		    
		    if(helperItemTotal)helperItemTotal.disabled = true;
		}else{
			itemPrice.disabled = false;
			if(helperItemTotal)helperItemTotal.disabled = true;
		}
	}
	
	RefreshMultilineTotal(this,_layer,_form,'_obj__'+multi_line_name,prefix+'VALUE');
	
	WarnforWarehouseQty(rec, item_wh_qty, quantity, itemWarehouseText.value);

}

function RespProcesser_ListPrice(_layer, _form, from, rownum, degree, m_response){
	_form = (_form == null) ? 0 : _form;
	page = GetLayerDoc(_layer);

	layer = document.forms[0]._currentlayer.value;

	multi_line_name = 'ENTRIES';

	listPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__RETAILPRICE'];
	itemUnit = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UNITFACTOR'];

	nodes = m_response.getElementsByTagName("price");
	price = nodes.item(0).getAttribute("num");

	listPrice.value = RoundCurrency(price * itemUnit.value,degree);
}

function AppendWhseData(whse) {

	whsedensearr[whsedensearr.length] = whse;
	document.forms[0].elements['.newWhseAdded'].value = 1;
	document.forms[0].elements['.index'].value = whsedensearr.length;
	return true;
	
}

function AppendItemData(nodes, itemIdvalue) {	
	
	var itemObj = new item();
	
	var itemid = nodes.item(0).getAttribute("itemid");
	
	if (itemid) {		
		itemObj.itemid = nodes.item(0).getAttribute("itemid");		
		itemObj.name = nodes.item(0).getAttribute("desc");
		itemObj.itemname = nodes.item(0).getAttribute("name");
		itemObj.itemtype = nodes.item(0).getAttribute("itemtype");
		itemObj.tax = nodes.item(0).getAttribute("taxable"); 		
		itemObj.prodline = nodes.item(0).getAttribute("prodline");
		itemObj.punit = nodes.item(0).getAttribute("punit");
		itemObj.sunit = nodes.item(0).getAttribute("sunit");
		itemObj.stdunit = nodes.item(0).getAttribute("stdunit");
		itemObj.precision = nodes.item(0).getAttribute("precision"); 
		itemObj.price = nodes.item(0).getAttribute("price");
		itemObj.qtycheck = nodes.item(0).getAttribute("qtycheck");
		itemObj.listprice = nodes.item(0).getAttribute("listprice");
		itemObj.quantity = nodes.item(0).getAttribute("quantity");
		itemObj.memo = nodes.item(0).getAttribute("memo");
		itemObj.sfdcpricing = nodes.item(0).getAttribute("sfdcpricing");
		itemObj.servicestartdate = nodes.item(0).getAttribute("servicestartdate");
		itemObj.serviceenddate = nodes.item(0).getAttribute("serviceenddate");
		itemObj.hasstartenddate = nodes.item(0).getAttribute("hasstartenddates");
		itemObj.termperiod = nodes.item(0).getAttribute("termperiod1");
		itemObj.totalperiod = nodes.item(0).getAttribute("totalperiods1");
		itemObj.computeforshortterm = nodes.item(0).getAttribute("computeforshortterm1");
		itemObj.itemIdvalue = itemIdvalue;
		itemObj.renewalmacroid = nodes.item(0).getAttribute("renewalmacroid");
		itemObj.enablesno = nodes.item(0).getAttribute("enablesno");
		itemObj.standardcost = nodes.item(0).getAttribute("standardcost");
		itemObj.cost_method = nodes.item(0).getAttribute("cost_method");
		itemObj.invavailwhse = nodes.item(0).getAttribute("invavailwhse");		
		itemObj.punit_factor = nodes.item(0).getAttribute("punit_factor");		
		itemObj.sunit_factor = nodes.item(0).getAttribute("sunit_factor");
		itemObj.isserialized = nodes.item(0).getAttribute("isserialized");
		itemObj.uomgrpkey = nodes.item(0).getAttribute("uomgrpkey");
		
		
		// Assign to the itemdensearr
		itemdensearr[itemid] = itemObj;
	}
			
	// again clubing the itemid and name in the line entry
	//var newItem = Array(itemIdvalue, itemDesc, taxable, prodLine, baseUOM, purUOM, saleUOM, precision, price, '', itemtype, qtycheck, listprice, quantity, memo, sfdcpricing, servicestartdate, serviceenddate,hasstartenddate,termperiod,totalperiod,computeforshortterm,renewalmacroid,enablesno);	//alert(newItem);	
	//itemdensearr[itemdensearr.length] = newItem;
	
	return true;
}

function AutoPopulateWarehouse(_layer, _form) {
	page0 = GetLayerDoc(_layer);
	default_whse = page0.forms[_form].elements['_obj__WAREHOUSE__LOCATIONID'].value;

	if (default_whse == '') { return true; }

	var found = false;
	var whseIndex = 0;
	
	for (var j=0 ; j < whsedensearr.length; j++) {
		if (whsedensearr[j].i_slice(0, '--') == default_whse) {
			whseIndex = j;
			found = true;
			break;
		}
	}

	if (!found) {
		alert('No match for warehouse found.');
		return false;
	} else {

		var itemsAvailable = false;

		_form1 = 'Layer1_form';
		multi_line_name = 'ENTRIES';
		page1 = GetLayerDoc('Layer1');

		numrec = page1.forms[_form1].elements['_obj__' + multi_line_name + '_numofrows_top'].value;

		//Getting the warehouseid and name for the newly created warehouse from the document header
		ind = document.forms[0].elements['.index'].value-1;
		newwhse = whsedensearr[ind];

		for (var m=0; m<numrec; m++) {
			itemWarehouse = page1.forms[_form1].elements['_obj__'+multi_line_name+'('+m+')__WAREHOUSE__LOCATION_NO'];
			
			whsestr = itemWarehouse.value.i_slice(0, '--');
			
			if (whsestr != '' && whsestr != default_whse) {
				itemsAvailable = true;
				break;
			}
		}

		if (itemsAvailable) {

			var confirmProgress = confirm("This will cause all warehouses in the Line Items tab to default to '"+default_whse+"' and may change the total costs of items accordingly.\n Do you want to proceed?");

			if (confirmProgress) {
				for (var m=0; m<numrec; m++) {
					
					thisPage = page1.forms[_form1];
					itemID = thisPage.elements['_obj__'+multi_line_name+'('+m+')__ITEMID'];
					
					if (itemID.value) {
						//thisPage.elements['_obj__'+multi_line_name+'('+m+')__WAREHOUSEAVAIL'].value = default_whse;

						helper_itemWhse = thisPage.elements['helper__obj__'+multi_line_name+'('+m+')__WAREHOUSEAVAIL'];
					
						itemWarehouseText = thisPage.elements['_obj__'+multi_line_name+'('+m+')__WAREHOUSE__LOCATION_NO'];
						costmethod = thisPage.elements['_obj__'+multi_line_name+'('+m+')__COST_METHOD'];
					
						if (document.forms[0].elements['.newWhseAdded'].value == '1') {
							helper_itemWhse.options[whseIndex] = new Option(newwhse, newwhse,true,true);
							document.forms[0].elements['.newWhseAdded'].value = '0';
						}

						if (helper_itemWhse.disabled == false) {
							helper_itemWhse.options.selectedIndex = whseIndex;
							itemWarehouseText.value = helper_itemWhse.options[helper_itemWhse.options.selectedIndex].text;//helper_item_wh_qty.options[helper_item_wh_qty.options.selectedIndex].text;
							//AutoFill('Layer1', _form1,'WAREHOUSEAVAIL',m,'');
						}
					}
	
					if (transferin || transferout){
					
						itemid = itemID.value.i_slice(0, '--');

						rec = AutoFillLookUpID(itemid, false);
						
						if (rec && costmethod.value =='S' && rec.itemtype != 'SK'){
							itemUIQty = thisPage.elements['_obj__'+multi_line_name+'('+m+')__UIQTY'];
							itemUOM = thisPage.elements['_obj__'+multi_line_name+'('+m+')__UNITFACTOR'];							
							itemQty = itemUIQty.value* itemUOM.value;
							degree = (rec && rec.precision != null && rec.precision != '') ? rec.precision : app_precision;
							GetCost_InvTransInOut(itemid,itemWarehouseText.value,itemQty,_layer, _form, '', m, degree);
						}
					}
					
				}//for
			}
		}
	}
}

function WarnforWarehouseQty(rec, item_wh_qty, quantity, itemwarehouse) {
	
	var kitSalesReturn = (item_wh_qty.value) ? true : false;
	
	if(!ShowWarningOnLowQty(docpar) || !kitSalesReturn){
		return true;
	}	
			
	if(rec.itemtype=='K' && rec.qtycheck!=''){
		alert(rec.qtycheck);
	} else if (rec.itemtype == 'I' || rec.itemtype == 'SK') {
	
		indx = AutoFillLookUp(rec.wh, 'warehousekey', itemwarehouse);
		
		if(indx != -1){			
			item_onorder = ( rec.wh[indx].qtyonorder != null ) ? rec.wh[indx].qtyonorder : 0;
			item_onhand = ( rec.wh[indx].qtyonhand != null ) ? rec.wh[indx].qtyonhand : 0;
			item_onhold = ( rec.wh[indx].qtyonhold != null ) ? rec.wh[indx].qtyonhold : 0;
			var qtyavailable = (item_onhand - item_onhold);
			
			if(quantity > qtyavailable){
				alert('There are only '+qtyavailable+' items available in selected warehouse\n ONORDER = '+item_onorder+'\n ONHAND = '+item_onhand+'\n ONHOLD = '+item_onhold);
			}
		}			
	}
	
	return true;
}

function clrOptions(selObj) {
	var newSelObj = selObj.cloneNode(false);
	selObj.parentNode.replaceChild(newSelObj, selObj);
	return newSelObj;
}

String.prototype.i_slice = function (indx, sep) {
	return ((this.indexOf(sep) != -1) ? this.slice(indx, this.indexOf(sep)) : this);
}

String.prototype.startsWith = function(str) {
	return (this.match("^"+str)==str)
}

function GetCost_InvTransInOut(itemid,warehouseid,itemqty,_layer, _form, from, m, fieldid) {

	var qrequest = new QRequest;
	var url = 'qrequest.phtml?.function=ItemCost_InvTransInOut&.handler=QRequest&.entity=item&.sess='+sess+'&.otherparams=itemid,warehouseid,itemqty&.itemid='+escape(itemid)+'&.warehouseid='+escape(warehouseid)+'&.itemqty='+escape(itemqty);		
	var updateFunc = "RespProcesser_InvTransInOutCost";
	var updateArgs = "'"+_layer+"','"+_form+"','"+from+"','"+m+"','"+fieldid+"'";
	qrequest.quickRequest(url, updateFunc, updateArgs,false);
}

function RespProcesser_InvTransInOutCost(_layer, _form, from, m, degree,m_response){
	_form = (_form == null) ? 0 : _form;
	page = GetLayerDoc(_layer);
	_form = 'Layer1_form';
	multi_line_name = 'ENTRIES';
	
	itemPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__UIPRICE'];
	itemTotal = page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__UIVALUE'];
	itemQty = page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__UIQTY'];
	itemUOM = page.forms[_form].elements['_obj__'+multi_line_name+'('+m+')__UNITFACTOR'];
	quantity = itemQty.value*itemUOM.value;

	nodes = m_response.getElementsByTagName("itemcost");
	invtransoutcost = nodes.item(0).getAttribute("invtransoutcost");	
	itemPrice.value =   RoundCurrency(invtransoutcost,degree);
	itemTotal.value = RoundCurrency(itemPrice.value*quantity);
}

function GetCost_InvTransInOutConvertCopy(){
	_layer1 = 'Layer1';
	_form1 	= 'Layer1_form';
	multi_line_name = 'ENTRIES';
	page1 		= GetLayerDoc('Layer1');
	thisPage 	= page1.forms[_form1];
	numrec 		= thisPage.elements['_obj__' + multi_line_name + '_numofrows_top'].value;

	for (var m=0; m<numrec; m++) {
		
		itemID = thisPage.elements['_obj__'+multi_line_name+'('+m+')__ITEMID'];
		itemWarehouseText = thisPage.elements['_obj__'+multi_line_name+'('+m+')__WAREHOUSE__LOCATION_NO'];
		costmethod = thisPage.elements['_obj__'+multi_line_name+'('+m+')__COST_METHOD'];
		itemUIQty = thisPage.elements['_obj__'+multi_line_name+'('+m+')__UIQTY'];
		itemUOM = thisPage.elements['_obj__'+multi_line_name+'('+m+')__UNITFACTOR'];
		
		if (costmethod.value=='S')
		{			
			itemQty = itemUIQty.value * itemUOM.value;		
			rec = AutoFillLookUpID(itemID.value.i_slice(0, '--'), false);
			degree = (rec && rec.precision != null && rec.precision != '') ? rec.precision : app_precision;			
			GetCost_InvTransInOut(itemID,itemWarehouseText.value,itemQty,_layer1, _form1, '', m, degree);
		}
		
	}
}


function AutoFill(_layer, _form, from, rownum,fieldid) {

	_form = (_form == null) ? 0 : _form;
	page = GetLayerDoc(_layer);
	//var rec = 0;

	layer = document.forms[0]._currentlayer.value;
	custvendid = '';
	billtocontact='';
	shiptocontact='';
	currency='';
	
	if(ismcpEnabled){
		prefix = "TRX_";
	} else {
		prefix = "UI";
	}
	
	var headerform	 	= GetOwnerDocumentForm(layer, 0);
	var lineitemform 	= GetOwnerDocumentForm(layer, 1);
		
	if (mod != 'inv') {
		custvendid = headerform.elements['_obj__CUSTVENDID'].value;
		billtocontact = headerform.elements['_obj__BILLTO__CONTACTNAME'].value;
		shiptocontact = headerform.elements['_obj__SHIPTO__CONTACTNAME'].value;
		
		t_arr = custvendid.split("--");
		custvendid = t_arr[0];
		custvendname = t_arr[1];
		
	}
	
	date = headerform.elements['_obj__WHENCREATED'].value;
	docpar = headerform.elements['_obj__DOCPARID'].value;
	
	if(ismcpEnabled){		
		currency = lineitemform.elements['_obj__CURRENCY'].value;
	}
	exchRate = GetExchangeRate(_layer, _form);

	if (from == 'WHENCREATED' || fieldid == '_obj__WHENCREATED') {
		AutoPopulateDate(date);
		return true;
	}
	
	if (from == 'DOCNO' || fieldid == '_obj__DOCNO') {
		docno = headerform.elements['_obj__DOCNO'].value;
		AutoPopulateDocno(docno);
		return true;
	}

	if (fieldid != '') {

		if (fieldid == '_obj__CUSTVENDID') {
			AutoPopulateCustVendID(custvendid+'--'+custvendname,'true');
			return true;
		}

		if (fieldid == '_obj__CREATEDFROM' || fieldid == '_obj__COPYFROM') {
			mode = (fieldid == '_obj__CREATEDFROM') ? 'Update' : 'Copy';
			source = headerform.elements[fieldid].value;

			RefreshOnSourceTransSelect(mode, source, false);
			return true;
		}

		fromarr = fieldid.split('(');
		if (fromarr[1]) {
			anothersplit = fromarr[1].split(')');
			rownum = anothersplit[0];
		}


		fromarr = fieldid.split('__');
		from = fromarr[(fromarr.length)-1];
	}

	//Constants
	//ITEMID = 0; DESC = 1; TAX = 2; PRODLINE = 3; BASEUOM = 4; PURUOM = 5; SALUOM=6; PRECISION = 7; PRICE = 8; WH = 9, ITEMTYPE = 10, QTYCHECK=11;
	//These extra constants are used for Contract
	//HASSTARTENDDATE=18; TERMPERIOD=19; TOTALPERIOD=20; COMPUTEFORSHORTTERM=21;
	multi_line_name = 'ENTRIES';

	itemCurrentId = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__ITEMCURRENT'];
	itemUnitText = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UNIT'];
	itemWarehouseText = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__WAREHOUSE__LOCATION_NO'];
	itemId = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__ITEMID'];
	itemTaxable = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__ITEM__TAXABLE'];
	itemProdLine = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__PRODLINE'];
	itemDesc =  page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__ITEMDESC'];
	itemPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__'+prefix+'PRICE'];
	var discPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__DISCOUNTPERCENT'];
	listPrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__RETAILPRICE'];
	itemUnit = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UNITFACTOR'];
	itemTotal = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__'+prefix+'VALUE'];
	
	if(ismcpEnabled){
		basePrice = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UIPRICE'];
		baseTotal = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UIVALUE'];
	}else{
		basePrice='';
		baseTotal='';
	}

	helperItemPrice = page.forms[_form].elements['helper__obj__'+multi_line_name+'('+rownum+')__'+prefix+'PRICE'];
	helperItemUnit = page.forms[_form].elements['helper__obj__'+multi_line_name+'('+rownum+')__UNITFACTOR'];	
	itemQuantity = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__UIQTY'];
	item_wh_qty = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__WAREHOUSEAVAIL'];
	helper_item_wh_qty = page.forms[_form].elements['helper__obj__'+multi_line_name+'('+rownum+')__WAREHOUSEAVAIL'];
	avail_link = document.getElementsByName('helper__obj__'+multi_line_name+'('+rownum+')__AVAILLINK');
	itemLocation = null;
	if (page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__LOCATION']) {
		itemLocation = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__LOCATION'];
	}

	if (page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__DEPARTMENT']) {
		itemDept = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__DEPARTMENT'];
	}

	if (page.forms[_form].elements['_obj__TERM__NAME']) {
		termName = page.forms[_form].elements['_obj__TERM__NAME'];
	}
	duedate = page.forms[_form].elements['_obj__WHENDUE'];
	whencreated  = page.forms[_form].elements['_obj__WHENCREATED'];

	//parsing item from itemid-name before sending it to ItemFetch() function
	if (itemId){
		var item = itemId.value.i_slice(0, '--');
	}
	
	// New pricing code
	if (from == 'ITEMID' || from == 'ITEMDESC') {

		if (itemCurrentId.value == itemId.value && itemId.value != '') {
			return true;
		}

		if (from == 'ITEMID') {			
			if (itemId.value == '') {
				if(enableoverridetax == 'true') {
					helperOverrideTax = page.forms[_form].elements['helper__obj__'+multi_line_name+'('+rownum+')__OVERRIDETAX'];
					overrideTax = page.forms[_form].elements['_obj__'+multi_line_name+'('+rownum+')__OVERRIDETAX'];

					overrideTax.disabled = true;
					overrideTax.value = 'false';
					helperOverrideTax.checked=false;
					helperOverrideTax.disabled = true;
				}
				return false;
			}
			rec = AutoFillLookUpID(item, true);
			
		} else {
			if (itemId.value != '') return true;
			if (itemDesc.value == '') return false;
			rec = AutoFillLookUpDesc(itemDesc.value);
		}

		if(from == 'ITEMID') {
			// Rev Rec
			if(enablerevrec != null && enablerevrec != '' && enablerevrec != 'None') {
				revrectempl_fld = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__REVRECTEMPLATE'];
				revrectempl_pick = document.getElementsByName('fld_obj__ENTRIES('+rownum+')__REVRECTEMPLATE');
				revrec_link = document.getElementById('anchorREVREC'+rownum);

				var startDate = document.forms[1].elements['_obj__WHENCREATED'].value;
				var termStartDate = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__REVRECSTARTDATE'];
				var sc_termStartDate = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__SC_REVRECSTARTDATE'];
				if(termStartDate){
				  termStartDate.value = startDate;
				}
				if(sc_termStartDate){
				  sc_termStartDate.value = startDate;
				}
				//alert(revrectempl_pick);
				//alert(itemId.value);
				//alert(revrectempl_fld.value);

				if(itemId.value) {				
					//var strLoc = fetchdefaultrevrectemplateURL+'&itemlabel='+iaEncodeURIComponent(itemId.value);					
					var defaulttemplate = doAjax(fetchdefaultrevrectemplateURL+'&itemlabel='
						+iaEncodeURIComponent(itemId.value.encrypt())+'&.dt='+iaEncodeURIComponent(dt));					
					revrectempl_fld.value = (defaulttemplate!='') ? defaulttemplate : '';
				}
				
				if(itemId.value  && revrectempl_fld.value == 'Use Kit Revenue Rules'){
					revrectempl_fld.disabled = true;
					baseSetText(revrec_link, revrec_link.getAttribute('viewtitle'));
					//revrectempl_pick.style.display = 'none';
					  if(revrectempl_pick[0]){
									revrectempl_pick[0].style.visibility = 'hidden';
					  }
					  if(revrectempl_pick[1]){
									revrectempl_pick[1].style.visibility = 'hidden';
					  }
				}else{
					revrectempl_fld.disabled = false;
					//revrectempl_pick.style.display = 'block';
					if(revrectempl_pick[0]){
						revrectempl_pick[0].style.visibility = 'visible';
					}
					if(revrectempl_pick[1]){
						revrectempl_pick[1].style.visibility = 'visible';
					}
					
					if(revrectempl_fld.value && revrectempl_fld.value != ''){					
						baseSetText(revrec_link, revrec_link.getAttribute('viewtitle'));
					} else {
						baseSetText(revrec_link, revrec_link.getAttribute('addtitle'));
					}
				}
			}
		}
		
		page0 = GetLayerDoc('Layer0');
		default_whse = page0.forms['Layer0_form'].elements['_obj__WAREHOUSE__LOCATIONID'].value;
		
		whse = (default_whse != '') ? default_whse : whsedensearr[0];
	
		if (wareselmethod=='Use the default warehouse'){
			whse = (default_whse) ? default_whse:upref_docpar_whse;
		}
		
		ItemFetch(custvendid, billtocontact, shiptocontact, item, itemProdLine.value, 1, date, docpar, whse, currency, _layer, _form, from, rownum, fieldid, 'RespProcesser_ItemID');
	}

	if(itemsHaveStartEndDate == true){		
	  if (mod == 'so' && (from == 'REVRECSTARTDATE' || from == 'REVRECENDDATE' || from == 'PRORATE' || from == 'SC_REVRECSTARTDATE' || from == 'SC_REVRECENDDATE' )) {
		  
		  var termStartDate = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__REVRECSTARTDATE'];
		  var termEndDate   = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__REVRECENDDATE'];

		  var sc_termStartDate = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__SC_REVRECSTARTDATE'];
		  var sc_termEndDate   = page.forms[_form].elements['_obj__ENTRIES('+rownum+')__SC_REVRECENDDATE'];
		  var proratePrice  = document.getElementsByName('_obj__ENTRIES('+rownum+')__PRORATEPRICE')[0];
		  var computeForShortTerm  = itemdensearr[item].computeforshortterm;
		  var hasStartEndDate  = itemdensearr[item].hasstartenddate;

		  if( from == 'SC_REVRECSTARTDATE' || from == 'SC_REVRECENDDATE'){
			  if(ReformatDate(sc_termEndDate.value,USERDATEFORMAT,' Ymd') < ReformatDate(sc_termStartDate.value,USERDATEFORMAT,' Ymd')) {
					warnstr = 'Warning: End Date ('+sc_termEndDate.value+') entered is less than the Start Date ('+sc_termStartDate.value+')';
					alert(warnstr);
					sc_termEndDate.select();
					sc_termEndDate.focus();
					return false;
			  }else{
					return true;
			  }
		  }
		
		  if(hasStartEndDate == 'T' && from=='REVRECSTARTDATE' && termStartDate.value!=''){
			  //var strLoc = fetchRenewalTermurl+'&itemlabel='+iaEncodeURIComponent(itemId.value)+'&startdate='+ReformatDate(termStartDate.value, USERDATEFORMAT, STANDARDFORMAT);
			  //var output = baseLoadXMLHTTP(strLoc, true);
			  //var endDate = ReformatDate(output, STANDARDFORMAT, USERDATEFORMAT);
			  var endDate = ReformatDate(doAjax(fetchRenewalTermurl+'&itemlabel='
				  +iaEncodeURIComponent(itemId.value.encrypt())+'&.dt='+iaEncodeURIComponent(dt)+'&startdate='
				  +ReformatDate(termStartDate.value, USERDATEFORMAT, STANDARDFORMAT)), STANDARDFORMAT, USERDATEFORMAT);
			  termEndDate.value = endDate;
		  }

		  if(termEndDate.value != '' && termStartDate.value != '') {
			  if(ReformatDate(termEndDate.value,USERDATEFORMAT,' Ymd') < ReformatDate(termStartDate.value,USERDATEFORMAT,' Ymd')) {
					warnstr = 'Warning: End Date ('+termEndDate.value+') entered is less than the Start Date ('+termStartDate.value+')';
					alert(warnstr);
					termEndDate.select();
					termEndDate.focus();
					return false;
			  }
			  //else{
			  //     alert("Warning: Either Start or End Date are missing");
			  //     termEndDate.select();
			  //     termEndDate.focus();
			  //     return false;
			  //}
			  var item = itemId.value.i_slice(0, '--');

			  if((shouldProrate=='true') && rec  && (hasStartEndDate == 'T') && (from == 'PRORATE' || computeForShortTerm == 'T')){
				  proratePrice.value='true';
				  PriceCheck(custvendid, billtocontact, shiptocontact, item, itemProdLine.value, 1, date, docpar, degree, currency,'', _layer, _form, from, rownum, fieldid, 'RespProcesser_ProratePrice');
			  }
		   }
	    }
	}
	
	if (from == 'UIQTY') {		
		qty = helperItemUnit.value * itemQuantity.value;
		whse = itemWarehouseText.value;
		degree = (rec && rec.precision != null && rec.precision != '') ? rec.precision : app_precision;
		PriceCheck(custvendid, billtocontact, shiptocontact, item, itemProdLine.value, qty, date, docpar, degree, currency,whse, _layer, _form, from, rownum, fieldid, 'RespProcesser_Quantity');
	}

	if (from == 'UNITFACTOR') {
		degree = (rec && rec.precision != null && rec.precision != '') ? rec.precision : app_precision;
		qty = helperItemUnit.value * itemQuantity.value;

		if(discPrice && discPrice.value){
			discPrice.value = '';
		}
		whse = itemWarehouseText.value;
	    PriceCheck(custvendid, billtocontact, shiptocontact, item, itemProdLine.value, qty, date, docpar, degree, currency,whse, _layer, _form, from, rownum, fieldid, 'RespProcesser_UnitFactor');
	}

	if ((from == 'DISCOUNTPERCENT') && listPrice &&  discPrice) {
		 var proratePrice  = document.getElementsByName('_obj__ENTRIES('+rownum+')__PRORATEPRICE')[0];
		 if (isLineDiscountOnSuggestedPrice=='T')
		 {
                     
			 if (proratePrice && proratePrice.value=='true' && discountProratePrice[rownum] && discountProratePrice[rownum]['itemkey']==item)
			 {
				 var discountPrice =   calcDiscount(discPrice.value,discountProratePrice[rownum]['prprice'],degree);
			 }else{
				 var discountPrice =   calcDiscount(discPrice.value,listPrice.value,degree);
			 }
		 }else{
			  var discountPrice =   calcDiscount(discPrice.value,itemPrice.value,degree);
		 }
		
		if(itemPrice){
			itemPrice.value = RoundCurrency(discountPrice,degree);
			itemPrice.value = (itemPrice.value) ? itemPrice.value : '0.00';
		}

		if(itemTotal){
			itemTotal.value =  RoundCurrency(itemQuantity.value * discountPrice,degree);
			itemTotal.value = (itemTotal.value) ? itemTotal.value : '0.00';
		}

		if(baseTotal){
			if(baseCurrency == currency){
				baseTotal.value = itemTotal.value;
			}else{
				baseTotal.value =  RoundCurrency(itemTotal.value * exchRate);
			}
		}

		if(basePrice){
			if(baseCurrency == currency){
				basePrice.value = itemPrice.value;
			}else{
				basePrice.value = RoundCurrency((itemTotal.value * exchRate)/itemQuantity.value, degree);
			}
		}

		RefreshMultilineTotal(this,_layer,_form,'_obj__'+multi_line_name,prefix+'VALUE');
	}

	if (from == prefix+'PRICE') {
		quantity = itemUnit.value * itemQuantity.value;

		if(discPrice && discPrice.value){
			discPrice.value = '';
		}

		rec = AutoFillLookUpID(itemId.value, true);
		degree = (rec && rec.precision != null && rec.precision != '') ? rec.precision : app_precision;

		itemPrice.value = RoundCurrency(itemPrice.value,degree);

		itemTotal.value =  RoundCurrency(itemQuantity.value * itemPrice.value);
		if(baseTotal){
			if(baseCurrency == currency){
				baseTotal.value = itemTotal.value;
			}else{
				baseTotal.value =  RoundCurrency(itemTotal.value * exchRate);
			}
		}
		if(basePrice){
			if(baseCurrency == currency){
				basePrice.value = itemPrice.value;
			}else{
				basePrice.value = RoundCurrency((itemTotal.value * exchRate)/itemQuantity.value, degree);
			}
		}
		RefreshMultilineTotal(this,_layer,_form,'_obj__'+multi_line_name,prefix+'VALUE');
	}

	if (from == prefix+'VALUE') {
		if(itemPrice.disabled != true){
			itemPrice.value =  RoundCurrency(itemTotal.value/itemQuantity.value,degree);
		}else{
			itemTotal.value =  RoundCurrency(itemPrice.value*itemQuantity.value,degree);
		}
		if(baseTotal){
			baseTotal.value =  RoundCurrency(itemTotal.value * exchRate);
		}
		if(basePrice){
			basePrice.value =  RoundCurrency((itemTotal.value * exchRate)/itemQuantity.value, degree);
		}
		if(discPrice && discPrice.value){
			discPrice.value = '';
		}
	}

	if (from == 'WAREHOUSEAVAIL') {

		itemWarehouseText.value = helper_item_wh_qty.options[helper_item_wh_qty.options.selectedIndex].text;
	
		quantity = itemUnit.value * itemQuantity.value;

		var whseid = itemWarehouseText.value.i_slice(0, '--');
		
		var item = itemId.value.i_slice(0, '--');
		
		ItemFetch(custvendid, billtocontact, shiptocontact, item,itemProdLine.value,quantity,date,docpar,whseid, currency, _layer, _form, from, rownum, fieldid, 'RespProcesser_Warehouse');
	}
	
	if (from == 'TERM.NAME' || from == 'NAME'){
	
		var termObj = term_arr[termName.value];
		
		if (termName.value == '' || !termObj) {
			return false;
		}
		
		if (whencreated.value != '' && duedate) {
			/*
			if (termObj["DAY"]) {
				duedate.value = CalculateDueDate(termObj["MNTH"],termObj["DAY"],whencreated.value);
			} else {
				duedate.value = CalculateDueDate('',termObj["MNTH"],whencreated.value);
			}
			*/
			duedate.value = CalculateDueDateEOM([termObj["MNTH"],termObj["DAY"]],whencreated.value);
		}
		
		termName.value = termObj["NAME"];
		
		//We need to unset the value of termkey so that the 'set' knows that the term name has changed
		if (from == 'TERMNAME') {
			termkey.value = '';
		}		
	}

	if ((from == 'DEPARTMENT' || from == 'LOCATION' || from.startsWith('HEADER_')) && (rownum == '') ) {
		
		default_deptloc = page.forms[_form].elements['_obj__'+from].value;
				
		if (default_deptloc == '') { return;}

		var itemsAvailable = false;

		_form1 = 'Layer1_form';
		multi_line_name = 'ENTRIES';
		page1 = GetLayerDoc('Layer1');

		numrec = page1.forms[_form1].elements['_obj__' + multi_line_name + '_numofrows_top'].value;
	
		var displayFrom = from;	
		
		// Strip the HEADER_ from the id for all dimension fields.		
		if (from.startsWith('HEADER_')) {
			displayFrom = page.forms[_form].elements['_obj__'+from].getAttribute('displayname');
			from = from.split('_');
			from = from[1];
			if (!displayFrom) {
				displayFrom = from;
			}
		}		

		for (var m=0; m<numrec; m++) {
			itemIDField = page1.forms[_form1].elements['_obj__'+multi_line_name+'('+m+')__ITEMID'];
			deptlocField = page1.forms[_form1].elements['_obj__'+multi_line_name+'('+m+')__'+from];
			if (itemIDField.value != '' && deptlocField.value != default_deptloc) {
				itemsAvailable = true;
				break;
			}
		}
		
		//Populate dept/loc for subtotals from the default dept/loc in header tab
		var subtotalsAvailable = false;
		multi_line_name_subtotal = 'SUBTOTALS';
		numrecsubtotals = document.all[layer].ownerDocument.forms[0].elements['_subtotalCount'].value;

		for (var m=1; m<numrecsubtotals-1; m++) {
			itemIDField = page1.forms[_form1].elements['_obj__'+multi_line_name_subtotal+'('+m+')__DESCRIPTION'];
			deptlocField = page1.forms[_form1].elements['_obj__'+multi_line_name_subtotal+'('+m+')__'+from];

			if (deptlocField && deptlocField.value!='' && deptlocField.value != default_deptloc) {
				subtotalsAvailable = true;
				break;
			}
		}

		if(itemsAvailable && !subtotalsAvailable){
			var str_Itemsub = "Line Items tab";
		}else if(!itemsAvailable && subtotalsAvailable){
			var str_Itemsub = "Subtotals";
		}else if(itemsAvailable && subtotalsAvailable){
			var str_Itemsub = "Line Items and Subtotals";
		}
				
		//show message only if line entries are present or subtotals dept/loc are present
		if (itemsAvailable || subtotalsAvailable) {
			var confirmProgress = confirm("This will cause all "+displayFrom.toLowerCase()+"s in the "+ str_Itemsub +" to default to '"+default_deptloc+"'.\n Do you want to proceed?");
		}
		
		if (confirmProgress) {
			
			ApplyDefaultsToLine(numrec, page1, _form1, multi_line_name, default_deptloc, from);
			ApplyDefaultsToSubtotalLine(numrecsubtotals, page1, _form1, multi_line_name_subtotal, default_deptloc, from);
			
			// In case where the project changes, the default customer associated with the project also changes, In those cases we have to 
			// apply the defaults to the customer values also.
			if (from == 'PROJECTID') {
                if (page.forms[_form].elements['_obj__HEADER_CUSTOMERID']) {
                    default_customerid = page.forms[_form].elements['_obj__HEADER_CUSTOMERID'].value;
                    if (default_customerid) {
                        ApplyDefaultsToLine(numrec, page1, _form1, multi_line_name, default_customerid, 'CUSTOMERID');
                        ApplyDefaultsToSubtotalLine(numrecsubtotals, page1, _form1, multi_line_name_subtotal, default_customerid, 'CUSTOMERID');
                    }
                }
                if (page.forms[_form].elements['_obj__HEADER_BILLABLE']) {
                    default_customerid = page.forms[_form].elements['_obj__HEADER_BILLABLE'].value;
                    if (default_customerid) {
                        ApplyDefaultsToLine(numrec, page1, _form1, multi_line_name, default_customerid, 'BILLABLE');
                        ApplyDefaultsToSubtotalLine(numrecsubtotals, page1, _form1, multi_line_name_subtotal, default_customerid, 'BILLABLE');
                    }
                }
			}
            headerProjectOnChange(true);
            
		}else if(!subtotalsAvailable){
			ApplyDefaultsToSubtotalLine(numrecsubtotals, page1, _form1, multi_line_name_subtotal, default_deptloc, from);			
		}
	}
	if (fromRecurDocument) {		
		PopulateLayer();
	}
}

function ApplyDefaultsToLine(numrec, page1, _form1, multi_line_name, default_dimen, from) {
	
	for (var m=0; m<numrec; m++) {
		thisPage = page1.forms[_form1];
		itemID = thisPage.elements['_obj__'+multi_line_name+'('+m+')__ITEMID'];
		if (itemID.value && thisPage.elements['_obj__'+multi_line_name+'('+m+')__'+from]) {
			thisPage.elements['_obj__'+multi_line_name+'('+m+')__'+from].value = default_dimen;
		}
	}	
	return true;
	
}

function ApplyDefaultsToSubtotalLine(numrecsubtotals, page1, _form1, multi_line_name_subtotal, default_dimen, from) {
	
	for (var m=1; m<numrecsubtotals-1; m++) {
		thisPage = page1.forms[_form1];
		itemID = thisPage.elements['_obj__'+multi_line_name_subtotal+'('+m+')__DESCRIPTION'];
		if (itemID.value && thisPage.elements['_obj__'+multi_line_name_subtotal+'('+m+')__'+from]) {
			thisPage.elements['_obj__'+multi_line_name_subtotal+'('+m+')__'+from].value = default_dimen;
		}
	}
	return true;
	
}

//Helper function for AutoFill to look up values in the array
function AutoFillLookUp(arr, field, id) {

	var partialMatchBegIndex;
	var partialMatchMidIndex;

	numRec = arr.length;
	idlen = id.length;

	// Look for a case sensitive Complete Match first
	for (i=0; i<numRec; i++) {
		if (arr[i] != null && arr[i][field] == id){
			return i;
		}
	}

	// Look for a case insensitive Complete Match first
	for (i=0, j=0; i<numRec; i++) {
		if (arr[i] != null) {
			if (arr[i][field].toLowerCase() == id.toLowerCase()){
				return i;
			}

			//Look for a partial match, matching at Beginning
			if (arr[i][field].substring(0,idlen).toLowerCase() == id.toLowerCase()) {
				j++;
				partialMatchBegIndex = i;
			}

			// If a Beginning match is not found, Look for a partial match, matching within the string
			if (arr[i][field].toLowerCase().indexOf(id.toLowerCase()) != -1) {
				partialMatchMidIndex = i;
			}
		}
	}

	if (partialMatchBegIndex != null) {
		if (j == 1) {
			return partialMatchBegIndex;
		}else{
			return (-1);
		}
	}

	if (partialMatchMidIndex != null) {
		return partialMatchMidIndex;
	}

	return (-1); //failed
}

// function RoundCurrency(val,degree)
function RoundCurrency(val,degree) {
	if (val === null || val === '') return '';

	if(arguments.length > 1 ) {
		//alert("Value..:"+val+"\nDegree..:"+degree);
		var n = Math.pow(10,degree);
		if (val !== '') {
			val = Math.round(val * n)/n;

			var suffixStr = (val.toString().indexOf('.') == -1) ? '.' : '';
			suffixLen = degree - ((val.toString().indexOf('.') > 0) ? (val.toString().length - val.toString().indexOf('.')-1) : 0 );

			for(i=0; i < suffixLen; i++) {
				suffixStr +='0';
			}
			if (suffixLen > 0 || (val.toString().indexOf('.') == -1 && degree > 0)) {
				val = val + suffixStr;
			}
			if (val.toString().charAt(val.toString().length-degree) == '.') {
				val = val + '0';
			}
		}
	} else {
		//	Default Round Currency Method
		if (val !== '') {
			val = Math.round(val * 100)/100;
			if (val.toString().indexOf('.') == -1) {
				val = val + '.00';
			}
			if (val.toString().charAt(val.toString().length-2) == '.') {
				val = val + '0';
			}
		}
	}
	return val.toString();
}


function doAjax(url) {
	//alert('doAjax '+url);
	
	var httpRequest=false;
	var output = '';
	// Standard
	if (window.XMLHttpRequest) {
		httpRequest = new XMLHttpRequest();
	}
	// IE
	else if (window.ActiveXObject) {
		httpRequest = new ActiveXObject('Microsoft.XMLHTTP');
	}
	if(!httpRequest) {
		alert('The browser doesn\'t support this feature');
		return false;
	}
	httpRequest.open('POST', url, false); //true=Asynchronous, false=Synchronous
	//httpRequest.onreadystatechange=function() {
	//	if((httpRequest.readyState==4) && (httpRequest.status==200)) {
	//		output = httpRequest.responseText;
	//	}
	//}
	httpRequest.send(null);
	output = httpRequest.responseText;
	return output;
	
	/*
	var qrequest = new Qrequest;
	var updateFunc = null;
	var updateArgs = null;
	return qrequest.quickRequest(url, updateFunc, updateArgs, false);
	*/
}


function beforeSubmitJS() {
	ok = true;
	if ( ShowWarningOnLowCrLimit() && creditlimitcheck !='NOCHECK' ) {
		ok = WarnCreditLimit();
	}
	return ok;
}

function calcDiscount(dispercent,suggestPrice,degree) {
	var discount = ((suggestPrice * dispercent)/100)
	var discountPrice =   suggestPrice-discount;
	var discountPrice =  RoundCurrency((suggestPrice-discount),degree);
	return discountPrice;
}

/* -------- Multi Document Conversion related -----------*/
function ShowSourceDocsMenu (obj, menuid) {
	  var hrefHTML = getSourceDocsDivText();
	  menuExpand = document.getElementById(menuid);
	  if (!menuExpand) return false;
	  menuExpand.style.left = baseGetObjectLeft(obj) + obj.offsetWidth;
	  menuExpand.style.top = baseGetObjectTop(obj);
	  menuExpand.style.position = 'absolute';
	  menuExpand.style.visibility = 'visible';
	  menuExpand.innerHTML = hrefHTML;
	  return false;
}

function getSourceDocsDivText() {
	var hrefHTML = '';

	if (sourceDocsArr.length >= 1) {
		hrefHTML = '<TABLE BORDER=\"1\" CELLSPACING=\"0\" CELLPADDING=\"1\"><tr><td><table>';
	}
	
	for (i=0; i<sourceDocsArr.length; i++) {
		url = 'editor.phtml?.sess='+sess+'&.op='+docviewop+'&.mod='+mod+'&.r='+escape(sourceDocsArr[i])+'&.popup=1';
		Launchurl = "Launch('"+url+"','mywindow','700','500');";

		hrefHTML += '<A CLASS="Result2"  HREF="#"  onclick = '+Launchurl+'><font size=1>'+sourceDocsArr[i]+'</font></A><BR>';
		//hrefHTML += "<A CLASS='Result2'  HREF='#' onmouseout='setTimeout(\'hideSourceDocsMenu()\', 100);window.status=\'\'; return true;' onclick = "+Launchurl+"><font size=1>"+sourceDocsArr[i]+"</font></A><BR>";
	}
	if (hrefHTML) {
		hrefHTML += '</table></td></tr></table>';
	}
	return hrefHTML;
}

function hideSourceDocsMenu(menuid) {
	if (onMousedLayer == 'set') {
		return;
	}
	onMousedLayer = '';

	menuExpand = document.getElementById(menuid);
	if (!menuExpand) return false;
	menuExpand.style.visibility = 'hidden';
}
/* ------------ Multi Document Conversion related ends ----------*/

function ShowApprovalHistory() {

	page = GetLayerDoc('Layer0');				

	docid = page.forms[0].elements['_obj__DOCID'].value;
        
	url = 'lister.phtml?.sess='+sess+'&.op='+approvalHistoryOP+'&DOCUMENT_DOCID='+encodeURIComponent(docid.encrypt());

	Launch(url,'mywindow','1000','600');        

}

function DisallowNegativeQtyForSerializedItems(_layer, _form, currentrow, currentelement) {

	_form = (_form == null) ? 0 : _form;
	page = GetLayerDoc(_layer);
	layer = document.forms[0]._currentlayer.value;
	thisPage = page.forms[_form];
	multi_line_name='ENTRIES';
	enableser = thisPage.elements['_obj__' + multi_line_name + '(' + currentrow + ')__ENABLESNO'];
	itemid = thisPage.elements['_obj__'+multi_line_name+'('+currentrow+')__ITEMID'];
	uiqty=thisPage.elements['_obj__'+multi_line_name+'('+currentrow+')__UIQTY'];
	if (currentelement.value<0 && enableser.value=='T') {
		alert('Quantity cannot be negative for serialized item '+itemid.value+'. Resetting it to default value 1.');
		currentelement.value=1;
		currentelement.onchange();
		currentelement.focus();
	}
}

function revrecProjectOnChange(rownum)
{
    populateTasks(rownum);
    projectOnChange(rownum, 'revrec');
}

function dimensionProjectOnChange(control)
{
    if (control.name == '_obj__HEADER_PROJECTID') {
        headerProjectOnChange(false);
    } else {
        rownum = getRecurFieldRowNum(control.name)
        projectOnChange(rownum, 'dimension');
    }
}

function projectOnChange(rownum, from)
{
    var revproject = "_obj__ENTRIES(" + rownum + ")__PROJECT";
    if(!document.forms['Layer1_form'].elements[revproject]) {
        return;
    }
    var dimproject = "_obj__ENTRIES(" + rownum + ")__PROJECTID";
    if(!document.forms['Layer1_form'].elements[dimproject]) {
        return;
    }
    var revProjectValue = document.forms['Layer1_form'].elements[revproject].value;
    var dimProjectValue = document.forms['Layer1_form'].elements[dimproject].value;
//    alert(revProjectValue + '=' + dimProjectValue);
    if (revProjectValue == dimProjectValue) {
        return;
    }
    if (from == 'revrec') {
        document.forms[0].elements[dimproject].value = revProjectValue;
        document.forms['Layer1_form'].elements[dimproject].value = revProjectValue;
    } else if (from == 'dimension') {
        document.forms[0].elements[revproject].value = dimProjectValue;
        document.forms['Layer1_form'].elements[revproject].value = dimProjectValue;
    }
}

function headerProjectOnChange(confirmedUpdate)
{
//    alert(confirmedUpdate);
	if(document.forms['Layer1_form'].elements['_obj__ENTRIES_numofrows_top']) {
		var topnum = document.forms['Layer1_form'].elements['_obj__ENTRIES_numofrows_bottom'].value;
	}
	if(document.forms['Layer1_form'].elements['_obj__ENTRIES_numofrows_bottom']) {
		var botnum = document.forms['Layer1_form'].elements['_obj__ENTRIES_numofrows_bottom'].value;
	}
    var maxnumrows = Math.max(topnum, botnum);

    var doUpdate = true;
    if (!confirmedUpdate) {
        for (var i=0; i< maxnumrows; i++) {
            var item = "_obj__ENTRIES(" + i + ")__ITEMID";
            var itemValue = document.forms['Layer1_form'].elements[item].value;
            if (itemValue != '') {
                doUpdate = false;
            }
    //        alert(itemValue);
        }
    }
//    alert(doUpdate);
    if (!doUpdate) {
        return;
    }

    var headproject = "_obj__HEADER_PROJECTID";
    var headProjectValue = document.forms['Layer1_form'].elements[headproject].value;
 	for (var i=0; i< maxnumrows; i++) {
		var revproject = "_obj__ENTRIES(" + i + ")__PROJECT";
        var revProjectValue = document.forms['Layer1_form'].elements[revproject].value;
//        alert(revProjectValue + '=' + headProjectValue);
        document.forms[0].elements[revproject].value = headProjectValue;
        document.forms['Layer1_form'].elements[revproject].value = headProjectValue;
	}
}

function populateTasksAllRows() {
	if(document.forms['Layer1_form'].elements['_obj__ENTRIES_numofrows_top']) {
		var topnum = document.forms['Layer1_form'].elements['_obj__ENTRIES_numofrows_bottom'].value;
	}
	if(document.forms['Layer1_form'].elements['_obj__ENTRIES_numofrows_bottom']) {
		var botnum = document.forms['Layer1_form'].elements['_obj__ENTRIES_numofrows_bottom'].value;
	}
	var maxnumrows = Math.max(topnum, botnum);
	for (var i=0; i< maxnumrows; i++) {
		populateTasks(i);
	}
}

function populateTasks(rownum)
{
	var projectElement = "_obj__ENTRIES(" + rownum + ")__PROJECT";
	var rrTemplateElement = "_obj__ENTRIES(" + rownum + ")__REVRECTEMPLATE";
	var bTemplateElement = "_obj__ENTRIES(" + rownum + ")__BILLINGTEMPLATE";
	if (!document.forms['Layer1_form'].elements[projectElement] ||
	    !document.forms['Layer1_form'].elements[projectElement].value
    ) {
		updateSelectOptions("ENTRIES(" + rownum + ")__TASKKEY", 1, {'--Select Task--':''});
		return;
	}
	var project = document.forms['Layer1_form'].elements[projectElement].value;
	var projectid = project.split('--')[0];

	var qrequest = new QRequest;
	var url = 'qrequest.phtml?.function=GetProjectTasks&.handler=QRequest&.entity=project&.sess=' + sess+
        '&.otherparams=projectid,rrTemplate,bTemplate&.projectid=' + escape(projectid);
	if (document.forms['Layer1_form'].elements[rrTemplateElement] &&
	    document.forms['Layer1_form'].elements[rrTemplateElement].value
	) {
		var rrTemplate = document.forms['Layer1_form'].elements[rrTemplateElement].value;
    	url += '&.rrTemplate=' + escape(rrTemplate);
    }
	if (document.forms['Layer1_form'].elements[bTemplateElement] &&
	    document.forms['Layer1_form'].elements[bTemplateElement].value
	) {
		var bTemplate = document.forms['Layer1_form'].elements[bTemplateElement].value;
    	url += '&.bTemplate=' + escape(bTemplate);
    }
	var updateFunc = RespProcesser_populateTasks;
	var updateArgs = rownum;
	qrequest.quickRequest(url, updateFunc, updateArgs, true);
}

function RespProcesser_populateTasks(rownum, m_response) {
	var nodes = m_response.getElementsByTagName("task");
	var options = {'--Select Task--': ''};
	for( var i = 0 ; i < nodes.length ; i++ ) {
		var taskkey = nodes[i].getAttribute("taskkey");
		var taskname = nodes[i].getAttribute("taskname");
		options[taskkey + '--' + taskname] = taskkey + '--' + taskname;
	}
	updateSelectOptions("ENTRIES(" + rownum + ")__TASKKEY", 1, options);
}

function ChangePostingDate() {		
	layer = document.forms[0]._currentlayer.value;
	var headerform = GetOwnerDocumentForm(layer, 0);	
	if (headerform.elements['_obj__WHENPOSTED'] && !headerform.elements['_obj__WHENPOSTED'].readonly) {
		if (autoBatchFrequency != 'M') {
			headerform.elements['_obj__WHENPOSTED'].value = headerform.elements['_obj__WHENCREATED'].value; 
		} else {
			idate = headerform.elements['_obj__WHENCREATED'].value;
			headerform.elements['_obj__WHENPOSTED'].value = LastDateOfPeriod(idate, datefmt);
		}
	}
}
