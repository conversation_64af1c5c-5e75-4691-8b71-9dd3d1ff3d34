ia::definition:
    methodPermissions:
        GET:
            - gl/lists/glacctgrp/view
            - gl/lists/glacctgrp
        POST:
            - gl/lists/glacctgrp/create
            - gl/quickadds/glacctgrp/create
        PATCH:
            - gl/lists/glacctgrp/edit
        DELETE:
            - gl/lists/glacctgrp/delete
s1:
    hash: '0'
    type: rootObject
    includeDynamic: true
    systemViews:
        systemfw1:
            revision: s1
            hash: '0'
        systemfw2:
            revision: s1
            hash: '0'
    uiMetadataHash: '0'
