title: projects-project-group-member
x-mappedTo: projectgrpmember
type: object
description: Project group members
properties:
  key:
    type: string
    description: System-assigned key for the project group member.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: Project group members ID.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  href:
    type: string
    description: Endpoint for the project group member.
    readOnly: true
    example: /objects/projects/project-group-member/23
  sortOrder:
    type: string
    description: Sort order.
    x-mappedTo: SORTORD
    example: 'asc'
  project:
    type: object
    x-mappedTo: project
    x-object: projects/project
    properties:
      key:
        type: string
        description: Members.
        x-mappedTo: PROJECTKEY
        example: '1'
      id:
        type: string
        description: Members.
        x-mappedTo: PROJECTID
        example: DIM - BTI
      name:
        type: string
        x-mappedTo: PROJECTNAME
        example: Dimensions - Berkeley Technology Inc
      href:
        type: string
        description: Endpoint for the project.
        readOnly: true
        example: /objects/projects/project/23
      status:
        type: string
        x-mappedTo: PROJECTSTATUS
        example: 'active'
  projectGroup:
    title: Project group
    description: Project group.
    type: object
    readOnly: true
    x-mappedTo: projectgroup
    x-object: projects/project-group
    properties:
      id:
        type: string
        description: Identifier for the Project group.
        x-mappedTo: ID
        readOnly: true
        example: Top Projectes
      key:
        type: string
        description: Unique system-assigned key of the project group to which this member belongs.
        x-mappedTo: GROUPKEY
        readOnly: true
        example: '33'
      href:
        type: string
        description: Endpoint for the Project group.
        readOnly: true
        example: /objects/projects/project-group/33
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml