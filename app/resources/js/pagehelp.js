

function PageHelp(pageHelp) {

    if ( jq('.pagehelp').size() != 0 ) {
        return;
    }

    return    pageHelpSimple('.pagehelphere', pageHelp)
           || pageHelpSimple('td.LISTHEADER_PAGEHELP', pageHelp)
           || pageHelpSimple('.qx-billboard', pageHelp)
           || pageHelpSimple('.iadynmap_module_container', pageHelp);
}

function pageHelpSimple(selector, pageHelp) {
    var $tryThis = jq(selector);
    if ($tryThis.size() == 0) {
        return false;
    }
    insertPageHelp($tryThis, pageHelp);
    return true;
}

function pageHelpFormEditor(pageHelp) {

    var $tryThis = jq('.bdMain > #ErrorMsg');
    if ($tryThis.size() == 0) {
        $tryThis = jq('.qxf-pages > #ErrorMsg');
    }
    if ($tryThis.size() == 0) {
        return false;
    }
    
    insertPageHelp($tryThis.parent(), pageHelp);
    
    if (window.view) {
        window.view.subscribe(function(type, args) {
            args = args[0];
            if (args.name == 'ErrorMessageEvent' && args.visible == true) {
                jq('.pagehelp').hide();
            } 
        });
    }

    return true;

}

function generatePageHelpHtml(pageHelp, sessionId)
{
    try {

        var showTourLink = true;
        var start = window;
        while ( start.opener != undefined ) {
            start = start.opener;
            showTourLink = false;
        }
        var topFrame = start.top;

        var hasExtraContent = pageHelp.content != '';
        var extraContentClass = hasExtraContent ? ' pagehelp_extracontent' : '';

        var pagehelpdiv = '';
        pagehelpdiv += '<div class="pagehelp ' + pageHelp.clazz + extraContentClass + '">';
        pagehelpdiv +=     '<div class="pagehelpbanner_wrapper"><div class="pagehelpbanner"><div class="pagehelpbanner_content"></div></div></div>';
        pagehelpdiv +=     '<div class="pagehelptitlebar">';
        pagehelpdiv +=         '<div class="pagehelptitlebar-right ia-inline-block">';
        if ( typeof(topFrame.isSample) == 'undefined' ) {
            pagehelpdiv +=             '<div class="ia-inline-block pagehelptitlebarbutton" id="pagehelp-hide">View later</div>';
            if ( sessionId && pageHelp.id && typeof(window.csrfQRequestToken) !== 'undefined' ) {
                pagehelpdiv += '<div class="ia-inline-block pagehelptitlebarbutton" id="pagehelp-close">Close</div>';
            }
        } else if ( showTourLink && typeof topFrame.hasTour != 'undefined' ) {
            pagehelpdiv +=             '<div class="ia-inline-block pagehelptitlebarbutton" id="pagehelp-tour">Go to tour</div>';
        }
        pagehelpdiv +=         '</div>';
        pagehelpdiv +=         '<div class="pagehelptitlebar-left ia-inline-block">';
        pagehelpdiv +=             '&nbsp;';
        pagehelpdiv +=         '</div>';
        pagehelpdiv +=         '&nbsp;';
        pagehelpdiv +=     '</div>';
        pagehelpdiv +=     '<div class="pagehelpcontent">';
        pagehelpdiv +=         '<div class="pagehelp-spacer">';
        pagehelpdiv +=             '<div class="pagehelpcontent-intro pagehelpcontent-intro-content">';
        pagehelpdiv +=                     pageHelp.summary;
        pagehelpdiv +=             '</div>';
        pagehelpdiv +=         '</div>';

        pagehelpdiv +=         '<div class="pagehelp-overlay">';
        pagehelpdiv +=             '<div class="pagehelpcontent-intro pagehelpcontent-intro-content">';
        pagehelpdiv +=                     pageHelp.summary;
        pagehelpdiv +=             '</div>';
        pagehelpdiv +=             '<div class="pagehelpcontent-full pagehelpcontent-full-content">';
        pagehelpdiv +=                     pageHelp.summary;
        if ( hasExtraContent != '' ) {
            pagehelpdiv +=                 '<br/><br/>';
            pagehelpdiv +=                 pageHelp.content;
        }
        pagehelpdiv +=             '</div>';
        pagehelpdiv +=         '</div>';
        pagehelpdiv +=     '</div>';
        pagehelpdiv += '</div>';

    } catch (e) {

        // Most likely a cross-site issue where we've been opened by another site like salesforce.  Just ignore
        // pagehelp.
        var pagehelpdiv = '<div></div>';

    }

    
    return pagehelpdiv;
}

function insertPageHelp($container, pageHelp) {
    var sessionId = false;
    var elem = null;
    if ( typeof _sess != "undefined" ) {
        sessionId = _sess;
    } else if ( typeof sess != "undefined" ) {
        sessionId = sess;
    } else if ( typeof gSess != "undefined" ) {
        sessionId = gSess;
    } else if ( elem = document.getElementById('.sess') ) {
        sessionId = elem.value;
    } else if ( elems = document.getElementsByName('.sess') ) {
        if ( elems.length > 0 ) {
            sessionId = elems[0].value;
        }
    }

    var pagehelpdiv = generatePageHelpHtml(pageHelp, sessionId);
    $container.prepend(pagehelpdiv);

    setPageHelpWidths();
    jq(window).resize(setPageHelpWidths);

    var $pagehelp = jq('.pagehelp');
    var $intro = jq('.pagehelp_extracontent .pagehelp-overlay .pagehelpcontent-intro');
    var $full = jq('.pagehelp_extracontent .pagehelp-overlay .pagehelpcontent-full');
    $intro.click(function() {
        $pagehelp.addClass('pagehelp_expanded');
    });
    $full.click(function() {
        $pagehelp.removeClass('pagehelp_expanded');
    });

    jq('#pagehelp-hide').click(function() {
        $pagehelp.hide();
        if (typeof addStickyHeader === "function") {
            addStickyHeader();
        }
    });
    if ( sessionId && pageHelp.id ) {
        jq('#pagehelp-close').click(function() {
            jq.ajax("qrequest.phtml?.ajax=helpcontentengine", {
                type: "POST",
                headers: { 'X-XSRF_TOKEN': csrfQRequestToken },
                data: {
                    dismiss: pageHelp.id,
                    '.sess': sessionId 
                },
                error: function() {
                    alert('There was an error dismissing this message.');
                },
                success: function(data) {
                    if (data.status != 'ok') {
                        alert('There was an error dismissing this message.');
                    }
                }
            });
            $pagehelp.hide();
            if (typeof addStickyHeader === "function") {
                addStickyHeader();
            }
        });
        
    }

    jq('div.pagehelp #pagehelp-tour').click(function() {
        if ( typeof(top.showTourByUser) == 'function' ) {
            top.showTourByUser();
        }
    });

    jq('div.pagehelp a.tournav').click(function(e) {
        if ( e.preventDefault ) {
            e.preventDefault();
        } else {
            e.returnValue = false;
        }
        var $this = jq(this);
        var url = $this.attr('href');
        if ( url ) {
            if ( typeof(top.openInIFrame) == 'function' ) {
                var walkmeId = $this.attr('thenwalk');
                if ( walkmeId ) {
                    top.autostartWalkMeId = walkmeId;
                }
                top.openInIFrame('iamain', url);
            }
        }
        return false;
    });

    jq('div.pagehelp a.startwalk').click(function(e) {
        if ( e.preventDefault ) {
            e.preventDefault();
        } else {
            e.returnValue = false;
        }
        var $this = jq(this);
        var walkid = parseInt($this.attr('href'));
        if ( walkid && walkid != NaN ) {
            
            if ( typeof(top.WalkMeAPI) == 'object' ) {
                top.WalkMeAPI.startWalkthruById(walkid);
            } else if ( typeof(top.WalkMeAPI) == 'object' ) {
                WalkMeAPI.startWalkthruById(walkid);
            }
        }
        return false;
    });

    // Page help requires fancybox.  Do not suppress Javascript errors if it's not defined.
    jq('div.pagehelp a.tourfancybox').fancybox();

}

function setPageHelpWidths() {
    var $window = jq(window);
    var $pagehelp = jq('.pagehelp');
    if (!$pagehelp.length) {
        return;
    }
    var winwidth = $window.width();
    var pageHelpLeft = $pagehelp.offset().left;
    var pageHelpPadding = $pagehelp.outerWidth(true) - $pagehelp.width();
    var $overlay = jq('.pagehelp-overlay');

    $pagehelp.width('');
    $overlay.width('');

    var width = $pagehelp.width();
    width = Math.min(width, winwidth - pageHelpPadding - (pageHelpLeft - pageHelpPadding / 2));
    $pagehelp.width(width);
    $overlay.width(width);

    if (parent && parent.document) {
        var $iframe = jq('.quixote-modal.qx-modal-last.in .qx-modal-iframe', parent.document);
        var $iframePageHelp = $iframe.contents().find('.pagehelp');
        if($iframePageHelp.length) {
            $iframePageHelp.css({
                width: 'auto',
                margin: '65px 15px 0'
            });
        }
    }
}

function pageHelpOpenTopic (helpTopic) {
    var fo = 'dummy'; // fo is not used in HelpWin
    //for action UI, we try to use the HelpWin defined in parent if possible
    if (typeof PAGE_LAYOUT_TYPE != 'undefined' && PAGE_LAYOUT_TYPE === 'Q') {
        if (typeof window.parent.HelpWin == 'function') {
            window.parent.HelpWin(fo, helpTopic);
        } else if (typeof HelpWin == 'function') {
            HelpWin(fo, helpTopic);
        }
    } else {
        if (typeof HelpWin == 'function') {
            HelpWin(fo, helpTopic);
        } else if (typeof window.parent.HelpWin == 'function') {
            window.parent.HelpWin(fo, helpTopic);
        }
    }
}

var ph_scriptid = 0;
function ph_addScriptToDocument(theScript) {
    var scriptElement = document.createElement("script");
    scriptElement.async = true;
    scriptElement.src = encodeURI(theScript);
    scriptElement.id = "phscript_" + ph_scriptid++;
    head = document.getElementsByTagName("head")[0];
    head.appendChild(scriptElement);
}

jq(function() {
    if (typeof getHelpContent == 'function') {
        var helpContent = getHelpContent();
        if (helpContent && helpContent.pagehelp) {
            PageHelp(helpContent.pagehelp);
        }
    }
    if ( ! top.WalkMeAPI ) {
        try {
            if (    window.opener
                 && window.opener.top
                 && window.opener.top.mt_cdn_server_name
                 && window.opener.top.walkme_script_name ) {
                window.mt_cdn_server_name = window.opener.top.mt_cdn_server_name;
                ph_addScriptToDocument(window.opener.top.mt_cdn_server_name + window.opener.top.walkme_script_name);
            }
        } catch ( dontcare ) {
        }
    }
});

