<?php
/**
 * Picker class for the Standard Task object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2018 Sage Intacct Inc.
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class StandardTaskPicker extends NPicker
{

    function __construct()
    {
        parent::__construct(
            array(
                'entity'        =>  'standardtask',
                'fields'        =>  array('STANDARDTASKID', 'NAME'),
                'pickfield'        =>  'STANDARDTASKID',
                'addlPickFields'        => array('NAME'),
            )
        );
    }

    /**
     * Filter out any standard tasks for which tasks have been created for the project
     *
     * @return array querySpec
     */
    function BuildQuerySpec()
    {
        $querySpec = parent::BuildQuerySpec();

        $projectID = '';
        foreach ($this->querySpec['filters'][0] as $filter) {
            if ($filter[0] == 'PROJECTID') {
                $projectID = $filter[2];
            }
        }

        // when we are filtering by projectid, we want to return all standard tasks that have not already been added to this project
        if( !isNullOrBlank($projectID) ) {
            // Build our subquery to find all tasks that have already been created for this project

            $projectManager = Globals::$g->gManagerFactory->getManager('project');

            $querySpec['filters'][0][] = ['STANDARDTASKID', 'NOT IN SUBQUERY',
                                          "SELECT TASKID as STANDARDTASKID
                                            FROM TASK
                                            WHERE PROJECTKEY = " . $projectManager->GetRecordNoFromVid($projectID) . " AND cny#=" . GetMyCompany()];

            // Undo the parts of the filter related to the 'restrict' clause.
            $restrictedFields = ['PROJECTID'];
            foreach ($restrictedFields as $restrictedField) {
                $restrictedFieldIndex = false;
                foreach ($querySpec['filters'][0] as $qryKey => $qryFilter) {
                    if ($qryFilter[0] == $restrictedField) {
                        $restrictedFieldIndex = $qryKey;
                        break;
                    }
                }
                if ($restrictedFieldIndex !== false) {
                    array_splice($querySpec['filters'][0], $restrictedFieldIndex, 1);
                }
            }
        }

        return $querySpec;
    }
}

