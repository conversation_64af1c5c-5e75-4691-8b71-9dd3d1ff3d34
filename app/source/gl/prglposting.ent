<?php

/**
 *    FILE:            prglposting.ent
 *    AUTHOR:          <PERSON><PERSON><PERSON> <<EMAIL>>
 *    DESCRIPTION:     Entity to get GL postings for PR records
 *
 *    (C) 2020, Intacct Corporation, All Rights Reserved
 *
 */
$kSchemas['prglposting'] = [

    'children' => [
        'prentry' => [ 'fkey' => 'prentrykey', 'invfkey' => 'record#', 'join' => 'inner', 'table' => 'prentrymst'],
        'glentry' => [ 'fkey'     => 'glentrykey', 'invfkey' => 'record#', 'join' => 'inner', 'table' => 'glentry',
                       'children' =>
                           [ 'glaccount' => [ 'fkey'    => 'account#',
                                              'invfkey' => 'record#',
                                              'join'    => 'inner',
                                              'table'   => 'baseaccount'
                           ],
                             'glbatch'   => [ 'fkey'     => 'batch#', 'invfkey' => 'record#',
                                              'join'     => 'outer',
                                              'table'    => 'glbatchmst',
                                              'children' =>
                                                  [ 'basejournal' => [ 'fkey'     => 'journal#',
                                                                       'invfkey'  => 'record#',
                                                                       'join'     => 'inner',
                                                                       'table'    => 'basejournal'
                                                  ]
                                                  ]
                             ],
                             'location' => [
                                 'fkey' => 'location#',
                                 'invfkey' => 'record#',
                                 'table' => 'locationmst',
                                 'join' => 'outer'
                             ],
                           ],
        ],
    ],
    'object'   => [
        'BATCHNO',
        'GLBATCHNO',
        'ENTRYDATE',
        'SYMBOL',
        'LINENO',
        'ACCOUNTNO',
        'ACCOUNTTITLE',
        'TRXTYPE',
        'CURRENCY',
        'TRXAMOUNT',
        'AMOUNT',
        'GLENTRYKEY',
        'PRENTRYKEY',
        'PRRECORDKEY',
        'LOCATION',
        'LOCATIONNAME',
        'MEMO',
        'BASECURRENCY',
        'CBPRENTRYKEY'
    ],
    'schema' => [
        'BATCHNO' => 'glbatch.record#',
        'GLBATCHNO'      => 'glbatch.batch_no',
        'ENTRYDATE'       => 'glentry.entry_date',
        'SYMBOL'           => 'basejournal.symbol',
        'LINENO'           => 'glentry.line_no',
        'ACCOUNTNO'       => 'glaccount.acct_no',
        'ACCOUNTTITLE'    => 'glaccount.title',
        'TRXTYPE'          => 'glentry.tr_type',
        'CURRENCY'         => 'glentry.currency',
        'TRXAMOUNT'       => 'TRX_AMOUNT',
        'AMOUNT'           => 'amount',
        'GLENTRYKEY'       => 'glentry.record#',
        'PRENTRYKEY'       => 'prentrykey',
        'PRRECORDKEY' => 'prentry.recordkey',
        'LOCATION' => 'location.location_no',
        'LOCATIONNAME' => 'location.name',
        'MEMO' => 'glentry.description',
        'BASECURRENCY' => 'glentry.basecurr',
        'CBPRENTRYKEY' => 'cbprentrykey'
    ],

    'publish' => array(
        'LINENO',
        'TRXTYPE',
        'ENTRYDATE',
        'AMOUNT',
        'TRXAMOUNT',
        'ACCOUNTNO',
        'ACCOUNTTITLE',
        'CURRENCY',
        'GLBATCHNO',
        'SYMBOL',
        'GLENTRYKEY',
        'PRENTRYKEY',
        'PRRECORDKEY'
    ),
    'fieldinfo' => [
        [
            'path' => 'ACCOUNTNO',
            'fullname' => 'IA.GL_ACCOUNT',
            'desc' => 'IA.GL_ACCOUNT',
            'type' => [
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'glaccount'
            ],
            'id' => 1
        ],
        [
            'path' => 'ACCOUNTTITLE',
            'fullname' => 'IA.ACCOUNT_TITLE',
            'desc' => 'IA.ACCOUNT_TITLE',
            'type' => ['ptype' => 'text', 'type' => 'text'],
            'id' => 2,
        ],
        [
            'path' => 'AMOUNT',
            'fullname' => 'IA.AMOUNT',
            'desc' => 'IA.AMOUNT',
            'type' => [
                'type' => 'decimal',
                'ptype' => 'decimal',
            ],
            'id' => 3
        ],
        [
            'path' => 'GLBATCHNO',
            'fullname' => 'IA.BATCH_NUMBER',
            'desc' => 'IA.BATCH_NUMBER',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
            ],
            'id' => 4
        ],
        [
            'path' => 'BATCHTITLE',
            'fullname' => 'IA.BATCH_TITLE',
            'desc' => 'IA.BATCH_TITLE',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
            ],
            'id' => 5,
        ],
        [
            'path' => 'ENTRYDATE',
            'fullname' => 'IA.ENTRY_DATE',
            'desc' => 'IA.ENTRY_DATE',
            'type' => [
                'type' => 'date',
                'ptype' => 'date',
                'format' => $gDateFormat
            ],
            'id' => 6
        ],
        [
            'path' => 'LINENO',
            'fullname' => 'IA.LINE_NUMBER',
            'desc' => 'IA.LINE_NUMBER',
            'type' => [
                'type' => 'integer',
                'ptype' => 'integer',
                'format' => $gRecordNoFormat
            ],
            'id' => 7
        ],
        [
            'path' => 'TRXTYPE',
            'fullname' => 'IA.TR_TYPE',
            'desc' => 'IA.TR_TYPE',
            'type' => [
                'type' => 'integer',
                'ptype' => 'enum',
                'validvalues' => array('1', '-1'),
                '_validivalues' => array('1', '-1'),
                'validlabels' => array('IA.ONE', 'IA.MINUS_ONE'),
            ],
            'id' => 8
        ],
        [
            'path' => 'TRXAMOUNT',
            'fullname' => 'IA.TXN_AMOUNT',
            'desc' => 'IA.TXN_AMOUNT',
            'type' => [
                'type' => 'decimal',
                'ptype' => 'decimal',
                'format' => $gDecimalFormat
            ],
            'id' => 9
        ],
        [
            'path' => 'CURRENCY',
            'fullname' => 'IA.TXN_CURRENCY',
            'desc' => 'IA.TRANSACTION_CURRENCY',
            'type' => [
                'type' => 'ptr',
                'ptype' => 'ptr',
                'entity' => 'trxcurrencies',
            ],
            'id' => 10
        ],
        [
            'path' => 'GLENTRYKEY',
            'fullname' => 'IA.GL_ENTRY_KEY',
            'type' => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
            'desc' => 'IA.GL_ENTRY_KEY',

            'id' => 11,
        ],
        [
            'path' => 'PRENTRYKEY',
            'fullname' => 'IA.PR_ENTRY_KEY',
            'type' => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
            'desc' => 'IA.PR_ENTRY_KEY',

            'id' => 11,
        ],
        [
            'path' => 'PRRECORDKEY',
            'fullname' => 'IA.PR_RECORD_KEY',
            'type' => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
            'desc' => 'IA.PR_RECORD_KEY',
            'id' => 13,
        ],
        [
            'path' => 'BASECURRENCY',
            'fullname' => 'IA.BASE_CURRENCY',
            'type' => [
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 3
            ],
            'desc' => 'IA.BASE_CURRENCY',
            'readonly' => true,
            'id' => 14,
        ],
        [
            'path' => 'CBPRENTRYKEY',
            'fullname' => 'IA.CB_PR_ENTRY_KEY',
            'type' => [
                'ptype' => 'integer',
                'type' => 'integer',
            ],
            'desc' => 'IA.CB_PR_ENTRY_KEY',
            'id' => 15,
        ],
    ],
    'sqldomarkup' => false,
    'sqlmarkupfields' => array(),
    'api' => array(
        'GET_BY_GET' => true,
        'PERMISSION_MODULES' => array('gl'),
        'PERMISSION_READ' => 'INTERNAL',
        'PERMISSION_CREATE' => 'NONE',
        'PERMISSION_UPDATE' => 'NONE',
        'PERMISSION_DELETE' => 'NONE'
    ),
    'table'     => 'prglentryresolve',
    'printas'   => 'IA.LINK_PR_AND_GL',
    'pluralprintas'   => 'IA.LINK_PR_AND_GLS',
    'module'    => 'gl',
    'vid'       => 'PRENTRYKEY',
    'nosysview' => true,

];
