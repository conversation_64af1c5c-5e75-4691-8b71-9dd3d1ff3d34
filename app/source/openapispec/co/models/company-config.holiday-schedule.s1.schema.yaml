title: company-config-holiday-schedule
x-mappedTo: HOLIDAYSCHEDULE
type: object
description: List of holiday schedules.
properties:
  key:
    type: string
    description: System-assigned key for the holiday-schedule.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '23'
  id:
    type: string
    description: id.
    x-mappedTo: NAME
    example: Holiday Schedule 2023
  href:
    type: string
    description: Endpoint for the holiday-schedule.
    readOnly: true
    example: /objects/company-config/holiday-schedule/23
  entity:
    type: object
    x-mappedTo: entity
    x-object: company-config/entity
    properties:
      key:
        type: string
        description: Entity key.
        x-mappedTo: LOCATIONKEY
        example: '21'
      id:
        type: string
        description: Entity ID.
        x-mappedTo: LOCATION.LOCATIONID
        example: '253422'
      href:
        type: string
        description: URL for Entity.
        readOnly: true
        example: /objects/company-config/entity/21
  timesheetRule:
    type: object
    x-mappedTo: tsrules
    x-object: time/timesheet-rule
    properties:
      key:
        type: string
        description: TimeSheetKey.
        x-mappedTo: TSRULESKEY
        example: '21'
      id:
        type: string
        description: TimeSheetKey ID.(same as key)
        x-mappedTo: TSRULESKEY
        example: '21'
      name:
        type: string
        description: Timesheet rule.
        readOnly: true
        x-mappedTo: TSRULES.NAME
        example: firstAndOnlyRule
      href:
        type: string
        description: URL for timesheet rule.
        readOnly: true
        example: /objects/time/timesheet-rule/21
  holidays:
    type: array
    x-mappedTo: HOLIDAYS
    x-object: company-config/holiday
    items:
      $ref: company-config.holiday.s1.schema.yaml



