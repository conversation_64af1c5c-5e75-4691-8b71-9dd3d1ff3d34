<?php
/**
 * Class PartnerFieldMapSoDocViewer
 * Implements specifics for SO Document salesforce object
 *
 * <AUTHOR>
 * @copyright 2000-2023 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class PartnerFieldMapSoDocViewer extends PartnerFieldMapViewer
{

    /**
     * For the project object make the sync rule editable by default
     *
     * @return bool
     */
    public function isCustomMappingSyncRuleEditable() : bool
    {
        return true;
    }

}