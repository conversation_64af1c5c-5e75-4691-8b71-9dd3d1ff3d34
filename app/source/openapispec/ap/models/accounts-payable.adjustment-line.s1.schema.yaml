title: accounts-payable-adjustment-line
x-mappedTo: apadjustmentitem
x-ownedBy: accounts-payable/adjustment
type: object
description: Line items in an adjustment represent transactions captured in that adjustment.
properties:
  id:
    type: string
    description: Unique identifier for the adjustment line item. This value is the same as the `key` for this object.
    readOnly: true
    x-mappedTo: RECORDNO
    example: '1'
  key:
    type: string
    description: System-assigned key for the adjustment line item.
    x-mappedTo: RECORDNO
    example: '1'
  href:
    type: string
    description: Endpoint for the AP adjustment line.
    readOnly: true
    example: /objects/accounts-payable/adjustment-line/5296
  baseAmount:
    type: string
    format: decimal-precision-2
    description: For multi-currency companies, the transaction amount in the base currency.
    x-mappedTo: AMOUNT
    example: '100'
  txnAmount:
    type: string
    format: decimal-precision-2
    description: The transaction amount for the adjustment line item in the currency specified for the transaction.
    x-mappedTo: TRX_AMOUNT
    example: '100'
  totalTxnAmount:
    type: string
    format: decimal-precision-2
    description: Total transaction amount, including tax, for tax-inclusive adjustments.
    x-mappedTo: TOTALTRXAMOUNT
    example: '100.00'
  memo:
    type: string
    description: Additional notes or comments about the adjustment line item for reference.
    x-mappedTo: ENTRYDESCRIPTION
    example: 'Service adjustments'
  lineNumber:
    type: string
    description: The specific line number assigned to the adjustment line item for identification.
    x-mappedTo: LINE_NO
    readOnly: true
    example: '1'
  hasForm1099:
    type: string
    description: |
      Set to true to add the line item amount to the vendor 1099 form.
    x-mappedTo: FORM1099
    example: 'false'
  form1099:
    type: object
    description: Details related to the 1099 form for the line item.
    title: Form1099
    properties:
      type:
        type: string
        description: |
          The type of the 1099 form applicable to the line item.
        x-mappedTo: FORM1099TYPE
        example: NEC
        nullable: true
      box:
        type: string
        description: |
          The specific box number on the 1099 form where the amount for this line item is reported. Each type of the 1099 form has different box numbers for reporting various types of income. 
        x-mappedTo: FORM1099BOX
        example: 15A
        nullable: true
  adjustmentType:
    type: string
    description: Adjustment type for the line item.
    x-mappedTo: RECORDTYPE
    readOnly: true
    example: 'pa'
  glAccount:
    $ref: ../../common/references/gl-account-ref.s1.schema.yaml
  overrideOffsetGLAccount:
    type: object
    description: The general ledger account where the system posts offsets.
    x-object: general-ledger/account
    x-mappedTo: glaccount
    title: overriddenOffsetAccount
    properties:
      key:
        type: string
        description: System-assigned key for the offset account number.
        x-mappedTo: OFFSETACCOUNTKEY
        example: '384'
      id:
        type: string
        description: Unique identifier for the offset account number.
        x-mappedTo: OFFSETGLACCOUNTNO
        example: '1234.36'
      name:
        type: string
        description: The name of the offset account used in the transaction.
        x-mappedTo: OFFSETGLACCOUNTTITLE
        readOnly: true
        example: AP ACCOUNT
      href:
        type: string
        description: URL endpoint for the offset account.
        readOnly: true
        example: /objects/general-ledger/account/384
  accountLabel:
    type: object
    description: Meaningful name of the account.
    x-object: accounts-payable/account-label
    x-mappedTo: accountlabel
    title: accountLabel
    properties:
      key:
        type: string
        description: System-assigned key for the account label.
        x-mappedTo: ACCOUNTLABELKEY
        example: '14'
        nullable: true
      id:
        type: string
        description: Unique identifier for the account label.
        x-mappedTo: ACCOUNTLABEL
        example: 'Accounting Fees'
        nullable: true
      href:
        type: string
        readOnly: true
        example: /objects/accounts-payable/account-label/14
  dimensions:
    type: object
    allOf:
      - $ref: ../../common/references/dimension-ref.s1.schema.yaml
      - type: object
        properties:
          department:
            type: object
            description: Department to assign the expense to.
            x-mappedTo: department
            x-object: company-config/department
            properties:
              key:
                type: string
                description: System-assigned key for the department.
                x-mappedTo: DEPT#
                example: '1'
                nullable: true
              id:
                type: string
                description: Unique identifier for the department. This value is the same as the `key` for this object.
                x-mappedTo: DEPARTMENTID
                example: '1'
                nullable: true
              name:
                type: string
                description: The name of the department assigned to the transaction.
                x-mappedTo: DEPARTMENTNAME
                readOnly: true
                example: 'Accounting'
                nullable: true
              href:
                type: string
                description: URL endpoint of the department.
                readOnly: true
                example: /objects/company-config/department/1
          location:
            type: object
            description: Location to assign the expense to.
            x-mappedTo: location
            x-object: company-config/location
            properties:
              key:
                type: string
                description: System-assigned key for the location.
                x-mappedTo: LOCATION#
                example: '1'
                nullable: true
              id:
                type: string
                description: Unique identifier for the location. This value is the same as the `key` for this object.
                x-mappedTo: LOCATIONID
                example: '1'
                nullable: true
              name:
                type: string
                description: The name of the location assigned to the transaction.
                x-mappedTo: LOCATIONNAME
                readOnly: true
                example: India
                nullable: true
              href:
                type: string
                description: URL endpoint of the location.
                readOnly: true
                example: /objects/company-config/location/1
  currency:
    type: object
    description: Currency details for multi-currency companies.
    title: currency
    properties:
      baseCurrency:
        type: string
        description: The base currency in which the line item is denominated.
        x-mappedTo: BASECURR
        readOnly: true
        example: USD
      txnCurrency:
        type: string
        description: The currency used for the transaction.
        x-mappedTo: CURRENCY
        readOnly: true
        example: USD
      exchangeRate:
        type: object
        description: Exchange rate details used to calculate the base amount.
        title: exchangeRate
        properties:
          date:
            type: string
            format: date
            example: '2021-01-23'
            description: Exchange rate date for this transaction. Can be the current date, the date the transaction was issued, or the date the transaction will be paid.
            x-mappedTo: EXCH_RATE_DATE
            x-mutable: false
            nullable: true
          rate:
            type: string
            description: Exchange rate used to calculate the base amount from the transaction amount.
            x-mappedTo: EXCHANGE_RATE
            example: '1.0789'
            x-mutable: false
          typeId:
            type: string
            description: The type of exchange rate used to calculate the base amount from the transaction amount.
            x-mappedTo: EXCH_RATE_TYPE_ID
            x-mutable: false
            example: 'Intacct Daily Rate'
            nullable: true
  paymentInformation:
    type: object
    description: Payment information for the adjustment line item.
    title: paymentInformation
    properties:
      totalBaseAmountPaid:
        type: string
        format: decimal-precision-2
        description: The total amount paid in the base currency for the line item.
        x-mappedTo: TOTALPAID
        readOnly: true
        example: '100'
      txnTotalPaid:
        type: string
        format: decimal-precision-2
        description: The total amount paid for the line item in the transaction currency.
        x-mappedTo: TRX_TOTALPAID
        readOnly: true
        example: '200'
      totalBaseAmountSelected:
        type: string
        format: decimal-precision-2
        description: The total amount selected in the base currency for the line item.
        x-mappedTo: TOTALSELECTED
        readOnly: true
        example: '100'
      txnTotalSelected:
        type: string
        format: decimal-precision-2
        description: The total amount selected in the transaction currency for the line item.
        x-mappedTo: TRX_TOTALSELECTED
        readOnly: true
        example: '100'
  taxEntries:
    type: array
    description: Tax Entries of the AP adjustment
    x-mappedTo: TAXENTRIES
    x-object: accounts-payable/adjustment-tax-entry
    items:
      $ref: accounts-payable.adjustment-tax-entry.s1.schema.yaml
  apAdjustment:
    title: ap-adjustmentref
    description: Header level details for the adjustment's line items.
    type: object
    x-mappedTo: apadjustment
    x-object: accounts-payable/adjustment
    properties:
      id:
        type: string
        description: Unique identifier for the adjustment. This value is the same as the `key` for this object.
        x-mappedTo: RECORDKEY
        readOnly: true
        example: '1'
      key:
        type: string
        description: System-assigned key for the adjustment.
        x-mappedTo: RECORDKEY
        example: '1'
      href:
        type: string
        description: URL endpoint of the adjustment.
        readOnly: true
        example: /objects/accounts-payable/adjustment/24
    readOnly: true
  audit:
    $ref: ../../common/models/audit.s1.schema.yaml
