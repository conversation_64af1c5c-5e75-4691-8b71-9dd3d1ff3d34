<?
    /**
    *    FILE:        pickinglist.rpt
    *    AUTHOR:        <PERSON><PERSON><PERSON>
    *    DESCRIPTION:    ent file item activity filters
    *
    *    (C) 2000, Intacct Corporation, All Rights Reserved
    *
    *    This document contains trade secret data that belongs to Intacct
    *    Corporation and is protected by the copyright laws.  Information
    *    herein may not be used, copied or disclosed in whole or in part
    *    without prior written consent from Intacct Corporation.
*/
    $fromDocID = array (
        'fullname' => 'IA.FROM_DOCUMENT_ID',
        'assist' => 'report',
        'type' => array ( 
            'ptype' => 'ptr',
            'entity' => 'sodocument',                                    
            'type' => 'ptr',
            'maxlength' => 20,
            'format' => $gEntityIDFormat
        ),
        'desc' => 'IA.FROMDOCID',
        'path' => 'FROMDOCID',
        'noedit' => true,
        'nonew' => true,
        'noview' => true,
        'renameable' => 1,
        'labelcssclass' => 'rf_grplabel_cell',
        'onchange' => "AutoPopulateToFieldUsingFrom(document.main.elements['_obj__FROMDOCID'],document.main.elements['_obj__TODOCID']);",
    );
    $toDocID = array (
        'fullname' => 'IA.TO_DOCUMENT_ID',
        'assist' => 'report',
        'type' => array ( 
            'ptype' => 'ptr',
            'entity' => 'sodocument',                                    
            'type' => 'ptr',
            'maxlength' => 20,
            'format' => $gEntityIDFormat
        ),
        'desc' => 'IA.TODOCID',
        'path' => 'TODOCID',
        'noedit' => true,
        'nonew' => true,
        'noview' => true,
        'renameable' => 1,
        'labelcssclass' => 'rf_grplabel_cell',
        'onchange' => "AutoPopulateToFieldUsingFrom(document.main.elements['_obj__FROMDOCID'],document.main.elements['_obj__TODOCID']);",
    );

    $kSchemas['pickinglist'] = array (
        'schema' => array (
            array (
                'FROMDOCID' => 'fromdocid',
                'TODOCID' => 'todocid', 
                'FROMWAREHOUSEID' => 'fromwarehouseid',
                'TOWAREHOUSEID' => 'towarehouseid', 
                )
        ),
        'fieldgroup' => array (
            'FROMTODOC' => array(    'layout' => 'landscape',
                    'fields'=>array($fromDocID,
                    $toDocID,

                    )
            ),
            'FROMTOWH' => array(    'layout' => 'landscape',
                    'fields'=>array($gInvRptCtlFromWarehouseID,
                            $gInvRptCtlToWarehouseID,
                        )
            ),
        ),
        'fieldinfo' => array ( 
                'lines' => array (
                    array (
                        //'title' => _('Filters'),
                        'fields' => array (
                                    array(   'type' => array('type' => 'fieldgroup'), 
                                    'path' => 'FROMTODOC'),
                                    array(   'type' => array('type' => 'fieldgroup'), 
                                    'path' => 'FROMTOWH'),
                                
                        ),
                    ),
                ),
        ),
        'controls' => $gInvRptControls,
        'layout' => 'frame',
        'layoutproperties' => $gInvRptLayoutProperties,
        'popupfilterflds' => array('FROMDOCID','TODOCID','FROMWAREHOUSEID', 'TOWAREHOUSEID'),
        'xsl_file' => 'pickinglist',
        'printas' => 'IA.PICKING_LIST',
        'module' => 'inv',
        'helpfile' => 'Run_a_Picking_List_Report'
    );

