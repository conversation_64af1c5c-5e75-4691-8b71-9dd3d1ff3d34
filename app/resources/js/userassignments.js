/**
 * Overwrite getFilters function to ignore the fields that do not have filters
 * Return non-excluded filter values in [path, operator, value] format.
 *
 * @returns {Array}
 */
Grid.prototype.getFilters = function () {

    var filters = [];
    if (this.children) {
        for (var i = 0, l = this.children.length; i < l; i++) {
            var column = this.children[i];
            if (column.heading && !column.hidden) {
                var field = _getHeaderFieldWithProperty(column.heading, 'gridHeading', 'searchPath');
                if (field === null) {
                    continue;
                }
                // to update the field with the latest data from html
                field.getDataFromHTML();
                var value = field.value;
                if (value && typeof value !== 'object' && !field.searchExclude) {
                    // Provide a split for pickers.
                    if (field.type && field.type.ptype === 'ptr') {
                        value = value.split('--')[0];
                    }
                    var operator = field.searchOperator ? field.searchOperator : '=';
                    var filterComponentAPI = this.filterComponentProps;
                    // override the operator variable with the supported values by the filter component
                    if (filterComponentAPI && filterComponentAPI.isInitialized) {
                        operator = field.uiControl === 'ControlText' ? 'contains' : 'equals';
                    }
                    filters.push([field.searchPath, operator, value]);
                }
            }
        }
    }
    return filters;
}

function AssignInstancesGridField() {
}

AssignInstancesGridField.inheritsFrom(Field);

AssignInstancesGridField.prototype.draw = function() {
    if (!window.view.readonly) {
        var  current_instance = this.parentValue;
        var grid = window.editor.findComponents('SELECT_INSTANCES', 'Grid');

        var fields = grid[0].findComponents(this.path);
        var line_no = fields.findIndex(
            function(elem) {
                return elem.parentValue['SELECT_INSTANCESID'] == current_instance['SELECT_INSTANCESID'];
            });

        if (current_instance['SELECTED_INSTANCE'] == 'false') {
            fields[line_no].updateProperty('readonly', true);
        }
    }
    return Field.prototype.draw.call(this);
}

/***
 * Toggle all the checkboxes from p_field1
 * If p_field2 was provided then:
 *   If p_field1 is unchecked then p_field2 will be set as readonly and will be uncheck, otherwise p_field2 will not be readonly
 *
 * @param {string} p_grid_name
 * @param {Event} p_event click event
 * @param {string} p_field1
 * @param {string} p_field2
 */
function gridCheckBoxHandler(p_grid_name, p_event, p_field1, p_field2) {
    if (p_event) {
        var grid = editor.findComponents(p_grid_name, 'Grid');
        if (grid && grid[0]) {
            grid = grid[0];
            var start = grid.getPageStartLine();
            var end = grid.getPageEndLine();
            for (var ix = start; ix < end; ix++) {
                var field1 = grid.findLineComponent(p_field1, ix);
                if (!field1.readonly) {
                    var value = (p_event.checked) ? 'true' : 'false';
                    field1.setValue(value);
                    if (p_field2 != undefined) {
                        var field2 = grid.findLineComponent(p_field2, ix);

                        if (!p_event.checked) {
                            field2.setValue('false');
                        }
                        field2.updateProperty('readonly', !p_event.checked);
                        field2.redraw();
                    }
                }
            }
        }
    }
}

/***
 * If p_column is unchecked then p_field will be set readonly and unchecked, otherwise p_field will not be readonly
 *
 * @param {string} p_grid_name
 * @param {Event} p_event click event
 * @param {string} p_field
 * @param {Event} p_column
 */
function toggleCheckbox(p_grid_name, p_event, p_field, p_column) {
    if (p_event) {
        var grid = editor.findComponents(p_grid_name, 'Grid');
        if (grid && grid[0]) {
            grid = grid[0];
            var field = grid.findLineComponent(p_field, p_column.context[2]);
            if (p_event.checked) {
                field.updateProperty('readonly', false);
                field.redraw();
            } else {
                field.updateProperty('readonly', true);
                field.setValue('false');
                field.redraw();
            }
        }
    }
}
