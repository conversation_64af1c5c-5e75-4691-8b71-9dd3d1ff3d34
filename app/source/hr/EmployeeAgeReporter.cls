<?php
/**
 * EmployeeAge entity Reporter
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2015 Intacct Corporation, All Rights Reserved
 */

import('APARAgeReporter');
require_once 'util.inc';

/**
 * Reporter class for the EmployeeAge entity
 */
class EmployeeAgeReporter extends APARAgeReporter
{
    /**
     * @param array $_params
     */
    function __construct($_params = array())
    {
        $_params = INTACCTarray_merge(
            array('report' => 'employee_aging', '2stage' => false, 'reportslide' => true),
            $_params
        );
        parent::__construct($_params);
        $this->params['isgraph'] = '0';
        $this->params['object'] = 'employee';
        $this->params['re_type'] = 'REPORT';
        $this->params['2stage'] = false;
        
        // IGC fields 'Reporting Currency' and 'Convert Currency from' are missing for this report
        // but 'Convert Currency from' is mandatory to run the report from root
        // so defaulting to 'base' all the time
        $this->params['REVALUATIONTYPE'] = 'base';
    }

    /**
     * createDataXML
     *
     * @param mixed $dataxml resulting data xml representation
     *
     * @return bool true on success, false on error
     */
    function createDataXML(&$dataxml)
    {
        // Convert rpt variable to old variable names
        $this->params['sumdet'] = $this->params['REPORTTYPE'];

        // parse employee to and from id
        $from_emid = $this->params['FROM'];
        $to_emid = $this->params['TO'];

        list($from_empid) = explode('--', $from_emid);
        list($to_empid) = explode('--', $to_emid);

        $this->params['from_id'] = $from_empid;
        $this->params['to_id'] = $to_empid;

        $this->params['showzeros'] = $this->params['SHOWEMPLOYEESWITHZEROBALANCE'];
        $this->params['sortby'] = $this->params['SORTBY'];

        return parent::CreateDataXML($dataxml);
    }
}
