<?
/**
 * Interface for data exporter.
 */
interface Pt_ISpreadsheetWriter
{

    /**
     * Append String value.
     *
     * @param string $value
     */
    public function append($value);

    /**
     * Append Date value.
     *
     * @param string $value
     */
    public function appendDate($value);

    /**
     * Append Date value.
     *
     * @param string $value
     */
    public function appendGMTTimestamp($value);

    /**
     * Append Number value.
     *
     * @param int|float $value
     */
    public function appendNumber($value);

    /**
     * Proceed to next row.
     */
    public function nextRow();

    /**
     * Get writer's buffer
     *
     * @return string[]
     */
    public function getBuffer();

}
