<?php

/**
 * Base validator class for payment processing.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 Intacct Corporation All, Rights Reserved
 */
class BasePaymentValidator
{
    /** @var string[] $modulePreferences Module preferences */
    protected $modulePreferences = null;
    /** @var bool $isAPICall Flag to show whether the call is from API or UI, by default true */
    protected $isAPICall = true;
    /**
     * @var bool $isCreditOnlyPayment
     */
    private bool $isCreditOnlyPayment = false;

    /** @var string $paymethod */
    private $paymethod;

    /** @var string $transactiontype */
    private $transactiontype;

    /** @var bool $shouldWarn */
    private static $shouldWarn = false;

    /**
     * @return string[]
     */
    public function getModulePreferences()
    {
        return $this->modulePreferences;
    }

    /**
     * @param string[] $modulePreferences
     */
    public function setModulePreferences($modulePreferences)
    {
        $this->modulePreferences = $modulePreferences;
    }

    /**
     * @return bool
     */
    public function getIsAPICall()
    {
        return $this->isAPICall;
    }

    /**
     * @param bool $isAPICall
     */
    public function setIsAPICall($isAPICall)
    {
        $this->isAPICall = $isAPICall;
    }

    /**
     * @return string
     */
    public function getPayMethod()
    {
        return $this->paymethod;
    }

    /**
     * @param string $paymethod
     */
    public function setPayMethod($paymethod)
    {
        $this->paymethod = $paymethod;
    }

    /**
     * @param string $transactiontype
     */
    public function setTransactionType($transactiontype)
    {
        $this->transactiontype = $transactiontype;
    }

    /**
     * @return string
     */
    public function getTransactionType()
    {
        return $this->transactiontype;
    }

    /**
     * Retrieves the value indicating whether the payment is credit only.
     *
     * @return bool
     */
    public function getIsCreditOnlyPayment(): bool
    {
        return $this->isCreditOnlyPayment;
    }

    /**
     * Sets the value indicating whether the payment is credit only.
     *
     * @param bool $isCreditOnlyPayment
     */
    public function setIsCreditOnlyPayment(bool $isCreditOnlyPayment): void
    {
        $this->isCreditOnlyPayment = $isCreditOnlyPayment;
    }

    /**
     * Method to validate the payment request.
     *
     * @param PaymentRequest $request
     *
     * @return bool
     *
     * @throws IAException
     */
    public function validate(PaymentRequest $request)
    {
        try {
            // validate the module preferences
            if (empty($this->modulePreferences)) {
                throw new IAException("Module preference is not set for validator, please set the module preference and 
            try again", "BL01001973");
            }
            //set the value to creditonlypayment variable based on the paymentdto
            $this->setIsCreditOnlyPayment($request->getPaymentDTO()->isCreditOnlyPymtRequest());
            // Validate the input
            $ok = $this->validateInput($request);
            // Validate the payment detail input
            $ok = $ok && $this->validatePaymentDetailsInput($request);
        } catch (IAException $e) {
            $ok = false;
            Globals::$g->gErr->addIAErrorInstance($e->getIAError());
            //TODO:i18N-SL-Error-Message (need to discuss with file owner)
        }
        return $ok;
    }

    /**
     * Method to validate the input details of the payment request.
     *
     * @param PaymentRequest $request
     *
     * @return bool
     *
     * @throws IAException
     */
    protected function validateInput(PaymentRequest $request)
    {
        $ok = true;
        $paymentDTO = $request->getPaymentDTO();

        // validate the payment action
        $ok = $ok && $this->validateAction($paymentDTO->getAction());

        // Validate the financial account if there is any non-credit payment
        if ($ok && !$request->getPaymentDTO()->isCreditOnlyPymtRequest()) {
            $ok = $this->validateInputForNonCreditPayment($request);
        }
        $ok = $ok && $this->validateBaseCurrency($paymentDTO);

        $ok = $ok && $this->validateInputCurrency($paymentDTO);

        $ok = $ok && $this->validateExchangeDetails($paymentDTO);
        return $ok;
    }

    /**
     * @param PaymentRequest $request
     *
     * @return bool
     */
    protected function validateInputForNonCreditPayment(PaymentRequest $request): bool
    {
        return $this->validateFinancialAccount($request);
    }


    /**
     * Method to validate the payment detail input.
     *
     * @param PaymentRequest $request
     *
     * @return bool
     *
     * @throws IAException
     */
    protected function validatePaymentDetailsInput(PaymentRequest $request)
    {
        $ok = true;
        $paymentDTO = $request->getPaymentDTO();
        $paymentDetails = $paymentDTO->getPaymentDetailList();
        if (!empty($paymentDetails)) {
            // Variable to store the currency
            $billCurrency = '';
            $financialAccount = $request->getFinancialAccount();
            $baseCurr = $paymentDTO->getBaseCurrency() ?: $request->getBaseCurrency();
            // If the bank currency empty then use the base currency
            if (empty($financialAccount['CURRENCY'])) {
                $financialAccount['CURRENCY'] = $baseCurr;
                $request->setFinancialAccount($financialAccount);
            }
            $bankCurrency = $financialAccount['CURRENCY'];

            $this->validateBankCurrency($paymentDTO->isCreditOnlyPymtRequest(), $paymentDTO->getCurrency(), $bankCurrency);

            $trxLocationItemArr = [];
            $trxHeaderValidKeys = [];
            $paymentMap = [];
            $entryPaymentMap = [];
            $totalEntryBaseAmount = 0;
            $linePaymentCounter = 0;
            // dummy field to validate the discount type map detail in pymdetail
            $discountTypeMap = [];
            foreach ($paymentDetails as $paymentDetail) {
                if (!($paymentDetail instanceof PaymentDetailDTO)) {
                    $ok = false;
                    break;
                }

                $ok = $ok && $this->validatePaymentDetailEntry($paymentDetail);

                $ok = $ok && $this->validateTotalDueForInlineKey($paymentDetail, $request);

                $ok = $ok && $this->validatePaymentDetail($request, $paymentDetail);

                $ok = $ok && $this->validateAndSetDiscountDetails($request, $paymentDetail, $discountTypeMap);

                if(!$ok) {
                    break;
                }
                // Get the transation key
                $transactionKey = $paymentDetail->getTransactionKey();
                $entryKey = $paymentDetail->getTransactionEntryKey();

                // Validate the transaction
                $transaction = $request->getTransactions()[$transactionKey];

                if (empty($transaction)) {
                    $errorMsg = "Oops, we can't find this transaction; enter a valid " . $transactionKey . " key, then try again.";
                    $errorCode = "BL01001973";
                    throw new IAException($errorMsg, $errorCode);
                }

                if ($request->getPaymentDTO()->isMultiEntityPayment()) {
                    $entityID = $transaction->getCustomFieldValue($this->getEntityFieldPath());
                } else {
                    $entityID = $this->getEntityID($request);
                }

                if ($paymentDetail->getPaymentType() != SubLedgerTxnManager::INLINE_PYMT) {
                    if (!isset($trxHeaderValidKeys[$transactionKey])) {
                        $ok = $ok && $this->validateTransaction($transaction, $transactionKey, $entityID);
                    }
                    // Validate the entry
                    $ok = $ok && $this->validateEntries($transaction, $entryKey, $paymentDTO->getIsUpdatePayment());
                }
                // If there is validation error, break the loop
                if (!$ok) {
                    break;
                }
                // Validate payment amount
                // $ok = $ok && $this->validatePaymentAmount($paymentDetail, $transaction->getTrxTotalDue());
                // validate the header level of the transaction
                if (!isset($trxHeaderValidKeys[$transactionKey])) {
                    // Validate currency
                    $ok = $ok && $this->validateCurrency($request, $transactionKey, $bankCurrency, $baseCurr,
                            $billCurrency);
                    // Validate tha payment date
                    // validate the payment date if there is payment amount
                    $billdate = $transaction->getWhenCreated();
                    $paymentDate = $this->getTxnPaymentDate($request);
                    $ok = $ok && $this->validatePaymentDate($paymentDate, $billdate, $this->getShouldWarn());
                    // Validate the period close date
                    $ok = $ok && $this->validatePeriodClose($paymentDate, $request->getPeriodOpenDate(), true);
                    // Validate the duplicate document number
                    $ok = $ok && $this->validateDuplicateDocument($paymentDTO->getDocNumber(),
                            $financialAccount['BANKACCOUNT.NAME'] ?? '', $paymentDTO->isDuplicateDocNumber());
                }
                // Validate the credits
                $ok = $ok && $this->validateCredits(
                        $paymentDetail, $request, $transactionKey,$billCurrency, $entityID
                    );
                // get All the line item Location
                $ok = $ok && $this->getAllLineItemsLocationIDs(
                        $request, $transactionKey, $trxLocationItemArr, $trxHeaderValidKeys
                    );
                $ok = $ok && $this->validatePaymentDetailKeys($paymentDetail);
                // Ignore the payment amount validation for update cases as there won't be any change in the amount
                if ($paymentDTO->getIsUpdatePayment() == false && $paymentDTO->getIsBalanceForward() == false) {
                    // Validate payment amount
                    $ok = $ok && $this->validateTotalPaymentAmount(
                            $paymentDetail, $transaction,$paymentMap,$entryPaymentMap
                        );
                    // Validate the base payment amount
                    $ok = $ok && $this->validateBasePaymentAmount(
                        $paymentDTO, $paymentDetail, $transaction, $totalEntryBaseAmount,
                        $linePaymentCounter, $bankCurrency, $baseCurr
                        );
                }
                if (!$ok) {
                    break;
                }
                $trxHeaderValidKeys[$transactionKey] = $transactionKey;
            }
            // Ignore the payment amount validation for update cases as there won't be any change in the amount
            if ($ok && $paymentDTO->getIsUpdatePayment() == false && $paymentDTO->getIsBalanceForward() == false) {
                // Validate the base payment amount
                $totalBaseAmountToPay = $paymentDTO->getCustomFieldValue('AMOUNTTOPAY');
                $ok = $ok && $this->validateTotalBaseAmtAndEntryBaseAmt(
                        sizeof($paymentDetails), $totalEntryBaseAmount, $totalBaseAmountToPay, $linePaymentCounter
                    );
            }
            // validate the line items location ids
            $ok = $ok && $this->validateLocationIds($request, $trxLocationItemArr);
        } // for empty payment detail array, throw error
        else {
            $ok = false;
        }
        return $ok;
    }

    /**
     * @param PaymentRequest   $paymentRequest
     * @param PaymentDetailDTO $paymentDetail
     *
     * @return bool
     */
    protected function validatePaymentDetail(
        /** @noinspection PhpUnusedParameterInspection */ PaymentRequest $paymentRequest,
        /** @noinspection PhpUnusedParameterInspection */ PaymentDetailDTO $paymentDetail
    ): bool
    {
        return true;
    }

    /**
     * @param PaymentRequest $request
     * @param array          $trxLocationItemArr
     *
     * @return bool
     */
    protected function validateLocationIds(
        /** @noinspection PhpUnusedParameterInspection */ PaymentRequest $request,
        /** @noinspection PhpUnusedParameterInspection */ array $trxLocationItemArr
    ): bool
    {
        return true;
    }

    /**
     * @param PaymentRequest $request
     * @param string         $transactionKey
     * @param array          $trxLocationItemArr
     * @param array          $trxHeaderValidKeys
     *
     * @return bool
     */
    protected function getAllLineItemsLocationIDs(
        /** @noinspection PhpUnusedParameterInspection */ PaymentRequest $request,
        /** @noinspection PhpUnusedParameterInspection */ $transactionKey,
        /** @noinspection PhpUnusedParameterInspection */ array &$trxLocationItemArr,
        /** @noinspection PhpUnusedParameterInspection */ array &$trxHeaderValidKeys
    ): bool
    {
        return true;
    }

    /**
     * Method to validate the bank account.
     *
     * @param PaymentRequest $request
     * @return bool
     */
    protected function validateFinancialAccount(PaymentRequest $request)
    {
        $ok = true;
        $financialAccount = $request->getFinancialAccount();
        $bankId = $request->getPaymentDTO()->getFinancialEntity();
        // Make sure the bank is valid
        if (empty($bankId) || empty($financialAccount)) {
            $ok = false;
            self::throwInvalidObjectError(
                'BL01001973', 'bank account', $bankId, '', '', 'payment'
            );
        } else {
            // Validate the GL offset account key
            if (empty($financialAccount['GLACCOUNTKEY'])) {
                $errMsg = "Link the bank account " . $bankId . " to the GL account, then try again.";
                epp($errMsg);
                Globals::$g->gErr->addIAError('SL-0014', __FILE__ . ":" . __LINE__, $errMsg, ['BANK_ID' => $bankId]);
                $ok = false;
            }
        }

        return $ok;
    }

    /**
     * Method to check for restricted location entries
     *
     * @param PaymentDetailDTO $paymentDetail contains the payment object in consideration
     * @param PRRecordDTO $ppRecordDTO contains the prrecord object
     * @param int|string $entryKey contains the prentry key
     * @param array $restrictedLocationKeys contains the list of locations that is restricted to the bank
     * @param string $errorTag contains the error that needs to be displayed
     *
     * @return bool
     */
    public static function checkRestrictedLocationEntries($paymentDetail, $ppRecordDTO, $entryKey, $restrictedLocationKeys, $errorTag)
    {
        $ok = true;
        if ($paymentDetail instanceof PaymentDetailDTO) {
            $entryList = [];
            if ($ppRecordDTO instanceof PRRecordDTO) {
                $entryList = $ppRecordDTO->getEntryList();
            }
            foreach ($entryList as $entry) {
                if ($entry instanceof PREntryDTO) {
                    $checkEntryKey = isset($entryKey) && $entryKey != ''
                        ? $entryKey == $entry->getRecordNo()
                        : true;
                    if ($checkEntryKey && !in_array($entry->getLocationKey(), $restrictedLocationKeys)) {
                        $description = "Select an authorized " . $errorTag . " for this location transaction, then try again";
                        $recordId = $ppRecordDTO->getRecordId();
                        $lineNo = $entry->getLineNo();
                        $trxAmount = $entry->getTrxAmount();
                        $locationId  = $entry->getLocationId();
                        $msg = "Transaction#: " . $recordId . ', Line#: ' . $lineNo . ', Amount: ' . $trxAmount . ', Location: ' . $locationId;
                        Globals::$g->gErr->addIAError(
                            'SL-0416', __FILE__ . ":" . __LINE__,
                            $description , ['ERROR_TAG' =>$errorTag],
                            "", [],
                            $msg,
                            ['PPRECORDDTO_GETRECORDID'=> $recordId,
                                'ENTRY_GETLINENO' => $lineNo,
                                'ENTRY_GETTRXAMOUNT' => $trxAmount,
                                'ENTRY_GETLOCATIONID' => $locationId
                            ]
                        );
                        $ok = false;
                        break;
                    }
                }
            }
        }

        return $ok;
    }

    /**
     * Method to validate the transaction.
     *
     * @param PRRecordDTO $transaction
     * @param string $recordKey
     * @param string $entityID
     *
     * @return bool
     * @throws IAException
     */
    protected function validateTransaction(PRRecordDTO $transaction, $recordKey, $entityID)
    {
        $ok = true;
        // if there is no transaction related to the record key, throw error
        // also throw error if the transaction vendor id is different from the payment vendor
        if (!isset($transaction) || empty($transaction) ||
            strcmp($entityID, $transaction->getCustomFieldValue($this->getEntityFieldPath())) !== 0) {
            $ok = false;
            $errorMsg = "Invalid record key " . $recordKey;
            $placeholders['RECORDKEY'] = $recordKey;
            Globals::$g->gErr->addIAError('SL-1278', __FILE__.'.'.__LINE__, $errorMsg, $placeholders);
        }


        // Validate the Draft Transaction
        $ok = $ok && $this->validateDraftTransaction($transaction);

        // Validate the Approval Transaction
        $ok = $ok && $this->validateApprovalTransaction($transaction);

        // Validate the On Hold Transaction
        $ok = $ok && $this->validateTransactionOnHold($transaction);

        return $ok;
    }
    /**
     * Validate the payment detail amount with the due amount on the bill/line.
     *
     * @param PaymentDetailDTO $pymtDetail
     * @param string|float $totalDue
     * @param string|int|float $totalPaymentAmount
     *
     * @return bool
     * @throws IAException
     */
    private function validatePaymentAmount(PaymentDetailDTO $pymtDetail, $totalDue, &$totalPaymentAmount = 0)
    {
        $ok = true;
        //TODO: Need to remove $is30ApiRoutingEnable after 2.1 to 3.0 final code commit
        $is30ApiRoutingEnable = $this->isRoutingEnable();
        // if there is no due on the bill, return the error
        if ($pymtDetail->getPaymentType() != SubLedgerTxnManager::INLINE_PYMT && $totalDue <= 0) {
            $ok = false;
            // none of the payment amount present is valid in the request, throw error
            if (!empty($pymtDetail->getRecordKey())) {
                $msg = "There is no due on the " . $this->getTransactionType() . " " . $pymtDetail->getRecordKey();
            } else if (!empty($pymtDetail->getPostedAdjustmentKey())) {
                $msg = "There is no due on the adjustment " . $pymtDetail->getPostedAdjustmentKey();
            }
        } else {
            // Make sure we have a valid payment amounts.
            // This may will be a little redundant with BasePRRecord validations but this will avoid unnecessary processing
            $trxAmount = $pymtDetail->getTrxPaymentAmount();
            $advanceAmount = $pymtDetail->getTrxPostedAdvanceAmount();
            $overpaymentAmount = $pymtDetail->getTrxPostedOverpaymentAmount();
            $inlineAmount = $pymtDetail->getTrxInlineAmount();
            $adjustmtAmount = $pymtDetail->getTrxAdjustmentAmount();
            $negaBillInvAmount = $pymtDetail->getTrxNegativeBillInvAmount();
            $creditToApply = $pymtDetail->getCreditToApply();
            // Any one of the payment amount should be present in the request otherwise throw error
            if ((empty($trxAmount) || !is_numeric($trxAmount) || ibccomp($trxAmount, 0) <= 0) &&
                (empty($advanceAmount) || !is_numeric($advanceAmount) || ibccomp($advanceAmount, 0) <= 0) &&
                (empty($overpaymentAmount) || !is_numeric($overpaymentAmount) || ibccomp($overpaymentAmount, 0) <= 0) &&
                (empty($inlineAmount) || !is_numeric($inlineAmount) || ibccomp($inlineAmount, 0) <= 0) &&
                (empty($adjustmtAmount) || !is_numeric($adjustmtAmount) || ibccomp($adjustmtAmount, 0) <= 0) &&
                (empty($negaBillInvAmount) || !is_numeric($negaBillInvAmount) || ibccomp($negaBillInvAmount, 0) <= 0)&&
                (empty($creditToApply) || !is_numeric($creditToApply) || ibccomp($creditToApply, 0) <= 0)
            ) {
                $ok = false;
                // none of the payment amount present is valid in the request, throw error
                $msg = "Enter a valid payment amount, then try again.";
            } // Make sure all the payment amounts are valid numeric values
            else if ((!empty($trxAmount) && (!is_numeric($trxAmount) || ibccomp($trxAmount, 0) <= 0)) ||
                (!empty($advanceAmount) && (!is_numeric($advanceAmount) || ibccomp($advanceAmount, 0) <= 0)) ||
                (!empty($overpaymentAmount) && (!is_numeric($overpaymentAmount) || ibccomp($overpaymentAmount, 0) <= 0)) ||
                (!empty($inlineAmount) && (!is_numeric($inlineAmount) || ibccomp($inlineAmount, 0) <= 0)) ||
                (!empty($adjustmtAmount) && (!is_numeric($adjustmtAmount) || ibccomp($adjustmtAmount, 0) <= 0))
            ) {
                $ok = false;
                // if anyone of the payment is not valid throw error
                $msg = "Enter a positive payment amount, then try again.";
            }
            // Dynamic credit application and specific credit application can not be passed together in single
            // request, if should be either one, otherwise throw error
            else if ($is30ApiRoutingEnable && (!empty($creditToApply) && (!empty($advanceAmount) ||
                        !empty($inlineAmount) || !empty($adjustmtAmount)))) {
                $ok = false;
                $msg = "Can not apply dynamic credits and specific credits together. Enter either dynamic credit to apply or specific credit to apply.";
            } else {
                // get the total payment amount in the request
                $totalPaymentAmount = ibcadd($totalPaymentAmount, PaymentUtils::getTotalPaymentAmount($pymtDetail, true));
                // Add the credit to apply if available
                $totalPaymentAmount = ibcadd($totalPaymentAmount, $creditToApply);
                // if the total payment exceeds the due then throw error
                if ($pymtDetail->getPaymentType() != SubLedgerTxnManager::INLINE_PYMT && $totalPaymentAmount > $totalDue) {
                    $ok = false;
                    $msg = "The amount received is greater than the amount to apply. Reduce the amount received, reduce the credit amount to apply, or add an overpayment. Then try again.";
                }
            }
        }
        // If there is validation error, throw exception
        if (!$ok) {
            /** @noinspection PhpUndefinedVariableInspection */
            throw new IAException($msg, "BL01001973");
        }

        return $ok;
    }

    /**
     * @param int          $pymtDetailCount
     * @param int|string   $totalEntryBaseAmount
     * @param int|string   $totalBaseAmountToPay
     * @param int          $linePaymentCounter
     *
     * @return bool
     * @throws IAException
     */
    private function validateTotalBaseAmtAndEntryBaseAmt(
        int $pymtDetailCount, $totalEntryBaseAmount,
        $totalBaseAmountToPay, int $linePaymentCounter
    )
    {
        $errorMsg = '';
        $errorCode = '';
        $ok = true;
        // Validate the total base amount with the entry level base amounts
        // If the entry level payment present and if it doesn't match the header level then throw error
        if (Util::php7gt0($totalEntryBaseAmount) && Util::php7gt0($totalBaseAmountToPay) && $totalEntryBaseAmount != $totalBaseAmountToPay) {
            $ok = false;
            $errorMsg = "The total base payment amount at header level do not match with line level base payment 
                amount,  please provide valid base payment amount.";
            $errorCode = "BL01001973";
        }
        // Throw error if
        // 1. Base payment present on the header level
        // 2. Only few base payment entered at the entry level
        // 3. Some base payment not entered at the entry level
        // Conclusion: Either provide all the entry level or nothing
        if (Util::php7gt0($totalBaseAmountToPay) && Util::php7gt0($linePaymentCounter) && $linePaymentCounter != $pymtDetailCount) {
            $ok = false;
            $errorMsg = "Amount paid by bank at some entry level are not provided, please provide valid payment 
                amount for each entry level, alternatively provide only at the header level.";
            $errorCode = "BL01001973";
        }
        if (!$ok) {
            throw new IAException($errorMsg, $errorCode);
        }
        return $ok;
    }

    /**
     * Method to validate the base payment amount.
     *
     * @param PaymentDTO       $paymentDTO
     * @param PaymentDetailDTO $paymentDetail
     * @param PRRecordDTO      $transaction
     * @param int|string       $totalEntryBaseAmount
     * @param int              $linePaymentCounter
     * @param string           $bankCurrency
     * @param string           $baseCurr
     *
     * @return bool
     * @throws IAException
     */
    private function validateBasePaymentAmount(
        PaymentDTO $paymentDTO, PaymentDetailDTO $paymentDetail, PRRecordDTO $transaction,
        &$totalEntryBaseAmount, int &$linePaymentCounter, $bankCurrency, $baseCurr
    )
    {
        $ok = true;
        if (empty($transaction) || empty($paymentDetail->getTrxPaymentAmount())) {
            return $ok;
        }
        // Get the base payment amount if present
        $totalBaseAmountToPay = $paymentDTO->getCustomFieldValue('AMOUNTTOPAY');
        $trxAmountToPay = $paymentDTO->getCustomFieldValue('TRX_AMOUNTTOPAY');

        $txnCurrency = $transaction->getCurrency();
        $isInputBaseAmtRequired = PaymentUtils::isInputBankAmountRequired($txnCurrency, $baseCurr, $bankCurrency);
        // If the input required but there is no AMOUNTTOPAY then check for header level AMOUNTTOPAY
        // If both not present then throw error
        if ($isInputBaseAmtRequired) {
            $amountToPay = $paymentDetail->getCustomFieldValue('AMOUNTTOPAY');
            if ((!empty($amountToPay) && $amountToPay > 0) ||
                (!empty($totalBaseAmountToPay) &&  $totalBaseAmountToPay > 0))
            {
                if(!$paymentDTO->getExchangeRateTypeId()){
                    // Set the exchange rate as custom since the user setting the amount at header level and Exchange Rate Type Id is not passed.
                    $paymentDTO->setExchangeRateTypeId(CUSTOM_RATE);
                }
                // Set the input flag to true in the payment detail object which can be used later
                $paymentDetail->setCustomFieldValue('AMOUNTTOPAYREQUIRED', true);
                if (!empty($totalBaseAmountToPay) && $totalBaseAmountToPay > 0) {
                    if (!empty($amountToPay)) {
                        $totalEntryBaseAmount = ibcadd($totalEntryBaseAmount, $amountToPay);
                    }
                }
                if (!empty($amountToPay) && $amountToPay > 0) {
                    $linePaymentCounter++;
                }
            } else {
                $errorMsg = "Enter the amount to be paid by the selected bank.";
                $errorCode = "BL01001973";
                throw new IAException($errorMsg, $errorCode);
            }
        }
        if(isset($trxAmountToPay) && $trxAmountToPay !== '' &&
            isset($totalBaseAmountToPay) && $totalBaseAmountToPay !== '') {
            // In AR payment, we need to support base currency invoice paying througn foreign currency bank.
            if($txnCurrency == $baseCurr && $baseCurr != $bankCurrency) {
                $paymentDTO->setCustomFieldValue('BASETOFOREIGN', true);
            }
        }

        return $ok;
    }

    /**
     * Method to validate the payment amount for the bill.
     *
     * @param PaymentDetailDTO $paymentDetail
     * @param PRRecordDTO      $transaction
     * @param array            $paymentMap
     * @param array            $entryPaymentMap
     *
     * @return bool
     * @throws IAException
     */
    private function validateTotalPaymentAmount(
        PaymentDetailDTO $paymentDetail, PRRecordDTO $transaction,
        array &$paymentMap, array &$entryPaymentMap)
    {
        if (empty($paymentDetail) && empty($transaction)) {
            return false;
        }
        // Get the transaction key
        $transactionKey = $paymentDetail->getTransactionKey();
        $entryKey = $paymentDetail->getTransactionEntryKey();
        $dueAmount = 0;
        if (!empty($entryKey)) {
            $entryList = $transaction->getEntryList();
            $entryObj = $entryList[$entryKey];
            if ($entryObj instanceof PREntryDTO) {
                $entryAmount = ibcsub($entryObj->getTrxAmount(), $entryObj->getTrxAmountRetained());
                $dueAmount = ibcsub($entryAmount, $entryObj->getTrxTotalPaid());
                $existingPymtAmt = !empty($entryPaymentMap[$transactionKey][$entryKey]) ?
                    $entryPaymentMap[$transactionKey][$entryKey] : 0;
            }
        } else {
            $dueAmount = $transaction->getTrxTotalDue();
            $existingPymtAmt = !empty($paymentMap[$transactionKey]) ? $paymentMap[$transactionKey] : 0;
        }
        //For VAT payments include taxes of line
        if ( ! empty($entryKey) && PaymentUtils::isPartialPymtTaxCaptureSupported($transaction->getValues()) ) {
            $entryList = $transaction->getEntryList();
            $dueAmount = 0;
            foreach ( $entryList as $entry ) {
                if ( $entry instanceof PREntryDTO ) {
                    if ( in_array($entryKey, [ $entry->getRecordNo(), $entry->getParentEntry() ]) ) {
                        $entryAmount = ibcsub($entry->getTrxAmount(), $entry->getTrxAmountRetained());
                        $dueAmount = ibcadd($dueAmount, ibcsub($entryAmount, $entry->getTrxTotalPaid()));
                    }
                }
            }
        }
        // Validate the payment amount
        if ($this->validatePaymentAmount($paymentDetail, $dueAmount, $existingPymtAmt)) {
            // Update the payment map
            if (!empty($entryKey)) {
                $entryPaymentMap[$transactionKey][$entryKey] = $existingPymtAmt;
                $paymentMap[$transactionKey] = $existingPymtAmt;
            } else {
                $paymentMap[$transactionKey] = $existingPymtAmt;
            }
        } else {
            return false;
        }
        return true;
    }

    /**
     * Validates the payment currency with transaction and base currencies.
     *
     * @param PaymentRequest $request
     * @param string $transactionKey
     * @param string $bankCurrency
     * @param string $baseCurrency
     * @param string &$billCurrency
     *
     * @return bool
     *
     * @throws IAException
     */
    protected function validateCurrency(PaymentRequest $request, $transactionKey, $bankCurrency, $baseCurrency,
                                        &$billCurrency)
    {
        $ok = true;
        $billRecord = $request->getTransactions()[$transactionKey];
        $_recordid = $billRecord->getRecordId();
        $invCurrency = $billRecord->getCurrency();
        $recordBaseCurrency = $billRecord->getBaseCurrency();
        $isOnlyCreditPayment = $request->getPaymentDTO()->isCreditOnlyPymtRequest();
        $exchRateTypeID = $request->getPaymentDTO()->getExchangeRateTypeId() ?? '';
        // validate the multiple currency in case of multi bills in the single request
        // TODO current implementation do not allow multi currency, we may need to revisit this later
        if (!empty($billCurrency) && $billCurrency != $invCurrency) {
            $ok = false;
            $errorMsg = "Select bills with the same transaction currency, then try again.";
            $corrMsg = "";
            $errorCode = "PL03000098";
        } else {
            $billCurrency = $invCurrency;
        }

        // Continue validation if the above passed
        if ($ok) {
            // if there is no base currency set for the location then use the record base currency as base
            if (!$baseCurrency || empty($baseCurrency)) {
                $baseCurrency = $recordBaseCurrency;
            }

            // Validate other currency related scenarios. No need to go further if its credit only payment
            if (!$isOnlyCreditPayment) {
                // Validate future date for foreign currency payments
                // for a foreign currency payment, we cannot allow future payment date
                if (DateCompare($this->getTxnPaymentDate($request), $request->getCurrentDate()) > 0) {
                    if (!isset($exchRateTypeID) || $exchRateTypeID == '' || $exchRateTypeID == 'Intacct Daily Rate') {
                        if ($bankCurrency != $baseCurrency || $bankCurrency != $billCurrency) {
                            $ok = false;
                            $entityTypeName = $this->getEntityTypeName($request);
                            $errorMsg = "Change the date for the foreign currency payment to $entityTypeName to today's date or 
                        earlier, then try again.";
                            $errorCode = "BL01001973";
                        }
                    }
                    $ok = $ok && $this->validateMCMEBankLocCurrency($bankCurrency,
                            $request->getFinancialAccount()['LOCBASECURRENCY']);
                }
                // flag for base currency invoice paying with foreign currency
                $paybase = false;
                if ($baseCurrency == $invCurrency && $bankCurrency != $recordBaseCurrency) {
                    $paybase = true;
                }
                // throw error if
                // 1. the payment currency is not same as invoice currency or base currency
                // 2. And if the invoice currency not same as base currency
                if (!$paybase && $bankCurrency != $baseCurrency && $bankCurrency != $invCurrency && $baseCurrency != $invCurrency) {
                    $ok = false;
                    $errorMsg = "Change the bank currency " . $bankCurrency . " to match either the transaction currency "
                        . $invCurrency . " or the transaction base currency " . $baseCurrency . " for {$this->getTransactionType()}" . ($_recordid ? " " . $_recordid : "") . ", then try again.";
                    $errorCode = "BL01001973";
                }
                // Validate bank location currency when the bill or bank currency is different from base currency
                if ($ok && !empty($baseCurrency) && !empty($billCurrency) && !empty($bankCurrency) &&
                    ($billCurrency != $baseCurrency || $bankCurrency != $baseCurrency)) {
                    $locBaseCurrency = $request->getFinancialAccount()['LOCBASECURRENCY'];
                    $foreignBank = (!empty($locBaseCurrency) && $locBaseCurrency != $bankCurrency) ? true : false;

                    if ($foreignBank == true && $baseCurrency != $locBaseCurrency) {
                        $ok = false;
                        $errorMsg = "Select a bank with the same base currency as the entity for this inter-entity transaction, then try again.";
                        $errorCode = "BL01001973";
                    }
                }
            }
        }
        // If there is validation error, throw exception
        if (!$ok) {
            /** @noinspection PhpUndefinedVariableInspection */
            throw new IAException($errorMsg, $errorCode, '', $corrMsg);
        }
        return $ok;
    }

    /**
     * Validates the payment date based on payment date preference.
     *
     * @param string $pymtDate
     * @param string $billDate
     * @param bool $showWarning
     *
     * @return bool
     */
    public function validatePaymentDate($pymtDate, $billDate, $showWarning = false)
    {
        $ok = true;
        $paymentPref = $this->modulePreferences['PYMTDT_ISGREATER'];
        // check if the payment is made and preference is set to validate the date
        if ($paymentPref == 'Y') {
            // if the payment date is prior to bill date then throw error
            if (SysDateCompare($pymtDate, $billDate) < 0) {
                // throw new IAException("Payment date cannot be before bill creation date.", "BL03000113");
                $transactionType = $this->getTransactionType();
                Globals::$g->gErr->addIAError(
                    'SL-0417',
                    __FILE__ . ":" . __LINE__,
                    "Select a payment date that occurs after the " . $transactionType . " creation date, then try again.",
                    ['THIS_GETTRANSACTIONTYPE' => $transactionType]
                );
                $ok = false;
            }
        }
        if ($ok) {
            // Validate the posting date
            $glPostingDateCheck = $this->modulePreferences['GLPOSTINGDATECHECK'];
            // Throw exception if the configuration is disallow or its warning with user wanted to show the warning
            if ($glPostingDateCheck == 'DISALLOW' || ($glPostingDateCheck == 'WARN' && $showWarning)) {
                $gStandardDate = &Globals::$g->gStandardDate;
                $lastDayOfCurrentPeriod = GetCompanyLastDayOfCurrentPeriod();
                if (ReformatDate($pymtDate, $gStandardDate, ' Ymd') > ReformatDate($lastDayOfCurrentPeriod, $gStandardDate, ' Ymd')) {
                    if ($glPostingDateCheck == 'DISALLOW') {
                        $message = "Select a payment date within the current period, then try again";
                        Globals::$g->gErr->addError('SL-0015', __FILE__ . ":" . __LINE__, $message);
                    } else {
                        $message = "The payment posting date occurs outside the current period.";
                        Globals::$g->gErr->AddWarning($message, __FILE__ . ":" . __LINE__);
                    }
                    $ok = false;
                }
            }
        }
        return $ok;
    }

    /**
     * Method to validate the period close.
     *
     * @param string $pymtDate
     * @param string $openDate
     * @param string $isCreate
     *
     * @return bool
     * @throws IAException
     */
    public function validatePeriodClose($pymtDate, $openDate, $isCreate)
    {
        $ok = true;
        if (SysDateCompare($pymtDate, $openDate) < 0) {
            /** @noinspection PhpUnusedLocalVariableInspection */
            $action = $isCreate ? 'create' : 'update';
            $msg = "Enter a payment request date within an open period, then try again.";
            // IAException triggers to throw the error message using popup which we
            // don't need for some of the calls from UI.
            //throw new IAException($msg, "**********");
            Globals::$g->gErr->addError('SL-0016', __FILE__ . ":" . __LINE__, $msg);
            $ok = false;
        }
        return $ok;
    }

    /**
     * Method to validate the credit entries in the payment details.
     *
     * @param PaymentDetailDTO $paymentDetail
     * @param PaymentRequest $request
     * @param string $billKey
     * @param string $billCurrency
     * @param string $entityID
     *
     * @return bool true if the validation passes otherwise returns false.
     *
     * @throws IAException
     *
     */
    protected function validateCredits(PaymentDetailDTO $paymentDetail, PaymentRequest $request, $billKey, $billCurrency, $entityID)
    {
        $ok = true;
        $transactions = $request->getTransactions();
        if (!empty($paymentDetail) && !empty($transactions)) {
            $transaction = $transactions[$billKey];
            $transactionBaseCurrency = $transaction->getBaseCurrency();
            // validate the advance
            $advanceKey = $paymentDetail->getAdvanceKey();
            if (!empty($advanceKey)) {
                $advanceTxn = $transactions[$advanceKey];
                if($advanceTxn instanceof PRRecordDTO) {
                    $advanceEntryKey = $paymentDetail->getAdvanceEntryKey();
                    $ok = $this->validateCreditTransaction($advanceTxn, $advanceKey, $advanceEntryKey, $billCurrency,
                        $entityID, $transactionBaseCurrency );
                }else {
                    Globals::$g->gErr->addIAError(
                        'SL-0017', __FILE__ . ":" . __LINE__,
                        "Invalid advance key " . $advanceKey, ['ADVANCE_KEY' => $advanceKey]
                    );
                    $ok = false;
                }
            }

            // validate the inline credit
            if ($ok) {
                $inlineKey = $paymentDetail->getInlineKey();
                // go for inline key validation only if its not the same as bill transaction
                if (!empty($inlineKey) && $inlineKey != $billKey) {
                    $inlineTxn = $transactions[$inlineKey];
                    if($inlineTxn instanceof PRRecordDTO) {
                        $inlineEntryKey = $paymentDetail->getInlineEntryKey();
                        $ok = $this->validateCreditTransaction($inlineTxn, $inlineKey, $inlineEntryKey, $billCurrency,
                            $entityID, $transactionBaseCurrency);
                    } else {
                        Globals::$g->gErr->addIAError(
                            'SL-0018', __FILE__ . ":" . __LINE__,
                            "Invalid inline/negative transaction key " . $inlineKey, ['INLINEKEY' => $inlineKey]
                        );
                        $ok = false;
                    }
                }
            }

            // validate the negative credit
            if ($ok) {
                $negativeBillInvKey = $paymentDetail->getNegativeBillInvKey();
                // go for negative key validation only if its not the same as bill transaction
                if (!empty($negativeBillInvKey) && $negativeBillInvKey != $billKey) {
                    $negativeBillInvTxn = $transactions[$negativeBillInvKey];
                    if($negativeBillInvTxn instanceof PRRecordDTO) {
                        $negativeBillInvEntryKey = $paymentDetail->getNegativeBillInvEntryKey();
                        $ok = $this->validateCreditTransaction($negativeBillInvTxn, $negativeBillInvKey, $negativeBillInvEntryKey, $billCurrency,
                                                               $entityID, $transactionBaseCurrency);
                    } else {
                        Globals::$g->gErr->addIAError(
                            'SL-0019', __FILE__ . ":" . __LINE__,
                            "Invalid negative transaction key " . $negativeBillInvKey, ['NEGATIVE_BILL_INV_KEY' => $negativeBillInvKey]
                        );
                        $ok = false;
                    }
                }
            }

            // validate the adjustment
            if ($ok) {
                $adjKey = $paymentDetail->getAdjustmentKey();
                if (!empty($adjKey)) {
                    $adjTxn = $transactions[$adjKey];
                    if($adjTxn instanceof PRRecordDTO) {
                        $adjEntryKey = $paymentDetail->getAdjustmentEntryKey();
                        $ok = $this->validateCreditTransaction($adjTxn, $adjKey, $adjEntryKey, $billCurrency,
                            $entityID, $transactionBaseCurrency);
                    }else {
                        Globals::$g->gErr->addIAError(
                            'SL-0020', __FILE__ . ":" . __LINE__,
                            "Invalid adjustment key " . $adjKey, ['ADJ_KEY' => $adjKey]
                        );
                        $ok = false;
                    }
                }
            }
            // validate the adjustment
            if ($ok) {
                $overpymtKey = $paymentDetail->getOverpaymentKey();
                if (!empty($overpymtKey)) {
                    $overPymtTxn = $transactions[$overpymtKey];
                    if($overPymtTxn instanceof PRRecordDTO) {
                        $overPymtEntryKey = $paymentDetail->getOverpaymentEntryKey();
                        $ok = $this->validateCreditTransaction($overPymtTxn, $overpymtKey, $overPymtEntryKey, $billCurrency,
                            $entityID, $transactionBaseCurrency);
                    }else {
                        Globals::$g->gErr->addIAError(
                        'SL-0155', __FILE__ . ":" . __LINE__,
                        "Invalid overpayment key " . $overpymtKey, ['OVERPYMT_KEY' => $overpymtKey]
                    );
                        $ok = false;
                    }
                }
            }
        }
        return $ok;
    }

    /**
     * Validates the duplicate document number.
     *
     * @param string $documentId
     * @param string $bankId
     * @param bool $duplicateExist
     *
     * @return bool
     * @throws IAException
     */
    public function validateDuplicateDocument($documentId, $bankId, $duplicateExist)
    {
        $ok = true;
        if ($duplicateExist) {
            // 1 is warning, return the exception if the request is from UI
            // 2 is error, return the exception always
            if (($this->modulePreferences['WARNDUPLICATE'] == "1" && !$this->isAPICall) ||
                $this->modulePreferences['WARNDUPLICATE'] == "2"
            ) {
                if (!empty($bankId)) {
                    $message = sprintf('Replace the check number %1$s with an un-used check number against the %2$s bank, then try again.', $documentId, $bankId);
                    $errorCode = 'SL-0419';
                    $placeHolder = ['DOCUMENT_ID' => $documentId,'BANK_ID' => $bankId];
                } else {
                    $message = sprintf("The Check number %s entered already exists", $documentId);
                    $errorCode = 'SL-0418';
                    $placeHolder = ['DOCUMENT_ID' => $documentId];
                }
                // Throw the exception
                //throw new IAException($message, "BL01001973");
                Globals::$g->gErr->addIAError(
                    $errorCode, __FILE__ . ":" . __LINE__,
                    $message, $placeHolder,
                    "Please enter unique check number", []
                );
                $ok = false;
            }
        }
        return $ok;
    }

    /**
     * This function validates the credit inputs and currency information with respect to bill details.
     *
     * @param PRRecordDTO $creditTransaction
     * @param string $creditKey
     * @param string $creditEntryKey
     * @param string $billCurrency
     * @param string $entityID
     * @param string $transactionBaseCurrency
     *
     * @return bool returns true for success otherwise returns false.
     *
     * @throws IAException
     */
    protected function validateCreditTransaction(PRRecordDTO $creditTransaction, $creditKey, $creditEntryKey,
                                               $billCurrency, $entityID, $transactionBaseCurrency)
    {
        $ok = true;
        // if there is no record found then throw error
        if (!isset($creditTransaction) || empty($creditTransaction)) {
            $ok = false;
            $errorMsg = "One or more transactions have invalid credit transaction keys, submit transactions with valid keys." . $creditKey;
        }

        $creditEntryList = $creditTransaction->getEntryList();

        $ok = $ok && $this->validateApprovalTransaction($creditTransaction);

        if ($creditTransaction->getCustomerID() != $entityID && in_array($creditTransaction->getRecordType(),
                [PaymentUtils::ARADJUSTMENT_RECTYPE,
                    PaymentUtils::ARADVANCE_RECTYPE,
                    PaymentUtils::INVOICE_RECTYPE])) {
            $ok = false;
            $errorMsg =
                "Make the customer for the transaction and the applied credits the same, or remove the credits, then try again.";
        }

        if ($creditTransaction->getVendorID() != $entityID && in_array($creditTransaction->getRecordType(),
                [PaymentUtils::APADJUSTMENT_RECTYPE,
                    PaymentUtils::APADVANCE_RECTYPE,
                    PaymentUtils::BILL_RECTYPE])) {
            $ok = false;
            $errorMsg = I18N::getSingleToken("IA.MAKE_VENDOR_AND_CREDITS_SAME_OR_REMOVE_THE_CREDITS");
        }
        // validate the transaction state
        if ($creditTransaction->getState() == BasePRRecordManager::DRAFT_RAWSTATE ||
            $creditTransaction->getState() == BasePRRecordManager::DRAFT_STATE) {
            $ok = false;
            $errorMsg = "One or more credits are in Draft status. Post the credit, or remove them from the payment 
                request, and try again.";
        }
        // Fix for Ticket#51470
        // if the transaction is on hold then throw error message
        if ($creditTransaction->getOnhold() == 'T' || $creditTransaction->getOnhold() == 'true') {
            $ok = false;
            $errorMsg = "Remove the on-hold credit transactions from this request, or remove the on-hold status of the transactions, then try again.";
        }


        // validate the currency of the credit and bill
        // if the credit currency is different from bill/invoice currency, throw error
        else if (in_array($creditTransaction->getRecordType(),
                [PaymentUtils::ARADJUSTMENT_RECTYPE,
                    PaymentUtils::ARADVANCE_RECTYPE,
                    PaymentUtils::INVOICE_RECTYPE])
            && ($creditTransaction->getCurrency() != $billCurrency)) {
            $ok = false;
            $errorMsg = I18N::getSingleToken("IA.CHANGE_CREDIT_TRANSACTION_CURRENCY_TO_MATCH_INVOICE_CURRENCY");
        } else if (in_array($creditTransaction->getRecordType(),
                [PaymentUtils::APADJUSTMENT_RECTYPE,
                    PaymentUtils::APADVANCE_RECTYPE,
                    PaymentUtils::BILL_RECTYPE])
            && ($creditTransaction->getCurrency() != $billCurrency)) {
            $ok = false;
            $errorMsg = I18N::getSingleToken("IA.CHANGE_CREDIT_TRANSACTION_CURRENCY_TO_MATCH_BILL_CURRENCY");
        } else if ($creditTransaction->getRecordType() == PaymentUtils::ARPOSTEDPAYMENT_RECTYPE &&
            ($creditTransaction->getCurrency() != $billCurrency
                || $creditTransaction->getBaseCurrency() != $transactionBaseCurrency)) {
            // Validate OverPayment
            $ok = false;
            $errorMsg = I18N::getSingleToken("IA.PAYMENT_CURRENCY_AND_INVOICE_CURRENCY_CANT_BE_DIFFERENT");

        }
        else if ($creditTransaction->getBaseCurrency() !== $transactionBaseCurrency) {
            // validate the base currency of the credit and bill
            // if the credit base currency is different from bill base currency, throw error
            $ok = false;
            $errorMsg = "Invalid credit selected. You cannot apply the credit if the base currency of the credit is different from the base currency of your selected transaction.";
        } else if (!empty($creditEntryKey)) {
            // validate the credit entry if its available
            $creditEntry = $creditEntryList[$creditEntryKey];
            // if there is no entry data for the given input, then throw error
            if ( empty($creditEntry) ) {
                $ok = false;
                $errorMsg =
                    "One or more transactions have invalid credit transaction keys, submit transactions with valid keys.";
            }
        }
        //$ok = $ok && $this->validateSelectedEntries($creditEntryList, $creditKey, $updatePayment);

        // If there is validation error, throw exception
        if (!$ok) {
            /** @noinspection PhpUndefinedVariableInspection */
            throw new IAException($errorMsg, "BL01001973");
        }
        return $ok;
    }

    /**
     * Validates the payment action.
     *
     * @param string $action
     *
     * @return bool
     */
    protected function validateAction(string $action): bool
    {
        $ok = match ($action) {
            BasePRRecordManager::DRAFT_ACTION,
            BasePRRecordManager::SUBMIT_ACTION => true,
            default => false,
        };

        if (!$ok) {
            Globals::$g->gErr->addError(
                'SL-0165', __FILE__ . '.' . __LINE__,
                "Enter a valid action, such as Draft or Submit, then try again.",
                ""
            );
        }

        return $ok;
    }

    /**
     * Build the template error message for invalid object
     *
     * @param string $errorId the message error ID
     * @param string $objectName the object name we are validating
     * @param string $objectValue the objevt value
     * @param string $requestUniqueId the payment request unique identifier
     * @param string $lineNo
     * @param string $trxName
     */
    public static function throwInvalidObjectError(
        $errorId, $objectName, $objectValue = '', $requestUniqueId = '', $lineNo = '', $trxName = 'transaction')
    {
        // Build the error message
        $corr = "Enter a valid $objectName";
        if (!empty($lineNo)) {
            $corr .= " on line item $lineNo";
        }
        if (!empty($requestUniqueId)) {
            $corr .= ", in $trxName '$requestUniqueId'";
        }
        $corr .= ", and try again.";

        // Figure the right message to show based on the value passed.
        // Either is is an invalid value not a value was not passed.
        if (isset($objectValue) && $objectValue !== '') {
            $msg = "The $objectName '" . $objectValue . "' is invalid.";
        } else {
            $msg = $corr;
            $corr = '';
        }

        Globals::$g->gErr->addError($errorId, __FILE__ . ':' . __LINE__, $msg, '', $corr);
        //I18N::TODO (need to discuss with file owner)
    }

    /**
     * @param PaymentRequest $request
     *
     * @return mixed
     */
    public function getEntityID(/** @noinspection PhpUnusedParameterInspection */
        PaymentRequest $request)
    {
        return '';
    }

    /**
     * @return mixed
     */
    public function getEntityFieldPath()
    {
        return '';
    }

    /**
     * @return string
     */
    public function getFeatureKey()
    {
        return '';
    }


    /**
     * @param PaymentDetailDTO $paymentDetail
     *
     * @return bool
     */
    public function validatePaymentDetailKeys(PaymentDetailDTO $paymentDetail)
    {
        $inlineKey = $paymentDetail->getInlineKey();
        $trxInlineAmount = $paymentDetail->getTrxInlineAmount();
        $negativeBillInvKey = $paymentDetail->getNegativeBillInvKey();
        $trxNegativeBillInvAmount = $paymentDetail->getTrxNegativeBillInvAmount();
        $adjustmentKey = $paymentDetail->getAdjustmentKey();
        $trxAdjustmentAmount = $paymentDetail->getTrxAdjustmentAmount();
        $advanceKey = $paymentDetail->getAdvanceKey();
        $trxPostedAdvanceAmount = $paymentDetail->getTrxPostedAdvanceAmount();

        if (isset($inlineKey) && !Util::php7eqEmptyStr($inlineKey)) {
            if (!isset($trxInlineAmount) || $trxInlineAmount == '') {
                Globals::$g->gErr->addError(
                    'SL-0166', __FILE__ . '.' . __LINE__,
                    "Please provide the TRX_INLINEAMOUNT, if you are passing the inlinekey."
                );
                return false;
            }
        } else if (isset($trxInlineAmount) && $trxInlineAmount != '') {
            Globals::$g->gErr->addError(
                'SL-0167', __FILE__ . '.' . __LINE__,
                "Please provide the inlinekey, if you are passing the trxinlineamount."
            );
            return false;
        }
        if (isset($negativeBillInvKey) && $negativeBillInvKey != '') {
            if (!isset($trxNegativeBillInvAmount) || $trxNegativeBillInvAmount == '') {
                Globals::$g->gErr->addError(
                    'SL-0168', __FILE__ . '.' . __LINE__,
                    "Please provide the TRX_NEGATIVEINVOICEAMOUNT, if you are passing the negativeinvoicekey."
                );
                return false;
            }
        } else if (isset($trxNegativeBillInvAmount) && $trxNegativeBillInvAmount != '') {
            Globals::$g->gErr->addIAError(
                'SL-0156',
                __FILE__ . '.' . __LINE__,
                "Please provide the negativeinvoicekey, if you are passing the $trxNegativeBillInvAmount.",
                ['TRX_NEGATIVE_BILL_INV_AMOUNT' => $trxNegativeBillInvAmount]
            );
            return false;
        }
        if( isset($adjustmentKey) && $adjustmentKey != '' ) {
            if ( !isset($trxAdjustmentAmount) || $trxAdjustmentAmount == '' ) {
                Globals::$g->gErr->addError(
                    'SL-0169', __FILE__.'.'.__LINE__,
                    "Please provide the TRX_ADJUSTMENTAMOUNT, if you are passing the adjustmentkey."
                );
                return false;
            }
        } else if (isset($trxAdjustmentAmount) && $trxAdjustmentAmount != '' ) {
                Globals::$g->gErr->addError(
                    'SL-0170', __FILE__.'.'.__LINE__,
                    "Please provide the adjustmenkey, if you are passing the TRX_ADJUSTMENTAMOUNT."
                );
                return false;
        }
        if( isset($advanceKey) && $advanceKey != '' ) {
            if ( !isset($trxPostedAdvanceAmount) || $trxPostedAdvanceAmount == '' ) {
                Globals::$g->gErr->addError(
                    'SL-0171', __FILE__.'.'.__LINE__,
                    "Please provide the TRX_POSTEDADVANCEAMOUNT, if you are passing the advancekey."
                );
                return false;
            }
        } else if (isset($trxPostedAdvanceAmount) && $trxPostedAdvanceAmount != '' ) {
            Globals::$g->gErr->addError(
                'SL-0172', __FILE__.'.'.__LINE__,
                "Please provide the advancekey, if you are passing the TRX_POSTEDADVANCEAMOUNT."
            );
            return false;
        }
        return true;
    }

    /**
     * @param PaymentDTO $paymentDTO
     *
     * @return bool
     */
    protected function validateBaseCurrency(/** @noinspection PhpUnusedParameterInspection */ PaymentDTO $paymentDTO){
      return true;
    }

    /**
     * @param PaymentDTO $paymentDTO
     *
     * @return bool
     */
    protected function validateInputCurrency(/** @noinspection PhpUnusedParameterInspection */ PaymentDTO $paymentDTO){
        return true;
    }
    /**
     * @param PaymentDTO $paymentDTO
     *
     * @return bool
     */
    protected function validateExchangeDetails(/** @noinspection PhpUnusedParameterInspection */ PaymentDTO $paymentDTO){
        return true;
    }

    /**
     * @param bool $isOnlyCreditPayment
     * @param string $currency
     * @param string $bankCurrency
     *
     * @throws IAException
     */
    protected function validateBankCurrency( $isOnlyCreditPayment, $currency, $bankCurrency){
        // Check the bank currency with input payment currency
        // should not validate for only inline credit payments
        if (!$isOnlyCreditPayment && !empty($currency) &&
            $currency != $bankCurrency) {
            // If there is wrong payment currency input, then throw error back
            $errorMsg = "Change the bank currency to the currency of the selected bank, or leave the bank currency field empty, then try again.";
            $errorCode = "BL01001973";
            throw new IAException($errorMsg, $errorCode);
        }
    }

    /**
     * @param PaymentRequest $request
     *
     * @return string
     */
    protected function getTxnPaymentDate(PaymentRequest $request)
    {
        return $request->getPaymentDTO()->getPaymentDate();
    }

    /**
     * @return string
     */
    public function getEntityType()
    {
        return '';
    }

    /**
     * @param PaymentRequest $request
     *
     * @return mixed
     */
    public function getEntityTypeName(/** @noinspection PhpUnusedParameterInspection */
        PaymentRequest $request)
    {
        return '';
    }

    /**
     * @return bool
     * @throws Exception
     */
    public function isRoutingEnable()
    {
        return true;
    }

    /**
     * @param PRRecordDTO $transaction
     * @return bool
     *
     * @throws IAException
     */
    protected function validateDraftTransaction(PRRecordDTO $transaction)
    {
        $ok = true;

        // validate the transaction state
        if ($transaction->getState() == BasePRRecordManager::DRAFT_RAWSTATE ||
            $transaction->getState() == BasePRRecordManager::DRAFT_STATE) {
            $ok = false;
            $message = $this->setErrorMessage(BasePRRecordManager::DRAFT_STATE);
            Globals::$g->gErr->addIAError($message['CODE'], __FILE__ . '.' . __LINE__, $message['ERROR']);
        }

        return $ok;
    }

    /**
     * @param PRRecordDTO $transaction
     * @return bool
     *
     * @throws IAException
     */
    protected function validateApprovalTransaction(/** @noinspection PhpUnusedParameterInspection */ PRRecordDTO $transaction)
    {
        return true;
    }

    /**
     * @param PRRecordDTO $transaction
     * @return bool
     *
     * @throws IAException
     */
    protected function validateTransactionOnHold(/** @noinspection PhpUnusedParameterInspection */ PRRecordDTO $transaction)
    {
        return true;
    }

    /**
     * @param PRRecordDTO $transaction
     * @param string $entryKey
     * @param bool $isUpdatePayment
     *
     * @return bool
     *
     * @throws IAException
     */
    protected function validateEntries(PRRecordDTO $transaction, $entryKey, $isUpdatePayment = false)
    {
        $ok = true;

        $errorMsg = '';
        $errorCode = '';

        /** @var PREntryDTO[] $entryList */
        $entryList = $transaction->getEntryList();
        // if no line items, throw an error
        if (empty($entryList)) {
            $errorMsg = "Invalid transaction key " . $transaction->getRecordNo() .", No line items found";
            $errorCode = "BL01001973";
            throw new IAException($errorMsg, $errorCode);
        }
        if (!empty($entryKey)) {
            $entryObj = $entryList[$entryKey];
            if ( $transaction->isPaymentTaxCapture() && ( ! isset($entryObj) || empty($entryObj) ) ) {
                foreach ( $entryList as $entry ) {
                    if ( $entry->getParentEntry() === $entryKey ) {
                        $entryObj = $entry;
                        break;
                    }
                }
            }
            // if there is no entry data for the given input, then throw error
            if (!isset($entryObj) || empty($entryObj)) {
                $ok = false;
                $errorMsg = "Invalid entry key " . $entryKey;
                $errorCode = "BL01001973";
            }
        }
        // validate the release to pay
        $txnEntries = array();

        if ($transaction->getRecordType() != PaymentUtils::BILL_RECTYPE) {
            // Add the entries
            $txnEntries = $entryList;
        }

        // validate if there is any pending line item payment on this bill (ie., any previous payments waiting for approval)
        $ok = $ok && $this->validateSelectedEntries($entryList, $entryKey, $isUpdatePayment);

        // Update the transaction entries back to the DTO
        if ($ok && !empty($txnEntries)) {
            $transaction->setEntryList($txnEntries);
        }

        // If there is validation error, throw exception
        if (!$ok) {
            throw new IAException($errorMsg, $errorCode);
        }
        return $ok;
    }

    /**
     * @param array $txnEntries
     * @param string $entryKey
     * @param bool $isUpdatePayment
     *
     * @return bool
     *
     * @throws IAException
     */
    protected function validateSelectedEntries($txnEntries, $entryKey, $isUpdatePayment)
    {
        $ok = true;

        $errorMsg = '';
        $errorCode = '';

        if (isset($entryKey) && isSpecified($entryKey)) {
            $entryObj = $txnEntries[$entryKey];
            if($entryObj instanceof PREntryDTO && $entryObj->getTrxTotalSelected() > 0 && !$isUpdatePayment) {
                $ok = false;
                $errorMsg = I18N::getSingleToken("IA.PAYMENT_CANNOT_BE_PROCESSED_LINE_ITEM_PAID");
                $errorCode = "PL03000098";
            }
        } else {
            foreach ( $txnEntries as $entryObj) {
                if($entryObj instanceof PREntryDTO && $entryObj->getTrxTotalSelected() > 0 && !$isUpdatePayment) {
                    $ok = false;
                    $errorMsg = I18N::getSingleToken("IA.PAYMENT_CANNOT_BE_PROCESSED_LINE_ITEM_PAID");
                    $errorCode = "PL03000098";
                    break;
                }
            }
        }

        if (!$ok) {
            throw new IAException($errorMsg, $errorCode);
        }

        return $ok;
    }

    /**
     * @param PaymentDetailDTO $paymentDetail
     *
     * @return bool
     *
     * @throws IAException
     */
    private function validatePaymentDetailEntry(PaymentDetailDTO $paymentDetail)
    {
        $ok = true;
        $errorMsg = '';
        $errorCode = '';
        $transactionKey = $paymentDetail->getTransactionKey();
        $entryKey = $paymentDetail->getTransactionEntryKey();
        $entityType = $this->getEntityType();
        // Check for the paid items
        if (!empty($paymentDetail->getRecordKey()) && !empty($paymentDetail->getPostedAdjustmentKey())) {
            $ok = false;
            $errorMsg = I18N::getSingleToken("IA.CREATE_SEPARATE_PAYMENT_REQUEST_FOR_DEBIT_ADJUSTMENT", ["name" => "ENTITY", "value" => "vendor"]);
            $errorCode = "BL01001973";
        } // Validate the cross items for bill
        else if (!empty($paymentDetail->getRecordKey()) && !empty
            ($paymentDetail->getPostedAdjustmentEntryKey())) {
            $ok = false;
            $errorMsg = I18N::getSingleToken("IA.CREATE_SEPARATE_PAYMENT_REQUEST_FOR_DEBIT_ADJUSTMENT", ["name" => "ENTITY", "value" => $entityType]);
            $errorCode = "BL01001973";
        } // Validate the cross items for adjustment
        else if (!empty($paymentDetail->getPostedAdjustmentKey()) && !empty
            ($paymentDetail->getEntryKey())) {
            $ok = false;
            $errorMsg = I18N::getSingleToken("IA.CREATE_SEPARATE_PAYMENT_REQUEST_FOR_DEBIT_ADJUSTMENT", ["name" => "ENTITY", "value" => $entityType]);
            $errorCode = "BL01001973";
        } // Validate bill/transaction key
        else if (empty($transactionKey) || !is_numeric($transactionKey)) {
            $ok = false;
            $errorMsg = "Enter valid transaction key, then try again.";
            $errorCode = "BL01001973";
        } // Validate entry key
        else if (!empty($entryKey) && !is_numeric($entryKey)) {
            $ok = false;
            $errorMsg = "Enter valid transaction entry key, then try again.";
            $errorCode = "BL01001973";
        }
        if (!$ok) {
            throw new IAException($errorMsg, $errorCode);
        }
        return $ok;
    }

    /**
     * @param string $bankCurrency
     * @param string $locbaseCurrency
     *
     * @return bool
     *
     * @throws IAException
     */
    private function validateMCMEBankLocCurrency($bankCurrency, $locbaseCurrency)
    {
        $ok = true;
        if(!IsMCMESubscribed()) {
            return $ok;
        }

        if (!empty($locbaseCurrency) && $bankCurrency != $locbaseCurrency) {
            $ok = false;
            $errorMsg = "The transaction you’re trying to receive the payment is for future date. 
                We do not process IET or Multi- currency transaction for future date. Change the payment to today 
                or a previous date.";
            $errorCode = "BL01001973";
        }
        if (!$ok) {
            /** @noinspection PhpUndefinedVariableInspection */
            throw new IAException($errorMsg, $errorCode);
        }

        return $ok;
    }

    /**
     * @return bool $shouldWarn
     */
    public static function getShouldWarn(): bool
    {
        return self::$shouldWarn;
    }

    /**
     * @param bool $shouldWarn
     */
    public static function setShouldWarn(bool $shouldWarn)
    {
        self::$shouldWarn = $shouldWarn;
    }

    /**
     * @return bool
     */
    protected function isUserDefinedDiscountAllowed(): bool
    {
        return false;
    }

    /**
     * @param PRRecordDTO $transaction
     * @return bool
     */
    private function validateLineDiscountTerm($transaction)
    {
        // This function is called during the evaluation of a custom discount
        // In other words the logic below assumes a custom discount is being applied in the transaction.

        $ok = true;

        // Check the tax solution
        $acceptedTaxSolution = TaxSolutionManager::isTermWithDiscountSupportedTaxSoln($transaction->getTaxsolutionId());
        if (!$acceptedTaxSolution) {

            // If here then we don't haave a valid tax solution for custom discounts, so throw an error
            $taxSolutionId = $transaction->getTaxsolutionId();
            $invoiceId = $transaction->getRecordId();
            $msg = "A custom discount can not be applied to the {$invoiceId} because this is not permitted with the tax solution {$taxSolutionId}.";
            Globals::$g->gErr->addIAError(
                'SL-1301', __FILE__ . ':' . __LINE__,
                $msg, ['INVOICEID' => $invoiceId, 'TAXSOLUTIONID' => $taxSolutionId],
                "", [],
                "Select a different tax solution or remove the invoice."
            );
            $ok = false;

        } else {

            // If here then we have a valid tax solution and we need to examine the term
            $termKey = $transaction->getTermKey();
            if ($termKey) {

                // We have the term so now we look at the radio button
                $termVals = SubledgerPymtHelper::getTermvaluesForBillsByTerm($termKey);
                $isRadioButtonTwoOrThree = $termVals['DISCCALCON'] !== "L";
                if ($isRadioButtonTwoOrThree) {

                    // We don't allow custom discounts with terms with tax implications (radio button #2 or #3)
                    $invoiceId = $transaction->getRecordId();
                    Globals::$g->gErr->addIAError(
                        'SL-1300', __FILE__ . ':' . __LINE__,
                        "Invoice {$invoiceId} cannot use the selected term.", ['INVOICEID' => $invoiceId],
                        "", [],
                        "Select a different term or remove the invoice."
                    );
                    $ok = false;
                }
            }
        }

        return $ok;
    }

    /**
     * @param PaymentRequest   $request
     * @param PaymentDetailDTO $paymentDetail
     * @param array            $discountTypeMap
     *
     * @return bool
     * @throws IAException
     */
    protected function validateAndSetDiscountDetails(PaymentRequest $request, PaymentDetailDTO $paymentDetail, array &$discountTypeMap): bool
    {
        $discountDate = $paymentDetail->getDiscountDate();
        $totalDiscountToApply = $paymentDetail->getTrxTotalDiscountToApply() ?? 0;

        // if the discount date and discount amount is empty then no need to validate further
        if (empty($discountDate) && empty($totalDiscountToApply)) {
            return true;
        }

        // user should not pass the discount date or trx_discountamount amount and discount to apply together
        if (!empty($discountDate) && !empty($totalDiscountToApply)) {
            $errorMsg = "You can provide either a DISCOUNTDATE or a TRX_TOTALDISCOUNTTOAPPLY, but not both.";
            throw new IAException($errorMsg, 'SL-1269');
        }

        $transactionKey = $paymentDetail->getTransactionKey();
        // if transaction key is not set then return
        if (empty($transactionKey)) {
            logToFileError("No transaction key found in pymtdetail for discount validation.");
            return true;
        }

        $transaction = $request->getTransactions()[$transactionKey] ?? null;
        if (empty($transaction) || !$transaction instanceof PRRecordDTO) {
            logToFileError("No transaction details found while discount.");
            return true;
        }

        if ($totalDiscountToApply > 0) {
            return $this->handleCustomDiscount($request, $transaction, $discountTypeMap, $totalDiscountToApply);
        } else if (!empty($discountDate)) {
            return $this->handleTermDiscount($request, $transaction, $discountTypeMap, $discountDate);
        }

        return true;
    }

    /**
     * @param PaymentRequest $request
     * @param PRRecordDTO    $transaction
     * @param array          $discountTypeMap
     * @param float          $totalDiscountToApply
     *
     * @return bool
     * @throws IAException
     */
    private function handleCustomDiscount(PaymentRequest $request, PRRecordDTO $transaction, array &$discountTypeMap, float $totalDiscountToApply): bool
    {
        if ($transaction->getTxnIsVatEnabled() && !$this->validateLineDiscountTerm($transaction)) {
            return false;
        }

        // if the user define discount is not allowed but still request has discount to apply then throw error
        if ( !$this->isUserDefinedDiscountAllowed() && !empty($totalDiscountToApply)) {
            $errorMsg = "Custom discounts are not currently enabled. Either submit the transaction without a TRX_TOTALDISCOUNTTOAPPLY value or enable custom discounts and try again.";
            throw new IAException($errorMsg, 'SL-1270');
        }

        $paymentDTO = $request->getPaymentDTO();
        $discountRequestMap = $paymentDTO->getDiscountRequestMap();
        $transactionKey = $transaction->getRecordNo();
        $existingDiscountTypeMap = $discountTypeMap[$transactionKey] ?? [];
        $currentKey = PaymentUtils::DISCOUNT_CUSTOM;
        $conflictingKey = PaymentUtils::DISCOUNT_TERM;

        // As of now only Invoice is supported for user entered discount
        $supportedRecordType = [PaymentUtils::INVOICE_RECTYPE];
        if (!in_array($transaction->getRecordType(), $supportedRecordType)) {
            throw new IAException("This transaction type does not support the discount parameter you provided.", 'SL-1271');
        }

        $this->validateDiscountConflict(
            $existingDiscountTypeMap, $totalDiscountToApply, 'TRX_TOTALDISCOUNTTOAPPLY',
            $currentKey, $conflictingKey, 'SL-1272'
        );
        // set the type map
        $discountTypeMap[$transactionKey][$currentKey] = $totalDiscountToApply;

        // Skip processing if the basic validation is done and discountRequestMap is already created
        if (array_key_exists($transactionKey, $discountRequestMap)) {
            return true;
        }

        $paymentAmount = PaymentUtils::getTotalTxnPaymentAmount($paymentDTO->getPaymentDetailList(), $transactionKey, true);
        $totalPaymentAmount = ibcadd($totalDiscountToApply, $paymentAmount);
        // validate total payment amount with discount should not exceed the total due
        if ($totalPaymentAmount > $transaction->getTrxTotalDue()) {
            $desc1Log = 'The payment amount is greater than the total due for the bill ' . $transaction->getRecordId();
            $desc1PHs = [ 'TRANSACTION_GETRECORDID' => $transaction->getRecordId() ];
            throw IAException::newIAException('SL-0329', $desc1Log, $desc1PHs);
        }

        return $this->buildDiscountRequestMap(
            $paymentDTO, $transactionKey, $this->getTxnPaymentDate($request),
            $totalDiscountToApply, $currentKey
        );
    }

    /**
     * @param PaymentRequest $request
     * @param PRRecordDTO    $transaction
     * @param array          $discountTypeMap
     * @param string         $discountDate
     *
     * @return bool
     * @throws IAException
     */
    private function handleTermDiscount(PaymentRequest $request, PRRecordDTO $transaction, array &$discountTypeMap, string $discountDate): bool
    {
        $paymentDTO = $request->getPaymentDTO();
        $discountRequestMap = $paymentDTO->getDiscountRequestMap();
        $transactionKey = $transaction->getRecordNo();
        $existingDiscountTypeMap = $discountTypeMap[$transactionKey] ?? [];
        $currentKey = PaymentUtils::DISCOUNT_TERM;
        $conflictingKey = PaymentUtils::DISCOUNT_CUSTOM;

        $this->validateDiscountConflict(
            $existingDiscountTypeMap, $discountDate, 'discount date',
            $currentKey, $conflictingKey, 'SL-1273'
        );
        // set the type map
        $discountTypeMap[$transactionKey][$currentKey] = $discountDate;

        // Skip processing if the basic validation is done and discountRequestMap is already created
        if (array_key_exists($transactionKey, $discountRequestMap)) {
            return true;
        }

        if (!empty($transaction->getTrxTotalDiscountApplied()) && $this->isUserDefinedDiscountAllowed()) {
            throw new IAException("You cannot apply a term discount to a transaction where a custom discount has already been applied.", 'SL-1275');
        }

        if (empty($transaction->getTermKey())) {
            logToFileError("No term associated with the transaction key - '" . $transactionKey . "'");
            return true;
        }

        if ($this->isPartiallyPaidTrxOnVatTermDiscount($transaction)) {
            $recordid = $transaction->getRecordId();
            $msg = 'You cannot apply a term discount on the partially paid transaction ' . $recordid . '.';
            throw IAException::newIAException('SL-1280', $msg, ['TRANSACTION_RECORDID' => $recordid]);
        }

        if ($this->isCreditAppliedAndVatDiscount($paymentDTO, $transaction)) {
            $recordid = $transaction->getRecordId();
            $msg = 'You cannot apply a term discount to invoice ' . $recordid . ' because it has a credit.';
            throw IAException::newIAException('SL-1281', $msg, ['TRANSACTION_RECORDID' => $recordid]);
        }

        $paymentAmount = PaymentUtils::getTotalTxnPaymentAmount($paymentDTO->getPaymentDetailList(), $transactionKey, true);
        $discountAmount = SubledgerPymtHelper::calculateDiscountAmount($transaction, $discountDate);

        if ($paymentDTO->getIsBalanceForward() && $paymentAmount == 0) {
            $discountAmount = $transaction->getTotalDue() ?: $transaction->getTrxTotalDue();
        }

        if (empty($discountAmount)) {
            logToFileError("For Term Discount - amount is empty for transaction key - '" . $transactionKey . "'");
            return true;
        }

        $totalPaymentAmount = ibcadd($discountAmount, $paymentAmount);

        // TERM Discount cannot be applied for Partial payment.
        $trxNegTotalDue = $transaction->getTrxNegTotalDue();
        $totalEntered  = ibcsub($transaction->getTrxTotalEntered(), $transaction->getTrxTotalRetained());
        $totalPaid      = ibcadd($totalPaymentAmount, $transaction->getTrxTotalPaid());

        // if inline credit exists
        if(isset($trxNegTotalDue)) {
            $totalEntered = ibcsub($totalEntered, $trxNegTotalDue);
        }

        if($totalPaid != $totalEntered) {
            logToFileError('SL-0246 Discount amount cannot be applied for partial payment of the bill ' . $transactionKey);
            return true;
        }
        // validate the total payment amount after applying the discounts
        // if the total amount exceeds the total due after applying the discount then throw error
        if($totalPaymentAmount != $transaction->getTrxTotalDue()) {
            logToFileError('SL-0329 The payment amount is greater than the total due for the bill ' . $transactionKey);
            return true;
        }

        return $this->buildDiscountRequestMap(
            $paymentDTO, $transactionKey, $discountDate, $discountAmount, $currentKey
        );
    }

    /**
     * Validates if there is a conflict between existing and new discount values.
     *
     * @param array  $existingDiscount
     * @param mixed  $newValue
     * @param string $type
     * @param string $currentKey
     * @param string $conflictingKey
     * @param string $conflictErrorCode
     * @throws IAException
     */
    private function validateDiscountConflict(
        array $existingDiscount, mixed $newValue, string $type, string $currentKey,
        string $conflictingKey, string $conflictErrorCode
    ): void
    {
        // if nothing is set, return
        if (empty($existingDiscount)) {
            return;
        }
        if (!empty($existingDiscount[$currentKey]) && $existingDiscount[$currentKey] !== $newValue) {
            throw new IAException("Enter the same $type for all the lines for a transaction, then try again.", $conflictErrorCode);
        }

        if (!empty($existingDiscount[$conflictingKey])) {
            throw new IAException("Both discount term and discount date cannot be used together.", 'SL-1274');
        }
        $existingDiscount[$currentKey] = $newValue;
    }

    /**
     * @param PaymentDTO $paymentDTO
     * @param string     $transactionKey
     * @param string     $discountApplyDate
     * @param float      $discountAmount
     * @param string     $discountType
     *
     * @return bool
     */
    private function buildDiscountRequestMap(
        PaymentDTO $paymentDTO, string $transactionKey, string $discountApplyDate, float $discountAmount, string $discountType
    ): bool
    {
        $discountRequestMap = $paymentDTO->getDiscountRequestMap();
        $discountRequestMap[$transactionKey] = [
            'DATE' => $discountApplyDate,
            'AMOUNT' => $discountAmount,
            'TYPE' => $discountType
        ];
        $paymentDTO->setDiscountRequestMap($discountRequestMap);
        return true;
    }

    /**
     * @param PaymentDetailDTO $paymentDetail
     * @param PaymentRequest $request
     * @return bool
     */
    protected  function validateTotalDueForInlineKey(PaymentDetailDTO $paymentDetail, PaymentRequest $request): bool
    {
        return true;
    }

    /**
     * @param string $type
     * @return array
     *
     */
    protected function setErrorMessage(/** @noinspection PhpUndefinedVariableInspection */ string $type): array
    {
        return [];
    }

    /**
     * @param PRRecordDTO $transaction
     * @return bool
     */
    private function isPartiallyPaidTrxOnVatTermDiscount(PRRecordDTO $transaction) : bool
    {
        $termKey = $transaction->getTermKey();
        if (!empty($termKey) && $transaction->getState() === 'Partially Paid' && $transaction->getTxnIsVatEnabled()) {
            $term = SubledgerPymtHelper::getTermvaluesForBillsByTerm($termKey);
            $discountType = $term['DISCCALCON'] ?? null;
            if ($discountType === 'T' || $discountType === 'V') {
                return true;
            }
        }
        return false;
    }

    /**
     * Find out whether vat term discount and credit entries both exist for a transaction in the payment details
     * @param PaymentDTO $paymentDTO
     * @param PRRecordDTO $transaction
     * @return bool
     */
    private function isCreditAppliedAndVatDiscount(PaymentDTO $paymentDTO, PRRecordDTO $transaction) : bool
    {
        $termKey = $transaction->getTermKey();
        if (!empty($termKey) && $transaction->getTxnIsVatEnabled()) {
            $term = SubledgerPymtHelper::getTermvaluesForBillsByTerm($termKey);
            $discountType = $term['DISCCALCON'] ?? null;
            if ($discountType === 'T' || $discountType === 'V') {
                $trxRecordKey = $transaction->getRecordNo();
                $paymentDetails = $paymentDTO->getPaymentDetailList();
                foreach ($paymentDetails as $paymentDetail) {
                    if (!empty($paymentDetail)) {
                        if ($paymentDetail->getTransactionKey() !== $trxRecordKey) {
                            continue;
                        }
                        //Find credit by checking credit transaction keys
                        if (!empty($paymentDetail->getAdvanceKey()) ||     //advance
                            !empty($paymentDetail->getInlineKey()) ||    //inline credit
                            !empty($paymentDetail->getNegativeBillInvKey()) ||  //negative credit
                            !empty($paymentDetail->getAdjustmentKey()) ||      //adjustment
                            !empty($paymentDetail->getOverpaymentKey())) {    //overpayment
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }
}