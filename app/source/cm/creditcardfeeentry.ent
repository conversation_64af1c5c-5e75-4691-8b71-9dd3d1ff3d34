<?php

/**
 * Entity for the Credit Card Fee Entry object
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Intacct Corporation All, Rights Reserved
 */
$kSchemas['creditcardfeeentry'] = array(
    'children' => array(
        'creditcardfee' => array(
            'fkey' => 'recordkey', 'invfkey' => 'record#', 'table' => 'prrecordmst'
        ),
        'department' => array(
            'fkey' => 'dept#', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'department'
        ),
        'location' => array(
            'fkey' => 'location#', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'location'
        ),
        'glaccount' => array(
            'fkey' => 'accountkey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'baseaccount'
        ),
        'apaccountlabel' => array(
            'fkey' => 'accountlabelkey', 'invfkey' => 'record#', 'join' => 'outer', 'table' => 'accountlabel'
        ),
        'detail' => array(
            'fkey' => 'taxdetail#', 'invfkey' => 'record#', 'table' => 'taxdetailmst', 'join' => 'outer',
        ),
    ),
    'object' => array(
        'RECORDNO',
        'RECORDKEY',
        'RECORDTYPE',
        'ACCOUNTKEY',
        'ACCOUNTNO',
        'ACCOUNTTITLE',
        'ACCOUNTLABELKEY',
        'ACCOUNTLABEL',
        'AMOUNT',
        'TRX_AMOUNT',
        'DEPT#',
        'DEPARTMENTID',
        'DEPARTMENTNAME',
        'LOCATION#',
        'LOCATIONID',
        'LOCATIONNAME',
        'DESCRIPTION',
        'LINEITEM',
        'LINE_NO',
        'CURRENCY',
        'BASECURR',
        'BASELOCATION',
        'STATUS',
        'TOTALPAID',
        'TRX_TOTALPAID',
        'WHENCREATED',
        'WHENMODIFIED',
        'CREATEDBY',
        'MODIFIEDBY',
        'DEPARTMENTKEY',
        'LOCATIONKEY',
        'ISTAX',
        'DETAILKEY',
        'TAXRATE',
        'PARENTENTRY',
        'DETAILID',
        'EXCH_RATE_DATE',
        'EXCH_RATE_TYPE_ID',
        'EXCHANGE_RATE'
    ),
    'schema' => array(
        'RECORDNO' => 'record#',
        'RECORDKEY' => 'recordkey',
        'RECORDTYPE' => 'creditcardfee.recordtype',
        'ACCOUNTKEY' => 'accountkey',
        'ACCOUNTNO' => 'glaccount.acct_no',
        'ACCOUNTTITLE' => 'glaccount.title',
        'ACCOUNTLABELKEY' => 'accountlabelkey',
        'ACCOUNTLABEL' => 'apaccountlabel.label',
        'AMOUNT' => 'amount',
        'TRX_AMOUNT' => 'trx_amount',
        'DEPT#' => 'dept#',
        'DEPARTMENTID' => 'department.dept_no',
        'DEPARTMENTNAME' => 'department.title',
        'LOCATION#' => 'location#',
        'LOCATIONID' => 'location.location_no',
        'LOCATIONNAME' => 'location.name',
        'DESCRIPTION' => 'description',
        'LINEITEM' => 'lineitem',
        'LINE_NO' => 'line_no',
        'CURRENCY' => 'currency',
        'BASECURR' => 'basecurr',
        'BASELOCATION' => 'baselocation',
        'STATUS' => 'status',
        'TOTALPAID' => 'totalpaid',
        'TRX_TOTALPAID' => 'trx_totalpaid',
        'WHENCREATED' => 'whencreated',
        'WHENMODIFIED' => 'whenmodified',
        'CREATEDBY' => 'createdby',
        'MODIFIEDBY' => 'modifiedby',
        'DEPARTMENTKEY' => 'dept#',
        'LOCATIONKEY' => 'location#',
        'EXCH_RATE_TYPE_ID' => 'exch_rate_type_id',
        'EXCH_RATE_DATE' => 'exch_rate_date',
        'EXCHANGE_RATE' => 'exchange_rate',
        'DETAILKEY' => 'taxdetail#',
        'ISTAX' => 'istax',
        'DETAILID' => 'detail.detailid',
        'TAXRATE' => 'detail.value',
        'PARENTENTRY' => 'parententry',
    ),
    'sqldomarkup' => true,
    'sqlmarkupfields' => array(
        'WHENCREATED',
        'WHENMODIFIED'
    ),
    'publish' => array(
        'ACCOUNTNO',
        'ACCOUNTTITLE',
        'ACCOUNTLABEL',
        'AMOUNT',
        'TRX_AMOUNT',
        'DEPARTMENTID',
        'DEPARTMENTNAME',
        'LOCATIONID',
        'LOCATIONNAME',
        'DESCRIPTION',
        'CURRENCY',
        'BASECURR',
        'TOTALPAID',
        'TRX_TOTALPAID',
        'WHENCREATED',
        'WHENMODIFIED',
        'DETAILID',
        'TAXRATE',
    ),
    'nexus' => array(
        'creditcardfee' => array(
            'object' => 'creditcardfee',
            'relation' => ONE2MANY,
            'field' => 'prrecordkey'
        ),
        'department' => array(
            'object' => 'department',
            'relation' => MANY2ONE,
            'field' => 'departmentid'
        ),
        'location' => array(
            'object' => 'location',
            'relation' => MANY2ONE,
            'field' => 'locationid'
        ),
        'glaccount' => array(
            'object' => 'glaccount',
            'relation' => MANY2ONE,
            'field' => 'accountno'
        ),
        'apaccountlabel' => array(
            'object' => 'apaccountlabel',
            'relation' => MANY2ONE,
            'field' => 'accountlabel'
        )
    ),
    'fieldinfo' => array(
        $gRecordNoHiddenFieldInfo,
        array(
            'path' => 'RECORDKEY',
            'fullname' => 'IA.PARENT_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'hidden' => true,
            'id' => 1
        ),
        array(
            'path' => 'ACCOUNTKEY',
            'fullname' => 'IA.ACCOUNT_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'derived' => true,
            'hidden' => true,
            'id' => 2
        ),
        array(
            'path' => 'ACCOUNTNO',
            'fullname' => 'IA.ACCOUNT',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'pickentity' => 'glaccountpick',
                'entity' => 'glaccount'
            ),
            'behavesLikeDimension' => true,
            'standard' => true,
            'gridPath' => 'ITEMS',
            'id' => 3
        ),
        array(
            'path' => 'ACCOUNTTITLE',
            'fullname' => 'IA.ACCOUNT_TITLE',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 80,
            ),
            'id' => 4
        ),
        array(
            'path' => 'ACCOUNTLABELKEY',
            'fullname' => 'IA.ACCOUNT_LABEL_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'derived' => true,
            'hidden' => true,
            'id' => 5
        ),
        array(
            'path' => 'ACCOUNTLABEL',
            'fullname' => 'IA.ACCOUNT_LABEL',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'noSplit' => true,
                'entity' => 'apaccountlabel',
                'pickfield' => array('ACCOUNTLABEL', 'GLACCOUNTNO', 'GLACCOUNTTITLE')
            ),
            'id' => 6
        ),
        array(
            'path' => 'AMOUNT',
            'fullname' => 'IA.BASE_AMOUNT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18
            ),
            'hasTotal' => true,
            'hidden' => true,
            'readonly' => true,
            'id' => 7
        ),
        array(
            'path' => 'TRX_AMOUNT',
            'fullname' => 'IA.TRANSACTION_AMOUNT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18
            ),
            'hasTotal' => true,
            'required' => true,
            'id' => 8
        ),
        array(
            'path' => 'DEPT#',
            'fullname' => 'IA.DEPARTMENT_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'renameable' => true,
            'derived' => true,
            'hidden' => true,
            'idw' => false,
            'id' => 9
        ),
        array(
            'path' => 'DEPARTMENTID',
            'fullname' => 'IA.DEPARTMENT_ID',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'department',
                'pickentity' => 'departmentpick'
            ),
            'renameable' => true,
            'standard' => true,
            'isDimension' => true,
            'autofillrelated' => false,
            'id' => 10
        ),
        array(
            'path' => 'DEPARTMENTNAME',
            'fullname' => 'IA.DEPARTMENT_NAME',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 40
            ),
            'id' => 11
        ),
        array(
            'path' => 'LOCATION#',
            'fullname' => 'IA.LOCATION_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'renameable' => true,
            'derived' => true,
            'hidden' => true,
            'idw' => false,
            'id' => 12
        ),
        array(
            'path' => 'LOCATIONID',
            'fullname' => 'IA.LOCATION_ID',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'location',
                'pickentity' => 'locationpick'
            ),
            'renameable' => true,
            'standard' => true,
            'isDimension' => true,
            'autofillrelated' => false,
            'id' => 13
        ),
        array(
            'path' => 'LOCATIONNAME',
            'fullname' => 'IA.LOCATION_NAME',
            'type' => array(
                'type' => 'text',
                'ptype' => 'text',
                'maxlength' => 40
            ),
            'id' => 14
        ),
        array(
            'path' => 'DESCRIPTION',
            'fullname' => 'IA.DESCRIPTION',
            'type' => array(
                'type' => 'text',
                'ptype' => 'textarea',
                'maxlength' => 1000
            ),
            'id' => 15
        ),
        array(
            'path' => 'LINEITEM',
            'fullname' => 'IA.LINE_ITEM',
            'type' => array(
                'type' => 'text',
                'ptype' => 'enum',
                'validlabels' => array('IA.T', 'IA.F'),
                'validvalues' => array('T', 'F')
            ),
            'hidden' => true,
            'id' => 16
        ),
        array(
            'path' => 'LINE_NO',
            'fullname' => 'IA.LINE_NO',
            'type' => array(
                'ptype' => 'text',
                'type' => 'text',
                'maxlength' => 4,
                'format' => $gLineNoFormat
            ),
            'readonly' => true,
            'derived' => true,
            'id' => 17
        ),
        array(
            'path' => 'BASECURR',
            'fullname' => 'IA.BASE_CURRENCY',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'trxcurrencies'
            ),
            'readonly' => true,
            'required' => true,
            'id' => 19,
        ),
        array(
            'path' => 'CURRENCY',
            'fullname' => 'IA.TRANSACTION_CURRENCY',
            'type' => array(
                'type' => 'text',
                'ptype' => 'ptr',
                'entity' => 'trxcurrencies'
            ),
            'readonly' => true,
            'required' => true,
            'id' => 18,
        ),
        array(
            'path' => 'BASELOCATION',
            'fullname' => 'IA.BASE_LOCATION_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'renameable' => true,
            'hidden' => true,
            'id' => 20
        ),
        $gStatusFieldInfo,
        array(
            'path' => 'TOTALPAID',
            'fullname' => 'IA.TOTAL_PAID',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'id' => 21
        ),
        array(
            'path' => 'TRX_TOTALPAID',
            'fullname' => 'IA.TOTAL_TRANSACTION_PAID',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat
            ),
            'id' => 22
        ),
        array(
            'path' => 'DEPARTMENTKEY',
            'fullname' => 'IA.DEPARTMENT_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'renameable' => true,
            'derived' => true,
            'hidden' => true,
            'id' => 23
        ),
        array(
            'path' => 'LOCATIONKEY',
            'fullname' => 'IA.LOCATION_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'renameable' => true,
            'derived' => true,
            'hidden' => true,
            'id' => 24
        ),
        array(
            'path' => 'EXCH_RATE_TYPE_ID',
            'fullname' => 'IA.EXCHANGE_RATE_TYPE',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'ptr',
                'entity' => 'exchangeratetypesall'
            ),
            'hidden' => true,
            'id' => 25
        ),
        array(
            'path' => 'ISTAX',
            'fullname' => 'IA.IS_TAX',
            'type' => $gBooleanType,
            'default' => 'false',
            'readonly' => true,
            //'id' => 26,  //we don;t want to expose this via INSPECT sinec this is an internal only field
        ),
        array(
            'fullname' => 'IA.TAX_DETAIL',
            'type' => array(
                'ptype' => 'ptr',
                'type' => 'ptr',
                'entity' => 'potaxdetail',
                'pickfield' => array(
                    'DETAILID', 'DESCRIPTION', 'VALUE', 'STATUS'
                ),
            ),
            'noedit' => true,
            'nonew' => true,
            'desc' => 'IA.TAX_DETAIL_UNIQUE_ID',
            'path' => 'DETAILID',
            'id' => 26,
        ),
        array(
            'path' => 'DETAILKEY',
            'fullname' => 'IA.TAX_DETAIL_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'derived' => true,
            'hidden' => true,
        ),
        array(
            'path' => 'TRX_TAX',
            'fullname' => 'IA.TRANSACTION_TAX',
            'desc' => 'IA.TRANSACTION_TAX_AMOUNT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'format' => $gDecimalFormat,
                'size' => 22,
                'maxlength' => 15,
            ),
            'hasTotal' => true,
            'id' => 27
        ),
        array(
            'path' => 'TAX',
            'fullname' => 'IA.BASE_TAX',
            'desc' => 'IA.BASE_TAX_AMOUNT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'format' => $gDecimalFormat,
                'size' => 22,
                'maxlength' => 15,
            ),
            'hasTotal' => true,
            'readonly' => true,
            'id' => 28
        ),
        array(
            'fullname' => 'IA.RATE',
            'type' => array(
                'ptype' => 'decimal',
                'type' => 'decimal',
                'maxlength' => 12,
                'size' => 12,
            ),
            'precision' => 3,
            'desc' => 'IA.TAX_DETAIL_RATE_IN_PERCENT',
            'path' => 'TAXRATE',
            'id' => 29,
        ),
        array(
            'path' => 'TOTALBASEAMOUNT',
            'fullname' => 'IA.BASE_TOTAL',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18
            ),
            'readonly' => true,
            'id' => 30
        ),
        array(
            'path' => 'MULTIPLETAXES',
            'fullname' => 'IA.MULTIPLE_TAXES_ON_LINE',
            'type' => $gBooleanType,
            'default' => 'false',
        ),
        array(
            'path' => 'TOTALTRXAMOUNT',
            'fullname' => 'IA.TRANSACTION_TOTAL',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18
            ),
            'readonly' => true,
            'id' => 31
        ),
        array(
            'path' => 'BASETAXAMOUNT',
            'fullname' => 'IA.BASE_TAX_AMOUNT',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'currency',
                'maxlength' => 14,
                'format' => $gDecimalFormat,
                'size' => 18
            ),
            'readonly' => true,
            'id' => 32
        ),
        array(
            'path' => 'PARENTENTRY',
            'fullname' => 'IA.PARENT_ENTRY_KEY',
            'type' => array(
                'type' => 'integer',
                'ptype' => 'integer',
                'maxlength' => 8,
                'format' => $gRecordNoFormat
            ),
            'hidden' => true,
            'readonly' => true,
        ),
        array(
            'path' => 'EXCH_RATE_DATE',
            'fullname' => 'IA.EXCHANGE_RATE_DATE',
            'type' => array(
                'type' => 'date',
                'ptype' => 'date',
                'format' => $gDateFormat
            ),
            'id' => 33
        ),
        array(
            'path' => 'EXCHANGE_RATE',
            'fullname' => 'IA.EXCHANGE_RATE',
            'type' => array(
                'type' => 'decimal',
                'ptype' => 'decimal',
                'maxlength' => 12
            ),
            'precision' => 12,
            'noformat' => true,
            'readonly' => true,
            'rpdMeasure' => false,
            'id' => 34
        ),
        $gWhenCreatedFieldInfo,
        $gWhenModifiedFieldInfo,
        $gCreatedByFieldInfo,
        $gModifiedByFieldInfo,
        array (
            'path'      =>  'RECORDTYPE',
            'fullname'  =>  'IA.RECORD_TYPE',
            'desc'      =>  'IA.RECORD_TYPE',
            'type'      =>  array (
                'ptype'     =>  'text',
                'type'      =>  'text',
            ),
            'id' => 200
        ),
    ),
    'pairedFields' => array(
        'ACCOUNTNO' => 'ACCOUNTTITLE',
        'DEPARTMENTID' => 'DEPARTMENTNAME',
        'LOCATIONID' => 'LOCATIONNAME'
    ),
    'dbsorts' => array(array('LINE_NO')),
    'dbfilters' => array(
        array('creditcardfeeentry.lineitem', '=', 'T'),
        array(
            'creditcardfee.recordtype',
            '=',
            SubLedgerTxnManager::CREDITCARDFEE_RECTYPE,
        ),
        array( 'creditcardfeeentry.istax', '!=', 'T' ),
    ),
    'printas' => 'IA.CREDIT_CARD_CHARGES_AND_OTHER_FEES_ENTRY',
    'pluralprintas' => 'IA.CREDIT_CARD_CHARGES_AND_OTHER_FEES_ENTRYS',
    'table' => 'prentry',
    'updatetable' => 'prentrymst',
    'parententity' => 'creditcardfee',
    'module' => 'cm',
    'vid' => 'RECORDNO',
    'hasdimensions' => true,
    'auditcolumns' => true,
    'cachecustomdimensions' => true,
    'followgldimensions' => true,
    'customerp' => array(
        'SLTypes' => array(
            CUSTOMERP_SMARTLINKFETCH,
            CUSTOMERP_SMARTLINKCLICK,
            CUSTOMERP_SMARTLINKVALIDATE,
            CUSTOMERP_SMARTLINKWORKFLOW
        ),
        'SLEvents' => array(
            CUSTOMERP_EVENT_ADD,
            CUSTOMERP_EVENT_SET,
            CUSTOMERP_EVENT_ADDSET,
            CUSTOMERP_EVENT_CLICK
        ),
        'AllowCF' => true
    ),
    'api' => array(
        'GET_BY_GET' => true,
        'PERMISSION_CREATE' => 'NONE',
        'PERMISSION_UPDATE' => 'NONE',
        'PERMISSION_DELETE' => 'NONE',
    ),
    'allowDDS' => false,
    'description' => 'IA.DETAIL_AND_HEADER_INFORMATION_FOR_CREDIT_CARD_FEES',
);
// data transfer owned object will not be saved to database
$kSchemas['creditcardfeeentry']['ownedobjects'][] = [ 'entity'  => 'creditcardfeetaxentry',
                                                      'path'    => 'TAXENTRIES', 'fkey' => 'PARENTENTRY',
                                                      'invfkey' => 'RECORDNO', ];

