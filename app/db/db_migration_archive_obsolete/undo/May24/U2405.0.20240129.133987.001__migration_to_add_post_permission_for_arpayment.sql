--liquibase formatted sql
--changeset rajesh.kumar:U2405.0.20240129.133987.001 runOnChange:false logicalFilePath:U2405.0.20240129.133987.001__migration_to_add_post_permission_for_arpayment.sql

------------------------------------------------------------------------------------------------------------------------
----------------  DDL -- ticket  : Remove POST permission for AR Payment -----------------
------------------------------------------------------------------------------------------------------------------------
BEGIN
DBMS_APPLICATION_INFO.SET_CLIENT_INFO('F');
END;
/

DECLARE
    managepaymentpolicyid   iapolicy.record#%TYPE;
BEGIN
    --- select the manage payment record#
    SELECT record# INTO managepaymentpolicyid
        FROM iapolicy
        WHERE name = 'Manage Payments' AND module = 'ar';
    ----------------------------------------------------------------------------------------------------------
    ---- User based permission changes ------------------
    ----------------------------------------------------------------------------------------------------------
    -- concat post permission for manage payment which user have add permission
    UPDATE policyassignment p1
        SET p1.policyval = REPLACE(policyval, '|post', '')
    WHERE
        p1.policykey = managepaymentpolicyid
        AND p1.policyval like '%|post%';

    ----------------------------------------------------------------------------------------------------------
    ---- Role based permission changes ------------------
    ----------------------------------------------------------------------------------------------------------
    UPDATE rolepolicyassignment rp1
        SET rp1.policyval = REPLACE(rp1.policyval, '|post', '')
    WHERE
        rp1.policykey = managepaymentpolicyid
        AND rp1.policyval like '%|post%';

    --- update the perm_cache_valid to F for all the user that have manage payment permission
    UPDATE userinfo u
    SET u.perm_cache_valid = 'F'
    WHERE
        EXISTS (
            SELECT 1
            FROM policyassignment pa
            WHERE pa.cny# = u.cny#
                AND pa.user_role_key = u.record#
                AND pa.policykey = managepaymentpolicyid
        )
        OR
        EXISTS (
            SELECT 1
            FROM rolepolicyassignment rpa, ugroles ugr
            WHERE rpa.cny# = u.cny#
                AND ugr.cny# = u.cny#
                AND rpa.rolekey = ugr.rolekey
                AND ugr.type = 'U'
                AND ugr.u_o_gkey = u.record#
                AND rpa.policykey = managepaymentpolicyid
        );
END;
/

BEGIN
DBMS_APPLICATION_INFO.SET_CLIENT_INFO('T');
END;
/
