<?php
/**
 * Class SforceMessagePublisher
 *
 * Contains static methods publishing synchronization messages to IMS
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2016 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class SforceMessagePublisher
{

    const SALESFORCE_DISPATCHER_ENABLE = 'SALESFORCE_DISPATCHER_ENABLE';
    /**
     * Generic method for publishing the sync event.
     *
     * @param string[] $messageParams
     * @param string   $topic
     *
     * @return bool
     */
    private static function publishSyncEvent($messageParams, $topic = '')
    {
        if ( empty($topic) ) {
            $topic = SforceSetupMessageTopic::RESYNC;
        }
        $publisher = new ims_publish_1(
            IMS_MODE_NONBLOCKING, IMS_PROCESS_REMOTE, IMS_MODE_QUEUED);

        $ok = $publisher->PublishMsg(
            IntacctManagedObjectSyncService::SYNC_MESSAGE_SENDER,
            IntacctManagedObjectSyncService::SYNC_MESSAGE_RECIPIENT,
            $topic,
            IMS_PRIORITY_DEFAULT,
            $messageParams
        );

        if (!$ok) {
            logStackTraceBrief();
        }

        return $ok;
    }

    /**
     * Publishes the message to synchronize the objects of the input array
     * with salesforce.
     *
     * @param IntacctSyncObjectNode[] $intacctObjects
     * @param string                  $logAction
     * @param string|null             $resultId
     * @param string $documentReaderKey
     *
     * @return bool
     * @throws Exception
     */
    public static function publishSyncObjects($intacctObjects, $logAction, $resultId = null, $documentReaderKey='')
    {
        $ok = true;
        if (!empty($intacctObjects)) {
            $serializedIntacctObjects = serialize($intacctObjects);
            $messageParams =
                [
                    IntacctManagedObjectSyncService::SYNC_OBJECTS_KEY => $serializedIntacctObjects,
                    IntacctManagedObjectSyncService::LOG_ACTION_KEY => $logAction,
                    IntacctManagedObjectSyncService::SYNC_MODE_KEY => SyncMode::SYNC_OBJECTS,
                    IntacctManagedObjectSyncService::SYNC_DOCUMENT_KEY => $documentReaderKey
                ];
            if ($resultId !== null) {
                $messageParams[IntacctManagedObjectSyncService::RESULT_ID_KEY] = $resultId;
            }
            $ok = self::publishSyncEvent($messageParams);
        }

        return $ok;
    }

    /**
     * Publishes the resync salesforce batch message.
     *
     * @param string $nextId
     * @param IntacctSyncObjectNode $intacctObjectNode
     * @param string $logAction
     * @param string $resultId
     * @param int $batchIndex
     * @param int $numberOfAttempts
     * @param string $key
     *
     * @return bool
     */
    public static function publishResyncSforceBatch($nextId, $intacctObjectNode, $logAction, $resultId, $batchIndex, $numberOfAttempts, $key)
    {
        $serializedIntacctObjectNode = '';
        if (isset($intacctObjectNode)) {
            $serializedIntacctObjectNode = serialize($intacctObjectNode);
        }
        $messageParams = [
            IntacctManagedObjectSyncService::BATCH_ID_KEY => $nextId,
            IntacctManagedObjectSyncService::LOG_ACTION_KEY => $logAction,
            IntacctManagedObjectSyncService::RESULT_ID_KEY => $resultId,
            IntacctManagedObjectSyncService::NUMBER_OF_ATTEMPTS_KEY => $numberOfAttempts,
            IntacctManagedObjectSyncService::BUTCH_INDEX_KEY => $batchIndex,
            IntacctManagedObjectSyncService::SYNC_MODE_KEY => SyncMode::SYNC_SFORCE_BATCH,
            IntacctManagedObjectSyncService::SYNC_OBJECT_NODE_KEY => $serializedIntacctObjectNode,
            IntacctManagedObjectSyncService::SYNC_DOCUMENT_KEY => $key
        ];

        $ok = self::publishSyncEvent($messageParams);

        return $ok;
    }

    /**
     * Schedules to delete ALL reference objects asynchronously
     *
     * @return bool
     */
    public static function publishDeleteOnUnsubscribe()
    {
        $setupManager =
            Globals::$g->gManagerFactory->getManager('salesforcesetup');
        $prefs = $setupManager->get('');
        $prefs['SFORCESTATUS'] = SforceConfigurationStatus::SFORCE_STATUS_DEINSTALLED;

        $publisher = new ims_publish_1(
            IMS_MODE_NONBLOCKING, IMS_PROCESS_REMOTE, IMS_MODE_QUEUED);

        $ok = $publisher->PublishMsg(
            IntacctManagedObjectSyncService::UNSUBSCRIBE_MESSAGE_SENDER,
            IntacctManagedObjectSyncService::SYNC_MESSAGE_RECIPIENT,
            SforceSetupMessageTopic::DELETE_REFERENCES_ON_UNSUBSCRIBE,
            IMS_PRIORITY_DEFAULT,
            $prefs
        );

        return $ok;
    }

    /**
     * Schedules to sync UDD record asynchronously
     *
     * @param string           $action
     * @param Pt_DataObjectDef $objDef
     * @param string|int       $key Type of key determines if we get the object (via ID)
     *
     * @return bool
     */
    public static function publishSyncEventbyUDD($action, $objDef, $key)
    {
        $ok = false;
        $topic = isl_strtoupper($action . '_' . 'UDD');

        // The isSubscriptionActive is relevant only for non-global entities
        $uddDefName = $objDef->getObjectDefName();
        $setupManager = Globals::$g->gManagerFactory->getManager('salesforcesetup');
        $isUDDSyncEnabled = $setupManager->GetPreference('SFORCEUDDSYNC');
        $subscriptionState = $isUDDSyncEnabled === 'true' && $setupManager->isSubscribedUDDbyName($uddDefName);

        if ( $subscriptionState ) {
            $ok = true;
            if (is_string($key) || is_int($key)) {
                // Retrieve the object via string|int key
                // $mgr = new Pt_EntityManager($objDef);
                // $subscriptionObject = $mgr->Get($key);

                $obj = Pt_DataObjectManager::getById($objDef, $key);
                if ($obj === null) {
                    throw new Exception("Custom object with $key ID cannot be found");
                }

                $subscriptionObject = $obj->getShortFieldMap();
                //Set Object definition name
                $subscriptionObject['DISPLAY_LABEL'] = $uddDefName;
            } else {
                throw new Exception("Unexpected type for parameter \$key (expected string, int or array).");
            }
            $syncMetric = new MetricSforceSingleRecordSyncSyncPackage();
            if (FeatureConfigManagerFactory::getInstance()->isFeatureEnabled(self::SALESFORCE_DISPATCHER_ENABLE)) {
                /** @var SforceQueueManager $sforceQueueObjectMgr */
                $sforceQueueObjectMgr = Globals::$g->gManagerFactory->getManager('sforcequeue');
                $details[SforceQueueManager::STATUS]=SforceQueueManager::SFORCE_QUEUED;
                $details[SforceQueueManager::SYNCEVENT]=IntacctSyncProcessorTrait::$syncEvent;
                $sfpackagequeue = [
                    SforceQueueManager::TOPIC => $topic,
                    SforceQueueManager::OBJECT => databaseStringCompress(json_encode($subscriptionObject)),
                    SforceQueueManager::DOCTYPE => '',
                    SforceQueueManager::TYPE => $sforceQueueObjectMgr->getType(),
                    SforceQueueManager::DETAILS =>json_encode($details),
                    SforceQueueManager::OBJECTRECID => $subscriptionObject['DISPLAY_LABEL'].' | '.$obj->getObjectDefId().' | '.$subscriptionObject['id'],
                ];
                $sforceQueueObjectMgr->addJobInQueue($sfpackagequeue);
                $syncMetric->setType(SforceQueueManager::SFORCE_DISPATCHER);
            } else {
                self::publishSyncEvent($subscriptionObject, $topic);
            }
            $syncMetric->setObject($uddDefName);
            $syncMetric->setAction($topic);
            $syncMetric->setId(WebServerInfo::get()->REQUESTID);
            $dateTime = new DateTime();
            $timeStamp = $dateTime->getTimestamp();
            $syncMetric->setWhenCreated(strftime('%Y-%m-%d %H:%M:%S', $timeStamp));
            $syncMetric->incrementCount();
            $syncMetric->publish();
        }
        return $ok;
    }
}