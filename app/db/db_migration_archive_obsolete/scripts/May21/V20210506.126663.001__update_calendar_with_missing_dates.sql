--liquibase formatted sql
--changeset ovidiu.moldovan:V20210506.126663.001 runOnChange:false logicalFilePath:V20210506.126663.001__update_calendar_with_missing_dates.sql

------------------------------------------------------------------------------------------------------------------------
-- Start Rally User Story Id - DE20258 : Script for calendar migration
------------------------------------------------------------------------------------------------------------------------
INSERT INTO calendar
With c AS (SELECT TO_DATE(TO_CHAR(sysdate - 10 * 365 + l, 'mm/dd/yyyy'), 'mm/dd/yyyy') c_date,
    CAST(EXTRACT(DAY FROM (sysdate - 10 * 365 + l)) AS INT) c_day,
    CAST(EXTRACT(MONTH FROM (sysdate - 10 * 365 + l)) AS INT) c_month,
    CAST(EXTRACT(YEAR FROM (sysdate - 10 * 365 + l)) AS INT) c_year
FROM (SELECT level l FROM dual CONNECT BY LEVEL <= (365 * 13) + 90) a)
SELECT * FROM c
WHERE NOT EXISTS(SELECT * FROM calendar c2 WHERE (c2.c_date = c.c_date))
/
------------------------------------------------------------------------------------------------------------------------
-- Start Rally User Story Id - DE20258 : Script for calendar migration
------------------------------------------------------------------------------------------------------------------------