<?php
/**
 * Structure to facilitate the handling of requests in the class SocketRequest
 */
class SocketRequestStruct
{
    /* @var string   $id         identifier of the request*/
    public $id       = null;
    /* @var resource $socket     socket resource handle */
    public $socket   = null;
    /* @var string   $host       remote host (including port) */
    public $host     = null;
    /* @var string   $reqData    full request to be sent to remote host */
    public $reqData  = null;
    /* @var int      $reqLen     length of the request (in bytes) */
    public $reqLen   = 0;
    /* @var int      $startTime  start time of this request in microseconds (after queued state) */
    public $startTime= 0;
    /* @var int      $elapsed    number of milliseconds elapsed for this request (when finished/errored/timedout) */
    public $elapsed  = 0;
    /* @var int      $bytesW     Number of bytes written of the request */
    public $bytesW   = 0;
    /* @var bool     $bWantHdrs  whether or not to separate headers from data */
    public $bWantHdrs= false;
    /* @var string   $respHdrs   response http headers (if requested) */
    public $respHdrs = '';
    /* @var string   $response   response from remote host after reading (includes http headers) */
    public $response = '';
    /* @var bool $respEOF */
    public $respEOF  = false;
    /* @var callback $respDone   callback function to call once the response is done (finished/expired/error) */
    public $respDone = null;
    /* @var callback $checkResp  callback function which checks the response data to determine if it is 'finished' */
    public $checkResp= null;
    /* @var int      $bytesR     Number of bytes read of the response */
    public $bytesR   = 0;
    /* @var array    $errorArr   error array about this request; usually from error_get_last() */
    public $errorArr = null;
    /* @var int  $state      current state (SocketRequest::STATE_*) */
    public $state    = null;

    /**
     * @param string    $id
     * @param string    $host
     * @param string    $reqData
     * @param int       $state
     * @param bool      $bWantHdrs
     * @param callable  $respDone
     * @param callable  $checkResp
     *
     * @throws Exception
     */
    public function __construct($id, $host, $reqData, $state, $bWantHdrs=false, $respDone=null, $checkResp=null)
    {
        $this->id        = $id;
        $this->host      = $host;
        $this->reqData   = $reqData;
        $this->state     = $state;
        $this->reqLen    = strlen($reqData);
        $this->bWantHdrs = $bWantHdrs;

        // Verify the callback function $respDone if non-null
        if ( $respDone != null && !is_callable($respDone) ) {
            throw new Exception("Response-finished parameter '\$respDone' is not a callable.");
        }
        $this->respDone     = $respDone;

        // Verify the callback function $checkResp if non-null
        if ( $checkResp != null && !is_callable($checkResp) ) {
            throw new Exception("Response-check parameter '\$checkResp' is not a callable.");
        }

        $this->checkResp = $checkResp;
    }

    /**
     * cleanup
     */
    public function __destruct()
    {
        // Just in case the socket wasn't closed (it should have been)
        if ( isset($this->socket) && is_resource($this->socket) ) {
            logToFileWarning(__METHOD__ . "; closing socket via destructor for request id '"
                . $this->id
                . "' (should be done in SocketRequest::closeSocket() instead)\n");
            fclose($this->socket);
        }
    }
}