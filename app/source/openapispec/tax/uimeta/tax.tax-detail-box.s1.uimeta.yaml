fields:
  id:
    uiType: integer
    uiLabel: IA.RECORD_NUMBER
  box:
    uiType: text
    uiLabel: IA.TAXDETAIL_BOX_ID
  calculationBasedOn:
    uiType: enum
    uiLabel: IA.BOX_TYPE
    enumsLabels:
      -
        label: IA.TAX_DETAIL_BOX_NET
        value: net
      -
        label: IA.TAX_DETAIL_BOX_TAX
        value: tax
      -
        label: IA.TAX_DETAIL_BOX_NET_AND_TAX
        value: both
      -
        label: IA.TAX_DETAIL_BOX_CALCULATION
        value: calculation
  label:
    uiType: text
    uiLabel: IA.BOX_LABEL
  txnTypeToInclude:
    uiType: enum
    uiLabel: IA.TRANSACTION_TYPE
    enumsLabels:
      -
        label: IA.TRANSACTIONS_DEB_CRED
        value: both
      -
        label: IA.TRANACTIONS_DEB
        value: debit
      -
        label: IA.TRANSACTIONS_CRED
        value: credit
  vatRegistration:
    uiType: enum
    uiLabel: IA.CUST_VEND_VAT_REG
    enumsLabels:
      -
        label: IA.VAT_REGISTERED_POSITIVE
        value: yes
      -
        label: IA.VAT_REGISTERED_NEGATIVE
        value: no
      -
        label: IA.VAT_REGISTERED_EITHER
        value: either
refs:
  displayOptions:
    uiLabel: IA.DISPLAY_OPTIONS
    fields:
      displayAbsoluteValue:
        uiType: boolean
        uiLabel: IA.ABSOLUTE
        enumsLabels:
          -
            label: IA.TRUE
            value: true
          -
            label: IA.FALSE
            value: false
      displayTruncatedValue:
        uiType: boolean
        uiLabel: IA.TRUNCATE
        enumsLabels:
          -
            label: IA.TRUE
            value: true
          -
            label: IA.FALSE
            value: false
      displayNegativeAsZero:
        uiType: boolean
        uiLabel: IA.ZERO_IF_NEG
        enumsLabels:
          -
            label: IA.TRUE
            value: true
          -
            label: IA.FALSE
            value: false
      displayRoundedValue:
        uiType: boolean
        uiLabel: IA.ROUND
        enumsLabels:
          -
            label: IA.TRUE
            value: true
          -
            label: IA.FALSE
            value: false
  taxSolution:
    fields:
      id:
        uiType: ptr
        uiLabel: IA.TAX_SOLUTION
