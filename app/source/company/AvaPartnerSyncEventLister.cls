<?php
/**
 * AvaPartnerSyncEventLister.cls
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013 Intacct Corporation -- All Rights Reserved
 */

import('NLister');

/**
 * Class PartnerSyncEventLister implements a lister for partner sync events, sorted descending by record#.
 * A 'Sync Now' button is added as import button.
 */
class AvaPartnerSyncEventLister extends PartnerSyncEventLister
{

    public function __construct()
    {
        //parent::__construct();
        // _partner and and _qrSyncAllUrl variables are
        // expected to have been set as arguments in the
        // original menu request. See so_2011.menu.
        /** @var Request $r */
        //$r = Request::$r;
        $this->partner = 'Avalara';

        parent::__construct();
    }

    /**
     * override constructor args of parent class
     *
     * @return array
     */
    protected function getConstructorArgs()
    {
        return array(
            // Abuse import to add 'sync now' button.
            'importtype' => 'avapartnersyncevent',
            'entity' => 'avapartnersyncevent',
            'title' => "IA.AVATAX_LOGS",
            'fields' => array(
                'RECORDNO',
                'WHENCREATED',
                'CREATEDBY',
                'STATE',
                //'OBJCOUNT',
                //'OBJERRCOUNT'
                'OBJECT',
                'OBJECTID',
            ),
            'sortcolumn' => 'RECORDNO:d',
            'disabledadd' => true,
            'id' => 'RECORDNO',
        );
    }
}