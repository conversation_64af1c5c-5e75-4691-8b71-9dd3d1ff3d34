<?
/**
 *    FILE: glbudgetmanager.cls
 *    AUTHOR: <PERSON>
 *    DESCRIPTION:
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */

require_once 'backend_budget.inc';

class BudgetHeaderManager extends EntityManager
{
    /* @var ReportingPeriodManager $reportingPerMgr */
    var  $reportingPerMgr;

    /**
     * @param array $params
     */
    function __construct($params = array())
    {
        parent::__construct($params);
        $this->reportingPerMgr = Globals::$g->gManagerFactory->getManager('reportingperiod');
    }

    /**
     * @param string        $ID
     * @param string[]|null $fields
     *
     * @return array|false
     */
    function Get($ID, $fields = null)
    {
        $obj = parent::get($ID);

        //Do the full detailed budget query for XML gateway case.
        /** @noinspection PhpUnusedLocalVariableInspection */
        $ret = $this->GetGLBudget($obj);

        return $obj;
    }

    /**
     * Avoid unnecessary query for Editor call.
     *
     * @param string $ID
     *
     * @return array|false
     */
    function getWithoutBudgetDetails($ID)
    {
        return parent::get($ID);
    }

    /**
     * @param array $obj
     *
     * @return bool
     */
    function GetGLBudget(&$obj) 
    {
        $query = "select :1 as BUDGETID, glaccount.ACCT_NO as ACCOUNTNO,
						 department.DEPT_NO as DEPTTITLE, location.LOCATION_NO as LOCTITLE, glbudgettype.NAME as NAME, glbudget.AMOUNT as AMOUNT
				  from	 glbudget glbudget, glaccount glaccount, location location, department department, 
						 glbudgettype glbudgettype
				  where  glbudget.budgetkey = :2 and glbudget.cny# = :3 and 
						 glaccount.record# = glbudget.ACCOUNT#	and glaccount.cny# = glbudget.cny# and
						 department.record# = glbudget.dept# and department.cny# = glbudget.cny# and
						 location.record# = glbudget.location# and location.cny# = glbudget.cny# and
					     glbudgettype.record# = glbudget.bud_type# and glbudgettype.cny# = glbudget.cny#
						 and glbudgettype.budgeting = :4
				 order by glaccount.acct_no, department.dept_no, location.location_no";

        $result = QueryResult(array($query, $obj['BUDGETID'], $obj['RECORDNO'], GetMyCompany(), 'T'));

        if (is_array($result)) {
            $obj['glbudgetitem'] = $result;
        }

        return true;
    }

    /**
     * Get the reporting periods for the budget budgetID from the DB.
     *
     * @param string $budgetID the budgetID
     *
     * @return array|bool Reporting periods from the query
     */
    private function getBudgetReportingPeriodsFromDb($budgetID)
    {
        $cny = GetMyCompany();
        $query = "select reportingperiod.name, reportingperiod.record#
                    from glbudgettype reportingperiod
                        where reportingperiod.record# in
                          (select distinct(bud_type#) from glbudget where
                           budgetkey = (select record# from budgetheader
                           where budgetid = :1 and cny# = :2)
                            and cny# = :2)
                           and reportingperiod.budgeting = 'T'
                           and cny# = :2
                        order by reportingperiod.start_date";
        return QueryResult(array($query,$budgetID,$cny));
    }

    /**
     * Get the reporting periods
     * @param string $budgetID
     * @return array
     */
    public function getBudgetReportingPeriodsValidValues($budgetID)
    {
        $validVals = array();
        $result = $this->getBudgetReportingPeriodsFromDb($budgetID);

        if (count($result) > 0) {
            $count = 0;
            foreach ($result as $repPeriodRec) {
                $validVals[$repPeriodRec['NAME']] = $repPeriodRec['RECORD#'];
                /** @noinspection OnlyWritesOnParameterInspection */
                $count++;
            }
        }

        return $validVals;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularAdd(&$values)
    {
        $source = 'BudgetHeaderManager::Add';
        global $gErr;

        $ok = $this->_QM->beginTrx($source);
        
        if (!array_key_exists(':record#', $values)) {
            $nextId = $this->GetNextRecordKey();
            $values[':record#'] = $nextId;
            $values['RECORDNO'] = $nextId;
        }
        else {
            $values['RECORDNO'] = $values[':record#'];
        }

        $values['USERKEY'] = GetMyUserid(1);
        $values['WHENCREATED']    = GetCurrentDate();
        
        $ok = $ok && $this->validate($values, 'add');

        if ($ok 
            && $values['SYSTEMGENERATED'] != 'true' 
            && $values['DEFAULT_BUDGET'] != 'true'
            && !$this->checkDefault($values)
        ) {
            $values['DEFAULT_BUDGET'] = 'true';
        }
      
        if ($values['DEFAULT_BUDGET'] == 'true') {
            $ok = $ok && $this->updateDefault($values);
        }
        
        $ok = $ok && $this->ValidateBudgetHeader($values);

        // ISPABUDGET attribute can not be set thru API
        // or if this is a default budget, then
        // it will be set to false always unless internally set thru code..
        if ( $this->fromGateway ) {
            $values['ISPABUDGET'] = 'false';
        }

        $ok = $ok && parent::regularAdd($values);

        if ($values['CREATIONTYPE'] == 'Copy') {
            $ok = $ok && $this->CopyBudgetHeader($values);
        }

        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            if (HasErrors()) {
                $msg = "Could not create Budget record!";
                $gErr->addError('GL-2017', __FILE__ . ':' . __LINE__, $msg);
                epp("$source: Error: $msg");
            }

            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    protected function regularSet(&$values)
    {
        global $gErr;

        $source = 'BudgetHeaderManager::Set';

        $ok = $this->_QM->beginTrx($source);
        
        $values['USERKEY'] = GetMyUserid(1);

        if (!$values['DEFAULT_BUDGET'] || $values['DEFAULT_BUDGET'] == 'false') {
            if ($ok 
                && $values['SYSTEMGENERATED'] != 'true'
                && !$this->checkDefault($values)
            ) {
                $values['DEFAULT_BUDGET'] = 'true';
            }
        }

        if ($values['DEFAULT_BUDGET'] && $values['DEFAULT_BUDGET'] == 'true') {
            $ok = $ok && $this->updateDefault($values);
        }

        $ok = $ok && $this->validate($values, 'set');

        $ok = $ok && parent::regularSet($values);

        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            if (HasErrors()) {
                $msg = "Could not update Budget record!";
                $gErr->addError('GL-2018', __FILE__ . ':' . __LINE__, $msg);
            }

            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    function updateDefault(&$values)
    {
        $stmt = array('', GetMyCompany(), $values['RECORDNO']);
        $stmt[0] = "UPDATE budgetheader SET modified=sysdate, default_budget='F' WHERE cny# = :1
            AND record# != :2 ";
        $where = 'AND currency is null';
        if ($values['ISCONSOLIDATED'] == 'true') {
            $where = 'AND currency = :3';
            $stmt[] = $values['CURRENCY'];
        }
        
        $stmt[0] .= $where;
        
        $ok = ExecStmt($stmt);
        
        return $ok;
    }

    /**
     * @param array $values
     *
     * @return bool
     */
    function checkDefault(&$values)
    {
        $stmt = array('', GetMyCompany(), $values['RECORDNO']);
        $stmt[0] = "select record# from budgetheader WHERE cny# = :1 AND default_budget='T' and record# != :2";
        $where = ' AND currency is null';
        if ($values['ISCONSOLIDATED'] == 'true') {
            $where = ' AND currency = :3';
            $stmt[] = $values['CURRENCY'];
        }
        
        $stmt[0] .= $where;
        
        $res = QueryResult($stmt);
        
        $ok = $res[0]['RECORD#'] != '' ? true : false;
        
        return $ok;
    }

    /**
     * @param int|string $ID
     *
     * @return bool
     * @throws IAException
     */
    function Delete($ID)
    {
        global $gErr;

        $source = 'BudgetHeaderManager::Delete';
        $ok = $this->_QM->beginTrx($source);
        $glBudgetHeaderMgr = Globals::$g->gManagerFactory->getManager("glbudgetheader");
        $ok = $ok && $glBudgetHeaderMgr->validateForDelete($ID);

        if (!$ok) {
            $this->_QM->rollbackTrx($source);
            return false;
        }

        if (!$this->IsDeletable($ID)) {
            return false;
        }

        $this->DoEvent('Delete', $ID, false);

        $record = $this->GetRaw($ID);
        $raw[0]['RECORDNO'] = $record[0]['RECORD#'];
        
        // call parent beforeDelete
        $ok = $ok && $this->beforeDelete($raw);

        // Delete the relationship if any
        $ok = $ok && $this->DeletePlatform($raw, DELETE_FOR_DELETE);

        // clear SUBSCRIPTION records for GLBUDGET records here
        $glbudgetMgr = Globals::$g->gManagerFactory->getManager('glbudget');
        $params = [
            'selects' => ['RECORDNO'],
            'filters' => [
                [['BUDGETKEY', '=', $raw[0]['RECORDNO']]]
            ]
        ];
        $glbudgetRecords = $glbudgetMgr->GetList($params);
        $glbudgetRecordNos = array_column($glbudgetRecords, 'RECORDNO');
        
        if (count($glbudgetRecordNos) > 0) {
            $objMgr = Globals::$g->gManagerFactory->getManager('integrationdetail');

            $ok = $ok && $objMgr->deleteByObjectNameAndIntacctKey(
                $glbudgetMgr->getIntegrationObjectName(),  $glbudgetRecordNos
            );
        }
        
        $code = 'QRY_' . isl_strtoupper($this->_entity) . '_DELETE_VID';
        $ok = $ok && $this->DoQuery($code, array($ID));

        $ok = $ok && $this->_QM->commitTrx($source);
        if (!$ok) {
            if (HasErrors()) {
                $msg = 'Cannot delete Budget Record';
                $gErr->addError('GL-2019', __FILE__ . ':' . __LINE__, $msg);
                // epp("$source: Error: $msg");
            }

            $this->_QM->rollbackTrx($source);
        }

        return $ok;
    }

    /**
     * Creates the new budget records from an existing chosen budget
     *
     * @param array $values new budget data
     *
     * @return bool succes or failure of the copy operation
     */
    function CopyBudgetHeader(&$values)
    {
        global $gErr, $gManagerFactory;

        $cny = GetMyCompany();
        //eppp($values);dieFL();
        if (!isset($values['REPORTINGPERIODFROM'])) {
            $msg = 'Copy Budget Failed';
            /** @noinspection PhpUnusedLocalVariableInspection */
            $msg1 = 'Reporting Period Range From is not selected';
            $gErr->addError('GL-2020', __FILE__ . ':' . __LINE__, $msg);
            return false;
        }
        if (!isset($values['REPORTINGPERIODFROM'])) {
            $msg = 'Copy Budget Failed';
            $msg1 = 'Reporting Period Range To is not selected';
            $gErr->addError('GL-2021', __FILE__ . ':' . __LINE__, $msg, $msg1);
            return false;
        }
        $fromReportingPeriod = $this->reportingPerMgr->GetRaw($values['REPORTINGPERIODFROM']);
        $toReportingPeriod = $this->reportingPerMgr->GetRaw($values['REPORTINGPERIODTO']);
        if (DateCompare($fromReportingPeriod[0]['START_DATE'], $toReportingPeriod[0]['START_DATE']) > 0) {
            $msg1 = 'Copy Budget Failed';
            $msg2 = 'Reporting period range \'from\' date is greater than \'to\' date';
            $corr = 'select a \'from\' date prior than \'to\' date';
            $gErr->addError('GL-2088', __FILE__ . ':' . __LINE__, $msg1, $msg2, $corr);
            return false;
        }
        $fromReportingDate = $fromReportingPeriod[0]['START_DATE'];
        $toReportingDate = $toReportingPeriod[0]['START_DATE'];

        $reportingRecs = $this->reportingPerMgr->DoQuery('QRY_REPORTINGPERIOD_SELECT_PERIODS_RANGE', array($fromReportingDate, $toReportingDate, $cny));

        $glbudgetMgr = $gManagerFactory->getManager('glbudget');

        if (isset($values['COPYFROM']) && isset($values['REPORTINGPERIODFROM']) && isset($values['REPORTINGPERIODTO'])) {

            $res = $this->DoQuery('QRY_BUDGETHEADER_SELECT_RAW_BUDGETID', array($values['COPYFROM'], $cny));

            $budgetRecs = $this->GetBudgetRecs($reportingRecs, $res[0]['RECORD#']);

            $budgetRecsCnt = is_countable($budgetRecs) ? count($budgetRecs) : 0;
            for ($i = 0; $i < $budgetRecsCnt; $i++) {

                unset($budgetRecs[$i]['RECORD#']);
                $budgetRecs[$i]['BUDGETKEY'] = $values[':record#'];
                $budgetRecs[$i]['ACCOUNTKEY'] = $budgetRecs[$i]['ACCOUNT#'];
                $budgetRecs[$i]['DEPTKEY'] = $budgetRecs[$i]['DEPT#'];
                $budgetRecs[$i]['LOCATIONKEY'] = $budgetRecs[$i]['LOCATION#'];
                $budgetRecs[$i]['USERKEY'] = GetMyUserid();

                $budgetRecs[$i]['BASEDON'] = ( $budgetRecs[$i]['BASEDON'] != '' ? $glbudgetMgr->TransformValue("BASEDON", $budgetRecs[$i]['BASEDON'], 0) : '' );
                $budgetRecs[$i]['PERPERIOD'] = ( $budgetRecs[$i]['PERPERIOD'] != '' ? $glbudgetMgr->TransformValue("PERPERIOD", $budgetRecs[$i]['PERPERIOD'], 0) : '' );

                $ok = $glbudgetMgr->add($budgetRecs[$i]);

                if ( !$ok ) {
                    break; 
                }

            }

            /** @noinspection PhpUndefinedVariableInspection */
            if ( !$ok || HasErrors() ) {
                $msg = "Could not copy Budget records!";
                $gErr->addError('GL-2022', __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }

        } else {
            $msg = 'Copy Budget Failed';
            $gErr->addError('GL-2023', __FILE__ . ':' . __LINE__, $msg);
            return false;
        }

        return $ok;
    }

    /**
     * @param array $reportingRecs
     * @param string $budgetKey
     *
     * @return array|false|string[][]
     */
    function GetBudgetRecs($reportingRecs, $budgetKey)
    {
        $budTypes = $res = array();

        for ($cou = 0; $cou < count($reportingRecs); $cou++) {
            $budTypes[] = $reportingRecs[$cou]['RECORD#'];
        }

        if ( count($budTypes) > 0 ) {
            $budTypes = array_unique($budTypes);
            $select = 'SELECT a.*, b.normal_balance';
            $from = 'FROM glbudget a, baseaccount b';
            $where = 'WHERE a.cny# = :1 and a.budgetkey = :2 and b.cny# = a.cny# and b.record# = a.account#';
            
            $stmt = array("", GetMyCompany(), $budgetKey);
            if ( !util_isPlatformDisabled() ) {
                $glbudgetid = Util_StandardObjectMap::getObjectId('glbudget');
                $custDimFields = GLEntryManager::getPTFieldsMap();
                
                $i = 3;
                foreach ($custDimFields as $defId => $fieldName ) {
                    $fieldDef = Pt_DataFieldDefManager::getById($defId);
                    if ( $fieldDef instanceof Pt_FieldRelationship ) {
                        $rel = $fieldDef->getRelationshipDef();
                    }
                    if ( !Pt_RelationshipManagerChoose::newRelationshipRedesignReadNew($rel ?? null) ) {
                        $select .= ", R$fieldName.obj2_id as $fieldName";
                        $from .= ", pt_relationship R$fieldName";
                        $where .= " and R$fieldName.cny# (+) = :1 and R$fieldName.obj_def1_id (+) = :" . $i++ .
                                  " and R$fieldName.obj_def2_id (+) = :" . $i++
                                  . " and R$fieldName.obj1_id (+) = a.record#";

                        $stmt[] = $glbudgetid;
                        $stmt[] = $defId;
                    } else {
                        $columnName = $fieldDef->getColumnName();
                        $select .= ", a.$columnName as $fieldName";
                    }
                }
            }
            $stmt[0] = "$select $from $where";
            
            $stmt = PrepINClauseStmt($stmt, $budTypes, " and bud_type# ");
            $res = QueryResult($stmt);
            
        }

        return $res;
    }

    /**
     * @param array $values
     * @param bool $checkDefault
     *
     * @return bool
     */
    function ValidateBudgetHeader(&$values, $checkDefault = true)
    {
        global $gErr;
        if ($values['CREATIONTYPE'] == 'Copy') {
            if (!$values['COPYFROM'] || isl_trim($values['COPYFROM']) == '') {
                $msg = 'Budget header not selected';
                $gErr->addError('GL-2024', __FILE__ . ':' . __LINE__, $msg);
                return false;
            }
        }
        
        //For requests coming through XMLGW only.
        //Because, this checkbox is hidden  on UI, at Entity Level
        if ($checkDefault
            && $values['DEFAULT_BUDGET']!=''
            && $values['DEFAULT_BUDGET']!='false'
            && IsMultiEntityCompany()
            &&  GetContextLocation()!=''
        ) {
            $msg = 'Default Budget can only be created at top level.';
            $gErr->addError('GL-2025', __FILE__ . ':' . __LINE__, $msg);
            return false;
        }
        return true;
    }

    /**
     * @param array $values
     * @param string $mode
     *
     * @return bool
     */
    function validate($values, $mode='add')
    {
        $ok = true;
        $gErr = Globals::$g->gErr;

        $renamedProjectTxt = I18N::getSingleToken('IA.PROJECT');

        if ( $values['DEFAULT_BUDGET'] == 'true' && $values['ISPABUDGET'] == 'true' ) {
            $msg = sprintf('Budget cannot be both Default and %1$s Estimate.', $renamedProjectTxt);
            $corr = 'Choose Default or Project Estimate, not both.';
            $gErr->addError('GL-2026', __FILE__ . ':' . __LINE__, $msg, $corr, '');
            $ok = false;
        }

        if ( $values['DEFAULT_BUDGET'] == 'true' && $values['ISPCNBUDGET'] == 'true' ) {
            $msg = sprintf('Budget cannot be both Default and %1$s Contract.', $renamedProjectTxt);
            $corr = 'Choose Default or Project Contract, not both.';
            $gErr->addError('GL-2115', __FILE__ . ':' . __LINE__, $msg, '', $corr);
            $ok = false;
        }

        if ( $values['ISPABUDGET'] == 'true' && $values['ISPCNBUDGET'] == 'true' ) {
            $msg = sprintf('Budget cannot be both %1$s Estimate and %1$s Contract.', $renamedProjectTxt);
            $corr = 'Choose Project Estimate or Project Contract, not both.';
            $gErr->addError('GL-2116', __FILE__ . ':' . __LINE__, $msg, '', $corr);
            $ok = false;
        }

        if ( $values['ISCONSOLIDATED'] == 'true' && $values['ISPABUDGET'] == 'true' ) {
            $msg = sprintf('Consolidated Budget can not be a %1$s Estimate.', $renamedProjectTxt);
            $gErr->addError('GL-2027', __FILE__ . ':' . __LINE__, $msg);
            $ok = false;
        }

        if ( $values['ISCONSOLIDATED'] == 'true' && $values['ISPCNBUDGET'] == 'true' ) {
            $msg = sprintf('Consolidated Budget can not be a %1$s Contract.', $renamedProjectTxt);
            $gErr->addError('GL-2117', __FILE__ . ':' . __LINE__, $msg);
            $ok = false;
        }

        if ( $mode === 'set' ) {
            // get current budget header record..
            $dbCurrentRecord = $this->getWithoutBudgetDetails($values['BUDGETID']);

            $qry = "select count(1) cnt from glbudgetmst where cny# = :1 and budgetkey = :2";
            $cntResult = QueryResult(array($qry, GetMyCompany(), $dbCurrentRecord['RECORDNO']));

            if ( IsMCMESubscribed() && $dbCurrentRecord && $cntResult[0]['CNT'] > 0 ) {
                if ($dbCurrentRecord['CURRENCY'] != $values['CURRENCY']) {
                    $msg = 'Changing the currency is not supported.';
                    $gErr->addError('GL-2028', __FILE__ . ':' . __LINE__, $msg);
                    $ok = false;
                }

                if ($dbCurrentRecord['ISCONSOLIDATED'] != $values['ISCONSOLIDATED']) {
                    $msg = 'Changing consolidated budget is not supported.';
                    $gErr->addError('GL-2029', __FILE__ . ':' . __LINE__, $msg);
                    $ok = false;
                }
            }

            // validation for PABUDGET
            // if the budget header contains any budget records then we can not change the ISPABUDGET flag..
            if ( $dbCurrentRecord['ISPABUDGET'] != $values['ISPABUDGET'] && $cntResult[0]['CNT'] > 0 ) {
                $msg = sprintf('%1$s Estimate flag cannot be changed, since there are entries posted already.', $renamedProjectTxt);
                $gErr->addError('GL-2030', __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }

            // validation for PCNBUDGET
            // if the budget header contains any budget records then we can not change the ISPCNBUDGET flag..
            if ( $dbCurrentRecord['ISPCNBUDGET'] != $values['ISPCNBUDGET'] && $cntResult[0]['CNT'] > 0 ) {
                $msg = sprintf('%1$s contract flag cannot be changed, since there are entries posted already.', $renamedProjectTxt);
                $gErr->addError('GL-2119', __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }
        }

        if ($values['ISCONSOLIDATED'] == 'true') {
            global $kATLASid;
            if (!(IsMCMESubscribed()
                || (IsMultiEntityCompany() && IsModuleIdInstalled($kATLASid)
                    && $values['ALLOWCREATINGCONSBUDGET'] == 'true'))
            ) {
                $msg = 'Consolidated Budget is not supported.';
                $gErr->addError('GL-2031', __FILE__ . ':' . __LINE__, $msg);
                $ok = false;
            }
        }
       
        if ( $values['CURRENCY'] != '' && $values['ISCONSOLIDATED'] != 'true' ) {
            $msg = 'Consolidated Budget is required to select when Consolidated Currency is specified.';
            $gErr->addError('GL-2032', __FILE__ . ':' . __LINE__, $msg);
            $ok = false;
        }

        if ( $values['ISCONSOLIDATED'] == 'true' && $values['CURRENCY'] == '' ) {
            $msg = 'Consolidated Currency is required when Consolidated Budget is selected';
            $gErr->addError('GL-2033', __FILE__ . ':' . __LINE__, $msg);
            $ok = false;
        }

        if ( $ok && $mode === 'add' &&  !$this->checkDefault($values) && $values['ISPCNBUDGET'] == 'true' ) {
            $msg = sprintf('%1$s Contract Budget cannot be added before adding the Default Budget.', $renamedProjectTxt);
            $corr = sprintf('First add Default Budget and then add a %1$s Contract Budget.', $renamedProjectTxt);
            $gErr->addError('GL-2118', __FILE__ . ':' . __LINE__, $msg, '', $corr);
            $ok = false;
        }

        return $ok;
    }

    /**
     * @param string $operation
     * @param array  &$values
     *
     * @return bool
     */
    function API_Validate($operation, &$values = null)
    {
        $ok = parent::API_Validate($operation, $values);
        if ($ok && GlBudgetHandler::getInstance()->isMigratedUser()) {
            Globals::$g->gErr->addError(
                'GL-2034',
                "(" . __FILE__ . ':' . __LINE__ . ')',
                "BUDGETHEADER object is deprecated.",
                "", "Please use GLBUDGETHEADER object."
            );
            $ok = false;
        }
        return $ok;
    }

}

