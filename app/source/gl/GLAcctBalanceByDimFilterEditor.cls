<?php
/**
 * File GLAcctBalanceByDimFilterEditor.cls contains implementation of
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2015 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */

class GLAcctBalanceByDimFilterEditor extends GLReportFilterEditor
{
    /**
     * GLAcctBalanceByDimFilterEditor constructor.
     *
     * @param array $_params
     */
    public function __construct($_params)
    {
        parent::__construct($_params);
    }

    /**
     * Add Javascript to the page
     *
     * @param bool $addYuiCss
     */
    public function showScripts($addYuiCss = true)
    {
        ?>
        <script language="JavaScript" src="../resources/js/glreportfiltereditor.js"></script>
        <?php
        parent::showScripts($addYuiCss);
    }
    
    /**
     * @param array $editvalues
     * @param array $promptonrundata
     */
    function ShowTop($editvalues = array(), $promptonrundata = array())
    {
        $this->_params['onloadjs'] .= 'GLAccountGroupSelection();';
        parent::ShowTop($editvalues, $promptonrundata);
    }
    
    /**
     * @param string $beforeJS
     */
    function showSaveButton($beforeJS='')
    {
        $beforeJS = "populateMultiAcc();";
        parent::showSaveButton($beforeJS);
    }

    /**
     * Overriden to add Account as a dimension
     * 
     * @return array|null
     */
    public function GetDimensionList()
    {
        $dimensionObj = parent::GetDimensionList();

        $accountDim['baseaccount'] = [
                'fullname' => 'IA.ACCOUNT'
        ];
        
        $this->setShowIDNName(true);
        
        return array_merge($accountDim, $dimensionObj);
    }
}