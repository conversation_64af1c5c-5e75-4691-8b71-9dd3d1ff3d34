<?
/**
 * Manager class for DBSchema Throttle
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (C)2023 Sage Intacct Corporation, All Rights Reserved
 */
class DBSchemaThrottleManager extends BaseThrottleManager
{
    protected string $sqlStatementToValidateCurrentPod = "SELECT 1 FROM schemamap WHERE databaseid = :1 and podid = :2";
    /**
     * @return string
     */
    protected function getErrorMessageBadPod() : string
    {
        return 'DBschema is not valid or not in the current POD.';
    }

    /**
     * @return mixed|string|void
     */
    protected function getErrorMessageForMissingId() : string
    {
        return 'DBSchema cannot be null.';
    }
}
