<?php

/**
 * OutsourcedCheckUtils.cls
 *
 * <AUTHOR> <snar<PERSON><EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
*/

require_once "iadefines.inc";

/**
 * @category  Cls
 * @package   Source/apar
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2009 Intacct Corporation, All Rights Reserved
 */

class AMEXBaseUtil
{
    /**
     * @var array $paymentObjs
     */
    protected $paymentObjs;

    /**
     * @var string $userName
     */
    protected $userName;

    /**
     * @var string $psswd
     */
    protected $psswd;

    /**
     * @var APOPaymentsManager $proMgr
     */
    protected $proMgr;

    const EMAIL_HEADER = "Hi,\n\n";
    const EMAIL_FOOTER = "\n\nThanks,\nThe Intacct Team";
    const MATCH_STATUS = 'YES';
    const RESP_RECEIVED = 'Response Received';

    // Billing events constants
    const BILLING_EVENT_SUBSCRIBE_CHECK = 'OPYMTS_SUBSCRIBE_CHECK';
    const BILLING_EVENT_SUBSCRIBE_ACH = 'OPYMTS_SUBSCRIBE_ACH';
    const BILLING_EVENT_SUBSCRIBE_CARD = 'OPYMTS_SUBSCRIBE_CARD';
    const BILLING_EVENT_UNSUBSCRIBE_CHECK = 'OPYMTS_UNSUBSCRIBE_CHECK';
    const BILLING_EVENT_UNSUBSCRIBE_ACH = 'OPYMTS_UNSUBSCRIBE_ACH';
    const BILLING_EVENT_UNSUBSCRIBE_CARD = 'OPYMTS_UNSUBSCRIBE_CARD';

    const BILLING_EVENT_PAYMENT_ACH_PAID = 'OPYMTS_PAYMENT_ACH_PAID';
    const BILLING_EVENT_PAYMENT_ACH_RETURN = 'OPYMTS_PAYMENT_ACH_RETURN';
    const BILLING_EVENT_PAYMENT_CHECK_MAILED = 'OPYMTS_PAYMENT_CHECK_MAILED';
    const BILLING_EVENT_PAYMENT_CARD_PAID = 'OPYMTS_PAYMENT_CARD_PAID';
    const BILLING_EVENT_PAYMENT_CARD_DECLINED = 'OPYMTS_PAYMENT_CARD_DECLINED';
    const BILLING_EVENT_PAYMENT_CARD_FAILED = 'OPYMTS_PAYMENT_CARD_FAILED';

    // Number of days per garbage collection
    const GARBAGE_COLLECT_DAYS = 90;

    /**
     * @param string $user
     * @param string $pwd
     */
    public function __construct($user, $pwd)
    {
        $this->paymentObjs = array();
        $this->userName = $user;
        $this->psswd = $pwd;
        $this->proMgr = Globals::$g->gManagerFactory->getManager('apopayments');
    }

    /**
     * Populate SFTP credentials
     *
     * @param array &$credentials credentials for SFTP connection
     *
     */
    protected function populateSFTPCredentials(&$credentials)
    {
        $usekeys = GetValueForIAAMEXCFGProperty('AMEX_SFTP_USE_SSHKEYS');
        if ($usekeys) {

            $privKey = GetValueForIAAMEXCFGProperty('AMEX_SFTP_PRIVATE_KEY');
            $pubKey = GetValueForIAAMEXCFGProperty('AMEX_SFTP_PUBLIC_KEY');

            //for production the digital certificate file is going to be under /home/<USER>/etc
            if ( Globals::$g->islive ) {
                $pubKey = SYSETCDIR . $pubKey;
                $privKey = SYSETCDIR . $privKey;
            } else {
                $pubKey = realpath(ETCDIR . $pubKey);
                $privKey = realpath(ETCDIR . $privKey);
            }
            $credentials['userpwd'] = GetValueForIAAMEXCFGProperty('AMEX_SFTP_USER');
            $credentials['prvkeyfile'] = $privKey;
            $credentials['pubkeyfile'] = $pubKey;
            $credentials['keypasswd'] = GetValueForIAAMEXCFGProperty('AMEX_SFTP_PASSPHRASE', ia_cfg::DECRYPT_KEY);
        } else {
            $credentials = array('userpwd' => $this->userName . ":" . $this->psswd);
        }
    }

    /**
     * Publish Check Delivery related IMS request
     *
     * @param string $userkey   user key
     * @param string $cnyHash   company record#
     * @param string $imsTopic  IMS topic
     * @param mixed  $imsData   IMS data
     * @param string &$response response
     * @param string $locid     location id
     *
     * @return bool
     */

    protected static function publishIMSRequest($userkey, $cnyHash, $imsTopic, $imsData, &$response = null, $locid = '')
    {
        global $_userid;

        $_userid = $userkey."@$cnyHash";
        $credentials = array(
            'INTACCT_CNYTITLE' => GetMyCompanyTitle(),
            'INTACCT_COMPANY_CNY' => GetMyCompany(),
            'INTACCT_LOGIN' => GetMyLogin(),
            'INTACCT_PASSWORD' => GetMyPassword(),
            'INTACCT_USERID' => $userkey.'@'.GetMyCompany().'@'.GetMyApp(),
            'INTACCT_LOCATIONID' => $locid,
        );
        $publish = new ims_publish_1(IMS_MODE_NONBLOCKING, IMS_PROCESS_REMOTE, IMS_MODE_QUEUED);
        $retVal = $publish->PublishMsg($imsTopic, 'INTACCT', $imsTopic, '', $imsData, $credentials, $response);
        return $retVal;
    }

    /**
     * Handle global errors
     *
     * @param bool $donotClearErr
     *
     * @return bool
     */
    protected function handleGlobalErrors($donotClearErr = true)
    {
        $ok = true;
        $gErr = Globals::$g->gErr;
        if ($gErr->hasErrors()) {
            $err = $gErr->myToString(false);
            LogToFile("ERROR: Outsourced Payment Processing SFTP(AMEX) Issue:\n" . $err);
            $ok = false;
        }
        if (!$donotClearErr) {
            $gErr->Clear();
        }
        return $ok;

    }

    /**
     * Format input array to an XML string
     *
     * @param array $attachmentArray input array
     *
     * @return string
     */

    static function formatToXmlStr($attachmentArray)
    {
        $xmlString = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<VENDORSFAILED>\n";
        foreach ($attachmentArray as $vendor) {
            $xmlString .= "<VENDOR>\n<VENDORID>".$vendor['vendorid']."</VENDORID>";
            $errorXmlString = self::formatErrorsToXmlStr($vendor['errors']);
            $xmlString .= "\n<ERRORS>".$errorXmlString."\n</ERRORS>\n";
            $xmlString .= "</VENDOR>\n";
        }
        $xmlString .= "</VENDORSFAILED>\n";
        return $xmlString;
    }

    /**
     * Format error object to XML string
     *
     * @param mixed $errors Error object
     *
     * @return string
     */

    static function formatErrorsToXmlStr($errors)
    {
        $xmlString = '';
        foreach ($errors as $error) {
            $xmlString .= "\n  <ERROR>".$error."</ERROR>";
        }
        return $xmlString;
    }

    /**
     * Retrieve global errors
     *
     * @param string $separator
     *
     * @return string[]
     */

    static function getErrorMessage($separator = "\n")
    {
        global $gErr;
        $returnArray = array();
        $attArray = array();
        $errLines = '';
        $errary = array();
        $gErr->GetErrList($errary);

        if (count($errary) > 0) {
            foreach ($errary as $item) {
                if ((isl_stristr($item['NUMBER'], 'BL')) || (isl_stristr($item['NUMBER'], 'PL'))) {
                    $errLines .= $item['CDESCRIPTION'].$separator;
                    $attArray[] = $item['CDESCRIPTION'];
                }
            }
        }
        $gErr->Clear();
        $returnArray['errorMsg'] = $errLines;
        $returnArray['errorArray'] = $attArray;
        return $returnArray;
    }

    /**
     * Send notification email
     *
     * @param string $subject
     * @param string $msgBody
     *
     */

    static function sendNotificationEmail($subject, $msgBody)
    {
        GetModulePreferences(Globals::$g->kOPYMTSid, $prefs, true);
        $addresses = $prefs['CONTACTFORNOTIFY'];
        $emailArray = preg_split("/\s*,\s*/", $addresses);
        foreach ($emailArray as $address) {
            EmailHelper::ia_mail($address, $subject, self::EMAIL_HEADER . $msgBody . self::EMAIL_FOOTER, "From: <EMAIL>\nReply-To: <EMAIL>");
        }
    }

    /**
     * Inject conditions
     *
     * @param string $cond      condition
     * @param array  $wheres    where clause
     * @param array  &$args     arguments
     * @param string &$whereStr where string
     * @param array  &$type     type
     *
     * @return bool
     */

    static function injectConditions($cond, $wheres, &$args, &$whereStr, &$type)
    {
        $len = isl_strlen($cond);
        $pattern = "/\d/";
        $whereStr = '';
        $newArgs = array();
        $newType = array();
        $number = '';
        for ($i=0; $i<$len; $i++) {
            $str = isl_substr($cond, $i, 1);

            // if number is found keep collecting it until other char is found
            if (isl_preg_match($pattern, $str)) {
                $number .= $str;
            } else {
                // if other char found then, replace the number with condition
                if ( isset($number) && $number!='') {
                    if ( $wheres[$number-1] ) {
                        $whereStr .= $wheres[$number-1];

                        // changing the argument order based on the expression
                        // and ignore NoArgs
                        if ( $args[$number-1] != 'NoArgs') {
                            $newArgs[] = $args[$number-1];
                            $newType[] = $type[$number-1];
                        }

                    } else {
                        return false;
                    }
                }
                $number = '';
                $whereStr .= $str;
            }

            // if the last char is number then need to inject
            if ( $i == ($len-1) && isset($number) && $number!='') {
                if ( $wheres[$number-1] ) {
                    $whereStr .= $wheres[$number-1];

                    // changing the argument order based on the expression
                    // and ignore NoArgs
                    if ( $args[$number-1] != 'NoArgs') {
                        $newArgs[] = $args[$number-1];
                        $newType[] = $type[$number-1];
                    }
                } else {
                    return false;
                }
            }
        }

        $args = $newArgs;
        $type = $newType;
        return true;
    }
    /**
     * @return bool
     */
    static function haveAMEXSubscribedBankAccounts()
    {
        $qry = "select count(1) cnt
                    from bankaccount b, opsubscription ops
                    where b.cny# = ops.cny# and b.accountid = ops.bankaccountid
                    and ops.cny# = :1 and (ops.outsourcecheck = 'T' or ops.outsourceach = 'T')";
        $result = QueryResult(array($qry, GetMyCompany()));
        $amexBankFound = ($result[0]['CNT'] > 0 ? true : false);
        return $amexBankFound;
    }

    /**
     * @return bool
     */
    static function haveAMEXSubscribedCardAccounts()
    {
        $qry = "select count(1) cnt
                    from creditcard c, opsubscription ops
                    where c.cny# = ops.cny# and c.cardid = ops.cardid
                    and ops.cny# = :1 and (ops.outsourcecard = 'T')";
        $result = QueryResult(array($qry, GetMyCompany()));
        $amexCardFound = ($result[0]['CNT'] > 0 ? true : false);
        return $amexCardFound;
    }

    /**
     * Enable ACh status based on ACh stae & Vendor Account outsource ach
     *
     * @param string $outsourceAchState
     * @param string $vndAccOutsourceAch
     *
     * @return string
     */
    static function getVendorACHStatus($outsourceAchState, $vndAccOutsourceAch)
    {
        if($outsourceAchState == 'Pending Activation' || $vndAccOutsourceAch == 'Pending Activation'){
            $outsourceAchState = 'Pending Activation';
        }
        elseif($outsourceAchState == 'Validation Passed' && $vndAccOutsourceAch == 'true'){
            $outsourceAchState = 'Validation Passed';
        }
        elseif($outsourceAchState == 'Validation Passed' && $vndAccOutsourceAch == 'false'){
            $outsourceAchState = 'Validation Failed';
        }
        elseif($outsourceAchState == 'Validation Passed' && $vndAccOutsourceAch != 'false'){
            if($vndAccOutsourceAch == 'true'){
                $outsourceAchState = 'Validation Passed';
            }
            elseif($vndAccOutsourceAch == 'false'){
                $outsourceAchState = 'Validation Failed';
            }
            else{
                $outsourceAchState = $vndAccOutsourceAch;
            }

        }
        elseif($outsourceAchState == 'Subscribed' && $vndAccOutsourceAch == 'Subscribed'){
            $outsourceAchState = 'Subscribed';
        }
        elseif($outsourceAchState == 'Subscribed' && $vndAccOutsourceAch != 'Subscribed'){
            if($vndAccOutsourceAch == 'true'){
                $outsourceAchState = 'Validation Passed';
            }
            elseif($vndAccOutsourceAch == 'false'){
                $outsourceAchState = 'Validation Failed';
            }
            else{
                $outsourceAchState = $vndAccOutsourceAch;
            }
        }
        return $outsourceAchState;
    }

    /**
     * Enable ACh status based on ACh stae & Vendor Account outsource ach
     *
     * @param string $outsourceAch
     * @param string $achState
     * @param string $vndAccOutsourceAch
     *
     * @return string
     */
    static function getVendorACHState($outsourceAch, $achState, $vndAccOutsourceAch)
    {
        if ($achState == 'Validation Failed') {
            return 'Validation Failed';
        }
        if($achState == 'Inactive') {
            return 'Inactive';
        }
        if(!isset($outsourceAch) || $outsourceAch == '' || $outsourceAch == 'false'){
            return 'Not Enabled';
        }
        if($outsourceAch == 'true' && ($achState == 'Active' || $achState == 'Validation Passed') && $vndAccOutsourceAch == 'true'){
            return  'Active';
        }
        if($outsourceAch == 'true' && $achState == 'Not Enabled') {
            return 'Not Enabled';
        }
        return 'Pending Activation';
    }

    /**
     * @param string[][] $bankAccount
     * @param string[] $amexPymt
     *
     * @return bool
     */
    static function doautoSendAmexPayment($bankAccount, $amexPymt)
    {
        GetPaymethodKey(OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD, $achpayMethodKey);
        GetPaymethodKey(OutsourcedCheckUtils::OUTSOURCE_CHECK_METHOD, $cdMethodKey);
        GetPaymethodKey(OutsourcedCheckUtils::OUTSOURCE_CC_METHOD, $cardMethodKey);

        if ( ($bankAccount[0]['AUTOMATE_CHECK_PRINT'] == 'true') && ($amexPymt['PAYMETHODKEY'] == $cdMethodKey) ) {
            return true;
        } else if ( ($bankAccount[0]['AUTOMATE_ACH_PYMT_SEND'] == 'true') && ($amexPymt['PAYMETHODKEY'] == $achpayMethodKey) ) {
            return true;
        } else if ( ($bankAccount[0]['AUTOMATECARDPYMTSEND'] == 'true') && ($amexPymt['PAYMETHODKEY'] == $cardMethodKey) ) {
            return true;
        }

        return false;
    }

    /**
     * @param  string[] $pymtObj
     * @param string|null $ccpayMethodKey
     *
     * @return array|array[]
     */
    static function getAccountObj($pymtObj, $ccpayMethodKey = null)
    {
        if (!$ccpayMethodKey) {
            GetPaymethodKey(OutsourcedCheckUtils::OUTSOURCE_CC_METHOD, $ccpayMethodKey);
        }
        if ($pymtObj['PAYMETHOD'] == OutsourcedCheckUtils::OUTSOURCE_CC_METHOD) {
            $opCardSubscribeMgr  = Globals::$g->gManagerFactory->getManager('opcardsubscription');
            $filter = array(
                'selects' => array(
                    'CARDID',
                    'AUTOMATECARDPYMTSEND'
                ),
                'filters' => array(array(array('CARDID', '=', $pymtObj['FINANCIALENTITY'])))
            );
            $bankAccount = $opCardSubscribeMgr->GetList($filter);
        } else {
            $bankMgr = Globals::$g->gManagerFactory->getManager('checkingaccount');
            $filter = array(
                'selects' => array(
                    'BANKACCOUNTID',
                    'AUTOMATE_CHECK_PRINT', 'BANKNAME', 'AUTOMATE_ACH_PYMT_SEND'
                ),
                'filters' => array(array(array('BANKACCOUNTID', '=', $pymtObj['FINANCIALENTITY'])))
            );
            $bankAccount = $bankMgr->GetList($filter);
        }

        return $bankAccount;
    }

    /**
     * @param string[] $amexPymt
     */
    static function handleAutomatePaymentSend($amexPymt)
    {
        if ( FeatureConfigManagerFactory::getInstance()
            ->isFeatureEnabled('DISABLE_AMEX_INTEGRATION') ) {
            Globals::$g->gErr->addError(
                'AP-0742', __FUNCTION__, "Vendor Payments powered by American Express is no longer available as a payment method."
            );

            return false;
        }

        if ( IsMultiEntityCompany() && GetContextLocation() == '' ) {
            SetReportViewContext();
        }

        $bankAccount = self::getAccountObj($amexPymt);

        if (self::doautoSendAmexPayment($bankAccount, $amexPymt)) {
            // Automatically send payments in Check Delivery queue
            $paymentObjs = array('PYMTOBJS' => array(array('APPAYMENTKEY' => $amexPymt['RECORD#'])));
            $apopyObj = Globals::$g->gManagerFactory->getManager("apopayments");
            $ok = $apopyObj->add($paymentObjs);
            if (!$ok) {
                $pymtData = array();
                $vendormgr = Globals::$g->gManagerFactory->getManager('vendor');
                $queryparams = [
                    'selects' => array(
                        'VENDORID',
                        'NAME',
                    ),
                    'filters' => array(array(
                        array('CNY#', '=', GetMyCompany()),
                        array('VENDORID', '=', $amexPymt['VENDORID']),
                    ))
                ];
                $qryResult = $vendormgr->GetList($queryparams);
                $pymtData = INTACCTarray_merge($pymtData, $qryResult[0]);
                AMEXLog::createEventLog(
                    'Payment request failed(' . $amexPymt['RECORD#'] .'): ' .
                    explode('--', $paymentObjs['result'][$amexPymt['RECORD#']]['STATUSMSG'])[1],
                    AMEXLog::LOG_ACTION_PAYMENT,
                    AMEXLog::LOG_TYPE_INFO,
                    AMEXRESTClient::$API_DESC_MAPPING['sendPayment'],
                    AMEXRESTClient::$API_OBJECT_MAPPING['sendPayment'],
                    '', $amexPymt['RECORD#']
                );

                // Send a payment request API failure email
                $message = "Hi,<br><br>This is to notify you that one of your recent " . $amexPymt['PAYMETHOD'] . " payment request failed.";
                $message .= "<br><br>The payment request details as below:";
                $message .= "<br><br>Vendor ID: " . $pymtData['VENDORID'];
                $message .= "<br>Vendor Name: " . $pymtData['NAME'];
                $message .= "<br>Bank Account ID: " . $amexPymt['FINANCIALENTITY'];
                $message .= "<br>Payment Amount: " . $amexPymt['AMOUNT'];
                $message .= "<br>Payment Currency: " . $amexPymt['CURRENCY'];
                $message .= "<br>Payment Date: " . $amexPymt['WHENSUBMITTED'];
                $message .= "<br>Failure Description: " . explode('--', $paymentObjs['result'][$amexPymt['RECORD#']]['STATUSMSG'])[1];
                $message .= "<br><br>You can go to <b>Accounts Payable > Vendor Payment Processing</b> to view the payment and make decisions on next steps.";
                $message .= "<br><br>Thanks,<br>The Intacct Team";

                $emailid = QueryResult(
                    [
                        "select email1 from contact where cny#=:1 and record#=(select contactkey from userinfo where cny#=:1 and record#=
                        (select createdby from prrecordmst where cny#=:1 and record#=:2))", GetMyCompany(),
                        $amexPymt['RECORD#'],
                    ]
                );

                $emailobj = new IAEmail($emailid[0]['EMAIL1']);
                $emailobj->contenttype='text/html; charset="' . isl_get_charset() . '"';
                $emailobj->_from = "<EMAIL>";
                $emailobj->subject  =   'ACTION REQUIRED: Vendor Payment Services - ' . $amexPymt['PAYMETHOD'] . ' payment request failed' . $amexPymt['PAYMETHOD'];
                $emailobj->body =   $message;
                $emailobj->send();
            }
        }
    }

    /**
     * @param string[] $pymtrec
     *
     * @return array|null
     */
    static function getApprovedPaymentRec($pymtrec)
    {
        $outsourceservice = null;
        $amexapprovedPymt = null;

        GetPaymethodKey(OutsourcedCheckUtils::OUTSOURCE_CHECK_METHOD, $cdpayMethodKey);
        GetPaymethodKey(OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD, $achpayMethodKey);
        GetPaymethodKey(OutsourcedCheckUtils::OUTSOURCE_CC_METHOD, $ccpayMethodKey);
        if ($pymtrec['PAYMETHODKEY'] == $cdpayMethodKey) {
            $outsourceservice = OutsourcedCheckUtils::OUTSOURCE_CHECK_METHOD;
        } else if ($pymtrec['PAYMETHODKEY'] == $achpayMethodKey) {
            $outsourceservice = OutsourcedCheckUtils::OUTSOURCE_ACH_METHOD;
        }  else if ($pymtrec['PAYMETHODKEY'] == $ccpayMethodKey) {
            $outsourceservice = OutsourcedCheckUtils::OUTSOURCE_CC_METHOD;
        }

        if (isset($outsourceservice)) {
            $amexapprovedPymt = array('PAYMETHODKEY' => $pymtrec['PAYMETHODKEY'],
                'RECORD#' => $pymtrec['RECORD#'],
                'FINANCIALENTITY' => $pymtrec['FINANCIALENTITY'],
                'VENDORID' => substr($pymtrec['VENDENTITY'], 1),
                'PAYMETHOD' => $outsourceservice,
                'AMOUNT' => $pymtrec['TRX_TOTALENTERED'],
                'CURRENCY' => $pymtrec['CURRENCY'],
                'WHENSUBMITTED' => $pymtrec['WHENMODIFIED'],
                'RECORDTYPE' => $pymtrec['RECORDTYPE']);
        }

        return $amexapprovedPymt;
    }

    /**
     * @param string|array $selectClause
     *
     * @return array|array[]
     */
    public static function getAmexEnrolledCards($selectClause = '')
    {
        if (!$selectClause || $selectClause == '') {
            $selectClause = array('RECORDNO', 'CARDID', 'CARDNUM', 'OUTSOURCECARD', 'LIABILITYTYPE', 'LIABACCT#','VENDORID',
                'LOCATIONKEY', 'AUTOMATECARDPYMTSEND', 'AUTOMATECARDPYMTCONF'
            );
        }

        $creditCardMgr = Globals::$g->gManagerFactory->getManager('creditcard');
        $filter = array(
            'selects' => $selectClause,
            'filters' => array(array(
                array('CARDTYPE', '=', 'American Express'),
                array('LIABACCT#', 'IS NOT NULL'),
                array('LIABILITYTYPE', '=', 'Credit'),
                array('OUTSOURCECARD', '=', 'true')
            )),
            'orders' => array(array('creditcard.CARDID', 'asc')),
        );
        $amexCards = $creditCardMgr->GetList($filter);
        foreach ($amexCards as &$line) {
            $line['CARDTYPE'] = 'AMEX';
        }

        return $amexCards;
    }

    /**
     * @param string $billingEvent
     * @param string $verb
     * @param string $additionalInfo
     *
     * @return bool
     */
    public static function addBillingActivityLog($billingEvent, $verb, $additionalInfo = '')
    {
        import('BS_ActivityLogManager');

        $activityLogMgr = new BS_ActivityLogManager;
        $activity = ['PRODUCT' => $billingEvent, 'VERB' => $verb, 'USR1' => GetMyUserid(), 'USR2' => $additionalInfo];
        $ok = $activityLogMgr->add($activity);

        return $ok;
    }

    /**
     * @param mixed $object
     *
     * @return mixed   array if input is array or object otherwise input is returned unscathed
     */
    static function convertObjectToArray($object)
    {
        if( !is_object( $object ) && !is_array( $object ) )
        {
            return $object;
        }
        if (is_object( $object)) {
            /** @var mixed $object */
            $object = get_object_vars( $object );
        }
        /** @var array $object */
        $rVal = array_map( 'AMEXBaseUtil::convertObjectToArray', $object );

        return $rVal;

    }

    /**
     * @param array $obj
     * @param array $cachedObj
     * @param array $fieldObj
     *
     * @return bool
     */
    public static  function doPerformUpdate($obj, $cachedObj, $fieldObj)
    {
        $doUpdate = false;
        foreach ($fieldObj as $field) {
            if (strtoupper($obj[$field]) !== strtoupper($cachedObj[$field])) {
                $doUpdate = true;
                break;
            }
        }
        return $doUpdate;
    }

    /**
     * @return bool
     */
    public static function isAMEXInstalled()
    {
        $amexApiVersion = GetPreferenceForProperty(Globals::$g->kOPYMTSid, 'API_VERSION');

        return IsInstalled(Globals::$g->kOPYMTSid) && $amexApiVersion !== '';
    }


    /**
     * @param string $vendorID
     *
     * @return array
     */
    public static function getVendorSubscriptionData($vendorID)
    {
        $gManagerFactory = Globals::$g->gManagerFactory;
        $vendMgr = $gManagerFactory->getManager('vendorsubscription');

        $params = array(
            'filters' => array(
                array(
                    array('VENDORID', '=', $vendorID),
                ),
            ),
        );

        $vendUpd = $vendMgr->GetList($params);
        if (!count($vendUpd)){
            return [];
        }
        return $vendUpd[0];
    }

    /**
     * @param string $bankAccountID
     * @param string $service
     *
     * @return null|array
     */
    public static function getOPSubscription($bankAccountID, $service)
    {
        $opSubscriptionkManager = Globals::$g->gManagerFactory->getManager('opsubscription');
        if ($service === AMEXBase::AMEX_CARD) {
            $filter = 'CARDID';
        } else {
            $filter = 'BANKACCOUNTID';
        }
        $params = array(
            'selects' => [AMEXAccount::AMEX_BANK_ACCOUNT_ID, AMEXAccount::AMEX_CARD_ACCOUNT_ID],
            'filters' => array(
                array(
                    array($filter, '=', $bankAccountID),
                ),
            ),
        );

        $bankAccount = $opSubscriptionkManager->GetList($params);
        if (!count($bankAccount)){
            return null;
        }
        return $bankAccount[0];
    }

    /**
     * @param string $cny
     *
     * @return string|null
     */
    public static function getAmexOrganizationID($cny)
    {
        $kOPYMTSid = Globals::$g->kOPYMTSid;
        $sqlAmexID = "SELECT m.cny#, m.property,m.value
                FROM modulepref m
                WHERE m.cny#=:1 AND m.modulekey = :2 AND m.property = 'AMEX_ORGANIZATION_ID'";
        $orgInfo = QueryResult(array($sqlAmexID, $cny, $kOPYMTSid));
        if ($orgInfo && count($orgInfo)) {
            return $orgInfo[0]['VALUE'];
        }
        return null;
    }

    /**
     * get partner entity id
     *
     * @return string
     */
    public static function getPartnerEntityID()
    {
        return GetValueForIAAMEXCFGProperty('AMEX_PARTNER_ENTITY_ID');
    }

    /**
     * method to check if a key is set for a particular array
     * add the possibility to set a default value if that key is not set
     *
     * @param array $array
     * @param string|int $key
     * @param mixed $default
     *
     * @return mixed
     */
    public static function amexArrayValueForKey($array, $key, $default = null) {
        if (!is_array($array)) {
            return null;
        }
        return array_key_exists($key, $array) ? $array[$key] : $default;
    }

    /**
     * @return string
     */
    public static function getOrganizationAMEXAPIVersion() {
        $amexApiVersion = GetPreferenceForProperty(Globals::$g->kOPYMTSid, 'API_VERSION');
        if ($amexApiVersion) {
            return $amexApiVersion;
        }
        $amexV3Enabled = GetValueForIAAMEXCFGProperty(AMEXBase::AMEX_V3_ENABLE_CONFIG);
        return $amexV3Enabled === '1' ? AMEXBase::API_VERSION_REST_V3 : AMEXBaseObj::API_VERSION_REST_V1;
    }
}
