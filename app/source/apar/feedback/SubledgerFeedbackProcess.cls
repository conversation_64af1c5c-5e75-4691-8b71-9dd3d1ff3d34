<?php

/**
 *    SubledgerFeedbackProcess.cls
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022, Intacct Corporation, All Rights Reserved
 */

class SubledgerFeedbackProcess
{

    const IACFG_PROP = 'SL_FEEDBACK';

    /**
     * @var array|null $actionConfigs
     */
    private static $actionConfigs = null;

    /**
     * @var bool|null $isDebug
     */
    private static $isDebug = true;//TODO change this to null

    /**
     * @var AbstractSubledgerFeedbackDataHandler $dataHandler
     */
    protected $dataHandler;

    /**
     * @var int $timeLimit
     */
    protected $timeLimit;

    /**
     * @var MetricSLFeedbackInteractionsProcess $metricsObj
     */
    protected $metricsObj;

    /**
     * @var int $startTime
     */
    protected $startTime;

    /**
     * @var string $action
     */
    protected $action;

    /**
     * @var bool $payloadLeft
     */
    protected $payloadLeft = true;

    /**
     * @var bool $httpFailed
     */
    protected $httpFailed = false;

    /**
     * @var bool $noCacheToken
     */
    protected $noCacheToken = false;

    /**
     * @var int $runCount
     */
    protected $runCount = 0;

    /**
     * @var bool $success
     */
    protected $success = true;

    /**
     * @var bool $cnyNotFound
     */
    protected $cnyNotFound = false;

    const LOG_PREFIX = 'Subledger Feedback Interactions';

    const ACTION_FEEDBACK = 'feedback';

    /**
     * SubledgerFeedbackProcess constructor.
     *
     * @param string                  $action
     * @param int                     $timelimit
     */
    public function __construct(string $action, int $timelimit = 0)
    {
        $this->timeLimit = $timelimit;
        if ( $this->timeLimit === 0 ) {
            $this->timeLimit = DBSchemaInfo::DEFAULT_SL_FEEDBACK_CONSUMER_TIMELIMIT;
        }
        $this->metricsObj = new MetricSLFeedbackInteractionsProcess($action);
        $this->startTime = time();
        $this->action = $action;
        $this->dataHandler = $this->getDataHandler();
    }

    /**
     * Create Instance of a Datahandler based on action
     *
     * @return SubledgerFeedbackDataHandler
     * @throws SubledgerFeedbackException
     */
    protected function getDataHandler()
    {
        switch ($this->action) {
            case self::ACTION_FEEDBACK:
                return new SubledgerFeedbackDataHandler();
            default:
                throw new SubledgerFeedbackException(SubledgerFeedbackException::INVALID_ACTION);
        }
    }

    /**
     * @param string $payload
     *
     * @return SimpleHttpResponse
     * @throws SimpleHttpResponseException|SubledgerFeedbackException
     */
    private function sendPayload(string $payload) : SimpleHttpResponse
    {
        $this->log("Sending payload to {$this->dataHandler->getEndpoint()}. runCount : {$this->runCount}",
                   LogManager::DEBUG);
        $this->log($payload, LogManager::DEBUG);

        $slHttpResponse = SubledgerFeedbackHttpRequest::makeHttpRequest(
            $this->action, $this->dataHandler->getEndpoint(), $payload,
            $this->dataHandler->getHttpMethod(), $this->noCacheToken
        );

        if ( $slHttpResponse->getResponseCode() >= SubledgerFeedbackException::RES_CODE_SERVER_ERROR
             || $slHttpResponse->getResponseCode() == 0 ) {
            throw new SubledgerFeedbackException(SubledgerFeedbackException::SERVER_ERROR);
        } else if ( $slHttpResponse->getResponseCode() == SubledgerFeedbackException::RES_CODE_VALIDATION_ERROR ) {
            $this->setNoCacheToken(true);
            throw new SubledgerFeedbackException(SubledgerFeedbackException::AUTH_ERROR);
        }

        return $slHttpResponse;
    }

    /**
     * Process runner
     *
     * @return SubledgerFeedbackProcess
     * @throws SimpleHttpResponseException|SubledgerFeedbackException
     */
    public function run() : SubledgerFeedbackProcess
    {
        $this->metricsObj->startTime();
        $payload = $this->dataHandler->getPayLoad();
        $this->metricsObj->stopTime();
        do {
            $this->runCount++;
            try {
                if ( ! empty($payload) ) {
                    $SlfeedbackHttpResponse = $this->sendPayload($payload);
                    $this->metricsObj->startProcessTime();
                    $this->success = $this->dataHandler->processResponse($SlfeedbackHttpResponse);
                    $this->metricsObj->stopProcessTime();
                    if ( ! $this->success ) {
                        throw new SubledgerFeedbackException(SubledgerFeedbackException::PROCESS_ERROR);
                    }
                } else {
                    $this->payloadLeft = false;
                    $this->log("Payload is empty. Not making any request.");
                    break;
                }
            } catch ( SubledgerFeedbackException $slException ) {
                $this->success = false;
                $this->httpFailed = $slException->isServerError();
                $this->cnyNotFound = $slException->isCompanyNotFound();
                $this->metricsObj->setError($slException->getMessage());
            }
        } while ( $this->hasTime() && ! $this->isSuccess() && ! $this->isHTTPFailed() && ! $this->isCompanyNotFound()
                  && ( $this->runCount <= $this->dataHandler->getMaxRetryLimit() ) );
        $this->metricsObj->setSuccess($this->success);

        return $this;
    }

    /**
     * @return bool
     */
    public function isCompanyNotFound() : bool
    {
        return $this->cnyNotFound;
    }

    /**
     * @return bool
     */
    public function hasMore() : bool
    {
        return $this->payloadLeft;
    }

    /**
     * @return bool
     */
    public function hasTime() : bool
    {
        return ( time() - $this->startTime ) <= $this->timeLimit;
    }

    /**
     * Is Server failure
     *
     * @return bool
     */
    public function isHTTPFailed() : bool
    {
        return $this->httpFailed;
    }

    /**
     * @param int $limit
     */
    public function setTimeLimit(int $limit)
    {
        $this->timeLimit = $limit;
    }

    /**
     * @param bool $value Set True to use new JWT for Sage Cloud ID ( default : false )
     */
    public function setNoCacheToken(bool $value)
    {
        $this->noCacheToken = $value;
    }

    /**
     * Run count getter
     *
     * @return int
     */
    public function getRunCount() : int
    {
        return $this->runCount;
    }

    /**
     * @return bool
     */
    public function isSuccess() : bool
    {
        return $this->success;
    }

    /**
     * Subledger Feedback Logger
     *
     * @param string $msg
     * @param string $level
     * @param string $prefix
     */
    public static function log(string $msg, $level = LogManager::INFO, $prefix = self::LOG_PREFIX)
    {
        if ( ! self::isDebug() && $level === LogManager::DEBUG ) {
            return;
        }
        $cny = GetMyCompany();
        LogToFile("{$prefix} : {$cny} : {$msg}", LOG_FILE, false, $level);
    }

    /**
     * @return bool|null
     */
    public static function isDebug()
    {
        if ( self::$isDebug === null ) {
            try {
                self::$isDebug = (bool)self::getIAConfig('SL_FEEDBACK');
            } catch ( Exception $e ) {
                self::$isDebug = false;
            }
        }

        return self::$isDebug;
    }

    /**
     * Publish the Metrics on destroy
     */
    public function __destruct()
    {
        if ( $this->metricsObj instanceof IAMetrics ) {
            $this->metricsObj->publish();
        }
    }

    /**
     * Get Subledger Feedback Config from ia_init.cfg
     *
     * @param string $key
     *
     * @return string
     * @throws Exception
     */
    public static function getIAConfig($key)
    {
        $config = GetValueForIACFGProperty(self::IACFG_PROP);
        if ( ! isset($config[$key]) ) {
            throw new SubledgerFeedbackException(SubledgerFeedbackException::INVALID_PROPERTY);
        }

        return $config[$key];
    }

}