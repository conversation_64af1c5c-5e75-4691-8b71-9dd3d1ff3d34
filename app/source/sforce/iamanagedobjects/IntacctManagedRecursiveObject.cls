<?php

/**
 * Class IntacctManagedRecursiveObject
 *
 * Implementation of the parent class for all objects with the field referencing
 * to a parent object of its own type.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2017 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */
abstract class IntacctManagedRecursiveObject extends IntacctManagedUpdatableObject
{
    const ORIGINAL_PARENT_ID_KEY = 'original-parent-id';

    /**
     * Checks if the record has a parend field that wasn't converted to the external Id.
     * If such a field found the record is assigned the external id and the extra field is deleted.
     *
     * @param string[] $sforceReadyRecord
     */
    public function adjustSforceReadyRecord(&$sforceReadyRecord)
    {
        static $subscriptionManager = null;
        if ($subscriptionManager === null) {
            $subscriptionManager = new IMSSubscriptionManager();
        }
        if (isset($sforceReadyRecord[self::ORIGINAL_PARENT_ID_KEY])) {
            $cacheKey =
                $this->getSubscriptionObject() .
                $sforceReadyRecord[self::ORIGINAL_PARENT_ID_KEY] .
                SforceNamespaceHandler::SUBSCRIBER_NAME;

            $valueFromCache = SubscriptionManager::getFromCache($cacheKey);
            if ( ! isset($valueFromCache) ) {
                // Couldn't find in the cache. Read the external Id
                $externalId = $subscriptionManager->GetExternalId($sforceReadyRecord[self::ORIGINAL_PARENT_ID_KEY],
                                                                  $this->getSubscriptionObject(),
                                                                  SforceNamespaceHandler::SUBSCRIBER_NAME);
                if ($externalId) {
                    SubscriptionManager::addToCache($cacheKey, $externalId);
                }
            } else {
                $externalId = $valueFromCache;
            }
            $sforceReadyRecord[$this->getSforceParentField()] = $externalId;

            unset($sforceReadyRecord[self::ORIGINAL_PARENT_ID_KEY]);
        }
    }

    /**
     * Returns the name of the parent field for the object of the specific type.
     * The default is PARENTKEY which can be overridden by the extending
     * classes
     *
     * @return string
     */
    public function getParentField()
    {
        return 'PARENTKEY';
    }

    /**
     * The object has a reference to its parent.
     *
     * @return bool
     */
    public function isReversible()
    {
        return true;
    }

    /**
     * Implementation of the default method returning query parameters for the intacct objects referring
     * to the parent via the PARENTKEY field from the Intacct database.
     *
     * @return array
     */
    public function getQueryParams()
    {
        $recursive = [
            'start' => [
                [$this->getParentField(), 'is null']
            ]
        ];
        $params = ['selects' => $this->intacctFields, 'recursive' => $recursive];
        $params['selects'][] = 'RECORDNO';
        $params['usemst'] = true;

        return $params;
    }

}