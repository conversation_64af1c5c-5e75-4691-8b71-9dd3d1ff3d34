-- DB Migration Changes for Asset Disposal
ALTER TABLE FAASSET
    ADD (
        DISPOSALDATE DATE,
        DISPO<PERSON><PERSON><PERSON><PERSON> CHAR(1 CHAR),
        SALEPRICE NUMBER(14,2),
        DISPOSALCOMMENTS VARCHAR2(4000 CHAR),
        REC<PERSON><PERSON><PERSON>ESACCTKEY NUMBER(15,0),
        <PERSON>IN<PERSON>OSSACCTKEY NUMBER(15,0),
        CONSTRAINT FK_FAASSET_RECEIVABLESACCTKEY FOREIGN KEY (CNY#, RECEIVABLESACCTKEY) REFERENCES BASEACCOUNT (CNY#, RECORD#) DEFERRABLE ENABLE,
        CONSTRAINT FK_FAASSET_GAINLOSSACCTKEY FOREIGN KEY (CNY#, <PERSON><PERSON><PERSON><PERSON>SACCTKEY) REFERENCES BASEACCOUNT (CNY#, RECORD#) DEFERRABLE ENABLE
        )
/

CREATE INDEX IX_FAASSET_RECEIVABLESACCTKEY ON FAASSET (CNY#, REC<PERSON><PERSON><PERSON>ESACCTKEY) TABLESPACE ACCTINDX INVISIBLE
/

CREATE INDEX IX_FAASSET_GAINLOSSACCTKEY ON FAASSET (CNY#, <PERSON>INLOSSACCTKEY) TABLESPACE ACCTINDX INVISIBLE
/

ALTER TABLE FADEPRSCHEDULE
    ADD (
        GAINLOSSAMOUNT NUMBER(14, 2),
        DISPOSEDGLBATCHKEY NUMBER(15,0),
        CONSTRAINT FK_FADEPRSCHEDULE_DISPOSEDGLBATCHKEY FOREIGN KEY (CNY#, DISPOSEDGLBATCHKEY) REFERENCES GLBATCH (CNY#, RECORD#) DEFERRABLE ENABLE
        )
/

CREATE INDEX IX_FADEPRSCHEDULE_DISPOSEDGLBATCHKEY ON FADEPRSCHEDULE (CNY#, DISPOSEDGLBATCHKEY) TABLESPACE ACCTINDX INVISIBLE
/
