<?

$krenewaltemplateQueries['QRY_GET_SOURCE_HEADER_DETAILS'] = array (
	'QUERY'		=> 'select  
                        docpar.docid docpar,
                        dochdr.docid docid,
                        dochdr.whencreated whencreated,
                        customer.customerid||\'--\'||customer.name customer,
                        renewalmacro.macroid renewalmacro
                    from
                        renewalsdochdr,
                        docpar,
                        dochdr,
                        customer,
                        renewalmacro
                    where   
                        renewalsdochdr.cny#=?
                        and docpar.cny#=?
                        and dochdr.cny#=?
                        and customer.cny#=?
                        and renewalmacro.cny#=?
                        and renewalsdochdr.record#=?
                        and docpar.record#=renewalsdochdr.docparkey
                        and dochdr.record# = renewalsdochdr.dochdrkey
                        and customer.entity= dochdr.entity
                        and renewalmacro.record#=renewalsdochdr.renewalmacrokey', 
	'ARGTYPES' 	=> array ('integer','integer', 'integer','integer', 'integer', 'integer')
);

$krenewaltemplateQueries['QRY_GET_SOURCE_LINELEVEL_DETAILS'] = array (
	'QUERY'		=> 'select  
                        rendocent.itemkey || \'--\' || rendocent.itemdescr item
                    from
                        renewalsdocentry rendocent
                    where 
                        rendocent.cny# = ?
                        and rendocent.renewalsdochdrkey = ?
                    order by item', 
	'ARGTYPES' 	=> array ('integer','integer')
);