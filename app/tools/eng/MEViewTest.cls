<?php
/**
 * Class to test the new mega views
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2015 Intacct Corporation, All Rights Reserved
 */

/**
 * Test the new mega views by comparing definitions and data with old view and base objects
 */
class MEViewTest
{

    /**
     * @var string[][] $newViews
     */
    private $newViews = array();

    /**
     * @var string[][] $oldViews
     */
    private $oldViews = array();

    /**
     * @var string[][] $baseObjects
     */
    private $baseObjects = array();

    /**
     * @var string $owner
     */
    private $owner = '';

    /**
     * @var string[][] $results
     */
    private $results = array();

    /**
     * @var string[] $exclusions
     */
    private static $exclusions = array('V_VMEGLACCTGRP_HIERARCHY');

    public function __construct()
    {
        $this->newViews = $this->getViewList('MV_', '', $this->owner);
        $this->oldViews = $this->getViewList('V_%ME', '', $this->owner);
        $this->baseObjects = $this->getBaseObjects();
        $this->resetResults();
    }

    /**
     * Reset the results array. Used before running a new test.
     */
    public function resetResults()
    {
        $this->results = array('warn' => array(), 'error' => array());
    }

    /**
     * Compare definitions for new views vs old views and base objects.
     *
     * @return array the results of compare
     */
    public function compareDefinitions()
    {
        foreach ($this->newViews as $newView => $newCols) {
            $oldView = substr($newView, 1);
            $oldCols = $this->oldViews[$oldView];

            if (!$oldCols) {
                $this->results['warn'][] = "New mega view $newView doesn't have a corresponding old view, $oldView.";
            } else {
                //$diffNew = array_diff($newCols, $oldCols);
                $diffOld = array_diff($oldCols, $newCols);
                if ($diffOld) {
                    $this->results['warn'][] = "New mega view $newView has different columns than old view, " .
                        "$oldView. Missing cols: " . implode(',', $diffOld);
                }
            }

            if (substr($newView, 0, 5) == 'MV_ME') {
                $objName = substr($newView, 5);
            } else if (substr($newView, 0, 6) == 'MV_VME') {
                $objName = 'V_' . substr($newView, 6);
            } else {
                $this->results['warn'][] = "Can't detect the base object name from the new mega view name $newView";
            }

            /** @noinspection PhpUndefinedVariableInspection */
            if ($objName) {
                if (isset($this->baseObjects[$objName])) {
                    $objCols = $this->baseObjects[$objName];
                    $diffObj = array_diff($objCols, $newCols);
                    if ($diffObj) {
                        $this->results['warn'][] = "New mega view $newView has different columns than the base " .
                            "object, $objName. Missing cols: " . implode(',', $diffObj);
                    }
                } else {
                    $this->results['warn'][] = "New mega view $newView doesn't have a corresponding base object, " .
                        "$objName.";
                }
            }
        }

        return $this->results;
    }

    /**
     * Compare the results returned by all views
     *
     * @return array the results
     */
    public function compareAllViewResults()
    {
        foreach ( $this->newViews as $newView => $newCols) {
            $oldView = substr($newView, 1);
            $oldCols = $this->oldViews[$oldView];
            if ($oldCols) {
                $this->compareViewData($newView, $oldView, $oldCols);
            }
        }

        return $this->results;
    }

    /**
     * Compare one view results
     *
     * @param string $newView   the new view
     * @param string $oldView   the old view
     * @param array  $colsParam the column list
     */
    private function compareViewData($newView, $oldView, $colsParam)
    {
        if (in_array($oldView, self::$exclusions)) {
            return;
        }

        $cols = implode(',', $colsParam);
        $pred = "where cny# = :1";
        $cny = GetMyCompany();

        /* $res= QueryResult(array("select count(1) q from $this->owner.$newView $pred", $cny));
        LogtoFile("Rows: " . $res[0]['Q'] . "\n");
        $res= QueryResult(array("select count(1) q from $this->owner.$oldView $pred", $cny));
        LogtoFile("Rows: " . $res[0]['Q'] . "\n"); */

        $stmt = array(
            "select count(1) q from (select $cols from $this->owner.$newView $pred " .
            "minus select $cols from $this->owner.$oldView $pred " .
            "union all " .
            "select $cols from $this->owner.$oldView $pred minus select $cols from $this->owner.$newView $pred) a ",
            $cny
        );

        $res = QueryResult($stmt);
        if (!is_array($res) || count($res) != 1) {
            $this->results['error'][] = "Could not query $newView and $oldView.";
        } else if ($res[0]['Q'] != 0) {
            $this->results['error'][] = "The new view, $newView, produces different results than the old view, " .
                "oldView - " . $res[0]['Q'] . " rows";
        } else {
            $res = QueryResult(array("select count(1) C from $this->owner.$newView $pred", $cny));
            if (isset($res[0]['C']) && $res[0]['C'] == 0) {
                $this->results['warn'][] = "The views $newView and $oldView have no data.";
            }
        }
    }

    /**
     * Fetch the list of views and their columns from DB catalogs.
     *
     * @param string $prefix  the prefix of the views
     * @param string $extra   the extra filters for list
     * @param string &$owner  the owner place holder
     *
     * @return string[][]  the list of views
     */
    private function getViewList($prefix, $extra, &$owner)
    {
        $stmt = array(
            "select owner, table_name name, column_name col from all_tab_cols " .
            "where data_type not in ('CLOB', 'BLOB') and (owner, table_name) in " .
            "(select owner, view_name from ALL_VIEWS " .
            "where view_name like :1 || '%' " .
            ($extra ? " and $extra " : "") .
            "and owner = upper(sys_context('TMCtx', 'OWNERID'))) " .
            " and VIRTUAL_COLUMN = 'NO' and USER_GENERATED = 'YES' order by name, col",
            $prefix
        );
        $res = QueryResult($stmt);
        $views = array();
        $owner = $res ? $res[0]['OWNER'] : '';
        foreach ($res as $row) {
            $views[$row['NAME']][] = $row['COL'];
        }
        return $views;
    }

    /**
     *  Get the list of base objects from DB catalogs.
     *
     * @return string[][] the list of base objects
     */
    private function getBaseObjects()
    {
        $stmt = array(
            "select owner, table_name name, column_name col from all_tab_cols " .
            "where data_type not in ('CLOB', 'BLOB') and (" .
            "(owner, table_name) in (select owner, table_name from all_tables " .
            "where owner = upper(sys_context('TMCtx', 'OWNERID')) and table_name not like 'AU_%' " .
            " and table_name not like 'SYS_%' and table_name not like 'TEMP%' " .
            ") " .
            " or " .
            "(owner, table_name) in " .
            "(select owner, view_name from ALL_VIEWS " .
            "where view_name not like 'V_ME%' and view_name not like 'V_VME%' " .
            "and owner = upper(sys_context('TMCtx', 'OWNERID'))) " .
            ") and VIRTUAL_COLUMN = 'NO' and USER_GENERATED = 'YES' order by name, col",
        );
        $res = QueryResult($stmt);
        $objects = array();
        foreach ($res as $row) {
            $objects[$row['NAME']][] = $row['COL'];
        }
        return $objects;
    }

    /**
     * Pretty print the results under the given header.
     *
     * @param string $header  the header to use for printing
     */
    public function printResults($header)
    {
        echo "\n\n$header\n\n";
        echo "    Done comparing views (" . count($this->newViews) . " views): " . count($this->results['error']) .
            " errors and " . count($this->results['warn']) . " warnings.\n\n";

        if ($this->results['error']) {
            echo "    Errors:\n\n    " . implode("\n    ", $this->results['error']);
            echo "\n\n";
        }
        if ($this->results['warn']) {
            echo "    Warnings:\n\n    " . implode("\n    ", $this->results['warn']);
            echo "\n\n";
        }
    }
}