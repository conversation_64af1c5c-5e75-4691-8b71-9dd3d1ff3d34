<?php

/**
 * Header class for Ship to
 *
 * Class ShipToHeader
 */
class ShipToHeader extends HeaderGeneratorDecorator
{
    /**
     * @var array   $cnDetails
     */
    private $cnDetails;

    /**
     * @var string  $context
     */
    private $context;

    /**
     * ProjectGenerator constructor.
     *
     * @param HeaderGenerator $generator
     * @param string          $context
     * @param array           $cnDetails
     */
    function __construct(HeaderGenerator $generator, $context, $cnDetails)
    {
        parent::__construct($generator);
        $this->context = $context;
        $this->cnDetails = $cnDetails;
    }

    /**
     * @param string                    $grouping
     * @param GroupHandler              $groupHandler
     * @param GenInvoiceBillableLine[]  $billableLines
     *
     * @return mixed
     */
    public function generate($grouping, $groupHandler, $billableLines)
    {
        $header = parent::generate($grouping, $groupHandler, $billableLines);

        if ($this->context == HeaderContext::CONTRACT) {
            $cnDetail = $this->cnDetails[$groupHandler->getGroupValue(ContractGrouper::ID, $grouping)];
            $header[GenInvoiceUtil::SHIPTOKEY] = $cnDetail[GenInvoiceUtil::SHIPTOKEY];
        } elseif ($this->context == HeaderContext::RESOLVE) {
            $customerDetails = GenInvoiceUtil::getCustomerDetailsToResolve(
                $groupHandler->getGroupValue(CustomerGrouper::ID, $grouping));
            $contractInfos = GenInvoiceUtil::getContractInfoForHeader($billableLines, $this->cnDetails);
            $header[GenInvoiceUtil::SHIPTOKEY] = GenInvoiceUtil::getResolvedKey($customerDetails,
                                                                                $contractInfos,
                                                                                GenInvoiceUtil::SHIPTOKEY,
                                                                                true,
                                                                                GenInvoiceUtil::DISPLAYCONTACTKEY);
        }

        return $header;
    }
}