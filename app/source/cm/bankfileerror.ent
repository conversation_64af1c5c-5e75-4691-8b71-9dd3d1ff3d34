<?php
/**
 * bankfileerror.ent
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Intacct Corporation, All Rights Reserved
 */
$kSchemas['bankfileerror'] = [
    'object'        => [
        'RECORDNO',
        'BANKFILEKEY',
        'PAYMENTKEY',
        'DESCRIPTION',
    ],
    'schema'        => [
        'RECORDNO'    => 'record#',
        'BANKFILEKEY' => 'bankfilekey',
        'PAYMENTKEY'  => 'paymentkey',
        'DESCRIPTION' => 'description',
    ],
    'fieldinfo'     => [
        [
            'path'     => 'DESCRIPTION',
            'desc'     => 'IA.DESCRIPTION',
            'fullname' => 'IA.DESCRIPTION',
            'type'     => [
                'type'  => 'text',
                'ptype' => 'text',
            ],
            'id'        =>  1,
        ],
        [
            'path'     => 'RECORDNO',
            'desc'     => 'IA.RECORD_NUMBER',
            'fullname' => 'IA.RECORD_NUMBER',
            'readonly'   => true,
            'type'     => [
                'type'      => 'integer',
                'ptype'     => 'sequence',
                'maxlength' => 8,
                'size'      => 8,
                'format'    => $gRecordNoFormat,
            ],
            'id'       => 2,
        ],
    ],
    'table'         => 'bankfileerror',
    'vid'           => 'RECORDNO',
    'autoincrement' => 'RECORDNO',
    'module'        => 'cm',
    'pluralprintas' => 'IA.BANK_FILES_DETAIL',

];
