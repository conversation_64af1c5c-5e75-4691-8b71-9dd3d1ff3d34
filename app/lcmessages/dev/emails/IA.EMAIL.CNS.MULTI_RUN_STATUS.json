[{"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.SUBJECT.RECUR", "value": "Intacct recurring consolidation notification for '${CONSOLIDATION_BOOK}'"}, {"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.SUBJECT.WITHOUTRECUR", "value": "Intacct update consolidation notification for '${CONSOLIDATION_BOOK}'"}, {"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.BODY.NOFAILEDRECUR", "value": "Hello,<br> A recurring consolidation of the consolidation ${TYPE_OF_CONSOLIDATION} '${CONSOLIDATION_BOOK}' was processed on ${CURRENT_DATE}. All entities were successfully consolidated<br><br>", "markdown": true}, {"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.BODY.NOFAILEDUPDATE", "value": "Hello,<br> Update consolidation of the consolidation ${TYPE_OF_CONSOLIDATION} '${CONSOLIDATION_BOOK}' was processed on ${CURRENT_DATE}. All entities were successfully consolidated<br><br>", "markdown": true}, {"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.BODY.FAILEDRECUR", "value": "Hello,<br> A recurring consolidation of the consolidation ${TYPE_OF_CONSOLIDATION} '${CONSOLIDATION_BOOK}' was processed on ${CURRENT_DATE}, with ${NO_OF_SUCCESS} entities consolidated successfully and ${NO_OF_FAILED} failures. You'll find a summary of the consolidation below, along with an error log for any entities that could not be consolidated.<br><br>", "markdown": true}, {"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.BODY.FAILEDUPDATE", "value": "Hello,<br> Update consolidation of the consolidation ${TYPE_OF_CONSOLIDATION} '${CONSOLIDATION_BOOK}' was processed on ${CURRENT_DATE}, with ${NO_OF_SUCCESS} entities consolidated successfully and ${NO_OF_FAILED} failures. You'll find a summary of the consolidation below, along with an error log for any entities that could not be consolidated.<br><br>", "markdown": true}, {"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.BODY.CONSOLIDATIONSUMMARY", "value": "<b>Consolidation summary</b><br><br><table border='0'><tr><td>${TYPE_OF_CONSOLIDATION} name </td><td>${CONSOLIDATION_BOOK}<td></tr><tr><td>Parent company </td><td>${COMPANY_NAME}<td></tr><tr><td>Consolidation period </td><td>${PERIOD_NAME}<td></tr>", "markdown": true}, {"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.BODY.SUCCESSENTITYHEADER", "value": "<tr><td>Entities consolidated</td><td></td></tr>", "markdown": true}, {"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.BODY.SUCCESSENTITYINFO", "value": "<tr><td></td><td>${ENTITY}</td></tr>", "markdown": true}, {"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.BODY.FAILEDENTITYHEADER", "value": "<tr><td>Entities failed</td><td></td></tr>", "markdown": true}, {"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.BODY.FAILEDENTITYINFO", "value": "<tr><td></td><td>${ENTITY}</td></tr>", "markdown": true}, {"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.BODY.PERIODSCONSOLIDATEDHEADER", "value": "<tr><td>Periods consolidated</td><td></td></tr>", "markdown": true}, {"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.BODY.PERIODSCONSOLIDATEDINFO", "value": "<tr><td></td><td>${PERIOD}</td></tr>", "markdown": true}, {"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.BODY.ERRORMESSAGEHEADER", "value": "<b>Error log</b><br><br>${NO_OF_FAILED} entities could not be processed. <br>-----------------------------<br>", "markdown": true}, {"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.BODY.ERRORMESSAGE", "value": "Description : ${DESCRIPTION} - ${CDESCRIPTION}<br>Correction  : ${CORRECTION}<br>===================================================<br>", "markdown": true}, {"id": "IA.EMAIL.CNS.MULTI_RUN_STATUS.BODY.SIGNATURE", "value": "<br> <br> <PERSON><PERSON>,<br> -- The Intacct Team", "markdown": true}]