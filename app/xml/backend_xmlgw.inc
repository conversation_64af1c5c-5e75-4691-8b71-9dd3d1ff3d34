<?php
//=============================================================================
//
//	FILE:			backend_xmlgw.inc
//	AUTHOR:			<PERSON><PERSON>
//	DESCRIPTION:	backend functions for the xmlgw APIs
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct
//	corporation and is protected by the copyright laws. Information herein
//	may not be used, copied or disclosed in whole or part without prior
//	written consent from Intacct Corporation.
//
//=============================================================================

/**
 * @param array  $currapi
 * @param array  $result
 *
 * @return bool
 */
function AcctBalanceWrapper($currapi, &$result)
{

    epp("In AcctBalanceWrapper ...");
    global $gErr;

    $invalid = false;
    $ret = true;

    /** @noinspection PhpUnusedLocalVariableInspection */
    $lines = array();
    $result = array();
    $stat_acct = 'I';
    $includeRepBook = true;

    //CHECK FOR VALIDATOR ERRORS
    if ($currapi['REPORTINGPERIODNAME'] && $currapi['STARTDATE']) {
        $invalid = true;
    }
    if ($currapi['GLACCOUNTNO'] && $currapi['ACCOUNTGROUPNAME']) {
        $invalid = true;
    }
    if ( ($currapi['STARTACCOUNTNO'] || $currapi['ENDACCOUNTNO']) && $currapi['ACCOUNTGROUPNAME']) {
        $invalid = true;
    }
    if ( ($currapi['STARTACCOUNTNO'] || $currapi['ENDACCOUNTNO']) && $currapi['GLACCOUNTNO']) {
        $invalid = true;
    }

    if (isset($currapi['INCLUDEREPORTINGBOOK']) && strtolower($currapi['INCLUDEREPORTINGBOOK']) == 'false') {
        $includeRepBook = '';
    }

    if ($invalid) {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "Invalid subelement for get_trialbalance."
        );
        return false;
    }

    $rpname = trim($currapi["REPORTINGPERIODNAME"]);

    //retrieve group by location flag
    $groupByLocation = $currapi["GROUPBYLOCATION"] ?? false;

    $startdate = $currapi["STARTDATE"];
    $enddate = $currapi["ENDDATE"];
    $debitcreditbalance  =  (isset($currapi["DEBITCREDITBALANCE"]) && strtolower($currapi["DEBITCREDITBALANCE"]) == 'true');
    if ($rpname) {
        $where = array("NAME = :2", $rpname);
    } else {
        $where = array("start_date = :2 and end_date = :3", $startdate, $enddate);
    }
    $rp = GetNObjects('glbudgettype', '*', $where);
    $rp = $rp[0];

    if (!$rp) {
        if (array_key_exists("STARTDATE", $currapi) and array_key_exists("ENDDATE", $currapi)) {
            // reporting period not found so use these dates.
            // there aren't any period names. Thus the closing into accounts may not give correct results
            $startdate = $currapi["STARTDATE"];
            $enddate = $currapi["ENDDATE"];
            $rp = array('START_DATE' => $startdate, 'END_DATE' => $enddate, 'PERIOD#' => 'custom');
        } else {
            $gErr->addError('BL03000170', __FILE__ . '.' . __LINE__, "No reporting period found");
            return false;
        }
    }

    //VALIDATE ANY ACCOUNTNO'S
    if ($currapi['GLACCOUNTNO']) {
        if (!IsValidGLAccountNo($currapi['GLACCOUNTNO']) ) {
            $ret = false;
        }
        $startaccountno = $endaccountno = $currapi['GLACCOUNTNO'];
    } else {
        $startaccountno = $currapi['STARTACCOUNTNO'];
        $endaccountno = $currapi['ENDACCOUNTNO'];
    }

    //CHECK FOR VALID LOCATIONID
    if ($currapi['LOCATIONID']) {
        if(is_array($currapi['LOCATIONID'])) {
            $locations = $currapi['LOCATIONID'];
        } else {
            $locationid = $currapi['LOCATIONID'];
            if (!IsValidLocation($locationid) ) {
                $ret = false;
            } else {
                $locations = array(array('LOCATIONID' => $locationid));
            }
        }
    }

    //CHECK FROM VALID DEPARTMENT ID
    if ($currapi['DEPARTMENTID']) {
        $deptid = $currapi['DEPARTMENTID'];
        if (!IsValidDepartment($deptid) ) {
            $ret = false;
        } else {
            $departments = array(array('DEPARTMENTID' => $deptid));
        }
    }

    //CHECK FROM VALID Account Group Names
    if ($currapi['ACCOUNTGROUPNAME']) {
        $acctgroup = $currapi['ACCOUNTGROUPNAME'];
        if (!IsValidAccountGroupName($acctgroup) ) {
            $ret = false;
        }
    }


    //GET SHOWZEROBALANCES

    if ($currapi['SHOWZEROBALANCES']) {
        $showzerobal = $currapi['SHOWZEROBALANCES'];
        // SHOULD BE EITHER TRUE OR FALSE
        if (!GetValidChoice($_zeroAccts, $showzerobal, array('true', 'false'), array('Y','N'), 'showzerobalances') ) {
            $ret = false;
        }
    }

    //CHECK THE VALUE OF DEPARTMENT SUBS.
    if($currapi['DEPT_SUBS']) {
        // SHOULD BE EITHER TRUE OR FALSE
        if(!GetValidChoice($_dept_subs, $currapi['DEPT_SUBS'], array('true', 'false'), array('Y','N'), 'dept_subs')){
            $ret = false;
        }
    }

    //CHECK THE VALUE OF LOCATION SUBS.
    if($currapi['LOC_SUBS']) {
        // SHOULD BE EITHER TRUE OR FALSE
        if(!GetValidChoice($_dept_subs, $currapi['LOC_SUBS'], array('true', 'false'), array('Y','N'), 'loc_subs')){
            $ret = false;
        }
    }

    //CHECK THE VALUE OF PROJECTID SUBS.
    if($currapi['PROJECTID_SUBS']) {
        // SHOULD BE EITHER TRUE OR FALSE
        if(!GetValidChoice($_loc_subs, $currapi['PROJECTID_SUBS'], array('true', 'false'), array('Y','N'), 'projectid_subs')){
            $ret = false;
        }
    }

    //CHECK THE VALUE OF CUSTOMERID SUBS.
    if($currapi['CUSTOMERID_SUBS']) {
        // SHOULD BE EITHER TRUE OR FALSE
        if(!GetValidChoice($_cust_subs, $currapi['CUSTOMERID_SUBS'], array('true', 'false'), array('Y','N'), 'customerid_subs')){
            $ret = false;
        }
    }

    //CHECK THE VALUE OF VENDORID SUBS.
    if($currapi['VENDORID_SUBS']) {
        // SHOULD BE EITHER TRUE OR FALSE
        if(!GetValidChoice($_vendor_subs, $currapi['VENDORID_SUBS'], array('true', 'false'), array('Y','N'), 'vendorid_subs')){
            $ret = false;
        }
    }

    //CHECK THE VALUE OF EMPLOYEEID SUBS.
    if($currapi['EMPLOYEEID_SUBS']) {
        // SHOULD BE EITHER TRUE OR FALSE
        if(!GetValidChoice($_employee_subs, $currapi['EMPLOYEEID_SUBS'], array('true', 'false'), array('Y','N'), 'employeeid_subs')){
            $ret = false;
        }
    }

    //CHECK THE VALUE OF CLASSID SUBS.
    if($currapi['CLASSID_SUBS']) {
        // SHOULD BE EITHER TRUE OR FALSE
        if(!GetValidChoice($_class_subs, $currapi['CLASSID_SUBS'], array('true', 'false'), array('Y','N'), 'classid_subs')){
            $ret = false;
        }
    }

    //CHECK THE VALUE OF INCLUDEREPORTINGBOOK.
    if($currapi['INCLUDEREPORTINGBOOK']) {
        // SHOULD BE EITHER TRUE OR FALSE
        if(!GetValidChoice($_inc_rep, $currapi['INCLUDEREPORTINGBOOK'], array('true', 'false'), array('Y','N'), 'includereportingbook')){
            $ret = false;
        }
    }

    //CHECK THE VALUE OF DEBITCREDITBALANCE.
    if($currapi['DEBITCREDITBALANCE']) {
        // SHOULD BE EITHER TRUE OR FALSE
        if(!GetValidChoice($_inc_rep, $currapi['DEBITCREDITBALANCE'], array('true', 'false'), array('Y','N'), 'debitcreditbalance')){
            $ret = false;
        }
    }

    //CHECK THE VALUE OF CONTRACTID_SUBS.
    if($currapi['CONTRACTID_SUBS']) {
        // SHOULD BE EITHER TRUE OR FALSE
        if(!GetValidChoice($_inc_rep, $currapi['CONTRACTID_SUBS'], array('true', 'false'), array('Y','N'), 'contractid_subs')){
            $ret = false;
        }
    }

    //CHECK THE VALUE OF WAREHOUSEID_SUBS.
    if($currapi['WAREHOUSEID_SUBS']) {
        // SHOULD BE EITHER TRUE OR FALSE
        if(!GetValidChoice($_inc_rep, $currapi['WAREHOUSEID_SUBS'], array('true', 'false'), array('Y','N'), 'warehouseid_subs')){
            $ret = false;
        }
    }

    //CHECK THE VALUE OF TASKID_SUBS.
    if($currapi['TASKID_SUBS']) {
        // SHOULD BE EITHER TRUE OR FALSE
        if(!GetValidChoice($_inc_rep, $currapi['TASKID_SUBS'], array('true', 'false'), array('Y','N'), 'taskid_subs')){
            $ret = false;
        }
    }

    //CHECK THE VALUE OF COSTTYPEID_SUBS.
    if($currapi['COSTTYPEID_SUBS']) {
        // SHOULD BE EITHER TRUE OR FALSE
        if(!GetValidChoice($_inc_rep, $currapi['COSTTYPEID_SUBS'], array('true', 'false'), array('Y','N'), 'costtypeid_subs')){
            $ret = false;
        }
    }

    //CHECK THE VALUE OF SHOWLOCDETAIL
    if($currapi['SHOWLOCDETAIL']) {
        // SHOULD BE EITHER TRUE OR FALSE
        if(!GetValidChoice($_inc_rep, $currapi['SHOWLOCDETAIL'], array('true', 'false'), array('Y','N'), 'showlocdetail')){
            $ret = false;
        }
    }

    //CHECK THE VALUE OF SHOWDEPTDETAIL
    if($currapi['SHOWDEPTDETAIL']) {
        // SHOULD BE EITHER TRUE OR FALSE
        if(!GetValidChoice($_inc_rep, $currapi['SHOWDEPTDETAIL'], array('true', 'false'), array('Y','N'), 'showdeptdetail')){
            $ret = false;
        }
    }
    
   
    //retrieving statistical account
    if (isset($currapi["STATISTICAL"])) {
        switch (strtolower($currapi["STATISTICAL"])) {
            case "include" :
                $stat_acct = "I";
                break;
            case "exclude" :
                $stat_acct = "E";
                break;
            case "only" :
                $stat_acct = "O";
                break;
            default:
                eppp('error with GetValidChoice');
                $gErr->addError(
                    'XL03000004', __FILE__.'.'.__LINE__,
                    "'" . $currapi["STATISTICAL"] . "' is not a valid value for the element statistical, the valid values are: only, include, exclude");
                return false;
        }
    }

    //IF GAAP , TAX IS PRESENT REPLACE IT BY GAAPADJ ,TAXADJ RESPECTIVELY.
    if (is_array($currapi['ADJBOOKS']['ADJBOOK'])){
        //IF MORE THAN 1 BOOKS ARE PRESENT.
        $adjbooks = $currapi['ADJBOOKS']['ADJBOOK'];
    } else if (isset($currapi['ADJBOOKS']['ADJBOOK'])) {
        //if only one ADJBOOK IS PERSENT
        $adjbooks = array($currapi['ADJBOOKS']['ADJBOOK']);
    }

    /** @noinspection PhpUndefinedVariableInspection */
    if ($adjbooks) {
        $searchReplace = ['GAAP' => 'GAAPADJ', 'TAX' => 'TAXADJ'];

        foreach ($adjbooks as $key => $value) {
            if(array_key_exists(trim($value), $searchReplace)) {
                $adjbooks[$key] = $searchReplace[trim($value)];
            }
        }
    }
    $adjbooks = (isset($adjbooks) && is_array($adjbooks)) ? implode('#~#', $adjbooks) : null;

    //GET ALL DEPARTMENTS, IF DETAIL DETAIL REQUIRED
    if ($currapi['SHOWDEPTDETAIL'] == 'true') {
        // get the department list
        global $gManagerFactory;
        $mgrDept = $gManagerFactory->getManager('department');
        $departments = $mgrDept->GetList(array('selects' => array('DEPARTMENTID')));
        $departments[] = array('DEPARTMENTID' => '');
    } else if (!array_key_exists('DEPARTMENTID', $currapi)) {
        $departments = array(array('DEPARTMENTID' => ''));
    }


    //GET ALL LOCATIONS, IF LOCATION DETAIL REQUIRED
    if ($currapi['SHOWLOCDETAIL'] == 'true') {
        global $gManagerFactory;
        $mgrLoc = $gManagerFactory->getManager('location');
        $locations = $mgrLoc->GetList(array('selects' => array('LOCATIONID')));
        $locations[] = array('LOCATIONID' => '');
    } else if (!array_key_exists('LOCATIONID', $currapi)) {
        $locations = array(array('LOCATIONID' => ''));
    }

    if ($ret) {
        include_once 'std_reports.inc';
        include_once 'backend_tb.inc'; // Since the following function is shifted to this file from std_reports.inc
        $totallines = array();

        //set which reporting book to use.
        if (isset($currapi['REPORTINGBOOK'])) {
            $upperReportingBook = strtoupper($currapi['REPORTINGBOOK']);
            $reportingBook = $currapi['REPORTINGBOOK'];
            if (in_array($upperReportingBook, array('ACCRUAL', 'CASH'))) {
                $reportingBook = $upperReportingBook;
            }
        } else {
            $reportingBook = '';
        }

        // Prepare dimension filters
        $prtParam = $currapi;
        $prtParam['report'] = 'gl_tb';
        $prtParam['mod'] = 'gl';
        $cust_dims = IADimensions::getCustomDimensionObjects();

        $temp = $prtParam['USERDEFINEDDIMENSIONS']['USERDEFINEDDIMENSION'] ?? [];
        if($temp && !is_array($temp[0])){
            $temp = array($temp);
        }

        foreach ($temp as $arrayValue) {
            foreach ($cust_dims as $key=>$value){
                if ($value['entity'] == $arrayValue['OBJECTNAME']) {
                    $prtParam[$key] = $arrayValue['RECORDNAME'] ?? $arrayValue['RECORDID'];
                }
            }
        }
        $rpt = new GLTrialbalanceReporter($prtParam);

        foreach ($rpt->dimensions as $dimension) {
            if (!isNullOrBlank($dimension['referentialDimensions'][$dimension['path']])) {
                foreach($dimension['referentialDimensions'][$dimension['path']] as $referentialDimension) {
                    if (!isNullOrBlank($currapi[$dimension['path']]) && isNullOrBlank($currapi[$referentialDimension])) {
                        $gErr->addError(
                            'XL03000004', __FILE__ . '.' . __LINE__,
                            sprintf(_('%1$s can not be specified without also specifying %2$s.'), $dimension['path'], $referentialDimension)
                        );

                        return false;
                    }
                }
            }
        }

        $dimensionFilterClause = $rpt->GetDimensionFilterClause('', $dimensionValues);
        $includeSubsParams = getIncludeSubs($currapi);

        /** @noinspection PhpUndefinedVariableInspection */
        if (isset($departments) && is_array($departments)) {
            foreach ($departments as $deptid) {
                if ($groupByLocation) {
                    $lines = array();
                    if (isset($deptid) && $deptid['DEPARTMENTID'] != '') {
                        $deptid = array($deptid['DEPARTMENTID']);
                    } else {
                        $deptid = '';
                    }

                    /** @noinspection PhpUndefinedVariableInspection */
                    ProcessTrialBalanceReport(
                        'raw', $rp, $deptid, $locations, '',
                        $startaccountno, $endaccountno, true,
                        $_zeroAccts, $stat_acct, '', $acctgroup, '',
                        date('m/d/Y'), $lines, $groupByLocation,
                        'gl', 'true', 'P', '', '',
                        '', $reportingBook, '', $dimensionFilterClause, $dimensionValues
                        , false, 'S', $includeSubsParams, '',
                        $adjbooks, $includeRepBook , $debitcreditbalance
                    );

                    foreach ($lines as $key => $value) {
                        $totallines[$key] = $value;
                    }
                } else {
                    /** @noinspection PhpUndefinedVariableInspection */
                    foreach ($locations as $locationid) {
                        $lines = array();
                        if (isset($deptid) && $deptid['DEPARTMENTID'] != '') {
                            $xmldeptid = array($deptid['DEPARTMENTID']);
                        } else {
                            $xmldeptid = '';
                        }
                        if (isset($locationid) && $locationid['LOCATIONID'] != '') {
                            $locationid = array($locationid['LOCATIONID']);
                        } else {
                            $locationid = '';
                        }

                        /** @noinspection PhpUndefinedVariableInspection */
                        ProcessTrialBalanceReport(
                            'raw', $rp, $xmldeptid, $locationid, '',
                            $startaccountno, $endaccountno, true,
                            $_zeroAccts, $stat_acct, '', $acctgroup, '',
                            date('m/d/Y'), $lines, $groupByLocation,
                            'gl', 'true', 'P', '', '',
                            '', $reportingBook, '', $dimensionFilterClause, $dimensionValues
                            , false, 'S', $includeSubsParams, '',
                            $adjbooks, $includeRepBook , $debitcreditbalance
                        );

                        if ($currapi['SHOWDEPTDETAIL'] == 'true' || $currapi['SHOWLOCDETAIL'] == 'true') {
                            foreach ($lines as $lkey => $line) {
                                if ($currapi['SHOWDEPTDETAIL'] == 'true') {
                                    $lines[$lkey]['DEPARTMENTID'] = (is_array($xmldeptid)) ? $xmldeptid[0] : $xmldeptid;
                                }
                                if ($currapi['SHOWLOCDETAIL'] == 'true') {
                                    $lines[$lkey]['LOCATIONID'] = (is_array($locationid)) ? $locationid[0]
                                        : $locationid;
                                }
                            }
                        }
                        $totallines = INTACCTarray_merge($totallines, $lines);
                    }
                }
            }
        }
    }

    if ($ret) {
        /** @noinspection PhpUndefinedVariableInspection */
        $result = $totallines;
        /*
        foreach ($lines as $line) {
        $trialbalance = array();
        $trialbalance['accountno'][0]['cdata'] = $line['ACCT_NO'];
        $trialbalance['startbalance'][0]['cdata'] = $line['ACCFWD'];
        $trialbalance['debits'][0]['cdata'] = $line['DEBITS'];
        $trialbalance['credits'][0]['cdata'] = $line['CREDITS'];
        $trialbalance['endbalance'][0]['cdata'] = $line['ACCBAL'];
        $result[] = $trialbalance;
        }*/
    }

    return $ret;

}

/**
 * Initliase $includeSubsParams value, similar to GLTrialbalanceReporter logic
 *
 * @param array $params Input params
 *
 * @return array List of $includeSubsParams values
 */
function getIncludeSubs($params)
{
    $includeSubsParams = array();

    foreach ($params as $key => $param) {
        if (isl_strpos($key, '_SUBS')) {
            if($key === 'DEPT_SUBS')
            {
                $includeSubsParams['dept_SUBS'] = $param;
            }
            elseif ($key === 'LOC_SUBS')
            {
                $includeSubsParams['loc_SUBS'] = $param;
            }
            else
            {
                $includeSubsParams[$key] = $param;
            }
        }
    }

    return $includeSubsParams;
}

/**
 * @param string[] $currapi
 * @param array    $result
 *
 * @return bool
 */
function AccountGroupDetailsWrapper($currapi, &$result)
{
    global $gErr;

    //CHECK FROM VALID Account Group Names
    $acctgroup = $currapi['ACCOUNTGROUPNAME'];
    if (!IsValidAccountGroupName($acctgroup)) {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $ret = false;
    }

    $where = ["NAME = :2", $acctgroup];
    $acctgrp = GetNObjects('glacctgrp', 'RECORD#', $where);
    if (Util::countOrZero($acctgrp) == 0) {
        $gErr->addError(
            '**********', __FILE__ . '.' . __LINE__, "Account group name " .
                                                     $acctgroup . " is invalid."
        );

        return false;
    }

    include_once 'std_reports.inc';
    InitReportBase();

    $groupnames = [];
    $grouprows = [];
    $group = $acctgrp[0]['RECORD#'];

    global $gAccountGroupMap;

    for ($i = 0, $_ct_i=Util::countOrZero($gAccountGroupMap); $i < $_ct_i; $i++) {
        $item = $gAccountGroupMap[$i]['NAME'];
        $grouprows[$item] = ['ALWAYS' => 'T'];
    }

    $groupnames[] = $gAccountGroupMap[$group]['NAME'];

    $report = [
        'listing' => 1,
        'columns' => [],
        'groups'  => $groupnames,
        'rows'    => $grouprows,
    ];

    InitReport($report);

    $result = [];
    $result["accountgroupdetail"][] = xmlize_accountGroupHeirarchy($group);

    return true;
}

/**
 * @param string[] $data
 * @param array    $result
 *
 * @return string[][]|bool
 */
function SalesTotalsWrapper($data, /** @noinspection PhpUnusedParameterInspection */ &$result)
{
    $cust = $data['CUSTOMERID'];
    if (isset($data['REPORTINGPERIODNAME'])) {
        global $gManagerFactory;
        $mgr = $gManagerFactory->getManager('glbudgettype');
        $rp = $mgr->get($data['REPORTINGPERIODNAME']);
        // get the dates
        $startdate = $rp['START_DATE'];
        $enddate = $rp['END_DATE'];
    } else {
        // use the supplied dates.
        $startdate = $data['STARTDATE'];
        $enddate = $data['ENDDATE'];
    }
    /** @noinspection PhpSillyAssignmentInspection */
    $startdate = $startdate;
    /** @noinspection PhpSillyAssignmentInspection */
    $enddate = $enddate;

    // validate dates, etc
    $cny = GetMyCompany();

    $qry = "select substr(dochdr.entity,2) as customerid, docentry.itemkey as itemid,
			icitem.productlinekey as productlineid, sum(docentry.quantity) as quantity, sum(docentry.value) as value
			from dochdr, docentry, icitem
			where ";

    if ($cust != '') {
        $qry .= "dochdr.entity = 'C'||'$cust' and ";
    }

    $qry .= " dochdr.whencreated between '$startdate' and '$enddate' and
			docentry.dochdrkey = dochdr.record# and
			docentry.itemkey = icitem.itemid and
			dochdr.cny# = $cny and
			docentry.cny# = $cny and
			icitem.cny# = $cny
			group by entity, itemkey, productlinekey
			order by customerid, productlineid";

    $res = QueryResult($qry);

    return $res;
}

/**
 * @param string $_acctGroup
 *
 * @return array
 */
function xmlize_accountGroupHeirarchy($_acctGroup)
{
    global $gAccountGroupMap;
    global $gAccountMap;

    $node = $gAccountGroupMap[$_acctGroup];
    $tree = [];
    $tree['accountgroupname'][0]['cdata'] = $node['NAME'];

    $children = $node['CHILDREN'];
    for ($i = 0, $_ct_i = Util::countOrZero($children); $i < $_ct_i; $i++) {
        if ($node['ISLEAF'] != 'T') {
            $tree['accountgroupdetail'][] = xmlize_accountGroupHeirarchy($children[$i]);
        } else {
            $glaccount = [];
            $glaccount['accountno'][0]['cdata'] = $gAccountMap[$children[$i]]['ACCT_NO'];
            $glaccount['title'][0]['cdata'] = $gAccountMap[$children[$i]]['TITLE'];
            $glaccount['normalbalance'][0]['cdata'] =
                ($gAccountMap[$children[$i]]['NORMAL_BALANCE'] > 0) ? "true" : "false";
            $tree['glaccount'][] = $glaccount;
        }
    }

    return $tree;
}

/**
 * @param string $vendorid
 *
 * @return bool
 */
function ValidVendor($vendorid)
{
    include_once 'backend_vendor.inc';
    $res = GetVendors('VENDORID', "VENDORID = '$vendorid'");
    if (Util::countOrZero($res) == 0) {
        return false;
    }

    return true;
}

/**
 * @param string $customerid
 *
 * @return bool
 */
function ValidCustomer($customerid)
{
    include_once 'backend_customer.inc';
    $res = GetCustomers('CUSTOMERID', "CUSTOMERID = '$customerid'");
    if (Util::countOrZero($res) == 0) {
        return false;
    }

    return true;
}

/**
 * @param string $employeeid
 *
 * @return bool
 */
function ValidEmployee($employeeid)
{
    include_once 'backend_employee.inc';
    $res = GetEmployees('EMPLOYEEID', "EMPLOYEEID = '$employeeid'");
    if (Util::countOrZero($res) == 0) {
        return false;
    }

    return true;
}

/**
 * @param string $locationid
 *
 * @return bool
 */
function IsValidLocation($locationid)
{
    global $gErr;
    $where = ["LOCATION_NO = :2", $locationid];
    $res = GetNObjects('location', 'LOCATION_NO', $where);
    if (Util::countOrZero($res) == 0) {
        $gErr->addError(
            '**********', __FILE__ . '.' . __LINE__, "Locationid " .
                                                     $locationid . " is invalid."
        );

        return false;
    }

    return true;
}

/**
 * @param string $deptid
 *
 * @return bool
 */
function IsValidDepartment($deptid)
{
    global $gErr;
    $where = ["DEPT_NO = :2", $deptid];
    $res = GetNObjects('department', 'DEPT_NO', $where);
    if (Util::countOrZero($res) == 0) {
        $gErr->addError(
            '**********', __FILE__ . '.' . __LINE__, "Department no. " .
                                                     $deptid . " is invalid."
        );

        return false;
    }

    return true;
}

/**
 * @param string $acctgroup
 *
 * @return bool
 */
function IsValidAccountGroupName($acctgroup)
{
    global $gErr;
    $where = ["NAME = :2", $acctgroup];
    $res = GetNObjects('glacctgrp', 'NAME', $where);
    if (Util::countOrZero($res) == 0) {
        $gErr->addError(
            '**********', __FILE__ . '.' . __LINE__, "Account group name " .
                                                     $acctgroup . " is invalid."
        );

        return false;
    }

    return true;
}

/**
 * @param string $accountno
 *
 * @return bool
 */
function IsValidGLAccountNo($accountno)
{
    global $gErr;
    $where = ["ACCT_NO = :2", $accountno];
    $res = GetNObjects('glaccount', 'ACCT_NO', $where);
    if (Util::countOrZero($res) == 0) {
        $gErr->addError(
            'BL03000007', __FILE__ . ":" . __LINE__, "Account no. " .
                                                     $accountno . " is invalid."
        );

        return false;
    }

    return true;
}

/**
 * @param array  $returnitem
 * @param string $curritem
 * @param array  $userListArr
 * @param array  $DBListArr
 * @param string $key
 *
 * @return bool
 */
function GetValidChoice(&$returnitem, $curritem, $userListArr, $DBListArr, $key)
{
    global $gErr;
    $listcnt = Util::countOrZero($userListArr);
    for ($i = 0; $i < $listcnt; $i++) {
        if (trim($curritem) == $userListArr[$i]) {
            $returnitem = $DBListArr[$i];

            return true;
        }
    }
    eppp('error with GetValidChoice');
    $gErr->addError(
        'XL03000004', __FILE__ . '.' . __LINE__,
        trim($curritem) . " is not a valid value for the element $key, the valid values are " . join(', ',
                                                                                                     array_values($userListArr))
    );

    return false;
}

/**
 * @param string[] $currapi
 *
 * @return bool
 */
function BillPaymentWrapper($currapi)
{
    global $gErr;
    $amount = array();
    $discount = array();
    $ret = true;
    include_once 'backend_selecttopay.inc';

    $key = $currapi['KEY'];
    $amount[$key] = preg_replace("/[^0-9.-]/", "", $currapi['AMOUNT']);

    if (!GetPRRecord($key, $obj) ) {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "Key " . $key . " does not correspond to any bills."
        );
        $ret = false;
    }

    if ($obj['RECORDTYPE'] != 'pi' && $obj['RECORDTYPE'] != 'pa') {
        /** @noinspection PhpUndefinedVariableInspection */
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "Key " . $invoicekey . " does not correspond to any bills."
        );
        $ret = false;
    }

    if (GetEntityAttribute($obj['VENDENTITY'], 'BILLINGTYPE', 1) == 'B') {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "Bill with key " . $key ." corresponds to a balance forward vendor, please use the xml api select_vendorforpayment for paying the vendor ". $obj['ENTITY']
        );
        $ret = false;
    }

    if ($obj['TOTALDUE'] == 0) {
        //ERROR... BILL IS ALREADY PAID...
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "bill with key " . $key . " is already paid."
        );
        return false;
    }

    if ($obj['TERMKEY'] == "") {
        $terminfo = "";
    } else {
        GetTerm($obj['TERMKEY'], $term);
        $terminfo = $term['VALUE'];
    }

    $discount[$key] = round(GetDiscountForInvoice($terminfo, $obj), 2);
    $totaldue = $obj['TOTALDUE']-$discount[$key];

    if ($amount[$key] != '0' && $amount[$key]=='') {
        $amount[$key] = $totaldue;
    }

    if ( ($amount[$key] > $totaldue && $totaldue > 0) || ($amount[$key] < $totaldue && $totaldue < 0) ) {

        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "The amount due is only ".$totaldue. ". Please change the payment amount."
        );
        $ret = false;
    } else if ($amount[$key] > 0 && $obj['RECORDTYPE'] != 'pa' && $totaldue < 0) {

        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "The amount approved cannot be positive"
        );
        $ret = false;
    } else if ($amount[$key] < 0 && ($totaldue > 0 || $obj['RECORDTYPE'] == 'pa') ) {

        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "The amount approved cannot be negative."
        );
        $ret = false;
    }

    if (!$ret) {
        return false;
    }

    return ProcessSelectToPay($obj['VENDENTITY'], $amount[$key], $amount, $discount, $ignoredRet, 'O', "'pi','pa'");

}

/**
 * @param string[] $currapi
 *
 * @return bool
 */
function VendorPaymentWrapper($currapi)
{
    global $gErr;
    $ret = true;
    include_once 'backend_selecttopay.inc';

    $vendorid = $currapi['VENDORID'];
    $entity = "V". $vendorid;
    $amount = preg_replace("/[^0-9.-]/", "", $currapi['AMOUNT']);

    if (!ValidVendor($vendorid)) {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "No vendor exists by the id " . $vendorid
        );
        return false;
    }

    if (GetEntityAttribute($entity, 'BILLINGTYPE', 1) == 'O') {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "Please use the xml api select_billforpayment for paying, as the vendor ". $vendorid. " is an open item vendor."
        );
        return false;
    }

    if ($amount != '0' && $amount=='') {
        SelectAllPRRecordsForPayment($entity, "'pa','pi'", true);
    } else if ($amount == '0') {
        UnSelectAllPRRecordsForPayment($entity, "'pa','pi'");
    } else {

        $discount = array();
        foreach (GetPrrecordsForEntity($entity, "'pi','pa'") as $obj) {
            if ($obj['TERMKEY'] == "") {
                $terminfo = "";
            } else {
                GetTerm($obj['TERMKEY'], $term);
                $terminfo = $term['VALUE'];
            }
            $discount[$obj['RECORD#']] = round(GetDiscountForInvoice($terminfo, $obj), 2);
        }

        $ret = ProcessSelectToPay($entity, $amount, "", $discount, $ignoredRet, 'B', "'pi','pa'");
    }

    return $ret;

}

/**
 * @param array $currapi
 * @param array $result
 *
 * @return bool
 */
function APPaymentWrapper($currapi, &$result)
{
    eppp('in APPaymentWrapper');
    include_once 'backend_payment.inc';
    include_once 'backend_pdf.inc';
    include_once 'browser.inc';
    include_once 'pdfcheckformat.inc';
    $ret = true;

    $vendorid = $currapi['VENDORID'];
    if (!ValidVendor($vendorid)) {
        Globals::$g->gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "No vendor exists by the id " . $vendorid
        );
        $ret = false;
    }

    $_mod = '3.AP';
    $entity = 'vendor';
    /** @noinspection PhpUnusedLocalVariableInspection */
    $id = 'vendorid';
    $paymentrecordtype = 'pp';
    $groupby = "name, vendorid" ;
    $discountflag = true;

    $entityArr = array();
    $paymentdate = $currapi["PAYMENTDATE"];

    $filters = "(prrecord.recordtype = 'pi' OR prrecord.recordtype = 'pa' ) and " .
                           "(prrecord.state is null or prrecord.state not in ('Q') )" ;

    $fields = array('vendor.name as name',
                    'vendor.vendorid as vendorid',
                    'sum(prrecord.totalselected) as totalamount',
                    'sum(prrecord.totaldue)');

    $entityArr[] = "'" . GetEntityValue($paymentrecordtype, $vendorid) . "'";


    if ($currapi['ONLINE']) {

        $online = $currapi['ONLINE'];
        /** @noinspection PhpUnusedLocalVariableInspection */
        $bankaccountid = $online['BANKACCOUNTID'];

        //TODO: obsolete
        // PHP 5.4 issue: removed & from $bank but ValidateOnlinePayments doesn't exist.
        // $ret = $ret && ValidateOnlinePayments($bankaccountid, $bank);
        throw new Exception('Obsolete Code Called');

    } else if ($currapi['CHECK']) {

        $paymethod = "Manual";

        $manual = $currapi['CHECK'];
        $bankaccountid = $manual['BANKACCOUNTID'];
        /** @noinspection PhpUnusedLocalVariableInspection */
        $checkno = $manual['CHECKNO'];

        if (!GetBankAccount($bankaccountid, $bank) ) {
            Globals::$g->gErr->addError(
                '**********', __FILE__.'.'.__LINE__,
                "$bankaccountid does not correspond to a valid bankaccount id"
            );
        }

        if (!$bank['ACCOUNTNO']) {
            Globals::$g->gErr->addError(
                '**********', __FILE__.'.'.__LINE__,
                "$bankaccountid does not correspond to a valid bankaccount id"
            );
        }

        $checknum = $bank['CHECK_CURRNO'];

        $table = GetVendorsToPay($filters, $bankaccountid, $paymentrecordtype, $entity, $paymethod, $entityArr, $fields, $groupby);

        if (Util::countOrZero($table) == 0) {
            Globals::$g->gErr->addError(
                "**********", __FILE__ . __LINE__,
                "Their is no amount selected to pay"
            );
            return false;
        }

        $checks = array();

        foreach ($table as $check) {
            $checks[] = $vendorid . "::" . $check['TOTALAMOUNT'] . "::" . $paymentdate . "::" . $checknum;

            //TODO: obsolete
            throw new Exception('Obsolete Code Called');
            /*
            if (!CreatePayment($paymentrecordtype, $vendorid, $check['TOTALAMOUNT'], $bankaccountid, $checknum, $paymentdate)) {
                // No error message reqd. sinc the api is adding to HasErrors.
                eppp('error with Create payment');
                Globals::$g->gErr->AddError(
                    "**********", __FILE__ . __LINE__,
                    "Error generated in CreatePayment"
               );
                return false;
            }
            */

        }

        /** @noinspection PhpUndefinedVariableInspection */
        if (!GetChecksForPDF($_mod, false, $quick) ) {
            return false;
        }
        
        $paymentRec = array();
        /** @noinspection PhpUndefinedVariableInspection */
        if (!ConfirmPayments($paymentrecordtype, $bankaccaountid, $checks, $discountflag, $paymentRec) ) {
            return false;
        }

        $result = array();
        $result['checkno'][0]['cdata'] = $checknum;
        /** @noinspection PhpUndefinedVariableInspection */
        $result['amount'][0]['cdata'] = $check['TOTALAMOUNT'];
    }

    return $ret;

}

/**
 * @param string   $r
 * @param string[] $approvalinfo
 *
 * @return bool
 */
function ApproveExpenseWrapper(/** @noinspection PhpUnusedParameterInspection */ $r, $approvalinfo)
{
    /** @noinspection PhpUnusedLocalVariableInspection */
    $ret = GetValidChoice($action, $approvalinfo['ACTION'], array( 'approved', 'unapproved'), array( 1, 0), 'action');

    if (!ValidEmployee($approvalinfo['REVIEWERID']) ) {
        Globals::$g->gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "There is no employee with the id ". $approvalinfo['REVIEWERID']
        );
        return false;
    }

    include_once 'backend_expense.inc';
    //TODO: obsolete
    throw new Exception('Obsolete Code Called');
}

/**
 * @param string $bankaccountid
 *
 * @return bool
 */
function ValidateBankAccountId($bankaccountid)
{
    global $gErr;

    $where = ["status = 'T' and accountid = :2", $bankaccountid];
    $accts = GetNObjects('bankaccount', 'accountid', $where);

    if (!$accts) {
        $gErr->addError(
            '**********', __FILE__ . '.' . __LINE__,
            "$bankaccountid does not correspond to a valid bankaccount id"
        );

        return false;
    }

    return true;
}

/**
 * @param array  $currarr
 * @param string $key
 * @param array  $data
 *
 * @return bool
 */
function ARPaymentWrapper($currarr, &$key, &$data)
{
    global $kARid;
    $isAdvance = isset($currarr['ARPAYMENTITEM']) ? false : true;
    $gErr = Globals::$g->gErr;
    $gManagerFactory = Globals::$g->gManagerFactory;

    include_once 'backend_payment.inc';
    include_once 'backend_prrecord.inc';

    $ret = true;

    $total = 0;
    $amount = array();
    /** @noinspection PhpUnusedLocalVariableInspection */
    $discounts = array();
    $rectype = array();
    $termsArray = array();
    $customerid = $currarr['CUSTOMERID'];
    /** @noinspection PhpUnusedLocalVariableInspection */
    $refid = $currarr['REFID'];

    // set the date recieved value to today, if not supplied
    if (!$currarr["DATERECEIVED"]) {
        $currarr["DATERECEIVED"] = GetCurrentDate();
    }
    $datereceived = $currarr["DATERECEIVED"];

    // MCP2 changes. Bug# 199.
    $mcpsubscribed = IsMCPSubscribed();


    $aryValidPaymentMethods = array("Printed Check", "Online", "Cash", "EFT", "Credit Card", "Online Charge Card", 'Online ACH Debit');
    $paymentmethod = (array_key_exists('PAYMENTMETHOD', $currarr) && $currarr['PAYMENTMETHOD'] != '') ? $currarr['PAYMENTMETHOD'] : "Printed Check";
    if (!in_array($paymentmethod, $aryValidPaymentMethods)) {
        $gErr->addError('**********', __FILE__ . '.' . __LINE__, "Invalid payment method.  Valid payment methods are " . join(', ', $aryValidPaymentMethods));
        return false;
    }

    $paymentamount = preg_replace("/[^0-9.-]/", "", $currarr['PAYMENTAMOUNT']);

    // cant pay in negative
    if ( !isset($paymentamount) || $paymentamount == '' || $paymentamount < 0) {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "Invalid payment Amount. The payment amount cannot be empty or negative"
        );
        return false;
    }

    // Validation to check the amount should not be more than two decimal in XML
    if (iround($currarr['PAYMENTAMOUNT'], 2) != $paymentamount) {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "Invalid payment Amount. The Payment amount cannot contain more than two decimal value."
        );
        return false;
    }

    $translatedpymtamt = $currarr['TRANSLATEDAMOUNT'];

    if( isset($translatedpymtamt) && $translatedpymtamt != '' && iround($currarr['TRANSLATEDAMOUNT'], 2) != $translatedpymtamt) {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "Invalid translated amount. The translated amount cannot contain more than two decimal value."
        );
        return false;
    }

    // are we cash or accrual?
    /** @noinspection PhpUnusedLocalVariableInspection */
    $err = GetModulePreferences($kARid, $ARPreferences);

    // is autobatching enabled?
    $isAutoBatching = ( in_array($ARPreferences['RP_BATCHES'], array('D', 'E', 'M')) ? true : false );

    /** @noinspection PhpUnusedLocalVariableInspection */
    $isRPType = ( isset($currarr['ARPAYMENTITEM']) && is_array($currarr['ARPAYMENTITEM']) ? true : false );
    $bankAccount = $currarr['BANKACCOUNTID'];
    $undepFundsAccount = $currarr['UNDEPFUNDSACCT'];

    //============Creating Auto-Batch in case of daily or monthly setting =======
    if ( $isAutoBatching && (!isset($currarr['BATCHKEY']) || $currarr['BATCHKEY'] == '') ) {

        if ( (!isset($bankAccount) || $bankAccount == '')
            && (!isset($undepFundsAccount) || $undepFundsAccount == '') 
        ) {
            $gErr->addError(
                '**********',
                __FILE__.'.'.__LINE__,
                "Please provide bank account or undeposited funds account to deposit the payment"
            );
            return false;
        }

        $autoBatch = array();
        $autoBatch['POSTINGDATE'] = $datereceived;
        $autoBatch['PAYMETHOD'] = $paymentmethod;
        $autoBatch['BANKACCOUNT'] = $bankAccount;
        $autoBatch['UNDEPOSITEDACCOUNTNO'] = $undepFundsAccount;
        $autoBatch['DOCRECORDTYPE'] = 'rp';
        $autoBatch['STATE'] = '';

        $currarr['BATCHKEY'] = CreateARAutoBatch($autoBatch);

    }
    //============Creating Auto-Batch in case of daily or monthly setting =======

    // validate prbatch info
    if( !$currarr['BATCHKEY'] ) {
        $gErr->addError('**********', __FILE__ . '.' . __LINE__, "Please provide a payment batch key.");
        return false;
    }

    $ok = GetPRBatch($currarr['BATCHKEY'], $batchobj);

    if ( !$ok || !in_array($batchobj['RECORDTYPE'], array('rp', 'ro')) ) {
        $gErr->addError('BL********', __FILE__ . '.' . __LINE__, " batch key specified is invalid.");
        return false;
    }

    if ($batchobj['OPEN'] == "F") {
        $gErr->addError('BL02000087', __FILE__ . '.' . __LINE__, "The batch is closed.");
        $ret = false;
    }

    $filtercurr = '';
    $basecurr = '';
    if($mcpsubscribed) {
        if ( IsMCMESubscribed() ) {
            $basecurr = $currarr['BASECURR'];
        }
        else {
            $basecurr = GetBaseCurrency();
        }

        if ( !$undepFundsAccount ) {

            $qry = "select currency from bankaccount where CNY# = :1 and ACCOUNTID = :2";
            $bankcurr = QueryResult(array($qry, GetMyCompany(), $batchobj['ACCOUNTNOKEY']));
        } else {
            $bankcurr[0]['CURRENCY'] = $basecurr;
            if ($bankcurr[0]['CURRENCY'] != $currarr['CURRENCY']) {
                    $gErr->addError('**********', GetFL(), "You cannot make payment for foreign currency invoice for payment method '$paymentmethod' using Undeposited Fund Account.");
                    return false;
            } 
        }
        $filtercurr = " and PRRECORD.CURRENCY = :3 ";

        // Validations for Multi-currency invoice payment.
        if (in_array($paymentmethod, array('Credit Card', 'Online Charge Card', 'Online ACH Debit'))) {
            if ($bankcurr[0]['CURRENCY'] != '' && $bankcurr[0]['CURRENCY'] != $basecurr) {
                $gErr->addError('**********', GetFL(), "You cannot select a payment batch with a foreign currency bank for payment method '$paymentmethod'.");
                return false;
            }

        }
        // End Validations for MCP payment.
        if(!$isAdvance && $currarr['CURRENCY'] != $basecurr && $currarr['CURRENCY'] != $bankcurr[0]['CURRENCY'] && $bankcurr[0]['CURRENCY'] != $basecurr) {
            $gErr->addError(
                'BL03000051', GetFL(),
                "We can not pay a Foreign Currency Invoice using another Foreign Currency Batch."
            );
            return false;
        }
        if(!$isAdvance && ($currarr['CURRENCY'] != $basecurr || $bankcurr[0]['CURRENCY'] != $basecurr) && $currarr['CURRENCY'] != $bankcurr[0]['CURRENCY'] && $currarr['TRANSLATEDAMOUNT'] =='') {
            $gErr->addError(
                'BL03000051', GetFL(),
                "We Need to pass the Translated Base Amount for Foreign Currency Transactions."
            );
            return false;

        }
        
        if ($paymentmethod == 'Online Charge Card' && $currarr['CURRENCY'] != 'USD') {
            $gErr->addError(
                '**********', GetFL(),
                "Invalid currency for payment method Online Charge Card",
                "Currently, only 'USD' is supported for payment method Online Charge Card."
            );
            return false;
        }
        if ($paymentmethod == 'Online ACH Debit' && $currarr['CURRENCY'] != 'USD') {
            $gErr->addError(
                '**********', GetFL(),
                "Invalid currency for payment method Online ACH Debit",
                "Currently, only 'USD' is supported for payment method Online ACH Debit."
            );
            return false;
        }
        if (isset($currarr['ARPAYMENTITEM']) && is_array($currarr['ARPAYMENTITEM'])) {
            foreach ($currarr['ARPAYMENTITEM'] as $paymentItem) {
                if ( ! GetPRRecord($paymentItem['INVOICEKEY'], $prrecord)) {
                    $gErr->addError(
                        '**********', __FILE__ . '.' . __LINE__,
                        "No record exits for the invoicekey " . $paymentItem['INVOICEKEY']
                    );

                    return false;
                }

                if ($prrecord['CURRENCY'] != $currarr['CURRENCY']) {
                    if ($currarr['CURRENCY'] != $basecurr) {
                        $gErr->addError(
                            '**********', GetFL(),
                            "Invoice currency, {$prrecord['CURRENCY']} does not match the given currency, {$currarr['CURRENCY']}"
                        );
                    } else {
                        $gErr->addError(
                            '**********', GetFL(),
                            "No currency given and invoice currency, {$prrecord['CURRENCY']} does not match the base currency, $basecurr"
                        );
                    }

                    return false;
                }

                if ($prrecord['STATE'] === BasePRRecordManager::DRAFT_RAWSTATE) {
                    $gErr->addError(
                        '**********', __FILE__ . '.' . __LINE__,
                        "One or more invoices are in Draft status. Post the invoices, or remove them from the payment request, and try again."
                    );

                    return false;
                }
            }
        }

        // Setting invoice currency/ the currency supplied from request as prcurrency.
        $prcurrency = $currarr['CURRENCY'];

    } else {
        $prcurrency = GetBaseCurrency();
    }

    // Need validation of the customerid
    if (!ValidCustomer($customerid)) {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "The customer " . $customerid . " does not exist."
        );
        return false;
    }



    if ( isset($ARPreferences['CUSTACCTTYPE']) && $ARPreferences['CUSTACCTTYPE'] == 'B' ) {

        if (!$currarr['ARPAYMENTITEM']) {

            // RETRIEVE ALL THE OPEN INVOICES FOR THE GIVEN CUSTOMER
            $filter = array();
            $filter[0] = "PRRECORD.TOTALDUE > 0 and PRRECORD.RECORDTYPE in ('ri', 'ra') and PRRECORD.ENTITY = :2 $filtercurr";
            $filter[1] = 'C'.$customerid;
            if ( isset($filtercurr) && $filtercurr != '' ) {
                $filter[2] = $prcurrency;
            }
            $select = "PRRECORD.RECORD#, PRRECORD.TOTALDUE, PRRECORD.TOTALENTERED, PRRECORD.WHENCREATED";
            $order = "PRRECORD.WHENCREATED, PRRECORD.RECORD# asc";
            $invoices = GetNObjects('prrecord', $select, $filter, $order);


            // RUN THROUGH THE INVOICES CREATING PAYMENT ITEMS
            $amtapplied = 0;
            $totaltoapply = $currarr['PAYMENTAMOUNT'];

            if (Util::countOrZero($invoices) > 0) {
                foreach($invoices as $invoice) {
                    $lefttoapply = bcsub($totaltoapply, $amtapplied, 2);

                    // CREATE THE PAYMENT ITEM
                    $amttoapply = ($lefttoapply >= $invoice['TOTALDUE']) ? $invoice['TOTALDUE'] : $lefttoapply;
                    $currarr['ARPAYMENTITEM'][] = array(
                    'INVOICEKEY' => $invoice['RECORD#'],
                    'AMOUNT' => $amttoapply
                    );

                    // RESET FOR NEXT ITERATION
                    $amtapplied = bcadd($amtapplied, $amttoapply, 2);
                    if ($amtapplied >= $totaltoapply) {
                        break;
                    }
                }

            }
        }
    }

    if ($currarr['ARPAYMENTITEM']) {
        foreach($currarr['ARPAYMENTITEM'] as $paymentitem) {

            $invoicekey = $paymentitem['INVOICEKEY'];
            $amount[$invoicekey] = preg_replace("/[^0-9.-]/", "", $paymentitem['AMOUNT']);

            if (isset($paymentitem['AMOUNT']) && trim($paymentitem['AMOUNT']) !== '' && iround($amount[$invoicekey], 2) != $amount[$invoicekey]) {
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "Invalid invoice line item payment amount. The invoice line item payment amount cannot contain more than two decimal value."
                );
                return false;
            }

            if (!GetPRRecord($invoicekey, $prrecord) ) {
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "No record exits for the recordkey " . $invoicekey
                );
                $ret = false;
            }
            if (($ARPreferences['PYMTDT_ISGREATER'] == 'true') &&
                (SysDateCompare($currarr['DATERECEIVED'], $prrecord['WHENCREATED']) < 0)) {
                $gErr->addError('**********', __FILE__ . '.' . __LINE__,
                    "Payment date cannot be before Invoice creation date");
                $ret = false;
            }
            // MCP-2 changes. Bug# 199.
            if($mcpsubscribed) {
                /** @noinspection PhpUndefinedVariableInspection */
                if( $bankcurr[0]['CURRENCY'] == '' && $prrecord['CURRENCY'] != $basecurr) {
                    //NOT AN AR PAYMENT
                    $gErr->addError(
                        '**********', __FILE__.'.'.__LINE__,
                        "AR payment cannot be created for foreign currency invoices as the batch currency is not setup."
                    );
                    $ret = false;
                }
            }

            /** @noinspection OnlyWritesOnParameterInspection */
            $rectype[$invoicekey] = $prrecord['RECORDTYPE'];

            if ( !($prrecord['RECORDTYPE'] == 'ri' || $prrecord['RECORDTYPE'] == 'ra') ) {
                //NOT AN AR PAYMENT
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "The key " . $invoicekey . "does not belong to an accounts receivable document."
                );
                $ret = false;

            } else if ($prrecord['TRX_TOTALDUE'] == 0) {
                //ERROR... INVOICE IS ALREADY PAID...
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "invoice with key " . $invoicekey . " is already paid"
                );
                $ret = false;

            } else if ( $amount[$invoicekey] < 0 && ($prrecord['TOTALDUE'] > 0 || $prrecord['RECORDTYPE'] == 'ra') ) {
                //ERROR.. CAN'T PAY MORE THAN TOTAL DUE.....
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "The amount paid cannot be negative for invoice with key " . $invoicekey
                );
                $ret = false;

            } else if ( $amount[$invoicekey] > 0 && $prrecord['TOTALDUE'] < 0 && $prrecord['RECORDTYPE'] != 'ra') {
                //error.. can't pay more than total due.....
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "The amount paid cannot be negative for invoice with key " . $invoicekey
                );
                $ret = false;

            }
            $InvTotDue = $prrecord['TRX_TOTALDUE'] != ''  ? $prrecord['TRX_TOTALDUE'] : $prrecord['TOTALDUE'];
            if ($amount[$invoicekey] > $InvTotDue ) {
                //error.. can't pay more than total due.....
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "The amount paid is greater than amount due for invoice with key " . $invoicekey
                );
                $ret = false;
            }

            if ($prrecord['ENTITY'] != $customerid) {
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "No such customer"
                );
                $ret = false;
            }
            if ($prrecord['STATE'] === BasePRRecordManager::DRAFT_RAWSTATE) {
                $gErr->addError(
                    '**********', __FILE__ . '.' . __LINE__,
                    "One or more invoices are in Draft status. Post the invoices, or remove them from the payment request, and try again."
                );
                return false;
            }

            //FOLLOWING CODE IS PICKED UP FROM receiveinvoices.phtml

            $termkey = $prrecord['TERMKEY'] ;

            if ($termkey == "") {
                $terminfo = "";
            }
            else {
                if ( !isset($termsArray[$termkey])) {
                    GetTerm($termkey, $term);
                    $term = $term['VALUE'];
                    $termsArray[$prrecord['TERMKEY']] = $term;
                }
                $terminfo = $termsArray[$prrecord['TERMKEY']];
            }

            include_once 'backend_discpen.inc';
            $discount[$invoicekey] = GetDiscountForInvoice($terminfo, $prrecord);
            $total += str_replace(",", "", $amount[$invoicekey]);
        }
    }


    //CAN'T APPLY MORE THAN THE GIVEN AMOUNT...
    $paymentamount = bcadd($paymentamount, '0', 2); $total = bcadd($total, '0', 2);
    if ($paymentamount < $total) {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "Payment amount is less than amount being applied"
        );
        $ret = false;
    }

    //CHECK FOR VALID OVERPAYLOCID
    if ($currarr['OVERPAYLOCID']) {
        if (!IsValidLocation($currarr['OVERPAYLOCID']) ) {
             $ret = false;
        }
    }

    //CHECK FROM VALID OVERPAYDEPTID
    if ($currarr['OVERPAYDEPTID']) {
        if (!IsValidDepartment($currarr['OVERPAYDEPTID']) ) {
            $ret = false;
        }
    }

    $description2='';

    global $kCCPaymentMethods;
    if ($currarr['PAYMENTMETHOD'] == 'Credit Card' && isset($currarr['CCTYPE']) &&  $currarr['CCTYPE'] != ''  ) {

        if(in_array($currarr['CCTYPE'], $kCCPaymentMethods)){
            $description2 = array_search($currarr['CCTYPE'], $kCCPaymentMethods);
        }else{
            $gErr->addError('**********', __FILE__ . '.' . __LINE__,
                            "Please provide a valid Credit Card type");
            return false;
        }
    }

    // New API format
    $pRequest = array();
    $pRequest['ENTITIES'] = array();
    $pRequest['ENTITIES']['C'.$currarr['CUSTOMERID']] = array();
    $pRequest['ENTITIES']['C'.$currarr['CUSTOMERID']]['PAYMENTS'] = array();
    $pArray = array (
        'docnumber' => $currarr['REFID'],
        'totalselected' => $currarr['PAYMENTAMOUNT'],
        'paydate'    => $currarr['DATERECEIVED'],
        'receiptdate' => $currarr['DATERECEIVED'],
        'overpay_loc' => $currarr['OVERPAYLOCID'],
        'overpay_dept'    => $currarr['OVERPAYDEPTID'],
        'overpay_cust'    => $currarr['CUSTOMERID'],
        'description2'	=> $description2,
        'currency'    =>    $currarr['CURRENCY'],
    );
    /** @noinspection PhpUndefinedVariableInspection */
    if( $mcpsubscribed && ( $bankcurr[0]['CURRENCY'] != $basecurr || $currarr['CURRENCY'] != $basecurr)) {
        $pArray['exch_rate_type'] = ($bankcurr[0]['CURRENCY'] == $currarr['CURRENCY']) ? $currarr['EXCHRATETYPE'] : CUSTOM_RATE;
        $pArray['EXCH_RATE_TYPE_ID'] = ($bankcurr[0]['CURRENCY'] == $currarr['CURRENCY']) ? $currarr['EXCHRATETYPEID'] : CUSTOM_RATE_ID;
        $pArray['EXCHANGE_RATE'] = ($bankcurr[0]['CURRENCY'] == $currarr['CURRENCY']) ? $currarr['EXCHRATE'] : ibcdiv($currarr['TRANSLATEDAMOUNT'], $currarr['PAYMENTAMOUNT'], 14, 1);
        $pArray['EXCH_RATE_DATE'] = $currarr['DATERECEIVED'];
    }
    if($mcpsubscribed && (isset($translatedpymtamt) && $translatedpymtamt != '' )) {
        $pArray['basetotalselected'] = $translatedpymtamt;
    }
    //Added for IGC companies, if EXCHANGE_RATE not set. Set it to 1.
    $pArray['EXCHANGE_RATE'] = $pArray['EXCHANGE_RATE'] ?: 1;
    if(IsMCMESubscribed() && GetContextLocation()){
        $pArray['exch_rate_type'] = $currarr['EXCHRATETYPE']?:CUSTOM_RATE;
        $pArray['EXCH_RATE_TYPE_ID'] = $currarr['EXCHRATETYPEID']?:CUSTOM_RATE_ID;
        $pArray['EXCH_RATE_DATE'] = $currarr['DATERECEIVED'];
    }
    $invoiceRecordsArray = array();

    if (isset($currarr['ARPAYMENTITEM']) && is_array($currarr['ARPAYMENTITEM'])) {
        foreach ($currarr['ARPAYMENTITEM'] as $key => $pair) {
            /** @noinspection PhpUndefinedVariableInspection */
            if ($mcpsubscribed && ($bankcurr[0]['CURRENCY'] != $basecurr || $currarr['CURRENCY'] != $basecurr)) {
                if ($currarr['CURRENCY'] == $basecurr) {
                    $invAmount = $pair['AMOUNT'];
                } else if ($currarr['CURRENCY'] == $bankcurr[0]['CURRENCY']) {
                    $invAmount = iround($pair['AMOUNT'] * $currarr['EXCHRATE'], 2);
                } else {
                    $invAmount = iround(
                        ibcmul(
                            $pair['AMOUNT'], ibcdiv($currarr['TRANSLATEDAMOUNT'], $currarr['PAYMENTAMOUNT'], 14, 1), 14
                        ), 2
                    );
                }

                // If the same invoice is getting paid  multiple times in the same request,
                // then we need to summerize the amounts before sending to ReceviePayment API.
                $index = false;
                if (array_search($pair['INVOICEKEY'], $invoiceRecordsArray) !== false) {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $index = findExistingRecord($invArray, $pair['INVOICEKEY']);
                }
                if ($index !== false) {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $amt = ibcadd($invArray[$index]['selected'], $invAmount, 2, true);
                    $trx_amt = ibcadd($invArray[$index]['trx_selected'], $pair['AMOUNT'], 2, true);
                    $invArray[$index]['selected'] = $amt;
                    $invArray[$index]['trx_selected'] = $trx_amt;
                } else {
                    $exchRate = ($currarr['CURRENCY'] == $basecurr || $currarr['CURRENCY'] == $bankcurr[0]['CURRENCY'])
                        ? $currarr['EXCHRATE'] : ibcdiv($currarr['TRANSLATEDAMOUNT'], $currarr['PAYMENTAMOUNT'], 14, 1);
                    $invArrayElem = array(
                        'trx_selected'      => $pair['AMOUNT'],
                        'selected'          => $invAmount,
                        'record#'           => $pair['INVOICEKEY'],
                        'currency'          => $currarr['CURRENCY'],
                        'exch_rate_type'    => ($currarr['CURRENCY'] == $basecurr) ? '' : $currarr['EXCHRATETYPE'],
                        'EXCH_RATE_TYPE_ID' => ($currarr['CURRENCY'] == $basecurr) ? '' : $currarr['EXCHRATETYPEID'],
                        'EXCHANGE_RATE'     => $exchRate,
                        'EXCH_RATE_DATE'    => $currarr['DATERECEIVED'],
                    );
                    if ( ! ($currarr['CURRENCY'] == $basecurr || $currarr['CURRENCY'] == $bankcurr[0]['CURRENCY'])) {
                        $invArrayElem['exch_rate_type'] = CUSTOM_RATE;
                        $invArrayElem['EXCH_RATE_TYPE_ID'] = CUSTOM_RATE_ID;
                    }
                    $invArray[] = $invArrayElem;
                    $invoiceRecordsArray[] = $pair['INVOICEKEY'];
                }
            } else {
                // If the same invoice is getting paid  multiple times in the same request,
                // then we need to summerize the amounts before sending to ReceviePayment API.
                $index = false;
                if (array_search($pair['INVOICEKEY'], $invoiceRecordsArray) !== false) {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $index = findExistingRecord($invArray, $pair['INVOICEKEY']);
                }
                if ($index !== false) {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $amt = ibcadd($invArray[$index]['selected'], $pair['AMOUNT'], 2, true);
                    $invArray[$index]['selected'] = $amt;
                    if (IsMCMESubscribed() && GetContextLocation()) {
                        $invArray[$index]['trx_selected'] = $amt;
                        $invArray[$index]['EXCHANGE_RATE'] = $pArray['EXCHANGE_RATE'];
                        $invArray[$index]['EXCH_RATE_TYPE_ID'] = $pArray['EXCH_RATE_TYPE_ID'];
                        $invArray[$index]['EXCH_RATE_DATE'] = $pArray['EXCH_RATE_DATE'];
                    }
                } else {
                    $invoice = [
                        'selected' => $pair['AMOUNT'],
                        'record#'  => $pair['INVOICEKEY']
                    ];
                    if (IsMCMESubscribed() && GetContextLocation()) {
                        $invoice['trx_selected'] = $pair['AMOUNT'];
                        $invoice['EXCHANGE_RATE'] = $pArray['EXCHANGE_RATE'];
                        $invoice['EXCH_RATE_TYPE_ID'] = $pArray['EXCH_RATE_TYPE_ID'];
                        $invoice['EXCH_RATE_DATE'] = $pArray['EXCH_RATE_DATE'];
                    }
                    $invArray[] = $invoice;
                    $invoiceRecordsArray[] = $pair['INVOICEKEY'];
                }
            }
        }
    }
    /** @noinspection PhpUndefinedVariableInspection */
    $pArray['INVOICES'] = $invArray;
    $pRequest['ENTITIES']['C'.$currarr['CUSTOMERID']]['PAYMENTS'][] = $pArray;

    $doadvance = !(is_array($pArray['INVOICES']));

    $cny = GetMyCompany();

    $custMgr = $gManagerFactory->getManager('customer');
    $custObj = $custMgr->GetRaw($customerid);
    
    global $kCCPid;
    GetModulePreferences($kCCPid, $epaymentPreferences);
    if($ret && $paymentmethod == 'Online ACH Debit') {
        if(!IsModuleIdInstalled($kCCPid)) {
            $gErr->addError(
                '**********', __FILE__.'.'.__LINE__,
                "This company is not authorized for online transactions. Please subscribe to the 'Payment Services' application"
            );
            return false;
        }
        if ($epaymentPreferences['ENABLE_ACH_PAYMENT'] != 'true') {
            $gErr->addError('**********', __FILE__ . '.' . __LINE__, "Online ACH Debit payments have been disabled.");
            return false;
        }
        if ($custObj[0]['ENABLEONLINEACHPAYMENT'] != 'T') {
            $gErr->addError('**********', __FILE__ . '.' . __LINE__, "Online ACH Debit payments have been disabled for {$custObj[0]['NAME']}.");
            return false;
        }
        if ($doadvance) {
            $gErr->addError('**********', __FILE__ . '.' . __LINE__, "Online ACH Debits cannot be used for advances.");
            return false;
        }
        if ($currarr['OVERPAYDEPTID'] || $currarr['OVERPAYLOCID']) {
            $gErr->addError('**********', __FILE__ . '.' . __LINE__, "Online ACH Debits cannot be used for overpayments.");
            return false;
        }
        $achpayment = $currarr['ONLINEACHPAYMENT'];
        // do we need to use default account on system?
        if ($achpayment['USEDEFAULTACCOUNT'] == 'true') {
            //look up the default account
            $result = QueryResult(
                array("SELECT bankname,accounttype,accountnumber,aba,accountholder,record#
				FROM customerachinfo WHERE cny# = :1 and customerid = :2 and defaultaccount = 'T'", $cny, $customerid)
            );
            if(isset($result[0]['BANKNAME'])) {
                $customerbankaccount = $result[0];
                $bankname = $customerbankaccount['BANKNAME'];
                $accounttype = $customerbankaccount['ACCOUNTTYPE'];
                $accountnumber = $customerbankaccount['ACCOUNTNUMBER'];
                $routingnumber = $customerbankaccount['ABA'];
                $accountholder = $customerbankaccount['ACCOUNTHOLDER'];
            } else {
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "There is no default account set for the selected customer"
                );
                return false;
            }
        } else {
            if (empty($achpayment['BANKNAME'])) {
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "Bank Account is a required field"
                );
                return false;
            }
            if (empty($achpayment['ACCOUNTTYPE'])) {
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "Bank Account Type is a required field"
                );
                return false;
            }
            if (empty($achpayment['ACCOUNTNUMBER'])) {
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "Bank Account Number is a required field"
                );
                return false;
            }
            if (empty($achpayment['ROUTINGNUMBER'])) {
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "Bank Routing Number is a required field"
                );
                return false;
            }
            if (empty($achpayment['ACCOUNTHOLDER'])) {
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "Bank Account Holder is a required field"
                );
                return false;
            }
            $bankname = $achpayment['BANKNAME'];
            $accounttype = $achpayment['ACCOUNTTYPE'];
            $accountnumber = $achpayment['ACCOUNTNUMBER'];
            $routingnumber = $achpayment['ROUTINGNUMBER'];
            $accountholder = $achpayment['ACCOUNTHOLDER'];

        }
        // translate to Authorize.Net code
        $accountTypeCode = array(
        'Business Checking Account' => 'BUSINESSCHECKING',
        'Personal Checking Account' => 'CHECKING',
        'Personal Savings Account' => 'SAVINGS',
        );
        $accounttype = $accountTypeCode[$accounttype];

        if ($batchobj) {

            $batchtitle = $batchobj['TITLE'];
            $bankId = explode("~", $epaymentPreferences['ACH_ACCOUNT']);

            //Only batches tied to the checking accounts
            // set in the ACH Debit configuration should be allowed in ACH Debit card payment.
            //Get the batch bank account
            $arPymtBatchMgr = $gManagerFactory->getManager('arpaymentbatch');

            $batchTitleBankMap = $arPymtBatchMgr->GetTitleToBankMap();

            if($batchTitleBankMap[$batchtitle] != $bankId[0]) {
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "This batch is not attached to the bank account configured for ACH Debit Processing application"
                );
                return false;

            }
        } else{
            $gErr->addError(
                '**********', __FILE__.'.'.__LINE__,
                "Invalid batch specified"
            );
            return false;
        }
        
    } elseif($ret && $paymentmethod == 'Online Charge Card') {
        include_once 'backend_module.inc';
        include_once 'backend_npayment.inc';
        global $kCCPaymentMethods;
        if(!IsModuleIdInstalled($kCCPid)) {
            $gErr->addError(
                '**********', __FILE__.'.'.__LINE__,
                "This company is not authorized for online transactions. Please subscribe to the 'Payment Services' application"
            );
            return false;
        }
        if ($epaymentPreferences['ENABLE_CARD_PAYMENT'] != 'true') {
            $gErr->addError('**********', __FILE__ . '.' . __LINE__, "Online Charge Card payments have been disabled.");
            return false;
        }
        if ($custObj[0]['ENABLEONLINECARDPAYMENT'] != 'T') {
            $gErr->addError('**********', __FILE__ . '.' . __LINE__, "Online Charge Card payments have been disabled for {$custObj[0]['NAME']}.");
            return false;
        }
        if ($doadvance) {
            $gErr->addError('**********', __FILE__ . '.' . __LINE__, "Online charge cards cannot be used for advances.");
            return false;
        }
        if ($currarr['OVERPAYDEPTID'] || $currarr['OVERPAYLOCID']) {
            $gErr->addError('**********', __FILE__ . '.' . __LINE__, "Online charge cards cannot be used for overpayments.");
            return false;
        }
        
        //CHECKING FOR NEW FIELDS
        $onlineccp = $currarr['ONLINECARDPAYMENT'];

        // do we need to use default card on system?
        if ($onlineccp['USEDEFAULTCARD'] == 'true') {
            //look up the default card
            $result = QueryResult(
                array("SELECT cardnum,exp_month,exp_year,cardtype,record#
					FROM custcreditcard WHERE cny# = :1 and customerid = :2 and default_card = 'T'", $cny, $customerid)
            );

            if(isset($result[0]['CARDNUM'])) {
                $customercreditcard = $result[0];
                $ccnum = TwoWayDecryptPCI($customercreditcard['CARDNUM'], 'CREDITCARD');
                $exp_month = $customercreditcard['EXP_MONTH'];
                $exp_year = $customercreditcard['EXP_YEAR'];
                /** @noinspection PhpUnusedLocalVariableInspection */
                $cardtype = $kCCPaymentMethods[$customercreditcard['CARDTYPE']];

                // Check if card is expired 	
                list($month, , $year) = explode("/", GetCurrentDate());
                $expires = (($year >= $exp_year) && ($month > $exp_month));

                if ($expires) {
                    $gErr->addError(
                        '**********', __FILE__.':'.__LINE__, 
                        'Card has crossed the expiry date on '.$exp_month.'/'.$exp_year.' (MM/YYYY)' 
                    );
                    return false;
                }
            } else {
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "There is no default card set for the selected customer"
                );
                return false;
            }
        } else {
            if ($onlineccp['CARDNUM']) {
                $ccnum = $onlineccp['CARDNUM'];
                $cardtype = ucwords($onlineccp['CARDTYPE']);
                if (!isset($cardtype) || $cardtype == '') {
                    $gErr->addError(
                        '**********', __FILE__.'.'.__LINE__,
                        "Customer charge card type is a required field"
                    );
                    return false;
                }
                $exp_year = $onlineccp['EXPIRYDATE']['EXP_YEAR'];
                $exp_month = GetMonthFromName($onlineccp['EXPIRYDATE']['EXP_MONTH']);
                if ($exp_year && $exp_month) {
                    $expiry = $onlineccp['EXPIRYDATE'];
                }else{
                    $gErr->addError(
                        '**********', __FILE__.'.'.__LINE__,
                        "Expiry date is a required field"
                    );
                    return false;
                }

            }else{
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "Customer charge card number is a required field"
                );
                return false;
            }

            $CCMgr = $gManagerFactory->getManager('customercreditcard');

            if (!$CCMgr->ValidateCreditCardDetails($ccnum, $cardtype, $expiry)) {
                $gErr->addError(
                    '**********', __FILE__.'.'.__LINE__,
                    "Credit Card validation failed.", '', "Please check the card number and expiry."
                );
                return false;
            }
        }
        
        if ($batchobj) {

            $batchtitle = $batchobj['TITLE'];
            $bankId = explode("~", $epaymentPreferences['MERCHANT_ACCOUNT']);

            //Only batches tied to the checking accounts
            // set in the Charge Card configuration should be allowed in Online Credit card payment.
            //Get the batch bank account
            $arPymtBatchMgr = $gManagerFactory->getManager('arpaymentbatch');

            $batchTitleBankMap = $arPymtBatchMgr->GetTitleToBankMap();
            if ( (isset($bankAccount) && $bankAccount != '' ) ) {

                if($batchTitleBankMap[$batchtitle] != $bankId[0]) {
                    $gErr->addError(
                        '**********', __FILE__.'.'.__LINE__,
                        "This batch is not attached to the bank account configured for Credit Card Processing application"
                    );
                    return false;
    
                }
            }
        } else{
            $gErr->addError(
                '**********', __FILE__.'.'.__LINE__,
                "Invalid batch specified"
            );
            return false;
        }

    }
    else if ( $ret && $paymentmethod == 'Credit Card') {
        // authcode for offline CC transactions,  use this as reference#
        if ($currarr['AUTHCODE']) {
            $currarr['REFID'] = $currarr['AUTHCODE'];
        }
    }
    if (!$ret) {
        return false;
    } else {
        if ($doadvance) {
            global $gManagerFactory;
            $objAdv = $gManagerFactory->getManager('aradvance', true);
            // GET THE ADVANCE OFFSET ACCOUNT
            if (!GetAdvanceOffsetAcct(PRRECORD_TYPE_ARADVANCE, $acctkey)) {
                global $gErr;
                $gErr->addError(
                    '**********',
                    __FILE__.'.'.__LINE__,
                    "Creating an advance payment requires an offset account.",
                    "Offset account not selected.",
                    "Select Company &gt; Services &gt; Configure Accounts Receivable. Choose a GL Account for Customer Advances and click Save."
                );
                return false;
            }

            if (!array_key_exists('DATERECEIVED', $currarr) || $currarr['DATERECEIVED'] == '') {
                $currarr['DATERECEIVED'] = GetCurrentDate();
            }
            // need to get the batch title
            $batchtitle = $batchobj['TITLE'];
            $aryAdv = array('PAYMENTMETHOD' => $paymentmethod,
             'PRBATCH' => $batchtitle,
             'prbatchkey:' => $currarr['BATCHKEY'],
             'CUSTOMERID' => $currarr['CUSTOMERID'],
             'PAYMENTDATE' => $currarr['DATERECEIVED'],
             'RECEIPTDATE' => $currarr['DATERECEIVED'],
             'DOCNUMBER' => $currarr['REFID'],
             'BASECURR' => $basecurr);
            if(IsMCPEnabled()) {
                if(isset($currarr['CURRENCY']) && $currarr['CURRENCY'] != '') {
                    $aryAdv['CURRENCY'] = $currarr['CURRENCY'];
                } else {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $aryAdv['CURRENCY'] = $basecurr;
                }
                if( isset($currarr['TRANSLATEDAMOUNT']) && $currarr['TRANSLATEDAMOUNT'] != '' ) {
                    $translatedbaseamount =  preg_replace("[^0-9.-]", "", $currarr['TRANSLATEDAMOUNT']);
                    $translatedbaseamount = bcadd($translatedbaseamount, '0', 2);

                    $exchRate = $currarr['USEREXCHRATE'];
                    if(isset($exchRate)) {
                        $calculatedbaseAmt = ibcmul($paymentamount, $exchRate, 2, true);
                        if($translatedbaseamount != $calculatedbaseAmt ) {
                            $gErr->addError(
                                'PL03000001', __FILE__.'.'.__LINE__,
                                "Translated base amount is not correct as per given exchange rate"
                            );
                            return false;
                        }
                    } else {
                        $exchRate = ibcdiv($translatedbaseamount, $paymentamount, 14, 1);
                    }
                } else {
                    $exchRate = $currarr['EXCHRATE'];
                    $translatedbaseamount = $currarr['TRANSLATEDAMOUNT'] = ibcmul($paymentamount, $exchRate, 2, true);
                }

                $aryAdv['EXCHANGE_RATE'] = $exchRate;

                if (isset($currarr['EXCHRATETYPE'])) {
                    $aryAdv['EXCH_RATE_TYPE_ID'] = $currarr['EXCHRATETYPE'];
                }

                $aryAdv['EXCH_RATE_DATE'] = $currarr['EXCHRATEDATE'] ?? $currarr['DATECREATED'];

                $aryAdv['ITEMS'] =  array(array('ACCOUNTNO' => $acctkey,
                'TRX_AMOUNT' => $currarr['PAYMENTAMOUNT'], 'AMOUNT' => $translatedbaseamount));
            } else {
                $aryAdv['ITEMS'] =  array(array('ACCOUNTNO' => $acctkey,
                'TRX_AMOUNT' => $currarr['PAYMENTAMOUNT']));
            }

            if (array_key_exists('OVERPAYLOCID', $currarr) && $currarr['OVERPAYLOCID'] != '') {
                $aryAdv['ITEMS'][0]['LOCATIONID'] = $currarr['OVERPAYLOCID'];
            }
            if (array_key_exists('OVERPAYDEPTID', $currarr) && $currarr['OVERPAYDEPTID'] != '') {
                $aryAdv['ITEMS'][0]['DEPARTMENTID'] = $currarr['OVERPAYDEPTID'];
            }
            if (array_key_exists('CUSTOMERID', $currarr) && $currarr['CUSTOMERID'] != '') {
                $aryAdv['ITEMS'][0]['CUSTOMERID'] = $currarr['CUSTOMERID'];
            }
            // set the batch already created
            $objAdv->setIsBatchAlreadyCreated(true);
            // We dont have validate account label in case of 2.1 request
            // as we already have ADVANCE OFFSET ACCOUNTNO
            $objAdv->setHasAcctLabel(false);
            $ok = $objAdv->add($aryAdv);
            $key = $aryAdv['RECORDNO'];
        }
        else {
            $ok = ReceivePayment($pRequest, $currarr['BATCHKEY'], 'prrecord', $paymentmethod, $doit, $key);
        }
    }
    if ( ($paymentmethod == 'Online Charge Card' || $paymentmethod == 'Online ACH Debit') && $ok && (isset($key) && $key != '') ) {
        $invoiceID = '';
        if (Util::countOrZero($pArray['INVOICES']) == 1) {
            $arinvoiceMgr = $gManagerFactory->getManager('arinvoice');
            $params = array(
                'selects' => array('RECORDID'),
            );
            $params['filters'][0][] = array('RECORDNO', '=', $pArray['INVOICES'][0]['record#']);
            $invoices = $arinvoiceMgr->GetList($params);
            if (!empty($invoices)) {
                $invoiceID = $invoices[0]['RECORDID'];
            }
        }
        include_once 'cc_processing.inc';
        if ( $paymentmethod == 'Online ACH Debit') {
            $cc_processor = GetACHProcessor();
            if ($cc_processor) {
                /** @noinspection PhpUndefinedVariableInspection */
                $result = $cc_processor->ACHTransfer(
                    $paymentamount, 
                    $routingnumber, 
                    $accountnumber, 
                    $accounttype, 
                    $bankname, 
                    $accountholder, 
                    $customerid, 
                    $invoiceID
                );
                /** @noinspection PhpUndefinedVariableInspection */
                if ( $achpayment['USEDEFAULTACCOUNT'] == 'true') {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $result['CUSTOMERBANKACCOUNTKEY'] = $customerbankaccount['RECORD#'];
                }
                $accounttext = $bankname . '--' . $accountnumber;
                $errorNo = 'BL34000081';
                LogEPayment($pRequest, $paymentmethod, $customerid, $prcurrency, $accounttext, $key, $result);    
            } else {
                $errorNo = '**********';
                $result = array('RESULT' => 'Error', 'ERRORMESSAGE' => ' Invalid gateway', 'Transaction ID' => 0);
            }
        } else {
            $cc_processor = GetCCProcessor();
            
            if ($cc_processor) {
                 $ccMgr = $gManagerFactory->getManager('CustomerCreditCard');
                /** @noinspection PhpUndefinedVariableInspection */
                $ccinfo = $ccMgr->GetList(
                    array('selects' => array('MAILADDRKEY', 'USEBILLTOADDR'), 'filters' => array(array(array('CARDNUM', '=', TwoWayEncryptPCI($ccnum, 'CREDITCARD')),
                                                        array('CUSTOMERID', '=', $customerid) )))
                );
               
                if ( $ccinfo[0]['USEBILLTOADDR'] == 'true' ) {
                    GetNObject('customer', $customerid, $customer);
                    if ( !$customer['BILLTOKEY'] ) {
                        GetContact($customer['DISPLAYCONTACTKEY'], $contact);
                    } else {
                        GetContact($customer['BILLTOKEY'], $contact);
                    }
                    $mailadd = $contact['MAILADDR'];
                } else if ( $ccinfo[0]['MAILADDRKEY'] != '' ) {
                    GetNObject('mailaddress', $ccinfo[0]['MAILADDRKEY'], $mailadd);
                }

                /** @noinspection PhpUndefinedVariableInspection */
                $result = $cc_processor->AuthAndCapture(
                    $paymentamount, 
                    $ccnum, 
                    $exp_month, 
                    $exp_year, 
                    $onlineccp['SECURITYCODE'], 
                    $customerid,  
                    $mailadd, 
                    $invoiceID
                );
                if ($onlineccp['USEDEFAULTCARD'] == 'true') {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $result['CUSTOMERCREDITCARDKEY'] = $customercreditcard['RECORD#'];
                }
                $accounttext = $onlineccp['CARDTYPE'] . '...' . substr($ccnum, -4);
                $errorNo = 'BL34000077';
                LogEPayment($pRequest, $paymentmethod, $customerid, $prcurrency, $accounttext, $key, $result);    
            } else {
                $errorNo = '**********';
                $result = array('RESULT' => 'Error', 'ERRORMESSAGE' => ' Invalid gateway', 'Transaction ID' => 0);
            }
        }
        
        if ($result['RESULT'] != 'APPROVED') {
            $result['ERRORMESSAGE'] = str_replace('<br/>', '', $result['ERRORMESSAGE']);
            /** @noinspection PhpUndefinedVariableInspection */
            $gErr->addError($errorNo, __FILE__ . '.' . __LINE__, $errorContainer, $result['ERRORMESSAGE']);
            $ok = false;
        }else{
            // If we could receive payments and successfuly auth and capture online charge card posting,
            // lets update payment records's reference# with transaction ID we received from payment GW
            $prrecMgr = $gManagerFactory->getManager('prrecord');
            $prrecMgr->UpdateDocNumber($key, $result['TRANSACTION_ID']);

            $data = array();
            $data['transactionid'][0]['cdata'] = $result['TRANSACTION_ID'];
        }
    }

    return $ok;
}

/**
 * @param string $entity
 * @param string $uid
 *
 * @return mixed
 */
function GetRealCaseFromUppercase($entity, $uid)
{
    $kUidfieldmap = [
        'department' => 'DEPT_NO',
        'location'   => 'LOCATION_NO',
    ];

    $uidfld = $kUidfieldmap[$entity];

    // construct the query
    $select = "RECORD#, $uidfld";
    $where = ["upper($uidfld) = :2", strtoupper($uid)];
    $rec = GetNObjects($entity, $select, $where);

    if (Util::countOrZero($rec) == 0) {
        return $uid; // return the original value
    }

    $newuid = $rec[0][strtoupper($uidfld)];

    return $newuid; // return the value in the correct case
}

/**
 * @param array  $currarr
 * @param string $tkey
 *
 * @return bool
 */
function ApplyARPaymentWrapper($currarr, &$tkey)
{
    global $gErr;

    include_once 'backend_payment.inc';
    include_once 'backend_prrecord.inc';

    $paymentRecord = getTransactionDetail($currarr['ARPAYMENTKEY'],'payment');

    // is the arpaymentkey key given already voided, then we need error out
    if ( $paymentRecord['STATE'] == 'V' ) {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "The given AR Payment key: ".$currarr['ARPAYMENTKEY']." has been reversed."
        );
        return false;
    }

    if($paymentRecord['RECORDTYPE'] == 'ra' && $paymentRecord['TOTALENTERED'] > 0)  //adjustment
    {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "The given AR Payment key: ".$currarr['ARPAYMENTKEY']." must be a credit-memo."
        );
        return false;
    }

    if($paymentRecord['TRX_TOTALDUE'] == 0) {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "The given AR Payment key: ".$currarr['ARPAYMENTKEY']." is already fully applied."
        );
        return false;
    }

    $currarr['CUSTOMERID'] = $paymentRecord['ENTITY'];

    $ret = true;
    $total = 0;
    $amount = array();
    /** @noinspection PhpUnusedLocalVariableInspection */
    $discounts = array();
    $rectype = array();
    $termsArray = array();
    $customerid = $currarr['CUSTOMERID'];

    // Need validation of the customerid
    if (!ValidCustomer($customerid)) {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "The customer " . $customerid . " does not exist."
        );
        return false;
    }

    if (isset($currarr['ARPAYMENTITEMDETAILS']) && is_array($currarr['ARPAYMENTITEMDETAILS'])) {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "This feature is not yet implemented", "This feature is not yet implemented"
        );
        $ret = false;
    }

    if (!isset($currarr['PAYMENTDATE']) || $currarr['PAYMENTDATE'] == '') {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "The paymentdate is not provided."
        );
        return false;
    }

    // Get the payment batch.
    // If any batchkey is provided, use it else create a payment batch using the payment date provide.
    if (isset($currarr['BATCHKEY']) && $currarr['BATCHKEY'] != '') {
        GetPRBatch($currarr['BATCHKEY'], $batchValues);
        $recordBatchkey = $currarr['BATCHKEY'];
        if (!isset($currarr['PAYMENTDATE'])) {
            $currarr["PAYMENTDATE"] = $batchValues['CREATED'];
        }
    } else if (isset($currarr['PAYMENTDATE']) && $currarr['PAYMENTDATE'] != '') {
        if ($paymentRecord['TRX_TOTALDUE'] > 0) {
            $payRecType = 'ro';
        } else {
            $payRecType = 'rp';
        }
        include_once 'backend_autobatch.inc';
        /** @noinspection PhpUnusedLocalVariableInspection */
        $ok = GetSubsysDocBatch($payRecType, $currarr['PAYMENTDATE'], $recordBatchkey);
    }



    if (!$currarr['ARPAYMENTITEMS']) {

        // RETRIEVE ALL THE OPEN INVOICES FOR THE GIVEN CUSTOMER
        $filter = array("PRRECORD.TRX_TOTALDUE > 0 and PRRECORD.RECORDTYPE in ('ri', 'ra') and PRRECORD.ENTITY = :2", 'C'.$customerid);
        $select = "PRRECORD.RECORD#, PRRECORD.TRX_TOTALDUE, PRRECORD.TRX_TOTALENTERED, PRRECORD.WHENCREATED";
        $order = "PRRECORD.WHENCREATED, PRRECORD.RECORD# asc";
        $invoices = GetNObjects('prrecord', $select, $filter, $order);

        // RUN THROUGH THE INVOICES CREATING PAYMENT ITEMS
        $amtapplied = 0;
        $totaltoapply = ibcabs($paymentRecord['TRX_TOTALDUE']);

        if (Util::countOrZero($invoices) > 0) {
            foreach($invoices as $invoice) {
                $lefttoapply = bcsub($totaltoapply, $amtapplied, 2);

                // CREATE THE PAYMENT ITEM
                $amttoapply = ($lefttoapply >= $invoice['TRX_TOTALDUE']) ? $invoice['TRX_TOTALDUE'] : $lefttoapply;
                $temp['ARPAYMENTITEM'][] = array(
                'INVOICEKEY' => $invoice['RECORD#'],
                'AMOUNT' => $amttoapply
                                                    );

                // RESET FOR NEXT ITERATION
                $amtapplied = bcadd($amtapplied, $amttoapply, 2);
                if ($amtapplied >= $totaltoapply) {
                    break;
                }
            }
        }
        /** @noinspection PhpUndefinedVariableInspection */
        $currarr['ARPAYMENTITEMS'][] = $temp;
    }

    //Process PayItems
    $paymentAmount = '';
    foreach ($currarr['ARPAYMENTITEMS'] as $arpaymentitems) 
    {
        foreach($arpaymentitems as $paymentLineItem)
        {
            foreach($paymentLineItem as $paymentitem) 
            {
                $invoicekey = $paymentitem['INVOICEKEY'];
                $amount[$invoicekey] = preg_replace("/[^0-9.-]/", "", $paymentitem['AMOUNT']);

                $prrecord = getTransactionDetail($invoicekey);
                if (empty ($prrecord) ) {
                    $gErr->addError(
                        '**********', __FILE__.'.'.__LINE__,
                        "No record exits for the recordkey " . $invoicekey
                    );
                    $ret = false;
                }

                /** @noinspection OnlyWritesOnParameterInspection */
                $rectype[$invoicekey] = $prrecord['RECORDTYPE'];

                if ( !($prrecord['RECORDTYPE'] == 'ri' || $prrecord['RECORDTYPE'] == 'ra') ) {
                    //NOT AN AR PAYMENT
                    $gErr->addError(
                        '**********', __FILE__.'.'.__LINE__,
                        "The key " . $invoicekey . "does not belong to an accounts receivable document."
                    );
                    $ret = false;

                } else if ($prrecord['TRX_TOTALDUE'] == 0) {
                    //ERROR... INVOICE IS ALREADY PAID...
                    $gErr->addError(
                        '**********', __FILE__.'.'.__LINE__,
                        "invoice with key " . $invoicekey . " is already paid"
                    );
                    $ret = false;

                }  else if ( $amount[$invoicekey] < 0 && ($prrecord['TRX_TOTALDUE'] > 0 || $prrecord['RECORDTYPE'] == 'ra') ) {
                    //ERROR.. CAN'T PAY MORE THAN TOTAL DUE.....
                    $gErr->addError(
                        '**********', __FILE__.'.'.__LINE__,
                        "The amount paid cannot be negative for invoice with key " . $invoicekey
                    );
                    $ret = false;

                } else if ( $amount[$invoicekey] > 0 && $prrecord['TRX_TOTALDUE'] < 0 && $prrecord['RECORDTYPE'] != 'ra') {
                    //error.. can't pay more than total due.....
                    $gErr->addError(
                        '**********', __FILE__.'.'.__LINE__,
                        "The amount paid cannot be negative for invoice with key " . $invoicekey
                    );
                    $ret = false;

                } else if ($amount[$invoicekey] > $prrecord['TRX_TOTALDUE']) {
                    //error.. can't pay more than total due.....
                    $gErr->addError(
                        '**********', __FILE__.'.'.__LINE__,
                        "The amount paid is greater than amount due for invoice with key " . $invoicekey
                    );
                    $ret = false;
                }

                if ($prrecord['ENTITY'] != $customerid) {
                    $gErr->addError(
                        '**********', __FILE__.'.'.__LINE__,
                        "No such customer"
                    );
                    $ret = false;
                }

                if ( $prrecord['STATE'] == 'D' ) {
                    $gErr->addError(
                        '**********', __FILE__.'.'.__LINE__,
                        "One or more invoices are in Draft status. Post the invoices, or remove them from the payment request, and try again."
                    );
                    return false;
                }

                $paymentAmount = $paymentAmount + $amount[$invoicekey];

                //FOLLOWING CODE IS PICKED UP FROM receiveinvoices.phtml

                $termkey = $prrecord['TERMKEY'] ;

                if ($termkey == "") {
                    $terminfo = "";
                }
                else {
                    if ( !isset($termsArray[$termkey])) {
                        GetTerm($termkey, $term);
                        $term = $term['VALUE'];
                        $termsArray[$prrecord['TERMKEY']] = $term;
                    }
                    $terminfo = $termsArray[$prrecord['TERMKEY']];
                }

                include_once 'backend_discpen.inc';
                $discount[$invoicekey] = GetDiscountForInvoice($terminfo, $prrecord);
                /** @noinspection OnlyWritesOnParameterInspection */
                $total += str_replace(",", "", $amount[$invoicekey]);
            }
        }
    }
    $currarr['TRX_PAYMENTAMOUNT'] = $paymentAmount;

    if (bccomp($currarr['TRX_PAYMENTAMOUNT'], ibcabs($paymentRecord['TRX_TOTALDUE'])) > 0) {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "The total amount paid is greater than amount due for payment with key " . $currarr['ARPAYMENTKEY']
        );
        $ret = false;
    }

    //CHECK FOR VALID OVERPAYLOCID
    if ($currarr['OVERPAYLOCID']) {
        if (!IsValidLocation($currarr['OVERPAYLOCID']) ) {
             $ret = false;
        }
    }

    //CHECK FROM VALID OVERPAYDEPTID
    if ($currarr['OVERPAYDEPTID']) {
        if (!IsValidDepartment($currarr['OVERPAYDEPTID']) ) {
            $ret = false;
        }
    }

    // New API format
    $pRequest = array();
    $pRequest['ENTITIES'] = array();
    $pRequest['ENTITIES']['C'.$currarr['CUSTOMERID']] = array();
    $pRequest['ENTITIES']['C'.$currarr['CUSTOMERID']]['PAYMENTS'] = array();

    $pRequest['ENTITIES']['C'.$currarr['CUSTOMERID']]['PAYMENTS'][0] = array(
                        'docnumber' => $currarr['REFID'],
                        'paydate'    => $currarr['PAYMENTDATE'],
                        'receiptdate' => $currarr['PAYMENTDATE'],
                        'totalselected' => '',
                        'overpay_dept'    => $currarr['OVERPAYDEPTID'],
                        'overpay_loc'    => $currarr['OVERPAYLOCID'],
                        'INVOICES'        => array()
                    );
    $distToNeg = true;
    $overpaymentPymt = false;
    if( $paymentRecord['RECORDTYPE'] == PRRECORD_TYPE_RECEIPT || $paymentRecord['RECORDTYPE'] == PRRECORD_TYPE_ARADVANCE ) {
        $distToNeg = false;
        $overpaymentPymt = true;
    }

    global $gManagerFactory;
    $itempymtMgr = $gManagerFactory->getManager('itempayment');

    $crSplitItems = $itempymtMgr->GetItemsForPymtProcessing($paymentRecord['RECORD#'], ibcabs($paymentRecord['TRX_TOTALDUE']), "", $distToNeg, $distToNeg, false);

    $paymentRec = array();
    foreach ($crSplitItems as $item) {
        $paymentRec[$item['RECORD#']] = $item;
    }
    $creditMap[$paymentRecord['RECORD#']] = $paymentRec;

    $invBaseCurrMap = array();
    $totalSelectedAmt = 0;
    $basecurr = "";
    foreach ($currarr['ARPAYMENTITEMS'] as $arpaymentitems) {
        foreach ($arpaymentitems as $arpaymentitem) {
            foreach ($arpaymentitem as $pair) {
                $splitItems = $itempymtMgr->GetItemsForPymtProcessing($pair['INVOICEKEY'], $pair['AMOUNT'], "", false, false, true);

                $prrec = array();
                foreach ($splitItems as $item) {
                    if (isset($invoiceMap[$pair['INVOICEKEY']][$item['RECORD#']])) {
                        /** @noinspection PhpUndefinedVariableInspection */
                        $item['DIST_AMT'] = $invoiceMap[$pair['INVOICEKEY']][$item['RECORD#']]['DIST_AMT'] + $item['DIST_AMT'];
                    }
                    $prrec[$item['RECORD#']] = $item;
                }

                $invoiceMap[$pair['INVOICEKEY']] = $prrec;
                if (isset($invoiceAmountMap[$pair['INVOICEKEY']])) {
                    /** @noinspection PhpUndefinedVariableInspection */
                    $invoiceAmountMap[$pair['INVOICEKEY']] = $invoiceAmountMap[$pair['INVOICEKEY']] + $pair['AMOUNT'];
                    $totalSelectedAmt = $totalSelectedAmt + $pair['AMOUNT'];
                } else {
                    $invoiceAmountMap[$pair['INVOICEKEY']] = $pair['AMOUNT'];
                    $totalSelectedAmt = $totalSelectedAmt + $pair['AMOUNT'];
                }

                GetPRRecord($pair['INVOICEKEY'], $invoiceRecord);
                if(!isset($invBaseCurrMap[$pair['INVOICEKEY']])) {
                    $invBaseCurrMap[$pair['INVOICEKEY']]['BASECURR'] = $invoiceRecord['BASECURR'];
                }
                $_rectypeMap[$pair['INVOICEKEY']] = $invoiceRecord['RECORDTYPE'];
                $_recmodkey[$pair['INVOICEKEY']] = $invoiceRecord['MODULEKEY'];
            }
        }
    }
    //$pRequest['ENTITIES']['C'.$currarr['CUSTOMERID']]['PAYMENTS'][0]['totalselected'] = $totalSelectedAmt;
    if(empty($invoiceMap)) {
        $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "Oops, we've encountered a glitch while fetchin invoice detail, review your transaction, then try again. "
        );
        $ret = false;
    }

    /** @noinspection PhpUndefinedVariableInspection */
    foreach( $invoiceMap as $invoiceKey => $entries) {
        /** @noinspection PhpUnusedLocalVariableInspection */
        $indexedRecKey = $invoiceKey . 'I';
        /** @noinspection PhpUndefinedVariableInspection */
        $amountSelected = $invoiceAmountMap[$invoiceKey];

        $amountSelected = str_replace(",", "", $amountSelected);
        $creditSelected = 0;
        $splits = array();
        $creditSplits = array();
        $currency = '';
        $basecurr = $invBaseCurrMap[$invoiceKey]['BASECURR'];;
        //Create SPLITS and CREDIT_SPLITS
        foreach($entries as $entryKey => $entry) {
            $entryamountSelected = $entry['DIST_AMT'];

            foreach($creditMap as $creditKey => $credit) {
                foreach($credit as $lineitemKey => $lineitem) {
                    $lineitem['DIST_AMT'] = ibcabs($lineitem['DIST_AMT']);
                    if ($lineitem['DIST_AMT'] > 0 && $entryamountSelected > 0) {
                        if ($lineitem['DIST_AMT'] >= $entryamountSelected) {
                            $thisCredit = $entryamountSelected;
                        } else {
                            $thisCredit = $lineitem['DIST_AMT'];
                        }

                        $lineitem['DIST_AMT'] = bcsub($lineitem['DIST_AMT'], $thisCredit);
                        $creditMap[$creditKey][$lineitemKey]['DIST_AMT'] = -$lineitem['DIST_AMT'];

                        if ($overpaymentPymt && isset($lineitem['TRX_DIST_AMT']) && $lineitem['TRX_DIST_AMT'] != '' ) {
                            $creditSplits[$entryKey][$creditKey][$lineitemKey] = ibcmul($thisCredit, $lineitem['EXCHANGE_RATE'], 2, true)."~".$lineitem['CURRENCY']."~".$thisCredit;
                        } else {
                            $creditSplits[$entryKey][$creditKey][$lineitemKey] = $thisCredit;
                        }
                        $entryamountSelected = bcsub($entryamountSelected, $thisCredit);

                        $creditSelected = bcadd($creditSelected, $thisCredit);
                        $amountSelected = bcsub($amountSelected, $thisCredit);
                    }
                }
            }
            if ( trim($entryamountSelected) != '') {
                $splits[] = array('RECORD#' => $entryKey, 'SELECTED' => $entryamountSelected);
            }
            $currency = $entry['CURRENCY'];
        }

        /** @noinspection PhpUndefinedVariableInspection */
        $pRQ = array(
            'selected'          => $amountSelected,
            'discount'          => '',
            'discdate'          => $currarr['PAYMENTDATE'],
            'trx_credit'        => $creditSelected,
            'currency'         => $currency,
            'recordtype'        => $_rectypeMap[$invoiceKey],
            'record#'           => $invoiceKey,
            'SPLITS'            => $splits,
            'CREDIT_SPLITS'     => $creditSplits,
            'basecurr'          => $basecurr,
            'modulekey'         => $_recmodkey[$invoiceKey]
        );


        $pRequest['ENTITIES']['C'.$currarr['CUSTOMERID']]['PAYMENTS'][0]['INVOICES'][] = $pRQ;
    }
    /** @noinspection PhpUndefinedVariableInspection */
    $pRequest['ENTITIES']['C' . $currarr['CUSTOMERID']]['PAYMENTS'][0]['currency'] = $currency;

    // invoice and payment record should be of same currency
    if ( $ret && ( ( $currency != $paymentRecord['CURRENCY'] || ( $basecurr != $paymentRecord['BASECURR'] ) ) ) ) {

            $gErr->addError(
            '**********', __FILE__.'.'.__LINE__,
            "Payment currency and Invoice currency can't be different " . $currarr['ARPAYMENTKEY']
        );
        $ret = false;
    }
    /** @noinspection PhpUnusedLocalVariableInspection */
    $ok = true;
    if (!$ret) {
        return false;
    } else {
            $tkey = '';
            /** @noinspection PhpUndefinedVariableInspection */
            $ok = ReceivePayment($pRequest, $recordBatchkey, 'prrecord', 'Printed Check', $doit, $tkey);
    }
    return $ok;
}


/**
 * get the index from the $invArray
 *
 * @param array $invArray     the invoice array
 * @param int   $recordHeader record to search
 *
 * @return bool index if found else FALSE
 */

function findExistingRecord($invArray, $recordHeader)
{
    foreach ($invArray as $index => $value) {
        if ($value['record#'] == $recordHeader) {
            return $index;
        }
    }

    return false;
}

/**
 * Returns the session time parameters:
 * - sessiontimestamp - last time the session was used for accessing the application
 * - sessiontimeout - expiration time for the session
 *
 * @param xmlgw  $xmlgw
 * @param string $requestSessionId
 *
 * @return array
 */
function getAuthTimeParams(xmlgw $xmlgw, string $requestSessionId)
{
    $authTimeParams = [];
    $lastSessionAccess = getLastAccessForSession($xmlgw->_sessionid);
    if ( ! empty($lastSessionAccess) && is_array($lastSessionAccess) ) {
        if (isset($lastSessionAccess['LASTACCESS'])) {
            $date = $lastSessionAccess;
        } else if (isset($lastSessionAccess[0])) {
            $date = $lastSessionAccess[0];
        } else {
            return [];
        }

        $lastaccess = $date['LASTACCESS'];
        $lastaccess = str_replace(" ", "T", $lastaccess); //convert to xsd format
        $lastaccess .= $date['SESSIONTIMEZONE'];
        $authTimeParams['sessiontimestamp'][0]['cdata'] = $lastaccess;

        if ( $requestSessionId !== null ) {
            $sessionHandler = IASessionHandler::getInstance($requestSessionId);
        } else {
            $sessionHandler = IASessionHandler::getInstance();
        }

        if ( $sessionHandler ) {
            $timeout = $sessionHandler->expiresAt();
            if ( $timeout ) {
                $authTimeParams['sessiontimeout'][0]['cdata'] =
                    date('Y-m-d\TH:i:s', $timeout) . $date['SESSIONTIMEZONE'];
            }
        }
    }

    return $authTimeParams;
}

/**
 * API to fetch the transaction total due based on the line level detail
 *
 * @param int    $txnKey
 * @param string $txnType
 *
 * @return array|false
 *
 */
function getTransactionDetail($txnKey, $txnType = '') {

    $gManagerFactory = Globals::$g->gManagerFactory;
    $prentryMgr = $gManagerFactory->getManager('prentry');

    //Get base prentries due for payment query - Note: cny# is always parm#1
    $args = $prentryMgr->GetEntriesDueForPaymentQuery($txnKey);
    $prentryquery = $args[0];

    // In case of payment type, we need to get the:
    // negative lines from Invoice and Adjustment
    // positive lines from Overpayment and Advance

    if($txnType == 'payment') {
        $extraFilter = " AND CASE
                            WHEN a.recordtype in ('ra','ri') 
                                AND a.state IN (".PRINVOICE_POSTED_NOTVOIDED_STATES.") 
                                AND NVL(b.trx_amount, b.amount) < 0 
                                AND NVL(b.trx_totaldue, b.totaldue) < 0 
                            THEN 1
                            WHEN 
                                a.recordtype in ('rp','rr') 
                                AND NVL(b.trx_amount, b.amount) > 0 
                                AND NVL(b.trx_totaldue, b.totaldue) > 0 
                            THEN 1
                        END = 1";
    } else {
        $extraFilter = " AND a.recordtype in ('ra','ri') 
                        AND a.state IN (".PRINVOICE_POSTED_NOTVOIDED_STATES.") 
                        AND NVL(b.trx_amount, b.amount) > 0 
                        AND NVL(b.trx_totaldue, b.totaldue) > 0 ";
    }

    $query = "SELECT a.record#,  a.currency, a.basecurr, a.recordtype,  SUBSTR( a.entity, 2, LENGTH (a.entity)) ENTITY,  a.state, a.termkey, 
                      SUM(b.amount)                                            AS TOTALENTERED,
                      SUM(b.totaldue)                                          AS totaldue,
                      SUM(b.trx_totaldue)                                      AS trx_totaldue,
                      SUM(DECODE(SIGN(b.trx_totaldue), 1, b.trx_totaldue, 0))  AS trx_postotaldue,
                      SUM(DECODE(SIGN(b.trx_totaldue), -1, b.trx_totaldue, 0)) AS trx_negtotaldue
				FROM	prrecordlite a, ($prentryquery) b
				WHERE   
				        a.cny# = :1
						and a.cny# = b.cny#
						and a.record# = :2
						and	a.record# = b.recordkey
						and b.lineitem = 'T' 
						and nvl(b.trx_totalselected, b.totalselected) = 0
						$extraFilter
				GROUP BY	
						a.record#,  a.currency, a.basecurr, a.recordtype,  a.entity,  a.state, a.termkey";
    $args[0] = $query;
    $args[1] = GetMyCompany();
    $args[2] = $txnKey;

    $result = QueryResult($args);

    return $result[0];
}
