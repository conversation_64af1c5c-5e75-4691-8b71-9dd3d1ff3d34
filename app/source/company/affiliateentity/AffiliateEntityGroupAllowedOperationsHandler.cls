<?php
/**
 * AffiliateEntityGroupAllowedOperationsHandler
 *
 * <AUTHOR>
 * @copyright Copyright (C)2025 Sage Intacct Corporation, All Rights Reserved
 *
 */

class AffiliateEntityGroupAllowedOperationsHandler extends AllowedOperationsHandler
{
    /**
     * @inheritDoc
     */
    public function __construct(EntityManager $entManager)
    {
        parent::__construct($entManager);
    }
    
    /**
     * Figure out the duplicate action is available or not
     *
     * @param array $record the delivery-service record
     * @param string|null $moduleKey
     *
     * @return bool
     */
    protected function canDuplicate(array $record, string $moduleKey = null): bool
    {
        return IsOperationAllowed(GetOperationId('co/lists/affiliateentitygroup/create'));
    }
    
    /**
     * @inheritDoc
     */
    protected function getOperations() : array
    {
        $operations = parent::getOperations();
        $operations = array_merge($operations, ['canDuplicate']);

        return $operations;
    }

}