<?

//================================================================================
//
// FILE:        BlockingLocalQueuedTester.cls
// AUTHOR:      <PERSON>
// DESCRIPTION: Test Class for IMS - Blocking, Local, Queued
//
//================================================================================



require_once 'IMSTester.cls';



class BlockingLocalQueuedTester extends IMSTester
{

    /**
     * @var string $ID
     */
    var $ID = 'BlockingLocalQueuedTester';


    /**
     * @param string $_name
     */
    function __construct($_name)
    {
        parent::__construct($_name);
        $this->_InitIMS();
    }



    function testSimpleTest() 
    {
        global $gErr;
        $testid = 'BlockingLocalQueuedTester:testSimpleTest';
        $args = array('result' => '');
        /** @noinspection PhpUnusedLocalVariableInspection */
        $ret = $this->_IMSPublisher->PublishMsg($this->ID, 'INTACCT', 'IMSGETTIME', IMS_PRIORITY_DEFAULT, $args, $this->_session, $response);

        $this->_ShowResult($response);

        if ($response['result'] == '') {
            $this->_AddError($testid, $gErr->errors[count($gErr->errors) - 1], 'failed');
            $this->_ThrowException();
        }
    }




    function _InitIMS() 
    {
        $this->_IMSPublisher = new ims_publish_1(IMS_MODE_BLOCKING, IMS_PROCESS_LOCAL, IMS_MODE_QUEUED);
        $this->_GetPkgSession();
    }


}


