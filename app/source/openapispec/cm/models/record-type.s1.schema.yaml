title: record-type  
type: string
description: Record type
enum:
  - debitCardTransaction
  - creditCardFees
  - depositSlips
  - bankCharge
  - creditCardCharge
  - manualCheck
  - bankInterest
  - creditCardPayment
  - quickInvoice
  - otherReceipts
  - fundsTransfer
  - cashMgmtPrintedQuickCheck
  - chargePayoffPayment
  - employeeExpenses
  - expenseRealizedMultiCurrencyGainLoss
  - appliedEmployeeAdvance
  - employeeReimbursements
  - employeeAdvance
  - apAdjustments
  - apDiscount
  - interEntityPayable
  - apBill
  - apRealizedMultiCurrencyGainLoss
  - apAppliedAdvance
  - apPayment
  - apAdvance
  - arAdjustments
  - arDiscount
  - interEntityReceivable
  - arInvoice
  - arRealizedMultiCurrencyGainLoss
  - arAppliedAdvanceOverpayment
  - arReceipts
  - arAdvance
  - journalEntry
  - arJournalEntry
  - apJournalEntry
  - arInitialOpenItems
  - apInitialOpenItems
  - arInitialOpenItemSummary
  - apInitialOpenItemSummary
  - arCcInitialOpenItems
  - apCcInitialOpenItems
  - arCcInitialOpenItemSummary
  - apCcInitialOpenItemSummary
x-mappedToValues:
  - cb 
  - cc
  - cd
  - ch
  - ci
  - ck
  - cn 
  - cp 
  - cq 
  - cr
  - ct
  - cw
  - cx
  - ei
  - em  
  - eo
  - ep
  - er
  - pa
  - pd
  - pe
  - pi
  - pm
  - po
  - pp
  - pr
  - ra
  - rd
  - re
  - ri
  - rm
  - ro
  - rp
  - rr
  - gl
  - rpgl
  - ppgl
  - rpoi
  - ppoi
  - rpsoi
  - ppsoi
  - rcoi
  - pcoi
  - rcsoi
  - pcsoi
x-mappedTo: RECORDTYPE
readOnly: true 
example: otherReceipts