--liquibase formatted sql
--changeset ya<PERSON><PERSON><PERSON>.<PERSON><PERSON><PERSON>wamy:V2311.0.20230906.106732.001 runOnChange:false logicalFilePath:V2311.0.20230906.106732.001__update_table_iataxformbox_iaform1099partnerboxfieldmap.sql

------------------------------------------------------------------------------------------------------------------------
-- IA-106732 - E-filing enhancements
------------------------------------------------------------------------------------------------------------------------
-----START INSERT INTO IATAXFORMBOX TABLE ----------
INSERT ALL
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 292, 2023, 1, '1A', '1a - Total Ordinary dividends', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 293, 2023, 1, '1B', '1b - Qualified dividends', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 294, 2023, 1, '2A', '2a - Total capital gain distr.', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 295, 2023, 1, '2B', '2b - Unrecap. sec. 1250 gain', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 296, 2023, 1, '2C', '2c - Section 1202 gain', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 297, 2023, 1, '2D', '2d - Collectibles (28%) gain', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 298, 2023, 1, '2E', '2e - Section 897 ordinary dividends', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 299, 2023, 1, '2F', '2f - Section 897 capital gain', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 300, 2023, 1, '3', '3 - Nondividend distributions', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 301, 2023, 1, '4', '4 - Federal income tax withheld', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 302, 2023, 1, '5', '5 - Section 199A dividends', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 303, 2023, 1, '6', '6 - Investment expenses', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 304, 2023, 1, '7', '7 - Foreign tax paid', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 305, 2023, 1, '9', '9 - Cash liquidation distributions', 'AMOUNT', 600)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 306, 2023, 1, '10', '10 - Noncash liquidation distribution', 'AMOUNT', 600)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 307, 2023, 1, '11', '11 - FATCA filing requirement', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 308, 2023, 1, '12', '12 - Exempt-interest dividends.', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 309, 2023, 1, '13', '13 - Specified private activity bond interest dividends.', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 310, 2023, 1, '16', '16 - State tax withheld.', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 311, 2023, 1, 'TIN', '2nd TIN not', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 312, 2023, 1, '8', '8 - Foreign country or U.S. possession', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 313, 2023, 1, '14', '14 - State', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 314, 2023, 1, '15', '15 - State identification no.', 'TEXTBOX')
-------------------
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 315, 2023, 2, '1', '1 - Interest income', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 316, 2023, 2, '2', '2 - Early withdrawal penalty', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 317, 2023, 2, '3', '3 - Interest on U.S. Savings Bonds and Treasury obligations', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 318, 2023, 2, '4', '4 - Federal income tax withheld', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 319, 2023, 2, '5', '5 - Investment expenses', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 320, 2023, 2, '6', '6 - Foreign tax paid', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 321, 2023, 2, '8', '8 - Tax-exempt interest', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 322, 2023, 2, '9', '9 - Specified private activity bond interest', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 323, 2023, 2, '10', '10 - Market Discount', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 324, 2023, 2, '11', '11 - Bond premium', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 325, 2023, 2, '12', '12 - Bond premium on Treasury obligations', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 326, 2023, 2, '13', '13 - Bond premium on tax-exempt bond', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 327, 2023, 2, '17', '17 - State tax withheld', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 328, 2023, 2, 'FATCA', 'FATCA filing requirement', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 329, 2023, 2, 'TIN', '2nd TIN not.', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 330, 2023, 2, '7', '7 - Foreign country or U.S. possession', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 331, 2023, 2, '14', '14 - Tax-exempt and tax credit bond CUSIP no.', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 332, 2023, 2, '15', '15 - State.', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 333, 2023, 2, '16', '16 - State identification no.', 'TEXTBOX')
-------------------
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 334, 2023, 3, '1', '1 - Rents', 'AMOUNT', 600)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 335, 2023, 3, '2', '2 - Royalties', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 336, 2023, 3, '3', '3 - Other incomes', 'AMOUNT', 600)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 337, 2023, 3, '4', '4 - Federal income tax withheld', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 338, 2023, 3, '5', '5 - Fishing boat proceeds', 'AMOUNT', 600)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 339, 2023, 3, '6', '6 - Medical and health care payments', 'AMOUNT', 600)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 340, 2023, 3, '8', '8 - Substitute payments in lieu of dividends or interest', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 341, 2023, 3, '9', '9 - Crop insurance proceeds', 'AMOUNT', 600)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 342, 2023, 3, '10', '10 - Gross proceeds paid to an attorney', 'AMOUNT', 600)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 343, 2023, 3, '11', '11 - Fish purchased for resale', 'AMOUNT', 600)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 344, 2023, 3, '12', '12 - Section 409A deferrals', 'AMOUNT', 600)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 345, 2023, 3, '13', '13 - FATCA filing requirement', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 346, 2023, 3, '14', '14 - Excess golden parachute payments', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 347, 2023, 3, '15', '15 - Nonqualified deferred compensation', 'AMOUNT', 600)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 348, 2023, 3, '16', '16 - State tax withheld', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 349, 2023, 3, '18', '18 - State income', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 350, 2023, 3, 'TIN', '2nd TIN not.', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 351, 2023, 3, '7', '7 - Payer made direct sales totaling $5000 or more of consumer products to recipient for resale', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 352, 2023, 3, '17', '17 - State/Payer''s state no.s', 'TEXTBOX')
------------------
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 353, 2023, 4, '1', '1 - Gross distribution', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 354, 2023, 4, '2A', '2a - Taxable amount', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 355, 2023, 4, '3', '3 - Capital gain (included in box 2a)', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 356, 2023, 4, '4', '4 - Federal income tax withheld', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 357, 2023, 4, '5', '5 - Employee contributions/Designated Roth contributions or insurance premiums', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 358, 2023, 4, '6', '6 - Net unrealized appreciation in employer\''s securities', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 359, 2023, 4, '8', '8 - Other', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 360, 2023, 4, '9B', '9b - Total employee contributions', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 361, 2023, 4, '10', '10 - Amount allocable to IRR within 5 year', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 362, 2023, 4, '14', '14 - State tax withheld', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 363, 2023, 4, '16', '16 - State distribution', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 364, 2023, 4, '17', '17 - Local tax withheld', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 365, 2023, 4, '19', '19 - Local distribution', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 366, 2023, 4, '12', '12 - FATCA filing requirement', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 367, 2023, 4, '2B', '2b - Taxable amount not determined', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 368, 2023, 4, 'TD', 'Total distribution', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 369, 2023, 4, 'IRA', 'IRA / SEP / SIMPLE', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 370, 2023, 4, '7', '7 - Distribution Code(s)', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 371, 2023, 4, '9A', '9a - Your percentage of total distribution', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 372, 2023, 4, '11', '11 - 1st year of desig. Roth contrib.', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 373, 2023, 4, '15', '15 - State/Payers state no.', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 374, 2023, 4, '18', '18 - Name of locality', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 375, 2023, 4, '13', 'Date of Payment', 'TEXTBOX')
------------------
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 376, 2023, 5, '2', '2 - Gross proceeds', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 377, 2023, 5, '6', '6 - Buyer\''s part of real estate tax', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 378, 2023, 5, '4', '4 - Check here if the transferor received or will receive property or services as part of the consideration', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 379, 2023, 5, '5', '5 - Check here if the transferor is a foreign person (nonresident alien, foreign partnership, foreign or foreign trust', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 380, 2023, 5, '1', '1 - Date of closing', 'TEXTBOX')
-----------------
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 381, 2023, 6, '1', '1 - Patronage dividends', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 382, 2023, 6, '2', '2 - Nonpatronage distributions', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 383, 2023, 6, '3', '3 - Per-unit retain allocations', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 384, 2023, 6, '4', '4 - Federal income tax withheld', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 385, 2023, 6, '5', '5 - Redeemed nonqualified notices', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 386, 2023, 6, '6', '6 - Section 199A(g) deduction', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 387, 2023, 6, '7', '7 - Qualified payments (Section 199A(b)(7))', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 388, 2023, 6, '8', '8 - Section 199A(a) qual. items', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 389, 2023, 6, '9', '9 - Section 199A(a) SSTB items', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 390, 2023, 6, '10', '10 - Investment credit', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 391, 2023, 6, '11', '11 - Work opportunity credit', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 392, 2023, 6, '12', '12 - Other credits and deductions', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 393, 2023, 6, 'TIN', '2nd TIN not.', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 394, 2023, 6, '13', '13 - Specified Coop', 'CHECKBOX')
------------------
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 395, 2023, 7, '1', '1- Unemployment compensation', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 396, 2023, 7, '2', '2 - State or local income tax refunds, credits or offsets', 'AMOUNT', 10)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 397, 2023, 7, '4', '4 - Federal income tax withheld', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 398, 2023, 7, '5', '5 - RTAA payments', 'AMOUNT', 600)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 399, 2023, 7, '6', '6 - Taxable grants', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 400, 2023, 7, '7', '7 - Agriculture payments', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 401, 2023, 7, '9', '9 - Market Gain', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 402, 2023, 7, '11', '11 - State income tax withheld', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 403, 2023, 7, '8', '8 - Check if box 2 is trade or business income', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 404, 2023, 7, 'TIN', '2nd TIN not.', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 405, 2023, 7, '3', '3 - Box 2 amount is for tax year', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 406, 2023, 7, '10A', '10a - State', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 407, 2023, 7, '10B', '10b - State identification no.', 'TEXTBOX')
------------------
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 408, 2023, 8, '1', '1 - Reportable Winnings', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 409, 2023, 8, '4', '4 - Federal income tax withheld', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 410, 2023, 8, '7', '7 - Winnings from identical wagers', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT) VALUES ( 411, 2023, 8, '14', '14 - State winnings', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 412, 2023, 8, '15', '15 - State income tax withheld', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 413, 2023, 8, '16', '16 - Local winnings', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 414, 2023, 8, '17', '17 - Local income tax withheld', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 415, 2023, 8, '2', '2 - Date won', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 416, 2023, 8, '3', '3 - Type of wage', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 417, 2023, 8, '5', '5 - Transaction', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 418, 2023, 8, '6', '6 - Race', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 419, 2023, 8, '8', '8 - Cashier', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 420, 2023, 8, '9', '9 - Winners taxpayer identification no.', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 421, 2023, 8, '10', '10 - Window', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 422, 2023, 8, '11', '11 - First identification', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 423, 2023, 8, '12', '12 - Second identification', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 424, 2023, 8, '13', '13 - State/Payers state identification no.', 'TEXTBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 425, 2023, 8, '18', '18 - Name of locality', 'TEXTBOX')
------------------
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 426, 2023, 9, '1', '1 - Nonemployee compensation', 'AMOUNT', 600)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 427, 2023, 9, '4', '4 - Federal income tax withheld', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 428, 2023, 9, '5', '5 - State tax withheld', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE, MINAMOUNT ) VALUES ( 429, 2023, 9, '7', '7 - State income', 'AMOUNT', 0)
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 430, 2023, 9, 'TIN', '2nd TIN not.', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 431, 2023, 9, '2', '2 - Payer made direct sales totaling $5,000 or more of consumer products to recipient for resale', 'CHECKBOX')
INTO IATAXFORMBOX ( RECORD#, YEAR, FORMTYPEKEY, ABBREVATION, NAME, TYPE ) VALUES ( 432, 2023, 9, '6', '6 - State/Payer’s state no.', 'TEXTBOX')
SELECT * FROM dual
/


-----START INSERT INTO IAFORM1099PARTNERBOXFIELDMAP TABLE ----------
INSERT ALL
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 280, 'B1aTotOrdiDiv', 1, 292, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 281, 'B1bQualiDiv', 1, 293, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 282, 'B2aTotCapGain', 1, 294, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 283, 'B2bUnRecapSecGain', 1, 295, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 284, 'B2cSec1202Gain', 1, 296, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 285, 'B2dCollGain', 1, 297, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 286, 'B2eSec897OrdiDiv', 1, 298, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 287, 'B2fSec897CapGain', 1, 299, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 288, 'B3NonDivDist', 1, 300, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 289, 'B4FedIncTaxWH', 1, 301, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 290, 'B5Sec199ADiv', 1, 302, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 291, 'B6InvestExp', 1, 303, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 292, 'B7ForeignTaxPaid', 1, 304, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 293, 'B8ForeignCountryOrUsPoss', 1, 312, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 294, 'B9CashLiquiDist', 1, 305, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 295, 'B10NonCashLiquiDist', 1, 306, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 296, 'B11IsFATCA', 1, 307, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 297, 'B12ExemptIntDiv', 1, 308, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 298, 'B13SpeciPrivActiBondIntDiv', 1, 309, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 299, 'Is2ndTINnot', 1, 311, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 300, 'StateIdNum', 1, 314, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 301, 'StateWH', 1, 310, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 302, 'StateCd', 1, 313, 2023, 1)
---------
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 303, 'B1IntIncome', 2, 315, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 304, 'B2EarlyWithdrawPenalty', 2, 316, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 305, 'B3InterestOnUS', 2, 317, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 306, 'B4FedIncomeTaxW', 2, 318, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 307, 'B5InvestExp', 2, 319, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 308, 'B6ForeignTaxPaid', 2, 320, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 309, 'B7ForeignCountry', 2, 330, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 310, 'B8TaxExemptInterest', 2, 321, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 311, 'B9BondInterest', 2, 322, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 312, 'B10MarketDiscount', 2, 323, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 313, 'B11BondPre', 2, 324, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 314, 'B12BondPreOnTreasOblig', 2, 325, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 315, 'B13BondPreOnTaxExempt', 2, 326, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 316, 'B14CUSIPno', 2, 331, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 317, 'IsFATCA', 2, 328, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 318, 'Is2ndTINnot', 2, 329, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 319, 'StateIdNum', 2, 333, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 320, 'StateWH', 2, 327, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 321, 'State', 2, 332, 2023, 1)
-----------
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 322, 'B1Rents', 3, 334, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 323, 'B2Royalties', 3, 335, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 324, 'B3OtherIncome', 3, 336, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 325, 'B4FedIncomeTaxWH', 3, 337, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 326, 'B5FishingBoatProceeds', 3, 338, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 327, 'B6MedHealthcarePymts', 3, 339, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 328, 'B7IsDirectSale', 3, 351, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 329, 'B8SubstitutePymts', 3, 340, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 330, 'B9CropInsurance', 3, 341, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 331, 'B10GrossProceeds', 3, 342, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 332, 'B11FishPurForResale', 3, 343, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 333, 'B12Sec409ADeferrals', 3, 344, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 334, 'B13IsFATCA', 3, 345, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 335, 'B14EPP', 3, 346, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 336, 'B15NonQualDefComp', 3, 347, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 337, 'Is2ndTINnot', 3, 350, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 338, 'StateWH', 3, 348, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 339, 'StateIdNum', 3, 352, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 340, 'StateIncome', 3, 349, 2023, 1)
---------
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 341, 'B1GrossDistribution', 4, 353, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 342, 'B2aTaxableAmt', 4, 354, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 343, 'B2bIsTaxableAmtNotDetermined', 4, 367, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 344, 'B2bIsTotalDistribution', 4, 368, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 345, 'B7IsSIRASEPSIMPLE', 4, 369, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 346, 'B3CapitalGain', 4, 355, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 347, 'B4FedTaxWH', 4, 356, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 348, 'B5EmpContribution', 4, 357, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 349, 'B6NUA', 4, 358, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 350, 'B8Other', 4, 359, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 351, 'B9aTotDistPercentage', 4, 371, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 352, 'B9bTotEmpContribution', 4, 360, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 353, 'B10AmtToIRR5Years', 4, 361, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 354, 'B11DesigRoth1stYear', 4, 372, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 355, 'B12IsFATCAFiling', 4, 366, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 356, 'B13DateOfPayment', 4, 375, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 357, 'StateWH', 4, 362, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 358, 'StateDistribution', 4, 363, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 359, 'StateIdNum', 4, 373, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 360, 'LocalDistribution', 4, 365, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 361, 'LocalTax', 4, 364, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 362, 'LocalityNm', 4, 374, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 363, 'B7DistCodes', 4, 370, 2023, 1)
-------------------
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 364, 'B1DateOfClosing', 5, 380, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 365, 'B2GrossProceeds', 5, 376, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 366, 'B4IsPropertyOrServiceRcvd', 5, 378, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 367, 'B5IsTransferorForeign', 5, 379, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 368, 'B6BuyersTax', 5, 377, 2023, 1)
-------------------
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 369, 'B1PatronageDiv', 6, 381, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 370, 'B2NonPatronageDist', 6, 382, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 371, 'B3PerUnitAllocations', 6, 383, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 372, 'B4FedTaxWH', 6, 384, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 373, 'B5RedmNonQualNotices', 6, 385, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 374, 'B6Sec199AgDeduction', 6, 386, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 375, 'B7QualPymtsSec199Ab7', 6, 387, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 376, 'B8Sec199AaQualItems', 6, 388, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 377, 'B9Sec199AaSSTBItems', 6, 389, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 378, 'B10InvestmentCredit', 6, 390, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 379, 'B11WorkOpporCredit', 6, 391, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 380, 'B12OtherCreditsAndDeductions', 6, 392, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 381, 'B13IsSpecifiedCoop', 6, 394, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 382, 'Is2ndTINnot', 6, 393, 2023, 1)
------------
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 383, 'B1UnempComp', 7, 395, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 384, 'B2TaxRefunds', 7, 396, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 385, 'B3TaxYearOfB2Amt', 7, 405, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 386, 'B4FedTaxWH', 7, 397, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 387, 'B5RTAAPayments', 7, 398, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 388, 'B6TaxableGrants', 7, 399, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 389, 'B7AgriPayments', 7, 400, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 390, 'B8IsTradeOrBizIncome', 7, 403, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 391, 'B9MarketGain', 7, 401, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 392, 'Is2ndTINnot', 7, 404, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 393, 'StateCd', 7, 406, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 394, 'StateWH', 7, 402, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 395, 'StateIdNum', 7, 407, 2023, 1)
-------------
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 396, 'B1ReportableWinnings', 8, 408, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 397, 'B2DateWon', 8, 415, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 398, 'B3WagerType', 8, 416, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 399, 'B4FedTaxWH', 8, 409, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 400, 'B5Txn', 8, 417, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 401, 'B6Race', 8, 418, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 402, 'B7WinningsIdWagers', 8, 410, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 403, 'B8Cashier', 8, 419, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 404, 'B10Window', 8, 421, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 405, 'B11FirstId', 8, 422, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 406, 'B12SecondId', 8, 423, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 407, 'StateIdNum', 8, 424, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 408, 'StateWinnings', 8, 411, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 409, 'StateWH', 8, 412, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 410, 'LocalWinnings', 8, 413, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 411, 'LocalWH', 8, 414, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 412, 'LocalityNm', 8, 425, 2023, 1)
---------------
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 413, 'B1NEC', 9, 426, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 414, 'B2IsDirectSales', 9, 431, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 415, 'B4FedTaxWH', 9, 427, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 416, 'Is2ndTINnot', 9, 430, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 417, 'StateWH', 9, 428, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 418, 'StateIdNum', 9, 432, 2023, 1)
INTO IAFORM1099PARTNERBOXFIELDMAP ( RECORD#, PARTNERFIELD, FORMTYPEKEY, FORMBOXKEY, YEAR, PARTNERKEY ) VALUES ( 419, 'StateIncome', 9, 429, 2023, 1)
------------
SELECT * FROM dual
/
----------END INSERT


