/**
 * Varible is useful for storing the discount value to check that we can apply discount on it or not after changing payment date.
 */

var previousDisccount = {};

/**
 * Method to populate the payment at bill or bill item level.
 *
 * @param fieldMeta
 * @param isItemPayment
 * @param isJointPayment
 */
function populateBillPayment(fieldMeta, isItemPayment, isJointPayment)
{
    // validate the field meta
    if(!fieldMeta) {
        return;
    }
    if (!isItemPayment && !isJointPayment) {
        currentRow = fieldMeta.getLineNo();
    }
    var rowData = fieldMeta.parentValue;
    var prevAmount = rowData['PREVPAYMENTAMOUNT'];
    // if there is an previous amount check with currently entered amount
    // Continue only if the amount varies
    if(prevAmount) {
        // if current value is empty then reset with previous amount
        if(!fieldMeta.getValue()) {
            fieldMeta.setValue(prevAmount);
            return;
        }
        else {
            if (prevAmount != fieldMeta.getValue()) {
                // set the payment distribution flag for new distribution
                rowData['SPLITPAYMENT'] = 'Y';
            } else {
                return;
            }
        }
    } else {
        prevAmount = 0;
        // set the payment distribution flag for new distribution as the amount is new
        rowData['SPLITPAYMENT'] = 'Y';
    }

    var currentIndex = currentRow;
    var grid = getPayableGrid();
    if(isItemPayment) {
        currentIndex = 0;
        if ((typeof(currentItemRow) != 'undefined') && currentItemRow >= 0) {
            currentIndex = currentItemRow;
        }
        grid = getPayableItemGrid();
    }

    if (typeof(rowData.TRX_TOTALDUE) == 'undefined') {
        rowData.TRX_TOTALDUE = grid.value[currentIndex].TRX_TOTALDUE;
    } 
    var isValid;
    var itemPayeeGrid = getGrid('JOINTLINEITEMS');
    if (isJointPayment && !isItemPayment) {
        jointPayableGrid = getGrid('JOINTITEMS');
        if (jointPayableGrid) {
            isValid = validateBillPayment(jointPayableGrid.value, false, isItemPayment, isJointPayment, true);
        }
    } else if (isJointPayment && isItemPayment) {
        if (itemPayeeGrid) {
            isValid = validateBillPayment(itemPayeeGrid.value, false, isItemPayment, isJointPayment, true);
        }
    } else {
        isValid = validateBillPayment(rowData, false, isItemPayment, isJointPayment, true);
    }
    // if the validation fails then reset the value
    if(!isValid) {
        // If the amount entered not valid, set the previous amount
        fieldMeta.setValue(prevAmount);
        grid.computeTotals();
        grid.refreshTotals();
        if (!isItemPayment && isJointPayment) {
            var itemGrid = getGrid('JOINTITEMS');
            if (itemGrid) {
                itemGrid.computeTotals();
                itemGrid.refreshTotals();
            }
        } else if (isItemPayment && isJointPayment) {
            if (itemPayeeGrid) {
                itemPayeeGrid.computeTotals();
                itemPayeeGrid.refreshTotals();
            }
        }
        return;
    }
    if (!rowData.JOINTPAYEES && typeof(rowData.JOINTPAYEENAME) != 'undefined') {
        if (grid.value && (typeof(rowData.RECORDNO) == 'undefined' || rowData.RECORDNO == '')) {
            if (isItemPayment) {
                if ((typeof(currentItemRow) == 'undefined') || currentItemRow == -1) {
                    rowData.RECORDNO = grid.value[0].RECORDKEY;
                } else {
                    rowData.RECORDNO = grid.value[currentItemRow].RECORDNO;
                }
            } else {
                rowData.RECORDNO = grid.value[currentIndex].RECORDNO;
            }
        }
    }
    
    // populate the line data
    if (isJointPayment && rowData.PAYABLES) {
        populateLineData(rowData.PAYABLES[currentIndex], isItemPayment, isJointPayment, currentIndex, true, true);   
    } else {
        populateLineData(rowData, isItemPayment, isJointPayment, currentIndex, true, true);
    }
    if(isItemPayment) {
        calculateAmountToPay(rowData, fieldMeta.getLineNo(), true, true);
    }
    //Need to set the flag as true to avoid re-distribution of line item payment
    isLineDetPopulated = true;
    // Store the payment amount in another variable to track the changes
    rowData['PREVPAYMENTAMOUNT'] = fieldMeta.getValue();
}

/**
 * Method to validate the bill or line item level payments.
 *
 * @param rowData
 * @param isCredit
 * @param isItemPayment
 * @param isJointPayment
 * @returns {boolean}
 */
function validateBillPayment(rowData, isCredit, isItemPayment, isJointPayment, pymtAmtChanged)
{
    if(!rowData) {
        return false;
    }
    var billNo = "";
    var grid = getPayableGrid();
    if(isItemPayment || isJointPayment) {
        billNo = window.editor.view.getField('BILLRECORDID');
        if(billNo && billNo.getValue()) {
            billNo = " "+billNo.getValue();
        } else {
            if(grid.value && grid.value[currentRow] && grid.value[currentRow].RECORDID) {
                billNo = grid.value[currentRow].RECORDID;
            } else {
                billNo = ".";
            }
        }
    } else {
        billNo = rowData['RECORDID'];
    }
    var token_i18n ;

    var creditsAvailable = parseFloat(rowData['CREDITSAVAILABLE'] ? rowData['CREDITSAVAILABLE'] : 0);

    var creditApplied = parseFloat(rowData['CREDITSAPPLIED'] ? rowData['CREDITSAPPLIED'] : 0);
    var amountDue = parseFloat(rowData['TRX_TOTALDUE']);
    var paymentAmount = parseFloat(rowData['PAYMENTAMOUNT']);
    paymentAmount = isNaN(paymentAmount) ? 0 : paymentAmount;
    var discountApplied = parseFloat(rowData['DISCOUNTAPPLIED']);
    discountApplied = isNaN(discountApplied) ? 0 : discountApplied;
    if (window.editor.view.value.PAYMENTMETHOD_D && window.editor.view.value.PAYMENTMETHOD_D == 'Joint Check') {
        discountApplied = 0;
    }
    
    if (isJointPayment) {
        paymentAmount = 0;
        for (var i = 0; i < rowData.length; ++i) {
            paymentAmount += parseFloat(rowData[i].PAYMENTAMOUNT ? rowData[i].PAYMENTAMOUNT : 0);
        }
        if(grid.value && grid.value[currentRow]) {
            if (isItemPayment) {
                amountDue = parseFloat(grid.value[currentRow].BILLITEMS[currentItemRow].TRX_TOTALDUE);
            } else {
                amountDue = parseFloat(grid.value[currentRow].TRX_TOTALDUE);
            }
        }
    }

    var isValid = true;
    // validate for +ve amount
    if(creditApplied < 0 || paymentAmount < 0) {
        token_i18n = { "id" : "IA.ENTER_A_POSITIVE_AMOUNT_FOR_BILL_PLACEHOLDERS", "placeHolders" : [{ "name":"BILL_NO", "value": billNo}]};
        alert(GT(token_i18n));
        isValid = false;
    }
    else {
        // first check the amount due
        if (pymtAmtChanged && (amountDue < roundAmt(creditApplied + roundAmt(discountApplied + paymentAmount)))) {
            token_i18n = { "id" : "IA.THE_AMOUNT_DUE_IS_ONLY_FOR_BILL_PLACEHOLDERS", "placeHolders" : [{ "name":"AMOUNT_DUE", "value": amountDue}, { "name":"BILL_NO", "value": billNo}]};
            alert(GT(token_i18n));
            isValid = false;
        }
        // if the credits applied more than the available then throw error
        if (isValid && isCredit) {
            var prevAmount = rowData['PREVCREDITSAPPLIED'] ? rowData['PREVCREDITSAPPLIED'] : 0;
            if(roundAmt(creditApplied - prevAmount) > creditsAvailable) {
                token_i18n = { "id" : "IA.APPLY_AN_AMOUNT_EQUAL_TO_OR_LESS_THAN_THE_AVAILABLE_CREDIT_AMOUNT_FOR_BILL_PLACEHOLDERS", "placeHolders" : [{ "name":"BILL_NO", "value": billNo}]};
                alert(GT(token_i18n));
                isValid = false;
            }
            if(isValid && (isItemPayment || isJointPayment)) {
                var amountToPay = amountDue - discountApplied - creditApplied;
                if(amountToPay < 0) {
                    alert(GT('IA.AMOUNT_TO_PAY_EXCEEDS'));
                    isValid = false;
                }
            }
        }
    }
    if (isValid) {
        isValid = validateAndAlertJointCheckCurrencyMismatch();
    }

    return isValid;
}

/** Validate matching currency between bill and bank for joint check payments.
 * 
 * @returns {boolean}
 */
function validateAndAlertJointCheckCurrencyMismatch() {
    // Prevent mismatched currency for Joint checks between bank and bill.
    if (window.editor.view.value['PAYMENTMETHOD_D'] == 'Joint Check' && 
        window.editor.view.value['FOREIGNCURRENCYENABLED']
    ) {
        var billCurrencyObj = window.editor.view.getField('BILLCURRENCY');
        if (typeof(billCurrencyObj.value) != 'undefined' && billCurrencyObj.value != null &&
            currentBankCurrency != "" && billCurrencyObj.value != "" &&
            billCurrencyObj.value != currentBankCurrency
        ) {
            alert(GT('IA.JOINT_CHECK_PAYMENT_METHOD_DOES_NOT_SUPPORT_PAYMENTS_WHERE_THE_BANK_CURRENCY'));
            return false;
        }
    }
    return true;
}

/**
 * Method to populate the line data after keying in the payment/credit values at bill/line level.
 *
 * @param rowData
 * @param isItemPayment
 * @param isJointPayment
 * @param lineNo
 */
function populateLineData(rowData, isItemPayment, isJointPayment, lineNo, pymtAmtChanged, clearJoints)
{
    // return if there is no row data
    if(!rowData) {
        return;
    }
    
    var grid = getPayableGrid();
    // for item payments, set the payment amounts to the bill level
    if ((isItemPayment && currentRow != -1)) {
        var currentBillData = grid.parentValue;
        if(currentBillData) {
            // Prev and current payment amount same after applying at the line level
            currentBillData['PAYMENTAMOUNT'] = getTotalLineAmount(getPayableItemGrid(), 'PAYMENTAMOUNT');
            currentBillData['PREVPAYMENTAMOUNT'] = currentBillData['PAYMENTAMOUNT'];
            currentBillData['CREDITSAPPLIED'] = getTotalLineAmount(getPayableItemGrid(), 'CREDITSAPPLIED');
            currentBillData['PREVCREDITSAPPLIED'] = currentBillData['CREDITSAPPLIED'];
            // set the split flag to false
            currentBillData['SPLITPAYMENT'] = 'N';
            currentBillData['SPLITCREDIT'] = 'N';
            if (isJointPayment) {
                if (rowData['PAYMENTAMOUNT'] || rowData['CREDITSAPPLIED']) {
                    grid.selectRow(grid.value[currentRow], currentRow);
                } else {
                    grid.deselectRow(grid.value[currentRow], currentRow);
                }
            } else {
                if (currentBillData['PAYMENTAMOUNT'] || currentBillData['CREDITSAPPLIED']) {
                    grid.selectRow(grid.value[currentRow], currentRow);
                } else {
                    grid.deselectRow(grid.value[currentRow], currentRow);
                }
            }
            prevSelectionState = grid.selectColumnProps.selectionState;
            grid.updateNrOfSelected();
            // Update the total bill selected in the UI
            updateTotalSelected();
            if (isJointPayment && !isItemPayment) {
                // Call only when the payment/credit applied at bill level
                distributePayment(rowData, currentRow, pymtAmtChanged);
            }
        }
    }
    else {
        var creditApplied = parseFloat(rowData['CREDITSAPPLIED'] ? rowData['CREDITSAPPLIED'] : 0);
        var paymentAmount = parseFloat(rowData['PAYMENTAMOUNT']);
        // mark the bill selected if the credit or amount field is entered
        if(paymentAmount > 0 || creditApplied > 0 || paymentAmount !== rowData.PREVPAYMENTAMOUNT) {
            if (isJointPayment) {
                lineNo = currentRow;
            }
            var discountApplied = rowData['DISCOUNTAPPLIED'];
            grid.selectRow(rowData, lineNo);
            grid.updateNrOfSelected();
            rowData['DISCOUNTAPPLIED'] = discountApplied;
            if (!grid.value[lineNo]['WHENPAID']) {
                // apply the payment date to today if there is no payment date is set
                grid.value[lineNo]['WHENPAID'] = getCurrentDate();
            }
            if(rowData['DISCOUNTAPPLIED']) {
                rowData['DISCOUNTDATE'] = grid.value[lineNo]['WHENPAID'];
            }
            prevSelectionState = grid.selectColumnProps.selectionState;
            // Update the total bill selected in the UI
            if (window.editor.view.value.PAYMENTMETHOD_D && window.editor.view.value.PAYMENTMETHOD_D == 'Joint Check') {
                clearItemPayments(rowData, true, true, false, clearJoints);
            }
            updateTotalSelected();
            calculateAmountToPay(rowData, lineNo, pymtAmtChanged);
            // Call only when the payment/credit applied at bill level
            if (isJointPayment) {
                var gridData = grid.parentValue;
                gridData.PAYMENTAMOUNT = 0;
                gridData.SPLITPAYMENT = rowData['SPLITPAYMENT'];
                for (var i = 0; i < gridData.JOINTITEMS.length; i++) {
                    gridData.PAYMENTAMOUNT += parseFloat(gridData.JOINTITEMS[i].PAYMENTAMOUNT ? gridData.JOINTITEMS[i].PAYMENTAMOUNT : 0);
                }
                gridData.RECORDNO = gridData.PAYABLES[lineNo].RECORDNO;
                gridData.JOINTPAYEES = jQuery.extend(true, [], gridData.PAYABLES[lineNo].JOINTPAYEES);
                gridData.BILLITEMS = jQuery.extend(true, [], gridData.PAYABLES[lineNo].BILLITEMS);
                gridData.CREDITAPPLIED = creditApplied;
                // gridData contains elements that are not needed for distribution, and may contain invalid html tags.  
                // We remove some of the unnecessary data here.
                var rowDataToDistribute = jQuery.extend(true, {}, gridData);
                rowDataToDistribute.PAYABLES = undefined;
                rowDataToDistribute.std_buttons = undefined;
                rowDataToDistribute.FINANCIALENTITIES = undefined;
                distributePayment(rowDataToDistribute, lineNo, pymtAmtChanged);
                if (creditApplied > 0) {
                    allocateCredit(gridData.PAYABLES[lineNo], gridData.JOINTITEMS[0], creditApplied, lineNo, isItemPayment, isJointPayment);
                    //window.setTimeout(function(){ applyCredits(true); }, 0);
                }
            } else {
                distributePayment(rowData, lineNo, pymtAmtChanged);
            }
        } else {
            clearItemPayments(rowData, false, false, false, clearJoints);
            if(totalBillsSelected > 0) {
                grid.deselectRow(rowData, lineNo);
                grid.updateNrOfSelected();
                prevSelectionState = grid.selectColumnProps.selectionState;
            }
            // Update the total bill selected in the UI
            updateTotalSelected();
            // set the adjusted bank amount
            populateAdjustedBankBalanceNew();
           // grid.value[lineNo]['WHENPAID'] = '';
        }
    }
    updateRecordNoLineNoMap(rowData['RECORDNO'], lineNo);
}

/**
 * Method to get the total amount for the given component.
 *
 * @param grid
 * @param component
 * @returns {number}
 */
function getTotalLineAmount(grid, component)
{
    var totalLineAmount = 0;
    var rowCount = (grid.value ?  grid.value.length : 0);
    for ( var i = 0; i < rowCount; i++ ) {
        // first check the row entity
        var lineComponent = grid.value[i][component];
        if (!lineComponent) {
            continue;
        }
        var amount = parseFloat(lineComponent);
        // sum the amount in each line
        totalLineAmount = roundAmt(totalLineAmount + (isNaN(amount) ? 0 : amount));
    }
    return totalLineAmount;
}

/**
 * Method to return the total payment amount applied (including discounts) to the bill.
 *
 * @returns {number}
 */
function getTotalBillPayments()
{
    var totalBillPayment = 0;
    var discountAvailObj = window.editor.view.getField('BILLDISCOUNTAVAILABLE');
    if(discountAvailObj) {
        var amount = parseFloat(discountAvailObj.getValue());
        // sum the amount in each line
        totalBillPayment = roundAmt(totalBillPayment + (isNaN(amount) ? 0 : amount));
    }
    // Get the payment amount
    totalBillPayment = roundAmt(totalBillPayment + getTotalLineAmount(getPayableItemGrid(), 'PAYMENTAMOUNT'));
    // Get the credit amount
    totalBillPayment = roundAmt(totalBillPayment + getTotalLineAmount(getPayableItemGrid(), 'CREDITSAPPLIED'));
    return totalBillPayment;
}

/**
 * Method to apply credits at line level.
 *
 * @param fieldMeta
 */
function applyBillCredits(fieldMeta, isItemPayment, isJointPayment)
{
    if(!fieldMeta) {
        return;
    }
    var isValid = applyCreditPayments(fieldMeta, isItemPayment, isJointPayment);

    if(isValid) {
        // if its valid, populate the line data
        if (!fieldMeta.parentValue.RECORDNO) {
            fieldMeta.parentValue.RECORDNO = getPayableGrid().value[currentRow].RECORDNO;
        }
        populateLineData(fieldMeta.parentValue, isItemPayment, isJointPayment, fieldMeta.getLineNo(), false, false);
        var rowData = fieldMeta.parentValue;
        // Store the credit amount in another variable to track the changes
        rowData['PREVCREDITSAPPLIED'] = fieldMeta.getValue();
        // For item payment, system should allocate the credit for the entered amount
        // so call the AJAX method to update credit allocation
        if(isItemPayment || isJointPayment) {
            var bill = window.editor.view.value['PAYABLES'][currentRow];
            var billItem = rowData;
            if (isItemPayment && isJointPayment) {
                billItem = bill.BILLITEMS[currentItemRow]; 
                billItem.CREDITSAPPLIED = billItem.JOINTLINEITEMS[0].CREDITSAPPLIED;
            } else if (!isItemPayment && isJointPayment) {
                billItem = bill;
                billItem.CREDITSAPPLIED = fieldMeta.getValue();
            }
            
            allocateCredit(bill, billItem, fieldMeta.getValue(), fieldMeta.getLineNo(), isItemPayment, isJointPayment);
        }
    }
}

/**
 * Method to clear the existing credits for the bill/line if there are any.
 *
 * @param vendorKey
 * @param billKey
 * @param billLineKey
 * @param creditKey
 */
function clearExistingCredits(vendorKey, billKey, billLineKey, creditKey)
{
    // validate the bill and vendor keys
    if(!billKey || !vendorKey) {
        return;
    }
    // if there is any existing applied credits for the bill then check that first
    var billCredit = appliedCredits[billKey];
    var existingCredit = 0;
    if(billCredit) {
        // Clear credits for given bill line key
        if(billLineKey) {
            billLineCredit = billCredit[billLineKey];
            // check if there is billLine level credits applied
            if (billLineCredit) {
                // check if its for the same credit key
                existingCredit = parseFloat(billLineCredit[creditKey] ? billLineCredit[creditKey] : 0);
                // if its already exist, reset the value
                if (existingCredit) {
                    billLineCredit[creditKey] = null;
                    billCredit[billLineKey] = billLineCredit;
                    // clear the allocated amount in credit object as well
                    clearCreditAllocation(vendorKey, creditKey, billKey, billLineKey, existingCredit);
                } else {
                    // If there is no credit key passed, clear all the bill line credits
                    if (!creditKey) {
                        clearAllBillLineCredits(vendorKey, billKey, billLineKey, billLineCredit);
                        billCredit[billLineKey] = null;
                    }
                }
                // set the bill credit back in the array
                if (existingCredit) {
                    appliedCredits[billKey] = billCredit;
                }
            }
        } else {
            // This means there is no billline key, so lets clear all the credits for the bill
            clearAllBillCredits(vendorKey, billKey, billCredit);
            appliedCredits[billKey] = null;
            delete  appliedCredits[billKey];
        }
    }
}

/**
 * Method to clear all the bill credits for the given bill key.
 *
 * @param vendorKey
 * @param billKey
 * @param billCredits
 */
function clearAllBillCredits(vendorKey, billKey, billCredits)
{
    if(billCredits) {
        for (var lineKey in billCredits) {
            if (billCredits.hasOwnProperty(lineKey)) {
                var billLineCredits = billCredits[lineKey];
                clearAllBillLineCredits(vendorKey, billKey, lineKey, billLineCredits);
                // After clearing set the bill line value as null
                billCredits[lineKey] = null;
            }
        }
    }
}

/**
 * Method to clear all the bill line credits for the given bill line key.
 *
 * @param vendorKey
 * @param billKey
 * @param billLineKey
 * @param billLineCredits
 */
function clearAllBillLineCredits(vendorKey, billKey, billLineKey, billLineCredits)
{
    if(billLineCredits) {
        for (var creditKey in billLineCredits) {
            if (billLineCredits.hasOwnProperty(creditKey)) {
                var amount = parseFloat(billLineCredits[creditKey]);
                clearCreditAllocation(vendorKey, creditKey, billKey, billLineKey, amount);
                billLineCredits[creditKey] = null;
            }
        }
    }
}

/**
 * Method to apply credit payments.
 *
 * @param fieldMeta
 * @param isItemPayment
 * @param isJointPayment
 */
function applyCreditPayments(fieldMeta, isItemPayment, isJointPayment)
{
    // if there is no field return
    if ( !fieldMeta ) {
        return;
    }
    var creditApplied = parseFloat(fieldMeta.getValue() ? fieldMeta.getValue() : 0);
    var rowData = fieldMeta.parentValue;
    var prevAmount = parseFloat(rowData['PREVCREDITSAPPLIED'] ? rowData['PREVCREDITSAPPLIED'] : 0);
    // if there is an previous amount check with currently entered amount
    // Continue only if the amount varies
    if(prevAmount) {
        // if current value is empty then reset with previous amount
        if(!fieldMeta.getValue()) {
            fieldMeta.setValue(prevAmount);
            return;
        }
        else {
            if (prevAmount != fieldMeta.getValue()) {
                // set the payment distribution flag for new distribution
                rowData['SPLITCREDIT'] = 'Y';
            } else {
                return;
            }
        }
    } else {
        // set the payment distribution flag for new distribution as the amount is new
        rowData['SPLITCREDIT'] = 'Y';
    }

    var grid = getPayableGrid();

    var lineNo = fieldMeta.getLineNo();
    //var entity = grid.findLineComponent('ENTITY', lineNo, 'Field');
    var recordFieldName = 'RECORDNO';
    var recordItemNo = null;
    var vendor = null;
    var isLineSave = false;
    var totalCreditsPresent = 0;
    // get item grid if its from line items
    if(isItemPayment) {
        recordFieldName = 'RECORDKEY';
        recordItemNo = rowData['RECORDNO'];
        grid = getPayableItemGrid();
        if (isJointPayment) {
            grid = getGrid('JOINTLINEITEMS');
        }
        /*var entityObj = grid.findLineComponent('ENTITY', currentRow, 'Field');
        if(entityObj) {
            vendor = entityObj.getValue();
        }*/
        vendor = window.editor.view.value['BILLVENDORID'];
        // clear the negative bill map array
        negativeBillUsageMap = {};
        var creditObj = credits[vendor];
        // get the total credits applied
        var creditsApplied = parseFloat(grid.getColumnTotal('CREDITSAPPLIED'));
        if(creditObj) {
            // get the total credits available for the vendor
            for (var i = 0 ; i < creditObj.length ; i++) {
                totalCreditsPresent += Math.abs(parseFloat(creditObj[i]['TRX_TOTALDUE']));
            }
            // update the vendor credit balance to the right value on change of the credits applied in bill details
            vendorCreditBalance[vendor] = totalCreditsPresent - creditsApplied;
            isLineSave = true;
        }

    } else {
        if (isJointPayment) {
            var grid = getPayableGrid();
            if(grid.value && grid.value[currentRow]) {
                vendor = grid.value[currentRow].ENTITY;
            }

            grid = getGrid('JOINTITEMS');
            rowData['ENTITY'] = vendor;
        } else {
            vendor = rowData['ENTITY'];
        }
    }
    var recordNo = rowData[recordFieldName];
    // validate the applied credit
    var isValid = validateBillPayment(rowData, true, isItemPayment, isJointPayment);
    // if the validation fails then reset teh value
    if(!isValid) {
        // update the previous credit amount
        fieldMeta.setValue(prevAmount);
        // Recalculate the total
        grid.computeTotals();
        grid.refreshTotals();
        if (isJointPayment) {
            var jointGrid = getGrid('JOINTITEMS');
            if (isItemPayment) {
                jointGrid = getGrid('JOINTLINEITEMS');
            } 
            if (jointGrid) {
                jointGrid.computeTotals();
                jointGrid.refreshTotals();
            }
        }

        if (isItemPayment) {
            grid = getPayableItemGrid();
            creditsApplied = parseFloat(grid.getColumnTotal('CREDITSAPPLIED'));
            // reset the vendor credit balance to the original state if any error is there
            if(typeof vendorCreditBalance !== 'undefined') {
                vendorCreditBalance[vendor] = totalCreditsPresent - creditsApplied;
            }
        }
    } else {
        // clear the negative bill map array
        negativeBillUsageMap = {};
        var creditObj = credits[vendor];
        var totalCreditsPresent = 0;
        // get the total credits applied
        var creditsApplied = parseFloat(grid.getColumnTotal('CREDITSAPPLIED'));
        // get the total credits available for the vendor
        if(creditObj) {
            for (var i = 0 ; i < creditObj.length ; i++) {
                totalCreditsPresent += Math.abs(parseFloat(creditObj[i]['TRX_TOTALDUE']));
            }
        }
        if(typeof vendorCreditBalance !== 'undefined') {
            // update the vendor credit balance to the right value on change of the credits applied in bill details
            vendorCreditBalance[vendor] = totalCreditsPresent - creditsApplied;
        }
        rowData['SPLITCREDIT'] == 'Y'

        // update the available credits on this line as well as on for all the lines with same vendor
        clearExistingCredits(vendor, recordNo, recordItemNo, null);
        // Refresh the available credits
        refreshAvailableCreditsNew(vendor, isItemPayment, isJointPayment);

    }
    return isValid;
}

/**
 * Method to clear the credit allocation by the credit key with the amount specified.
 *
 * @param vendorKey
 * @param creditKey
 * @param billKey
 * @param billLineKey
 * @param clearAmount
 */
function clearCreditAllocation(vendorKey, creditKey, billKey, billLineKey, clearAmount)
{
    if(!vendorKey || !creditKey) {
        return;
    }
    var allVendorCredits = credits[vendorKey];
    if(allVendorCredits) {
        for (var i = 0; i < allVendorCredits.length; i++) {
            var creditObj = allVendorCredits[i];
            if (creditKey == creditObj['RECORD#']) {
                // set back the previously allocated amount
                creditObj['TRX_TOTALSELECTED'] = roundAmt(Math.abs(creditObj['TRX_TOTALSELECTED']) - clearAmount);
                // set the credit object back to the list
                allVendorCredits[i] = creditObj;
                break;
            }
        }
        // set the credit list back to the map
        credits[vendorKey] = allVendorCredits;
    }
    if(billKey) {
        // Reset the credit to bill payment map
        var paidBills = creditBillMap[creditKey];
        if (paidBills) {
            var paidBill = paidBills[billKey];
            if (paidBill) {
                // reset the amount to 0
                paidBill[billLineKey] = 0;
                paidBills[billKey] = paidBill;
                creditBillMap[creditKey] = paidBills;
            }
        }
    }
}

/**
 * Refresh the available credits in the payable and item grid.
 *
 * @param vendorKey
 * @param releasedCredit
 * @param appliedCredit
 * @param isItemPayment
 */
function refreshAvailableCredits(vendorKey, releasedCredit, appliedCredit, isItemPayment)
{
    // If there is no vendor balance variable initialized no need to process further
    if(!vendorCreditBalance || !vendorKey) {
        return;
    }
    // get the existing credits available
    var vendorBalance = vendorCreditBalance[vendorKey];
    var existingCreditsAvailable = parseFloat(vendorBalance ? vendorBalance : 0);
    // calculate the new credits available
    var newCreditAvailable = roundAmt(existingCreditsAvailable + roundAmt(releasedCredit - appliedCredit));
    // set the new balance
    vendorCreditBalance[vendorKey] = newCreditAvailable;
    applyAvailableCredits(vendorKey, isItemPayment);
}

/**
 * Method to apply the available credit in the grid.
 * 
 * @param vendor
 * @param isItemPayment
 */
function applyAvailableCredits(vendorKey, isItemPayment) 
{
    // If there is no vendor balance variable initialized no need to process further
    if(!vendorCreditBalance || !vendorKey) {
        return;
    }
    var grid = null;
    var availableCredit = vendorCreditBalance[vendorKey];
    if(isItemPayment) {
        grid = getPayableItemGrid();
        // Update the bill credit available field
        var creditAvailObj = window.editor.view.getField('BILLCREDITSAVAILABLE');
        if (creditAvailObj) {
            // update the new credit available in the bill credit field
            creditAvailObj.setValue(availableCredit);
        }
    } else {
        grid = getPayableGrid();
    }
    // iterate each one line items and
    var rowCount = grid.getRowCount();
    for ( var i = 0; i < rowCount; i++ ) {
        // first check the row entity for bill level
        if(!isItemPayment) {
            var rowEntity = grid.findLineComponent('ENTITY', i, 'Field');
            if (!rowEntity || rowEntity.getValue() != vendorKey) {
                continue;
            }
        }
        var rowCreditAvailableObj = grid.findLineComponent('CREDITSAVAILABLE', i, 'Field');
        if ( !rowCreditAvailableObj ) {
            continue;
        }
        // set the new credit available value
        rowCreditAvailableObj.setValue(availableCredit);
    }
}

/**
 * Method to get the available credits
 *
 * @param vendorKey
 * @param isItemPayment
 * @returns {number}
 */
function getCreditsAvailable(vendorKey, isItemPayment) {
    // calculate the new credits available
    var newCreditAvailable = 0;
    // Get all the credits and calculate the amount
    var vendorCredits = credits[vendorKey];
    // Map to store the running total due for the -ve bill credits
    var docTotalDueMap = {};
    if(vendorCredits) {
        // Location level credit for the vendor
        var vendorLocCredit = {};
        for (var i = 0; i < vendorCredits.length; i++) {
            var creditObj = vendorCredits[i];
            // Get the credit available
            var creditAvailable = Math.abs(creditObj['TRX_TOTALDUE']);
            // Get the applied credits
            var appliedCredit = getAppliedCredit(creditObj['RECORD#']);
            // For -ve bill credits, we need to check the credit available with total doc total due
            // If the line credit more than the total due, then use only available due
            if(creditObj['CREDITTYPE'] == 'Bill') {
                var docTotalDue = creditObj['DOCTOTALDUE'];
                if(docTotalDueMap[creditObj['CRRECKEY']] || docTotalDueMap[creditObj['CRRECKEY']] == 0) {
                    // Since first line already added the doc total due as available credits, next time onwards add
                    // no credit available
                    creditAvailable = 0;
                } else {
                    docTotalDueMap[creditObj['CRRECKEY']] = parseFloat(isNaN(docTotalDue) ? 0 : docTotalDue);
                    // For the first credit line, add all the doc total due from the -ve bill for the available credit
                    creditAvailable = Math.abs(docTotalDue);
                }
                // Update the doc total due for the -ve bill with applied credits
                // Total due amount will be in -ve, so add the applied amount to reduce the doc total due for the bill
                docTotalDueMap[creditObj['CRRECKEY']] = roundAmt(docTotalDueMap[creditObj['CRRECKEY']] + appliedCredit);
            }
            // Check for inline credits to find the actual credits available
            // If the flow is from line level screen we need to calculate the negative bill usage map
            if (creditObj['CREDITTYPE'] == 'Inline') {
                // This means the bill has a -ve line but the total due is in +ve, so in this case
                // this credit is not available for others
                // Update the negative bill usage map for line item level payments
                if(isItemPayment) {
                    if (typeof negativeBillUsageMap[creditObj['RECORDKEY']] == "undefined") {
                        negativeBillUsageMap[creditObj['RECORDKEY']] = 0;
                    }
                    negativeBillUsageMap[creditObj['RECORDKEY']] = roundAmt(negativeBillUsageMap[creditObj['RECORDKEY']]
                        + appliedCredit);
                }
            } else {
                // Get the new available amount
                newCreditAvailable = roundAmt(newCreditAvailable + roundAmt(creditAvailable - appliedCredit));
                // Update the vendor location available credit
                if (parentLocationMap && creditLineLocationAmountMap) {
                    var parentLoc = parentLocationMap[creditObj['LOCATION#']];
                    var existingCredit = vendorLocCredit[parentLoc] ? vendorLocCredit[parentLoc] : 0;
                    var newLocCredit = roundAmt(existingCredit + roundAmt(creditAvailable - appliedCredit));
                    newLocCredit = (newLocCredit > 0) ? newLocCredit : 0;
                    vendorLocCredit[parentLoc] = newLocCredit;
                }
            }
        }
        // Update the vendor's location credit map in the root level map
        if(creditLineLocationAmountMap) {
            // Update the location credits for the vendor
            for(var locKey in vendorLocCredit) {
                var locCredit = vendorLocCredit[locKey];
                // location credit can not be greater than the total available credit, if so then the max credit
                // available is whichever is lesser, this is possible in case of -ve bill credit with +ve lines
                locCredit = (locCredit <= newCreditAvailable) ? locCredit : newCreditAvailable;
                // Set the updated credit available
                vendorLocCredit[locKey] = locCredit;
            }
            creditLineLocationAmountMap[vendorKey] = vendorLocCredit;
        }
    }

    return newCreditAvailable;
}

/**
 * Refresh the available credits in the payable and item grid.
 *
 * @param vendorKey
 * @param isItemPayment
 * @param isJointPayment
 */
function refreshAvailableCreditsNew(vendorKey, isItemPayment, isJointPayment)
{
    // If there is no vendor balance variable initialized no need to process further
    if(!vendorCreditBalance || !vendorKey) {
        return;
    }
    // Get the credit available for the vendor
    var availCredits = getCreditsAvailable(vendorKey, isItemPayment);
    vendorCreditBalance[vendorKey] = Math.abs(availCredits);
    applyAvailableCreditsNew(vendorKey, isItemPayment, isJointPayment);
}

/**
 * Method returns the amount applied using the input credit key.
 *
 * @param creditKey
 * @returns {number}
 */
function getAppliedCredit(creditKey)
{
    var appliedCreditAmount = 0;
    if(creditKey) {
        var bills = creditBillMap[creditKey];
        if(bills) {
            for(var billKey in bills) {
                var billLines = bills[billKey];
                if(billLines) {
                    for(var billLineKey in billLines) {
                        var amount = parseFloat(isNaN(billLines[billLineKey]) ? 0 : billLines[billLineKey]);
                        appliedCreditAmount = roundAmt(appliedCreditAmount + amount);
                    }
                }
            }
        }
    }
    return appliedCreditAmount;
}

/**
 * Method to apply the available credit in the grid.
 *
 * @param vendorKey
 * @param isItemPayment
 * @param isJointPayment
 */
function applyAvailableCreditsNew(vendorKey, isItemPayment, isJointPayment)
{
    // If there is no vendor balance variable initialized no need to process further
    if(!vendorCreditBalance || !vendorKey) {
        return;
    }
    var grid = null;
    var vendorId = null;
    var availableCredit = vendorCreditBalance[vendorKey];
    if(isItemPayment) {
        grid = getPayableItemGrid();
        if (isJointPayment) {
            grid = getGrid('JOINTLINEITEMS');
        }
        // Update the bill credit available field
        var creditAvailObj = window.editor.view.getField('BILLCREDITSAVAILABLE');
        if (creditAvailObj) {
            // update the new credit available in the bill credit field
            creditAvailObj.setValue(availableCredit + getAvailableInlineCredit(currentRow));
        }
        var currentRowData = window.editor.view.value['PAYABLES'][currentRow];
        if(currentRowData) {
            // Get the vendor id
            vendorId = currentRowData['ENTITY'];
        }
    } else {
        if (isJointPayment) {
            grid = getGrid('JOINTITEMS');
        } else {
            grid = getPayableGrid();
        }
    }
    // iterate each one line items and
    var rowCount = grid.getRowCount();
    for ( var i = 0; i < rowCount; i++ ) {
        var inlineCreditAvailable = 0;
        if (isJointPayment && i > 0) {
            break;
        }
        // first check the row entity for bill level
        if (!isItemPayment && !isJointPayment) {
            inlineCreditAvailable = getAvailableInlineCredit(i);
            var vendorIdObj = grid.findLineComponent('ENTITY', i, 'Field');
            if(vendorIdObj) {
                vendorId = vendorIdObj.getValue();
            } else {
                vendorId = window.editor.view.value['PAYABLES'][i]['ENTITY'];
            }
        } else {
            inlineCreditAvailable = getAvailableInlineCredit(currentRow);
            if (isJointPayment) {
                vendorId = vendorKey;
            }
        }
        // Do not update anything if the vendor is not same as incoming vendor
        if(!vendorId || vendorId != vendorKey) {
            continue;
        }
        var rowCreditAvailableObj = grid.findLineComponent('CREDITSAVAILABLE', i, 'Field');
        var totalCreditAvailable = roundAmt(availableCredit + inlineCreditAvailable);
        vendorCreditBalance[vendorKey] = totalCreditAvailable;
        if(!isItemPayment) {
            // For multi page grids, the row is in different page for which the field is not painted yet, update the
            // data on the back, it will get painted when the page is viewed
            if (isJointPayment) {
                grid.value[i]['CREDITSAVAILABLE'] = totalCreditAvailable;
            } else {
                window.editor.view.value['PAYABLES'][i]['CREDITSAVAILABLE'] = totalCreditAvailable;
            }
        } else {
            if (rowCreditAvailableObj) {
                rowCreditAvailableObj.setValue(totalCreditAvailable);
            } else {
                // update other pages
                if(grid && grid.value && grid.value[i]) {
                    grid.value[i]['CREDITSAVAILABLE'] = totalCreditAvailable;
                }
            }
        }
        if (rowCreditAvailableObj) {
            // set the new credit available value
            if(!isItemPayment || meDistributeFlag) {
                rowCreditAvailableObj.setValue(totalCreditAvailable);
            } else if(parentLocationMap && creditLineLocationAmountMap && creditLineLocationAmountMap[vendorKey]) {
                var billItems = window.editor.view.value['BILLITEMS'];
                if(billItems && billItems[i]) {
                    var parentLoc = parentLocationMap[billItems[i]['LOCATION#']]
                    var locCreditAvailable = creditLineLocationAmountMap[vendorKey][parentLoc];
                    rowCreditAvailableObj.setValue(locCreditAvailable);
                }
            }
        }
    }
}

/**
 * Method to get the available inline amount for the bill.
 *
 * @param payableRow
 * @returns {number}
 */
function getAvailableInlineCredit(payableRow)
{
    var inlineCreditAvailable = 0;
    if(payableRow != -1 && window.editor.view.value['PAYABLES']) {
        var rowData = window.editor.view.value['PAYABLES'][payableRow];
        if (rowData && rowData['TRX_NEGTOTALDUE']) {
            inlineCreditAvailable = Math.abs(rowData['TRX_NEGTOTALDUE']);
            if (inlineCreditAvailable > 0 && credits) {
                // Lets get the consumed amount
                // Get all the credits and calculate the amount
                var vendorCredits = credits[rowData['ENTITY']];
                if (vendorCredits) {
                    for (var i = 0; i < vendorCredits.length; i++) {
                        var creditObj = vendorCredits[i];
                        // Check if current bill's -ve line or not
                        if (rowData['RECORDKEY'] == creditObj['CRRECKEY']) {
                            // Get the applied credit amount
                            var appliedCredit = getAppliedCredit(creditObj['RECORD#']);
                            inlineCreditAvailable = roundAmt(inlineCreditAvailable - appliedCredit);
                        }
                    }
                }
            }
        }
    }
    return inlineCreditAvailable;
}

/**
 * Method to allocate given specific credit for the bill.
 *
 * @param vendorKey
 * @param billKey
 * @param billLineKey
 * @param inCreditKey
 * @param applyAmount
 */
function allocateSpecificCredit(vendorKey, billKey, billLineKey, inCreditKey, applyAmount)
{
    // validate the input
    if(!vendorKey || !billKey || !inCreditKey) {
        return;
    }
    if(credits) {
        var allVendorCredits = credits[vendorKey];
        if (allVendorCredits) {
            for (var i = 0; i < allVendorCredits.length; i++) {
                var creditObj = allVendorCredits[i];
                // Incoming credit key should match the credit from the list
                if (inCreditKey == creditObj['RECORD#']) {
                    var availableAmount = roundAmt(Math.abs(creditObj['TRX_TOTALDUE']) - Math.abs(creditObj['TRX_TOTALSELECTED']));
                    //var availableAmount = creditObj['TRX_TOTALDUE'];
                    // process further only if there is any available amount
                    if (availableAmount > 0) {
                        var currentCredit = Math.min(applyAmount, availableAmount);
                        // apply the amount
                        creditObj['TRX_TOTALSELECTED'] = roundAmt(Math.abs(creditObj['TRX_TOTALSELECTED']) + currentCredit);
                        //creditObj['TRX_TOTALDUE'] = creditObj['TRX_TOTALDUE'] - currentCredit;
                        var creditKey = creditObj['RECORD#'];
                        appliedCredits = appliedCredits ? appliedCredits : {};
                        var appliedBillCredits = appliedCredits[billKey] ? appliedCredits[billKey] : {};
                        var appliedLineCredits = appliedBillCredits[billLineKey] ? appliedBillCredits[billLineKey] : {};
                        appliedLineCredits[creditKey] = currentCredit;
                        appliedBillCredits[billLineKey] = appliedLineCredits;
                        // update the credit map
                        appliedCredits[billKey] = appliedBillCredits;

                        creditBillMap = creditBillMap ? creditBillMap : {};
                        var creditDistro = creditBillMap[creditKey] ? creditBillMap[creditKey] : {};
                        var creditBillDistro = creditDistro[billKey] ? creditDistro[billKey] : {};
                        creditBillDistro[billLineKey] = currentCredit;
                        // update the credit bill map
                        creditDistro[billKey] = creditBillDistro;
                        creditBillMap[creditKey] = creditDistro;
                        // set the credit object back to the list
                        allVendorCredits[i] = creditObj;
                    }
                    // Once applied break and return
                    break;
                }
            }
            // set the credit list back to the map
            credits[vendorKey] = allVendorCredits;
        }
    }
}


/**
 * Method to allocate credits for the bill.
 *
 *  *************************************************************************
 *  NOTE: SummaryByEntityManager::allocateCredits() IS BASED ON THIS LOGIC.
 *      If you change that logic, maybe change this logic, and vice versa!
 *  *************************************************************************
 *
 *
 * @param vendorKey
 * @param billKey
 * @param billLineKey
 * @param inCreditKey
 * @param applyAmount
 */
/*
**** THIS METHOD IS DEPRECATED, USE CreditPaymentDistributionHelper->allocateCredit() instead ***
function allocateCredits(vendorKey, billKey, billLineKey, inCreditKey, applyAmount)
{
    if(credits) {
        var allVendorCredits = credits[vendorKey];
        if (allVendorCredits) {
            var appliedAmount = 0;
            for (var i = 0; i < allVendorCredits.length; i++) {
                var creditObj = allVendorCredits[i];
                // #1 There is no incoming credit key, go ahead and process or
                // #2 Incoming credit key should match the credit from the list
                if (!inCreditKey || (inCreditKey && inCreditKey == creditObj['RECNUM'])) {
                    var availableAmount = roundAmt(Math.abs(creditObj['TRX_TOTALDUE']) - Math.abs(creditObj['TRX_TOTALSELECTED']));
                    //var availableAmount = creditObj['TRX_TOTALDUE'];
                    // process further only if there is any available amount
                    if (availableAmount > 0) {
                        // credit applied now
                        var currentCredit = roundAmt(applyAmount - appliedAmount);
                        currentCredit = (currentCredit > availableAmount) ? availableAmount : currentCredit;
                        // apply the amount
                        creditObj['TRX_TOTALSELECTED'] = roundAmt(Math.abs(creditObj['TRX_TOTALSELECTED']) + currentCredit);
                        //creditObj['TRX_TOTALDUE'] = creditObj['TRX_TOTALDUE'] - currentCredit;
                        var creditKey = creditObj['RECNUM'];
                        var appliedBillCredits = appliedCredits[billKey] ? appliedCredits[billKey] : {};
                        var appliedLineCredits = appliedBillCredits[billLineKey] ? appliedBillCredits[billLineKey] : {};
                        appliedLineCredits[creditKey] = currentCredit;
                        appliedBillCredits[billLineKey] = appliedLineCredits;
                        // update the credit map
                        appliedCredits[billKey] = appliedBillCredits;

                        var creditDistro = creditBillMap[creditKey] ? creditBillMap[creditKey] : {};
                        var creditBillDistro = creditDistro[billKey] ? creditDistro[billKey] : {};
                        creditBillDistro[billLineKey] = currentCredit;
                        // update the credit bill map
                        creditDistro[billKey] = creditBillDistro;
                        creditBillMap[creditKey] = creditDistro;
                        // update the applied amount
                        appliedAmount = roundAmt(appliedAmount + currentCredit);
                        // set the credit object back to the list
                        allVendorCredits[i] = creditObj;
                    }
                    // break if
                    //      there is no more amount to apply or
                    //      there is input credit key (since its applied, no need to iterate further)
                    if (appliedAmount == applyAmount || inCreditKey) {
                        break;
                    }
                }
            }
            // set the credit list back to the map
            credits[vendorKey] = allVendorCredits;
        }
    }
}
*/

/**
 * Returns the current date in mm/dd/yyyy format.
 *
 * @returns {Date}
 */
function getCurrentDate()
{
    var currentDate = new Date();
    var month = currentDate.getMonth() + 1;
    month = (month < 10) ? '0' + month : month;
    var date = currentDate.getDate();
    date = (date < 10) ? '0' + date : date;
    currentDate = month + '/' + date + '/' +  currentDate.getFullYear();

    return currentDate;
}

/**
 * Method to populate the credit line details on the bill line details page.
 *
 * @param obj
 * @param gridPath
 */
function populateCreditLineDetails(obj, gridPath)
{
    if ( !gridPath ) {
        gridPath = 'BILLITEMS';
    }

    // If this is from the payment request, no need to show the save button
    // Before showing the bill line disable the save button as its not needed for payment requests
    // we can't change the property of the floating page once it is visible. Hence we are changing the button property before.
    if(window.editor.view.id == 'paymentrequest') {
        var creditsPage = window.editor.getView().getFloatingPage('CREDITSPAGE');
        if (creditsPage && creditsPage.footer) {
            var buttons = creditsPage.footer.children;
            if (buttons) {
                showHideButtons(buttons, 'creditSaveButton', false);
            }
        }
    }

    window.editor.showPage('CREDITSPAGE', obj);
    var floatingSplit = window.editor.view.findComponents('CREDITITEMS', 'Grid');
    var creditGrid =  floatingSplit ? floatingSplit[0] : null;

    if(creditGrid) {
        var lineNo = obj.meta.parentComponent.getLineNo();
        var rowData = obj.meta.parentValue;
        if(parentLocationMap) {
            var billParentLocation = parentLocationMap[rowData['LOCATION#']];
        }
        if (window.editor.view.id != 'paymentrequest') {
            var entries = window.editor.view.findComponents(gridPath, 'Grid');
            if (entries && entries[0] && credits) {
                // Get the credit value
                var vendorObj = window.editor.view.getField('BILLVENDORID');
                if (!vendorObj) {
                    return;
                }

                if (credits[vendorObj.getValue()]) {
                    //var rowData = obj.parentElement.meta.parentValue;

                    currentBillLineRow = lineNo;
                    currentBillKey = rowData['RECORDKEY'];
                    currentBillLineKey = rowData['RECORDNO'];
                    var blCreditAvailable = rowData['CREDITSAVAILABLE'];
                    if (rowData) {
                        var tempVendorCredits = credits[vendorObj.getValue()];
                        var vendorCreditsLen = tempVendorCredits.length;
                        var vendorCredits = new Array();
                        for (var i = 0; i < vendorCreditsLen; i++) {
                            match = false;
                            if(parentLocationMap) {
                                var creditParentLocation = parentLocationMap[tempVendorCredits[i]['LOCATION#']];
                                if(billParentLocation == creditParentLocation || meDistributeFlag) {
                                    var creditObj = tempVendorCredits[i];
                                    vendorCredits[vendorCredits.length] = tempVendorCredits[i];
                                    match = true;
                                }
                            } else {
                                var creditObj = tempVendorCredits[i];
                                match = true;
                            }
                            if(match) {
                                var appliedCreditAmount = 0;
                                if (creditBillMap) {
                                    var billCredits = creditBillMap[creditObj['RECORD#']];
                                    if (billCredits) {
                                        var billLineCredits = billCredits[currentBillKey];
                                        if (billLineCredits) {
                                            appliedCreditAmount = billLineCredits[currentBillLineKey] ? billLineCredits[currentBillLineKey] : 0;
                                        }
                                    }
                                }
                                // update the credits applied with respect to the bill line to show in the UI
                                creditObj['BLCREDITAPPLIED'] = appliedCreditAmount;
                                // update the prev credit applied with applied amount to track the changes
                                creditObj['PREVBLCREDITAPPLIED'] = appliedCreditAmount;
                                // Get all the credits applied for this credit object
                                var appliedCredits = getAppliedCredit(creditObj['RECORD#']);
                                // update the credit available
                                var creditAvailable = roundAmt(Math.abs(creditObj['TRX_TOTALDUE']) - appliedCredits);
                                // Check for -ve bill credits to find the actual credits available
                                if (creditObj['RECORDTYPE'] == 'pi') {
                                    if (creditObj['DOCTOTALDUE'] >= 0) {
                                        // For +ve bill with negative items, that -ve item only available to that bill
                                        if (creditObj['RECORDKEY'] != currentBillKey) {
                                            creditAvailable = 0;
                                            var index = vendorCredits.indexOf(vendorCredits[i]);
                                            if (index > -1) {
                                                vendorCredits.splice(index, 1);
                                            }
                                        }
                                    } else {
                                        //creditAvailable = (creditAvailable < Math.abs(creditObj['DOCTOTALDUE']) ) ?
                                        // creditAvailable : Math.abs(creditObj['DOCTOTALDUE']);
                                        // If the line credit amount more than the total doc total due then
                                        // available credit is only doc total due
                                        var updatedDocTotalDue = roundAmt(Math.abs(creditObj['DOCTOTALDUE']) - appliedCredits);
                                        creditAvailable = (creditAvailable > updatedDocTotalDue) ? updatedDocTotalDue : creditAvailable;
                                        // Incase of bill line credit available is lesser than the credit
                                        // object's credit available, then the least will become available credit
                                        if(creditAvailable > blCreditAvailable) {
                                            creditAvailable = blCreditAvailable;
                                        }
                                    }
                                }
                                creditObj['BLCREDITAVAILABLE'] = creditAvailable;
                                // Set the credit selection
                                if (appliedCreditAmount > 0) {
                                    creditObj['SELECTCREDIT'] = true;
                                } else {
                                    creditObj['SELECTCREDIT'] = false;
                                }
                            }
                        }
                    }
                }
                creditGrid.value = vendorCredits;
                creditGrid.parentComponent.redraw();
            }
        } else {

            // Update the credits from the bill line row
            if(rowData && rowData['CREDITS']) {
                creditGrid.value = rowData['CREDITS'];
                creditGrid.parentComponent.redraw();
            }
        }
    }
}

/** Calllback method to close the Compliance popup window.
 * 
 * @param pageId    page id for the page to be closed
 * @param isSave    flag to indicate if the action is save or cancel
 * @param whenpaid  For Compliance popup, we need the payment date specified.
 */
function closeFloatingPage_Compliance(pageId, isSave, whenpaid) {
    if (isSave) {
        var grid = getPayableGrid();
        var vendors = [];
        for ( var i = 0; i < grid.getRowCount(); i++ ) {
            vendors.push({'VENDORID': grid.value[i]['VENDORID'], 'PRRECORDKEY': grid.value[i]['RECORDKEY']});
        }
        if(pageId === 'COMPLIANCEPAGE') {
            // This performs a single compliance check for the specified vendor, for the payment date specified at the header.
            ajaxArgs = { "vendorid" : vendors, "module" : "pay", "paymentdate" : whenpaid, "row":-1 };
            if (window.editor) {
                window.editor.ajax(false, 'calcComplianceForVendor', ajaxArgs, c_recalcComplianceForVendor);
            }
        }
    }
    if (window.editor) {
        hideFloatingPage(pageId);
    }
}

/**
 * Method to close the floating page
 *
 * @param pageId    page id for the page to be closed
 * @param isSave    flag to indicate if the action is save or cancel
 */
function closeFloatingPage(pageId, isSave)
{
    if(isSave) {
        // Save the changes for discount page
        if(pageId === 'DISCOUNTPAGE') {
            var paymentDate = window.editor.view.getField('WHENPAID_D');
            var discountDate = window.editor.view.getField('DISCOUNTDATE');
            var discountApplied = window.editor.view.getField('DISCOUNTAPPLIED_D');
            var lineNo = window.editor.view.getField('ROWNUM').getValue();
            // if the line number is undefined we can fairly assume its 0th element
            if(!lineNo)
                lineNo = 0;
            var payable = window.editor.view.value['PAYABLES'][lineNo];
            // Check if the amount is different from the existing discount
            var appliedDiscount = discountApplied.getValue() ? discountApplied.getValue() : 0;
            var prevDiscount = payable['DISCOUNTAPPLIED'];
           var procced = validateDiscountGettingApplied(payable, appliedDiscount);
           if(!procced) {
               return false;
           }
            payable['WHENPAID'] = paymentDate.getValue();
            payable['DISCOUNTDATE'] = discountDate.getValue();
            payable['DISCOUNTAPPLIED'] = discountApplied.getValue();
            // update the fields
            var grid = getPayableGrid();
            var paymentDate = grid.findLineComponent('WHENPAID', lineNo, 'Field');
            paymentDate.setValue(payable['WHENPAID']);
            var discountAppliedOnGrid = grid.findLineComponent('DISCOUNTAPPLIED', lineNo, 'Field');
            discountAppliedOnGrid.setValue(appliedDiscount);
            handleDiscountReset(payable, lineNo, true);
            if((payable['DISCOUNTAPPLIED'] || payable['DISCOUNTAPPLIED'] == 0 ) && (appliedDiscount !== prevDiscount)) {
                discountApplied.setValue(payable['DISCOUNTAPPLIED']);
                // used for not populating disc date after being removed
                if(payable['DISCOUNTAPPLIED'] == 0) {
                    payable['DISCOUNTREMOVED'] = true;
                } else {
                    payable['DISCOUNTREMOVED'] = false;
                }
                // Check if the discount applied is greater than the due amount
                // call to update the line payment details
               // updateLinePayments(payable, lineNo, false);
                // Check if there is an items exist already
                // Go for distribution only if any bill items exist for the bill
                // If the bill items present that means distribution happened
                // already, so need to redistribute
                // line will be selected if credits or pymt amt exists. distribute only if line selected
                if(payable['BILLITEMS'] && payable['SELECTED'] === 'true') {
                    payable['SPLITPAYMENT'] = 'Y';
                    // Since the amount is different call for the distribution
                    distributePayment(payable, lineNo);
                }
            }
        }
        // Save the changes for credit applied page
        else if(pageId === 'CREDITSPAGE') {

            // Get the credit value
            var vendorObj = window.editor.view.getField('BILLVENDORID');
            // Get the credit grid object
            var creditGridObj = getGrid('CREDITITEMS');
            if(!vendorObj || !creditGridObj) {
                return;
            }
            // Get only the credit items that are placed in the grid
            var creditItems = creditGridObj.getValue();
            if(creditItems) {
                var totalLineCreditApplied = getTotalLineCreditApplied(creditGridObj);
                var dataChanged = false;
                var currentBill = window.editor.view.value['BILLITEMS'] ? window.editor.view.value['BILLITEMS'][currentBillLineRow] : null;
                var currentBillDue = currentBill && !isNaN(parseFloat(currentBill['TRX_TOTALDUE'])) ? parseFloat(currentBill['TRX_TOTALDUE']) : null;
                var showCreditAvailableWarn = true;
                // Iterate each credit item and take the changes applied for the bill line
                for (var i = 0; i < creditItems.length; i++) {
                    var creditObj = creditItems[i];
                    if (
                        showCreditAvailableWarn === true &&
                        creditObj &&
                        creditObj.CREDITTYPE === 'Inline' &&
                        ((typeof creditObj.SELECTCREDIT == 'undefined') ||  (creditObj.SELECTCREDIT == 'false') ||
                            (creditObj.SELECTCREDIT == false)) &&
                        creditObj.CREDITRECORDKEY === currentBillKey && ((typeof creditObj.BLCREDITAVAILABLE != 'undefined')
                            && (creditObj.BLCREDITAVAILABLE > 0)) && (currentBillDue && (currentBillDue > totalLineCreditApplied))
                    ) {
                        if(!confirm(GT('IA.BE_SURE_TO_APPLY_THE_AVAILABLE_INLINE_CREDIT_BEFORE_APPLYING_ADDITIONAL'))) {
                            return false;
                        }
                        showCreditAvailableWarn = false; //Don't display warning message for each line, once it's already displayed
                    }
                    var currentCreditApplied = parseFloat(isNaN(creditObj['BLCREDITAPPLIED']) ? 0 : creditObj['BLCREDITAPPLIED']);
                    // Get the previously applied credit for this line
                    var prevCreditApplied = 0;
                    var paidBills = creditBillMap[creditObj['RECORD#']];
                    if (paidBills) {
                        var paidBill = paidBills[currentBillKey];
                        if (paidBill) {
                            prevCreditApplied = parseFloat(isNaN(paidBill[currentBillLineKey]) ? 0 : paidBill[currentBillLineKey]);
                        }
                    }
                    // If there is no change in the credit then continue to the next credit
                    if(currentCreditApplied == prevCreditApplied) {
                        continue;
                    }

                    if (currentBillKey && currentBillLineKey) {
                        // Clear any existing credits
                        clearExistingCredits(vendorObj.getValue(), currentBillKey, currentBillLineKey, creditObj['RECORD#']);
                        // allocate the credit
                        allocateSpecificCredit(vendorObj.getValue(), currentBillKey, currentBillLineKey, creditObj['RECORD#'], currentCreditApplied);
                        // reset the bill line credit selected
                        creditObj['BLCREDITAPPLIED'] = 0;

                    }
                    // Reset the select button
                    creditObj['SELECTCREDIT'] = false;
                    dataChanged = true;
                }
                // Update the credit applied in bill line item if there is any change
                if(dataChanged) {
                    var billItems = window.editor.view.value['BILLITEMS'];
                    if (billItems) {
                        var itemGrid = getPayableItemGrid();
                        var totalPrevLineCreditApplied = billItems[currentBillLineRow]['PREVCREDITSAPPLIED'];
                        totalPrevLineCreditApplied = isNaN(totalPrevLineCreditApplied) ? 0 : totalPrevLineCreditApplied;
                        // after allocating the credits, lets refresh the available credits
                        // TODO remove the below later and use the new API
                        // refreshAvailableCredits(vendorObj.getValue(), totalPrevLineCreditApplied,
                        // totalLineCreditApplied, true);

                        // clean the negative bill usage map before refresh
                        negativeBillUsageMap = {};
                        refreshAvailableCreditsNew(vendorObj.getValue(), true, false);
                        // update the credit applied
                        billItems[currentBillLineRow]['CREDITSAPPLIED'] = totalLineCreditApplied;
                        billItems[currentBillLineRow]['PREVCREDITSAPPLIED'] = totalLineCreditApplied;
                        var creditsAppliedObj = itemGrid.findLineComponent('CREDITSAPPLIED', currentBillLineRow, 'Field');
                        if(creditsAppliedObj) {
                            creditsAppliedObj.setValue(totalLineCreditApplied);
                        }
                        // Adjust the payment amount if the credit is applied over the payment amount
                        var lineDueAmount = getBillLineDueAmount(currentBillLineRow);
                        if (lineDueAmount < 0) {
                            // This mean, the credit is over applied, we need to adjust on the payment amount
                            var paymentAmount = billItems[currentBillLineRow]['PAYMENTAMOUNT'];
                            paymentAmount = parseFloat(paymentAmount ? paymentAmount : 0);
                            if (paymentAmount >= Math.abs(lineDueAmount)) {
                                // update the new payment amount
                                paymentAmount = roundAmt(paymentAmount + lineDueAmount);
                                billItems[currentBillLineRow]['PAYMENTAMOUNT'] = paymentAmount;
                                var paymentObj = itemGrid.findLineComponent('PAYMENTAMOUNT', currentBillLineRow, 'Field');
                                if(paymentObj) {
                                    paymentObj.setValue(paymentAmount);
                                }
                            } else {
                                // THIS should never happen
                                alert(GT('IA.SOMETHING_WENT_WRONG_CREDIT_APPLIED_WRONGLY_OVER_THE_PAYMENT_AMOUNT'));
                            }
                        }
                        var billLineData = window.editor.view.value['PAYABLES'][currentRow];
                        if(billLineData) {
                            calculateAmountToPay(billLineData, currentBillLineRow, false, true);
                        }
                        // refresh the bill item data in UI
                        window.editor.updateData(['view', 'BILLITEMS'], billItems);
                        window.editor.refreshData();
                        // Compute and refresh the total on the item grid
                        itemGrid.computeTotals();
                        itemGrid.refreshTotals();
                    }
                }
            }
        }
    }
    hideFloatingPage(pageId);
}

/**
 * Method to update the discount details when discount is clicked.
 *
 * @param fieldMeta
 */
function openDiscountDetailsPage(fieldMeta)
{
    if(!fieldMeta) {
        return;
    }
    window.editor.showPage('DISCOUNTPAGE', this);
    // Update the page with details
    var lineNo = fieldMeta.getLineNo();
    var payable = window.editor.view.value['PAYABLES'][lineNo];
    var paymentDate = window.editor.view.getField('WHENPAID_D');
    var cutoffDate = window.editor.view.getField('WHENDISCOUNT_D');
    var discountDate = window.editor.view.getField('DISCOUNTDATE');
    var discountApplied = window.editor.view.getField('DISCOUNTAPPLIED_D');
    var lineNoObj = window.editor.view.getField('ROWNUM');
    var recordIDObj = window.editor.view.getField('RECORDID_D');
    // Set the values
    recordIDObj.setValue(payable['RECORDID']);
    paymentDate.setValue((payable['RECPAYMENTDATE'] ? payable['RECPAYMENTDATE'] :payable['WHENPAID']));
    cutoffDate.setValue(payable['WHENDISCOUNT'] ? payable['WHENDISCOUNT'] : payable['WHENCREATED']);
    // discount removed will be false once user removes discount manually from disc details page and saves
    // populate discount date only if user applies discount or on initial load
    if(!payable['DISCOUNTREMOVED']) {
        var discDate = payable['DISCOUNTDATE'] ? payable['DISCOUNTDATE'] : (payable['RECPAYMENTDATE'] ? payable['RECPAYMENTDATE'] :payable['WHENPAID']);
        discountDate.setValue(discDate);
        discountApplied.setValue(payable['DISCOUNTAPPLIED']);
    }
    lineNoObj.setValue(lineNo);
    //discountApplied.parentComponent.redraw();
}

/**
 * Method to open the Credits to Apply inline popup
 */
function openCreditsToApply(line)
{
    var creditsPanel = jq('#creditsApplyPopup');
    if (creditsPanel.length) { // if there is already a popup we close it and clear its parent
        var creditsPanelParent = creditsPanel.parent();
        if (creditsPanelParent) {
            var creditsPanelParentId = creditsPanelParent[0];
            jq(creditsPanelParentId).css({'position': ''});
            creditsPanel.remove();
        }
    }

    var lineId = jq(line).attr('id');
    var parentBlock = jq('#' + lineId).parent();
    if (!parentBlock) {
        return;
    }
    var parentId = parentBlock[0];
    if (!parentId) {
        return;
    }

    jq(parentId).css({'position': 'relative'});
    jq(parentId).append('<div id="creditsApplyPopup">Credits Available:<br /> ' + jq(line).text() + '</div>');
    jq('#creditsApplyPopup').show();
}

/**
 * Method to update the discounts for the given payment date.
 *
 * @param payables
 * @param paymentDate
 * @param whichFlow
 */
function updateDiscounts(payables, paymentDate, whichFlow) {

    if(!payables || !paymentDate) {
        return;
    }
    for (var lineNo = 0; lineNo < payables.length; lineNo++) {
        // no need to go further if there is no discount amount for the invoice
        if(!payables[lineNo]['DISCOUNTAMOUNT']) {
            continue;
        }
        payables[lineNo]['DISCOUNTREMOVED'] = false;
        updateDiscount(payables[lineNo], paymentDate, lineNo, false, whichFlow);
        // update only if bill selected
        // on page load and when header pymt date changed
        if(payables[lineNo]['SELECTED'] &&
            ( (payables[lineNo]['SELECTED'] == true) || (payables[lineNo]['SELECTED'] == "true"))) {
            calculateAmountToPay(payables[lineNo], lineNo, false, false, true);
        }
    }
}

/**
 * Method to update the specific invoice discounts details.
 *
 * @param payable
 * @param paymentDate
 * @param rowNum
 * @param notInLoop single or bulk operation check
 * @param whichFlow flag used specificially to check bulk update of discount for refresh totals
 */
function updateDiscount(payable, paymentDate, rowNum, notInLoop, whichFlow) {

    if(!payable || !paymentDate || !payable['WHENDISCOUNT']) {
        return;
    }
    var isEligible = isLineDiscountEligible(payable);

    // if discount eligible then apply the discount
    if(isEligible) {
        var validate = true;
        if(typeof whichFlow != 'undefined') {
            if((whichFlow == 'isInitialLoad') || (whichFlow == 'afterUpdateGrid')) {
                //don't validate on initial page load and on update of grid
                validate = false;
            }
            if((whichFlow == 'headerPymtDateChnge' || whichFlow == 'linePymtDateChnge') &&
                (typeof payable['SELECTED'] != 'undefined') &&
                (payable['SELECTED'] == false || payable['SELECTED'] == 'false')) {
                //on header/line pymt date change don't validate if row is not selected
                validate = false;
            }
        }
        // if on initial load or on apply filter when grid load happens don't show alert
        if( validate && payable['DISCOUNTAMOUNT'] != 0)
        {
            var procced = validateDiscountGettingApplied(payable, payable['DISCOUNTAMOUNT']);
            if(!procced) {
                return false;
            }
        }
        payable['DISCOUNTAPPLIED'] = payable['DISCOUNTAMOUNT'];
        // set DISCOUNTDATE here, because its used only if discount is to be applied
        payable['DISCOUNTDATE'] = paymentDate;
    } else {
        // if there is no discount then show 0
        payable['DISCOUNTAPPLIED'] = 0;
    }

    if(notInLoop) {
        var grid = getPayableGrid();
        var discObj = grid.findLineComponent('DISCOUNTAPPLIED', rowNum, 'Field');
        // set the discount value
        if(discObj) {
            discObj.setValue(payable['DISCOUNTAPPLIED']);
        }
    }
    return true;
}

/**
 * Method to apply the payment date in selected line level payment date component.
 *
 * @param fieldMeta
 * @param fromDiscountPage
 * @param noLoadPaymentDate
 */
function applyLinePaymentDate(fieldMeta, fromDiscountPage, noLoadPaymentDate)
{
    var dataChanged = window.editor.dataChanged;
    // No data is changed? No need to continue
    if ( !dataChanged) {
        return;
    }
    QXUtil.showLoading();
    var paymentDate = fieldMeta.getValue();
    var lineNo = -1;
    var payables = window.editor.view.value['PAYABLES'];
    var doHideLoading = true;

    // If it is from discount page, calculate the discount from
    if(fromDiscountPage) {
        lineNo = window.editor.view.getField('ROWNUM').getValue();
        // if the line number is undefined we can fairly assume its 0th element
        if(!lineNo)
            lineNo = 0;
        var payable = payables[lineNo];
        var cutoffDate = window.editor.view.getField('WHENDISCOUNT_D').getValue();
        var isEligible = isDiscountEligible(cutoffDate, paymentDate);
        var discountAmount = 0;
        // if discount eligible then update the discount
        if (window.editor.view.value.PAYMENTMETHOD_D && window.editor.view.value.PAYMENTMETHOD_D == 'Joint Check') {
            discountAmount = 0;
        } else if(isEligible) {
            discountAmount = payable['DISCOUNTAMOUNT'];
        }
        // update the amount in the field
        var discountApplied = window.editor.view.getField('DISCOUNTAPPLIED_D');
        discountApplied.setValue(discountAmount);
        if (!noLoadPaymentDate) {
            // update the payment date field
            var payDate = window.editor.view.getField('WHENPAID_D');
            payDate.setValue(paymentDate);
        }
        // update the discount as of date
        var discountDate = window.editor.view.getField('DISCOUNTDATE');
        discountDate.setValue(paymentDate);
    } else {
        lineNo = fieldMeta.getLineNo();
        var bill = payables[lineNo];
        // Validate the payment date only if the row is selected
        if(bill && bill['SELECTED'] === 'true') {
            // Validate the payment date
            var bills = {};
            bills[0] = bill;
            validatePaymentDate(fieldMeta, bills);
        }

        // This performs a single compliance check for one row in the payment grid.
        if (typeof fieldMeta.parentValue['COMPLIANCE_ICON'] != 'undefined') {
            var vendorid = fieldMeta.parentValue['VENDORID'];
            var primaryDocs = [];
            if (fieldMeta.parentValue['PRIMARYDOCKEYS'] && fieldMeta.parentValue['PRIMARYDOCKEYS'].length > 0) {
                primaryDocs = fieldMeta.parentValue['PRIMARYDOCKEYS'];
            }
            ajaxArgs = {"vendorid": vendorid, "module": "pay", "paymentdate": paymentDate, "row": lineNo,
                "docs": JSON.stringify(primaryDocs)};
            doHideLoading = false;
            window.editor.ajax(false, 'calcComplianceForVendor', ajaxArgs, c_recalcComplianceForVendor);
        }
        
        bill['DISCOUNTREMOVED'] = false;
        var proceed = updateDiscount(bill, paymentDate, lineNo, true,'linePymtDateChnge');
        if(proceed) {
            // upadte amt to pay only if row selected
            if((typeof bill['SELECTED'] != 'undefined') && (bill['SELECTED'] == true || bill['SELECTED'] == 'true')) {
                calculateAmountToPay(bill, lineNo,false,false);
                toggleCheckbox(getPayableGrid(),lineNo, false, true);
                isLineDetPopulated = false;
                //We need to adjust the bank balance for the selected date
                populateAdjustedBankBalanceNew();
            } else {
                if (doHideLoading) {
                    QXUtil.hideLoading();
                }
            }
        } else {
            if (doHideLoading) {
                QXUtil.hideLoading();
            }
        }
    }
    if (doHideLoading) {
        QXUtil.hideLoading();
    }
}

/**
 * Method to reset or set the discount amount on change of the payment date
 * @param bill
 * @param lineNo
 */
function handleDiscountReset(bill, lineNo, notInLoop, whichFlow) {
    //Need to alter the amount payable based on the payment date and the discount availability
    var grid = getPayableGrid();
    var payable = grid.parentValue['PAYABLES'][lineNo];
    if (grid) {
        if(notInLoop) {
            var paymentAmountObj = grid.findLineComponent('PAYMENTAMOUNT', lineNo, 'Field');
        }
        if(grid.value && grid.value[lineNo]){
            var paymentAmount = grid.value[lineNo]['PAYMENTAMOUNT'];
        }
        if (paymentAmount) {
            var billDiscountApplied = grid.value[lineNo]['DISCOUNTAPPLIED'];
            var paymentAmount = parseFloat(paymentAmount);
            var billDiscountAmount = parseFloat(bill['DISCOUNTAMOUNT']);
            billDiscountApplied = parseFloat(billDiscountApplied);
            var credits = bill['CREDITSAPPLIED'] ? parseFloat(bill['CREDITSAPPLIED']) : 0;
            var billTrxDue = parseFloat(bill['TRX_TOTALDUE']);
            var paymentCredits = paymentAmount + credits;
            if(billDiscountAmount >= billTrxDue)
            {
                return false;
            }
            if (billDiscountApplied  && !Number.isNaN(billDiscountAmount) && paymentCredits === billTrxDue) {
                //paymentAmountObj.setValue(paymentAmount - billDiscountAmount);
                payable['PAYMENTAMOUNT'] = paymentAmount - billDiscountAmount;
            } else if (billDiscountApplied === 0 &&  !Number.isNaN(billDiscountAmount) && (billDiscountAmount + paymentCredits) === billTrxDue) {
                //paymentAmountObj.setValue(paymentAmount + billDiscountAmount);
                payable['PAYMENTAMOUNT'] = paymentAmount + billDiscountAmount;
            }

            if(notInLoop && paymentAmountObj && payable['PAYMENTAMOUNT'] >= 0 ) {
                paymentAmountObj.setValue(payable['PAYMENTAMOUNT']);
            }
        }
        // for single operations we need to refresh totals
        if(notInLoop && (whichFlow != 'bulkDiscUpdate')) {
            grid.computeTotals();
            grid.refreshTotals();
        }
    }
}

/**
 * Method to verify if the payable is eligible for discounts.
 *
 * @param payable       payable item
 * @returns {boolean}   return true if its eligible otherwise false
 */
function isLineDiscountEligible(payable)
{
    var isEligible = false;
    // no payable or discount removed by user
    if(!payable || payable['DISCOUNTREMOVED']) {
        return isEligible;
    }
    // Discounts are not used for Joint Check.
    if (window.editor.view.value.PAYMENTMETHOD_D && window.editor.view.value.PAYMENTMETHOD_D == 'Joint Check') {
        return isEligible;
    }
    var term = payable['TERMVALUE'];
    if(term && payable['DISCOUNTAMOUNT'] > 0) {
        // Check the discount eligibilty
        isEligible = isDiscountEligible(payable['WHENDISCOUNT'], payable['WHENPAID']);
    }
    return isEligible;
}

/**
 * Method to verify if the payment is eligible for the discount for the given inputs.
 *
 * @param cutoffDateStr
 * @param pymtDateStr
 * @returns {boolean}
 */
function isDiscountEligible(cutoffDateStr, pymtDateStr)
{
    if(!cutoffDateStr || !pymtDateStr) {
        return false;
    }
    // get the discount cuttoff date
    var cutOffDate = new Date(cutoffDateStr) / 1000;
    var pymtDate = new Date(pymtDateStr) / 1000;
    // if the cut off date is on or after the payment date then the payable is eligible
    return cutOffDate >= pymtDate;
}

/**
 * Method to calculate the amount of credit applied for the bill or bill line.
 *
 * @param billNo
 * @param billLineNo
 * @returns {number}
 */
function getAppliedCreditsForBill(billNo, billLineNo)
{
    var appliedAmount = 0;
    if(billNo && appliedCredits) {
        var allCredits = appliedCredits[billNo];
        if(allCredits) {
            if(billLineNo) {
                var billLineCredits = allCredits[billLineNo];
                for (var key in billLineCredits) {
                    appliedAmount = roundAmt(appliedAmount + parseFloat(billLineCredits[key] ? billLineCredits[key] : 0));
                }
            } else {
                // Iterate each bill line to get the amount details
                for (var lineKey in allCredits) {
                    var billLineCredits = allCredits[lineKey];
                    for (var creditKey in billLineCredits) {
                        appliedAmount = roundAmt(appliedAmount + parseFloat(billLineCredits[creditKey] ? billLineCredits[creditKey] : 0));
                    }
                }
            }
        }
    }
    return appliedAmount;
}


/**
 * Method to act on the select/unselect of credit line checkbox.
 * When the checkbox is selected, applies full credit not exceeding the total due amount.
 * When the checkbox is unchecked, it clears all the credits applied.
 *
 * @param fieldMeta
 */
function toggleCreditSelection(fieldMeta)
{
    if(!fieldMeta) {
        return;
    }
    var lineNo = fieldMeta.getLineNo();
    var creditGrid = window.editor.view.findComponents('CREDITITEMS','Grid');
    if(creditGrid) {
        creditGrid = creditGrid[0];
    }
    var rowData = fieldMeta.parentValue;
    var flag = fieldMeta.getValue() == 'true' ? true : false;
    // Get the credit line field object
    var blCreditAppliedObj = creditGrid.findLineComponent('BLCREDITAPPLIED', lineNo, 'Field');
    var creditAvailableObj = creditGrid.findLineComponent('BLCREDITAVAILABLE', lineNo, 'Field');
    var existingCredit = parseFloat(blCreditAppliedObj.getValue());
    var availableCredit =  parseFloat(rowData['BLCREDITAVAILABLE'] ? rowData['BLCREDITAVAILABLE'] : 0);
    var creditToApply = 0;
    // Apply credit
    if(flag) {
        if(availableCredit > 0) {
            // Get the credit to apply
            creditToApply = getCreditToApply(rowData, availableCredit, -1);
        }
    }
    // Remove credit
    else {
        // Add the released credit to available
        availableCredit = roundAmt(availableCredit + existingCredit);
    }
    // Set split credit to false
    blCreditAppliedObj.setValue(creditToApply);
    // Set the new available credit
    creditAvailableObj.setValue(roundAmt(availableCredit - creditToApply));
    // Call to update the credit available incase -ve bill scenarios
    updateCreditAvailableForBill(fieldMeta);
    // Set as the previous credit with same amount as applied
    rowData['PREVBLCREDITAPPLIED'] = creditToApply;

    creditGrid.computeTotals();
    creditGrid.refreshTotals();
}

/**
 * Method to get the credit that can be applied after calculating the line due.
 *
 * @param rowData
 * @param suggestedCredit
 * @param rowToSkip
 * @returns {number}
 */
function getCreditToApply(rowData, suggestedCredit, rowToSkip)
{
    var creditToApply = 0;
    if(rowData) {
        // Get the due amount on the bill line
        var dueAmount = getBillLineDueAmount(currentBillLineRow);
        // Get all credit applied so for for this line
        var creditGrid = getGrid('CREDITITEMS');
        if(creditGrid) {
            var credits = creditGrid.value;
            var currentCreditsApplied = 0;
            if(credits) {
                // This shows the amount of credits applied online so far on the credits page
                for(var i = 0; i < credits.length; i++) {
                    // Skip the row as that row may be the one changing the credit
                    if(rowToSkip != i) {
                        currentCreditsApplied = roundAmt(currentCreditsApplied + parseFloat(isNaN(credits[i]['BLCREDITAPPLIED']) ? 0 : credits[i]['BLCREDITAPPLIED']));
                    }
                }
            }
            // Lets check the line level credits applied previously before launching credits page
            var billItems = window.editor.view.value['BILLITEMS'];
            if(billItems) {
                var billLineRow = billItems[currentBillLineRow];
                if (billLineRow) {
                    var prevCreditApplied = parseFloat(isNaN(billLineRow['CREDITSAPPLIED']) ? 0 : billLineRow['CREDITSAPPLIED']);
                    // Now recalculate the due amount
                    dueAmount = roundAmt(dueAmount + roundAmt(prevCreditApplied - currentCreditsApplied))
                }
            }
        }
        // If there is any due amount exist then just apply that
        if (dueAmount > 0) {
            creditToApply = Math.min(suggestedCredit, dueAmount);
        } else {
            // In this case, we may need to take out the payment amount and apply the credit
            // Get the bill line items amount
            var billItems = window.editor.view.value['BILLITEMS'];
            if (billItems) {
                var billLineRow = billItems[currentBillLineRow];
                if (billLineRow) {
                    var paymentAmount = parseFloat(isNaN(billLineRow['PAYMENTAMOUNT']) ? 0 : billLineRow['PAYMENTAMOUNT']);
                    if (paymentAmount > 0) {
                        creditToApply = Math.min(suggestedCredit, paymentAmount);
                    }
                }
            }
        }
    }
    return creditToApply;
}

/**
 * Called when the credit amount is supplied in 'Credit applied' column of the credit grid.
 *
 * @param fieldMeta
 */
function onCreditPayment(fieldMeta)
{
    if(!fieldMeta) {
        return;
    }
    var lineNo = fieldMeta.getLineNo();
    var creditGrid = window.editor.view.findComponents('CREDITITEMS','Grid');
    if(creditGrid) {
        creditGrid = creditGrid[0];
    }
    var rowData = fieldMeta.parentValue;
    if(rowData['PREVBLCREDITAPPLIED'] == fieldMeta.getValue()) {
        // no change, just return
        return;
    }
    var prevCreditApplied = parseFloat(rowData['PREVBLCREDITAPPLIED'] ? rowData['PREVBLCREDITAPPLIED'] : 0);
    // Validate the applied credit or current value is empty then reset with previous amount
    if(!validateCreditPayment(rowData) || !fieldMeta.getValue()) {
        fieldMeta.setValue(prevCreditApplied);
    } else {
        var inputCreditAmount = fieldMeta.getValue();
        // Get the credit to apply based on due amount
        var creditToApply = getCreditToApply(rowData, inputCreditAmount, lineNo);
        // Add the
        fieldMeta.setValue(creditToApply);
        // Set the new available credit
        var creditAvailableObj = creditGrid.findLineComponent('BLCREDITAVAILABLE', lineNo, 'Field');
        var availableCredit =  parseFloat(rowData['BLCREDITAVAILABLE'] ? rowData['BLCREDITAVAILABLE'] : 0);
        availableCredit = roundAmt(availableCredit + roundAmt(prevCreditApplied - creditToApply));
        creditAvailableObj.setValue(availableCredit);
        // Call to update the credit available incase -ve bill scenarios
        updateCreditAvailableForBill(fieldMeta);
        // Set the prev credit applied as same as credits applied
        rowData['PREVBLCREDITAPPLIED'] = creditToApply;
        // set the selection
        var selectObj = creditGrid.findLineComponent('SELECTCREDIT', lineNo, 'Field');
        if(creditToApply > 0) {
            selectObj.setValue(true);
        } else {
            selectObj.setValue(false);
        }
    }
    window.editor.refreshData();
    creditGrid.computeTotals();
    creditGrid.refreshTotals();
}

/**
 * For -ve bill credit application, if there are any other line from the same bill, update the credit available with
 * amount after this application
 *
 * @param fieldMeta
 */
function updateCreditAvailableForBill(fieldMeta)
{
    if(!fieldMeta) {
        return;
    }
    var rowData = fieldMeta.parentValue;
    if(rowData && rowData['CREDITTYPE'] == 'Bill') {
        var creditGrid = window.editor.view.findComponents('CREDITITEMS', 'Grid');
        if (creditGrid) {
            creditGrid = creditGrid[0];
        }
        if (creditGrid && creditGrid.getValue()) {
            var lineNo = fieldMeta.getLineNo();
            //var creditAvailableObj = creditGrid.findLineComponent('BLCREDITAPPLIED', lineNo, 'Field');
            //var creditToApply = creditAvailableObj.getValue();
            // Iterate first to get the applied credit amount
            var billCredits = creditGrid.getValue();
            var billCreditApplied = 0;
            var prevAppliedCredits = 0;
            if(billCredits) {
                // Iterate to get how much applied on the -ve bill
                for(var i = 0; i < billCredits.length; i++) {
                    if(billCredits[i]['CRRECKEY'] == rowData['CRRECKEY']) {
                        // Credits applied as part of this session
                        billCreditApplied = roundAmt(billCreditApplied + parseFloat(isNaN(billCredits[i]['BLCREDITAPPLIED']) ? 0 : billCredits[i]['BLCREDITAPPLIED']));
                        // Credits applied previously
                        prevAppliedCredits = roundAmt(prevAppliedCredits + getAppliedCredit(billCredits[i]['RECORD#']));
                    }
                }
                var docTotalDue =  parseFloat(isNaN(rowData['DOCTOTALDUE']) ? 0 : rowData['DOCTOTALDUE']);
                // Add to doc total due as it would be in -ve for -ve bill credits
                var maxCreditAvailable = Math.abs(roundAmt(docTotalDue + billCreditApplied));
                // Deduct the previously applied credits for this bill as part of this session
                maxCreditAvailable = roundAmt(maxCreditAvailable - prevAppliedCredits);
                maxCreditAvailable = maxCreditAvailable > 0 ? maxCreditAvailable : 0;
                // Now update the other -ve bill lines
                for(var j = 0; j < billCredits.length; j++) {
                    if(j != lineNo && billCredits[j]['CRRECKEY'] == rowData['CRRECKEY']) {
                        var otherCrAvlObj = creditGrid.findLineComponent('BLCREDITAVAILABLE', j, 'Field');
                        var availableCredit =  parseFloat(billCredits[j]['BLCREDITAVAILABLE'] ? billCredits[j]['BLCREDITAVAILABLE'] : 0);
                        // If the line credit is more than max available credit then set the available credit to max
                        // available credit
                        availableCredit = availableCredit > maxCreditAvailable ? maxCreditAvailable : availableCredit;
                        if(otherCrAvlObj) {
                            otherCrAvlObj.setValue(availableCredit);
                        } else {
                            // This happen if the credit line not in the current page
                            billCredits[j]['BLCREDITAVAILABLE'] = availableCredit;
                        }
                    }
                }
                // Refresh the credits on the page
                window.editor.refreshData();
            }
        }
    }
}

/**
 * Validates the credit payment in credit grid.
 *
 * @param rowData
 * @returns {boolean}
 */
function validateCreditPayment(rowData) {
    var isValid = true;
    if(!rowData) {
        isValid = false;
    }
    var creditApplied = rowData['BLCREDITAPPLIED'];
    creditApplied = parseFloat(isNaN(creditApplied) ? 0 : creditApplied);
    // Get the prev credit applied
    var prevAmount = rowData['PREVBLCREDITAPPLIED'];
    prevAmount = parseFloat(prevAmount ? prevAmount : 0);
    // Validate the credit payment
    var availableCredit =  parseFloat(rowData['BLCREDITAVAILABLE'] ? rowData['BLCREDITAVAILABLE'] : 0);
    // validate for +ve amount
    if(creditApplied < 0) {
        alert(GT('IA.CAN_NOT_APPLY_NEGATIVE_CREDIT_AMOUNT'));
        isValid = false;
    }
    else {
        // if the credits applied more than the available then throw error
        if(roundAmt(creditApplied - prevAmount) > availableCredit) {
            var token_i18n = { "id" : "IA.CAN_NOT_APPLY_MORE_THAN_THE_AVAILABLE_CREDITS", "placeHolders" : [{ "name":"AVAILABLE_CREDIT", "value": availableCredit}]};
            alert(GT(token_i18n));
            isValid = false;
        }
    }
    return isValid;
}

/**
 * Method to get the bill line due amount.
 *
 * @param lineNo
 */
function getBillLineDueAmount(lineNo) {
    var dueAmount = 0;
    // Get the bill line object
    var billItems = window.editor.view.value['BILLITEMS'];
    if(billItems) {
        var billLineRow = billItems[lineNo];
        if(billLineRow) {
            var billLineTrxDue = parseFloat(isNaN(billLineRow['TRX_TOTALDUE']) ? 0 : billLineRow['TRX_TOTALDUE']);
            var paymentAmount = parseFloat(isNaN(billLineRow['PAYMENTAMOUNT']) ? 0 : billLineRow['PAYMENTAMOUNT']);
            var creditApplied = parseFloat(isNaN(billLineRow['CREDITSAPPLIED']) ? 0 : billLineRow['CREDITSAPPLIED']);
            var discountApplied = parseFloat(isNaN(billLineRow['DISCOUNTAPPLIED']) ? 0 : billLineRow['DISCOUNTAPPLIED']);
            // Get the due amount on the bill line
            dueAmount = roundAmt(billLineTrxDue - roundAmt(paymentAmount + roundAmt(creditApplied + discountApplied)));
        }
    }
    return dueAmount;
}

/**
 * Utility method to round the amount.
 *
 * @param amt
 * @returns {number}
 */
function roundAmt(amt) {
    return Math.round( amt * 100 ) / 100;
}

/**
 * Method to update the discount details when discount is clicked.
 * @param fieldMeta
 */
function openEstimatedDeliveryDetailsPage(fieldMeta) {
    if (!fieldMeta) {
        return;
    }
    var estimateDeliveryPage = window.editor.getView().getFloatingPage('ESTIMATEDELIVERYPAGE');

    var title = 'Estimate delivery date for Check Delivery payments';
    var paymentMethod = window.editor.view.getField('PAYMENTMETHOD_D');
    if (paymentMethod.getValue() === 'Amex ACH') {
        title = 'Estimate delivery date for Amex ACH payments';
    }
    estimateDeliveryPage.title = title;
    window.editor.showPage('ESTIMATEDELIVERYPAGE', this);

    var finalApprovalDate = window.editor.view.getField('FINALAPPROVALDATE_D');
    var currDate = getFinalApprovalDate();
    finalApprovalDate.setValue(dateToString(currDate, 'MM/DD/YYYY'));
    loadEstimatedPymtDeliveryDate();
}

/**
 * Method to get the Final Approval Date
 * @returns {Date}
 */
function getFinalApprovalDate() {
    var currDate = new Date();
    var sixMonthFromNow = new Date();
    sixMonthFromNow = new Date(sixMonthFromNow.getFullYear(), sixMonthFromNow.getMonth() + 6, sixMonthFromNow.getDate());

    var previousDay = new Date();
    previousDay.setDate(previousDay.getDate() - 1);

    if(currDate.getTime() >  sixMonthFromNow.getTime()){
        currDate = sixMonthFromNow;
    } else if(previousDay.getTime() >= currDate.getTime()){
        currDate = new Date();
        currDate = new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate());
    }

    return currDate;
}

/**
 * Method to load the Estimated Payment Delivery Date
 * @param finalDate
 */
function loadEstimatedPymtDeliveryDate(finalDate) {
    if (finalDate) {
        finalDate = finalDate.getValue();
        finalDate = new Date(finalDate);
    }
    var businessNumDays = getBusinessNumDays();
    var currDate = calculateEstimatedDeliveryDate(finalDate, businessNumDays);
    var estimatedDeliveryDate = window.editor.view.getField('ESTIMATEDPYMTDELDATE_D');
    estimatedDeliveryDate.setValue(dateToString(currDate, 'MM/DD/YYYY'));
}

/**
 * Method to get the Business Number of Days
 * @returns {number}
 */
function getBusinessNumDays() {
    var businessNumDays = 4;
    var paymentMethod = window.editor.view.getField('PAYMENTMETHOD_D');
    if (paymentMethod.getValue() === 'Check Delivery') {
        businessNumDays = 5;
    }
    return businessNumDays;
}

/**
 * Method to calculate the estimated Delivery Date
 * @param currDate
 * @param businessNumDays
 * @returns {Date}
 */
function calculateEstimatedDeliveryDate(currDate, businessNumDays) {
    if (typeof currDate === 'undefined' || !currDate) {
        currDate = new Date();
    }
    if (!businessNumDays) {
        businessNumDays = getBusinessNumDays();
    }

    currDate = recalEstimatedPymtDate(currDate, businessNumDays);

    return currDate;
}

/**
 * Skip holidays and calculate the actual estimated payment date
 * @param currDate
 * @param businessNumDays
 * @returns {*}
 */
function recalEstimatedPymtDate(currDate, businessNumDays) {
    var days = 0;
    while(days < businessNumDays)
    {
        currDate.setDate(currDate.getDate() + 1);

        //If saturday or sunday then skip
        if(currDate.getDay() == 6 || currDate.getDay() == 0)  continue;

        //If a federal holiday then skip
        var holiday = checkFederalHoliday(currDate);
        if(holiday != null) continue;

        //Check for any friday or monday there is a federal holiday on Saturday or Sunday repectively
        var theDayToCheck = currDate;
        if(currDate.getDay() == 5) { //If current date is a friday then see check for Saturday
            theDayToCheck  = new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate() + 1);
            holiday = checkFederalHoliday(theDayToCheck);
        }else if(currDate.getDay() == 1) { //if current day is a monday then check for sunday
            theDayToCheck  = new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate() -1);
            holiday = checkFederalHoliday(theDayToCheck);
        }
        //If any holiday observed on Monday or friday then skip
        if(holiday != null)  continue;

        //Finally count as a valid business day
        days ++;
    }

    return currDate;
}

/**
 * Check for federal holiday
 *
 * @param holidayDate
 * @returns {*}
 */
function checkFederalHoliday(holidayDate) {
    var holidayMeta = {
        //Only fixed holidays could float as these could fall on weekends
        'Fixed': {//Month, Day
            '01/01': "New Year's Day",
            '07/04': "Independence Day",
            '11/11': "Veteran's Day",
            '12/25': "Christmas Day"
        },
        //These can not fall on weekends and hence can not float
        'Week': {//Month, Week of Month, Day of Week
            '1/3/1': "Martin Luther King Jr. Day",
            '2/3/1': "Washington's Birthday",
            '5/5/1': "Memorial Day",
            '9/1/1': "Labor Day",
            '10/2/1': "Columbus Day",
            '11/4/4': "Thanksgiving Day"
        }
    };

    var month = 1 + (0 | (holidayDate.getDate() - 1) / 7);
    var memorial = (holidayDate.getDay() === 1 && (holidayDate.getDate() + 7) > 30) ? "5" : null;
    var fixDateKey = dateToString(holidayDate, 'MM/DD');
    var memKey = (holidayDate.getMonth()+1) + "/"+(memorial || month) + "/"+holidayDate.getDay();
    var holiday = (holidayMeta['Fixed'][fixDateKey ] || holidayMeta['Week'][memKey]);
    return holiday;
}

/**
 * Method to convert the date object into a string
 * @param dateObj
 * @param format
 * @returns {XML|*|string|void}
 */
function dateToString(dateObj, format){
    var yyyy = dateObj.getFullYear().toString();
    var mm = (dateObj.getMonth()+1).toString(); // getMonth() is zero-based
    var dd  = dateObj.getDate().toString();
    var dateStr = format.replace('YYYY', yyyy);
    dateStr = dateStr.replace('MM', (mm[1]?mm:"0"+mm[0]));
    dateStr = dateStr.replace('DD', (dd[1]?dd:"0"+dd[0]));
    return dateStr;
}

function applyPymtReqDiscount(date) {
    var rowData = date.parentValue;
    var paymentDate = date.getValue();
    var paymentGrid = getGrid('PAYMENTREQUESTS_BILLS');
    if (rowData['PAYMENTREQUESTS_BILLS']) {
        for (var i = 0; i < rowData['PAYMENTREQUESTS_BILLS'].length; i++) {
            var checked = paymentGrid.findLineComponent('PRSELECTBILL', i, 'Field');
            if( checked.getValue()== 'false' ){
                continue;
            }
            var discountAppliedObj = paymentGrid.findLineComponent('DISCOUNTAPPLIED', i, 'Field');
            if(previousDisccount[i] == undefined){
                previousDisccount[i] = parseFloat(discountAppliedObj.getValue());
            }
            if(!previousDisccount[i]){
                continue;
            }

            var bills = rowData['PAYMENTREQUESTS_BILLS'][i];
            var BalanceObj = paymentGrid.findLineComponent('BALANCE', i, 'Field');
            var isDiscount = isDiscountEligible(bills['WHENDISCOUNT'], paymentDate);

            if (!isDiscount) {
                BalanceObj.setValue(parseFloat( BalanceObj.getValue()) + parseFloat(discountAppliedObj.getValue()) );
                discountAppliedObj.setValue(0);
            } else {
                var term = bills['TERMVALUE'];
                term = term.split(":");
                term = term[0];
                var calculator = term[term.length - 1];

                if (calculator == '%') {
                    var discountAmount = parseFloat(term.substring(0, term.length - 1));
                    discountAmount = (bills['TRX_TOTALENTERED'] * discountAmount) / 100;
                }else{
                    discountAmount = term;
                }
                if (window.editor.view.value.PAYMENTMETHOD_D && window.editor.view.value.PAYMENTMETHOD_D == 'Joint Check') {
                    discountAmount = 0;
                }
                discountAppliedObj.setValue(discountAmount);
                if(discountAmount < BalanceObj.getValue()) {
                    BalanceObj.setValue(BalanceObj.getValue() - discountAmount);
                } else {
                    BalanceObj.setValue(0);
                }

                // if (parseFloat(bills['TRX_TOTALSELECTED']) !== parseFloat(bills['AMOUNTTOPAY'])) {
                //     discountAppliedObj.setValue(discountAmount);
                // }
            }
        }
    }
    paymentGrid.refreshData();
    // Recalculate the total
    paymentGrid.computeTotals();
    paymentGrid.refreshTotals();
}

/**
 * Method to calculate amount to pay field.
 *
 * @param rowData
 * @param lineNo
 * @param pymtAmtChanged
 * @param isItemPymt
 * @param isBulk
 *
 */
function calculateAmountToPay(rowData, lineNo, pymtAmtChanged, isItemPymt, isBulk)
{
    if(typeof rowData !== 'undefined' &&  (typeof lineNo !== 'undefined')) {

        if(!isItemPymt) {
            var grid            = getPayableGrid();
            if(!isBulk) {
                var linePymtAmtObj  = grid.findLineComponent('PAYMENTAMOUNT', lineNo, 'Field');
            }

        } else {
            var grid            = getPayableItemGrid();
            if (rowData['ISJOINTPAYMENT']) {
                grid = getGrid('JOINTITEMS');
            } else {
                if (rowData['BILLITEMS'] == undefined) {
                    /* need to handle applying credit at payee level */
                    var rowData = pymtAmtChanged ? rowData : rowData['JOINTITEMS'][lineNo];
                } else {
                    var rowData = pymtAmtChanged ? rowData : rowData['BILLITEMS'][lineNo];
                }
                if (!isBulk) {
                    var linePymtAmtObj = grid.findLineComponent('PAYMENTAMOUNT', lineNo, 'Field');
                }
            }
        }

        var creditsAvailable = parseFloat(rowData['CREDITSAVAILABLE'] ? rowData['CREDITSAVAILABLE'] : 0);
        var creditApplied    = parseFloat(rowData['CREDITSAPPLIED'] ? rowData['CREDITSAPPLIED'] : 0);
        var amountDue        = parseFloat(rowData['TRX_TOTALDUE']);
        var paymentAmount    = parseFloat(rowData['PAYMENTAMOUNT']);
        paymentAmount    = isNaN(paymentAmount) ? 0 : paymentAmount;
        var discountApplied  = parseFloat(rowData['DISCOUNTAPPLIED']);
        discountApplied  = isNaN(discountApplied) ? 0 : discountApplied;
        var isJoint = (window.editor.view.value.PAYMENTMETHOD_D && window.editor.view.value.PAYMENTMETHOD_D == 'Joint Check');
        if (isJoint) {
            discountApplied = 0;
        }
        var  updatePymtAmt   = false;

        // if credit to apply is changed manually
        if(!pymtAmtChanged) {
            var amountToPay = roundAmt(amountDue - discountApplied - creditApplied);
            updatePymtAmt = true;
        } else {
            // if pymt amt chnged
            // make sure amt not greater than due
            if(amountDue < roundAmt(creditApplied + roundAmt(discountApplied + paymentAmount))) {
                var billID = rowData['RECORDID'] ? rowData['RECORDID'] : grid.parentValue['BILLRECORDID'];
                var token_i18n = { "id" : "IA.THE_AMOUNT_DUE_IS_ONLY_FOR_BILL_PLACEHOLDERS", "placeHolders" : [{ "name":"AMOUNT_DUE", "value": amountDue}, { "name":"BILL_NO", "value":  billID}]};
                alert(GT(token_i18n));
                var pymtAmt = ((paymentAmount > amountDue) ? amountDue : paymentAmount);
                amountToPay =  roundAmt(pymtAmt - discountApplied - creditApplied);
                updatePymtAmt = true;
            }
        }
        if (isJoint) {
            updatePymtAmt = false;
        }
        if(updatePymtAmt) {
            rowData['PAYMENTAMOUNT'] = amountToPay;
            if(!isBulk) {
                if (linePymtAmtObj) {
                    linePymtAmtObj.setValue(amountToPay);
                }
                grid.computeTotals();
                grid.refreshTotals();
            }
        }
    }
}

function validateDiscountGettingApplied(payable, appliedDiscount) {

    if( appliedDiscount != 0 && (appliedDiscount >= payable['TRX_TOTALDUE']))
    {
        var token_i18n = { "id" : "IA.REMOVE_THE_DISCOUNT_APPLIED_PLACEHOLDERS", "placeHolders" : [{ "name":"RECORD_ID", "value": payable['RECORDID']}]};
        alert(GT(token_i18n));
        return false;
    }

    if( typeof payable['CREDITSAPPLIED'] != 'undefined' && payable['CREDITSAPPLIED'] != 0 )
    {
        var discountPlusCredit = appliedDiscount + parseFloat((payable['CREDITSAPPLIED']));
        if(discountPlusCredit > parseFloat(payable['TRX_TOTALDUE'])) {
            alert(GT('IA.MAKE_SURE_THE_AMOUNT_TO_PAY_IS_EITHER_A_POSITIVE_AMOUNT_OR_ZERO_THEN_TRY_AGAIN'));
            payable['DISCOUNTREMOVED'] = true;
            return false;
        }
    }
    return true;
}

function getTotalLineCreditApplied (creditGridObj) {
    let creditItems = creditGridObj.getValue();
    let totalLineCreditsApplied = 0;
    for (var i = 0; i < creditItems.length; i++) {
        let creditObj = creditItems[i];
        let currentCreditApplied = parseFloat(isNaN(creditObj['BLCREDITAPPLIED']) ? 0 : creditObj['BLCREDITAPPLIED']);
        totalLineCreditsApplied += currentCreditApplied;
    }
    return totalLineCreditsApplied;
}