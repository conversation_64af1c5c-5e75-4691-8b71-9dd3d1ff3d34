<?php

//=============================================================================
//
//    FILE:           memcache.phtml
//    AUTHOR:         <PERSON><PERSON>
//    DESCRIPTION:    manage memcache
//
//    (C)2001, Intacct Corporation, All Rights Reserved
//
//    Intacct Corporation Proprietary Information.
//    This document contains trade secret data that belongs to Intacct 
//    corporation and is protected by the copyright laws. Information herein 
//    may not be used, copied or disclosed in whole or part without prior 
//    written consent from Intacct Corporation.
//
//=============================================================================

require_once "debug.inc";
require_once 'util.inc';
require_once 'perfdata_utils.inc';
InitGlobals();

executeAction(Request::$r->_mcaction);

/**
 * Executes the received action. Isolates global variables.
 * @param string $memcacheAction Action o be executed
 */
function executeAction(&$memcacheAction)
{
    if ( Globals::$g->islive || !MONITOR_PERFDATA_DEBUG ) {
        header('Content-Type: text/plain');
    }

    // These are the actions that are allowed to bypass maintenance : flush, list, getValue
    if (isset($memcacheAction)) {
        $bypassMaintenance = $memcacheAction === 'flush' || $memcacheAction === 'list' || $memcacheAction === 'getVal';
    } else {
        $bypassMaintenance = false;
    }
    InitBase($bypassMaintenance);
    
    $mcTestKeyPrefix = 'vcr_';
    $mcTestKeyNo = 10;

    switch ($memcacheAction) {
    case 'lock':
        for( $ix = 0; $ix < $mcTestKeyNo; $ix++ ) {
            $lock = new Lock();
            if (  $lock->setLock($mcTestKeyPrefix . $ix, 24 * 60 * 60, false) ) {
                
                $lock->setClearLockOnDestroy(false);
            }
        }
        break;

    case 'list_locks':
        $client = CacheClient::getInstance(CacheClient::LOCK_POOL);
        $allKeys = [];
        $ok = $client->getAllKeys($allKeys);
        if ( $ok ) {
            print_r($allKeys);
        } else {
            echo "Could not fetch the memcache keys\n";
        }
        break;

        case 'flush':
            $hosts = CacheClient::getHosts('host');
            if (CacheClient::getInstance()->flush()) {
                echo "Memcache servers " . join(', ', $hosts) . ", have been flushed at " . gmdate('c') . "\n";
            } else {
                echo "Error flushing memcache servers " . join(', ', $hosts) . ", at " . gmdate('c') . "\n";
            }
            break;

        case 'refreshThrottle':
            ThrottleConfig::reset();
            printThrottleConfig();
            break;

        case 'getThrottle':
            printThrottleConfig();
            break;

        case 'list':
            $hosts = CacheClient::getHosts('host');
            echo join("\n", $hosts) . "\n";
            break;

        case 'clearCompanyCache':
            $cny = Request::$r->_cny;
            if (!isset($cny) || $cny == '') {
                echo "You must specify a cny. Arguments accepted:\n.cny=<cny#>\n";
                exit();
            }
            SetDBSchema($cny, "cny");
            $cache = CompanyCacheHandler::getInstance($cny);
            $cache->destroyCache();
            echo "Company Cache for cny# $cny deleted.\n";
            break;
        case 'getVal':
            // Make sure the key is set, if not print out some useful info
            $key = $_REQUEST['_key'];
            $encodedKey = util_encode($key);
            if (!isset($key) || $key == '') {
                echo "You must specify a memcache key. Arguments accepted:\n.key=<key name>\n.server=<server> [optional]\n";
                exit();
            }

            // Get information on the instance names
            $memcacheServer = CacheClient::GLOBAL_POOL;
            $validServers = CacheClient::getInstanceNamesList();
            $server = $_REQUEST['_server'];

            // Determine if the server parameter is set and valid
            if (isset($server)) {
                if (in_array($server, $validServers)) {
                    $memcacheServer = $server;
                } else {
                    echo "Invalid server, you must choose one of:\n" . print_r($validServers, true);
                    exit();
                }
            }
    
            $memcacheLockTestKeys = [];
            for( $ix = 0; $ix < $mcTestKeyNo; $ix++ ) {
                $memcacheLockTestKeys[] = $mcTestKeyPrefix . $ix;
            }
            
            // Specify which keys we allow per instance list
            $whiteList = [
                CacheClient::GLOBAL_POOL => ['ORA2393_error_count', 'test', 'test_not_found'],
                CacheClient::LOCK_POOL   => $memcacheLockTestKeys,
            ];

            $allowedKeys = $whiteList[$memcacheServer];
            if (is_array($allowedKeys) && in_array($key, $allowedKeys)) {
                $mc = CacheClient::getInstance($memcacheServer);
                $data = $mc->get($key);
                if (isset($data) && $data !== false) {
                    if (is_array($data) || is_object($data)) {
                        print_r($data);
                    } else {
                        echo $data;
                    }
                } else {
                    if (isset($_REQUEST['_forceZero'])) {
                        echo '0';
                    } else {
                        echo "[$encodedKey] not found";
                    }
                }
            } else {
                echo "Not allowed to access key '$encodedKey' on server '$memcacheServer'\n";
            }

            break;

        case 'perfData':
            $perfdata = Globals::$g->perfdata;
            initMonitorPerfData();
            printMonitoredData($perfdata);
            break;

        /* case 'clearOrphanLocks':
            $clearedList = Lock::clearOrphanedLocks();
            foreach ($clearedList as $lockId => $value) {
                echo "Lock $lockId ($value) was cleared.\n";
            }
            echo "\nDone. Cleared " . count($clearedList) . " locks.\n";
            break;
        */
        case 'clearThrottle':
            $key = Request::$r->key;
            $cny = Request::$r->cny;
            if (!$key || !$cny) {
                echo "Please specify a key (the throttle key) and a cny (the company record #)\n";
                break;
            }
            if (!SetDBSchema($cny, '')) {
                echo "Can't find the specified company or its database.";
            }
            $throttle = new Throttle();
            $result = $throttle->cleanupThrottle($key, $cny);
            if ($result) {
                echo "Cleared " . $result['cnyLocks'] . " company locks, " .
                    $result['schemaLocks'] . " database schema locks, " .
                    $result['dbServerLocks'] . " database server locks, and " .
                    $result['globalLocks'] . " global locks\n";
                if ($result['locks']) {
                    echo "\nThe following specific lock IDs have been cleared:\n";
                    foreach ($result['locks'] as $lockId) {
                        echo $lockId . "\n";
                    }
                }
            } else {
                echo "Some error occurred.\n";
            }
            break;

        default:
            echo "Unknown action '$memcacheAction'\n";
            echo "Please specify one of the actions using the URL parameter '.mcaction': " .
                 "flush, list, getVal, getThrottle, refreshThrottle, clearThrottle, clearOrphanLocks, clearCompanyCache, perfData\n";
    }
}

function printThrottleConfig()
{
    $data = ThrottleConfig::getInstance()->get();
    $format = "%-10s %15s %-30s %8d %8d %8d\n";
    printf(str_replace('8d', '-8s', $format), 'Level', 'ID', 'Key', 'Parallel', 'Wait', 'Expiration');
    foreach ($data as $type => $types) {
        if (is_array($types)){
            foreach ($types as $index => $keys) {
                foreach ($keys as $key => $values) {
                    printf($format, $type, $index, $key, $values['threads'], $values['wait'], $values['expire']);
                }
            }
        }
    }
}

/**
 * @param PerfData $perfdata
 */
function printMonitoredData($perfdata)
{
    $rPid = Request::$r->rPid;
    if ($rPid !== null) {
        $performanceData = $perfdata->getMemcachedData($rPid);
        if (!empty($performanceData)) {
            if ( !Globals::$g->islive && MONITOR_PERFDATA_DEBUG ) {
                echo generatePerfDataHTMLTable(array_keys($perfdata->createPerfDataArray()), $performanceData, $rPid);
            } else {
                echo end($performanceData);
            }
        } else {
            echo "No data for this PID.";
        }
    } else {
        echo "Provide a process ID in a GET parameter named rPid.";
    }
}


