<?

/**
*    FILE:            ChartOfAccountsReport.cls
*    AUTHOR:            NaveenS
*    DESCRIPTION:    
*
*    (C)2000, Intacct Corporation, All Rights Reserved
*
*    Intacct Corporation Proprietary Information.
*    This document contains trade secret data that belongs to Intacct 
*    corporation and is protected by the copyright laws. Information herein 
*    may not be used, copied or disclosed in whole or part without prior 
*    written consent from Intacct Corporation.
*/


require_once 'util.inc';
import('Reporter');


class ChartOfAccountsReport extends Reporter
{

    /* @var array $rawdata */
    var $rawdata; //it will have actual data in database

    /* @var array $company */
    var $company;

    /* @var bool $isstatistical */
    var $isstatistical = false; //denotes whether statiscal accounts are selected in filter

    /* @var bool $requirestatus */
    var $requirestatus = false; //denotes whether status column to be added

    /* @var bool $includeTaxCode */
    var $includeTaxCode = false; //denotes whether tax code column to be added
    
    
    /**
     * @param array $_params
     */
    function __construct($_params = array())
    {
        
        $_params = INTACCTarray_merge(
            array('report' => 'chartofaccounts', '2stage' => true),
            $_params
        );
        Reporter::__construct($_params);
        if(IsMCMESubscribed()) {
            $this->params['NOREPORTLOCCHECK'] = true;
        }

        $additionalTokens = ['IA.BAL','IA.INC','IA.DB','IA.CR','IA.ACTIVE',
        'IA.INACTIVE','IA.YES','IA.NO','IA.ACCOUNT_NO','IA.TITLE','IA.TYPE','IA.BALANCE','IA.CLOSES_INTO','IA.REQUIRE_DEPARTMENT','IA.REQUIRE_LOCATION','IA.STATUS','IA.TAX_RETURN_CODE','IA.M_3_RETURN_CODE'];
        I18N::addTokens(I18N::tokenArrayToObjectArray($additionalTokens));
        I18N::getText();
    }

    /**
     * @return bool
     */
    function DoQuery() 
    {
        // DoMap is doing the job
        global $_userid;
     
        /* GET COMPANY INFORMATION */
        $company = GetAcctCompany($_userid);

        // Assume Entities address instead of Company address, 
        // if the current user is from MEGL and SLID_IN into an entity
        if( IsMultiEntityCompany() && GetContextLocation() ) {
            $conloc = GetContextLocationDetails();
            $company['NAME'] = $conloc['REPORTPRINTAS'];
        }

        list( ,$cny) = explode('@', $_userid);
        // Get the logo if specified
        if ($this->params['HIDELOGO'] == 'false') {
            $logo = $company['LOGO'];
            if (isl_trim($logo)) {
                $logoURL = getResourceUrl("/acct/servecnylogo.phtml?.filename=cnylogo_g.gif&cny=" . $cny);
                $company['LOGO'] = $logoURL;
            }
        }
        else  {
            $company['LOGO']='';
        }
                                
        //Form Select clause
        $select = "SELECT A.RECORD#, A.ACCT_NO ACCT_NO,A.TITLE TITLE";

        $queryparams = array();
        $queryparamno = 0;

        //Form Where clause
        $where = " A.CNY# = :".++$queryparamno;
        array_push($queryparams, GetMyCompany());

        $_actstrt = $this->params['STARTINGACCOUNTNUMBER'];
        $_actend = $this->params['ENDINGACCOUNTNUMBER'];
        $_acttype = $this->params['ACCOUNTTYPE'];
        $_nbalance = $this->params['NORMALBALANCE'];
        $_closeable = $this->params['PERIODENDCLOSINGTYPE'];
        $_status = $this->params['STATUS'];
        $_requiredept = $this->params['REQUIREDEPT'];
        $_requireloc = $this->params['REQUIRELOC'];
        $_includeTaxCode = $this->params['INCLUDETAXCODE'];

         // Build where clause if any parameters were passed.
        if(isl_trim($_actstrt)) {
            if (isl_strstr($_actstrt, '--')) {
                list($actno) = explode('--', isl_trim($_actstrt));
            }
            $where .= " AND A.ACCT_NO >= :".++$queryparamno;
            /** @noinspection PhpUndefinedVariableInspection */
            array_push($queryparams, isl_trim($actno));
        }

        if(isl_trim($_actend)) {
            if (isl_strstr($_actend, '--')) {
                list($actno) = explode('--', isl_trim($_actend));
            }
            $where .= " AND A.ACCT_NO <= :".++$queryparamno;
            /** @noinspection PhpUndefinedVariableInspection */
            array_push($queryparams, isl_trim($actno));
        }

        if (!isset($_acttype)) {
            $select .= ",A.ACCOUNT_TYPE ACCOUNT_TYPE,A.NORMAL_BALANCE NORMAL_BALANCE,B.ACCT_NO CLOSETOACCTKEY, A.STATISTICAL";
        } else if(isset($_acttype) && $_acttype != 'S') {
            $select .= ",A.ACCOUNT_TYPE ACCOUNT_TYPE,A.NORMAL_BALANCE NORMAL_BALANCE,B.ACCT_NO CLOSETOACCTKEY, A.STATISTICAL";
            $where.=" AND A.STATISTICAL = :".++$queryparamno;
            array_push($queryparams, 'F');

            if(isset($_closeable) && ($_closeable != 'A')) { 
                $where .= " AND A.CLOSEABLE = :".++$queryparamno;
                array_push($queryparams, $_closeable);
            }
            if(isset($_acttype) && ($_acttype != 'A')) { 
                $where .= " AND A.ACCOUNT_TYPE = :".++$queryparamno;
                array_push($queryparams, $_acttype);
            }
            if(isset($_nbalance) && ($_nbalance != 'A')) { 
                $where .= " AND A.NORMAL_BALANCE= :".++$queryparamno; 
                array_push($queryparams, $_nbalance);
            }
            
        }
        elseif($_acttype == 'S') {
            $where .=" AND A.STATISTICAL = :".++$queryparamno;
            array_push($queryparams, 'T');
            $this->isstatistical=true;
        }
        if ($_requiredept == 'true') {
            $where .=" AND A.REQUIREDEPT = :".++$queryparamno;
            array_push($queryparams, 'T');
        }
        if ($_requireloc == 'true') {
            $where .=" AND A.REQUIRELOC = :".++$queryparamno;
            array_push($queryparams, 'T');
        }

        if (departmentsExist()) {
            $select .= ",A.REQUIREDEPT";
        }
        if (locationsExist()) {
            $select .= ",A.REQUIRELOC";
        }

        if ($_includeTaxCode == 'true' && !$this->isstatistical) {
            $select .= ",A.TAXCODE, A.MRCCODE";
            $this->includeTaxCode = true;
        }
        
        if(isset($_status) && ($_status != 'A')) { 
            $where .= " AND A.STATUS = :".++$queryparamno;
            array_push($queryparams, $_status);
        }
        elseif($_status != 'A') {
            $where .= " AND A.STATUS = :".++$queryparamno;
            array_push($queryparams, 'T');
        }
        else  {
            $select .= ",A.STATUS STATUS";
            $this->requirestatus = true;
        }
        //Form FROM statement
        $select .= " FROM BASEACCOUNT A";

        //Form WHERE statement upon query
        $wherest = " WHERE";
        //If the record is not Statistical then CloseToAccount to be selected using left outer join
        if(!isset($_acttype) || (isset($_acttype) && $_acttype != 'S')) {
            $select .= ", BASEACCOUNT B";
            $wherest = " WHERE B.RECORD#(+)=A.CLOSETOACCTKEY AND B.CNY#(+)=A.CNY# AND";
        }
        $where=$wherest.$where;

        // Form Order by clause
        $sort = " ORDER BY ACCT_NO";
        //Form Complete Binding Query to be run
        $query = array($select.$where.$sort);

        //Add parameters for Query
        $query = INTACCTarray_merge($query, $queryparams);

        // Get the accounts
        $glaccounts = QueryResult($query);

        // Convert type and balance to meaningful text
        $kActType = array('N'    => 'IA.BAL', 'I'    => 'IA.INC');
        $kNormalBalance = array('1' => 'IA.DB', '-1' => 'IA.CR');
        $kAcctstatus = array('T' => 'IA.ACTIVE', 'F' => 'IA.INACTIVE');
        $kRequire = array('T' => 'IA.YES', 'F' => 'IA.NO');
        foreach($glaccounts as $key=> $acc){
            $glaccounts[$key]['NORMAL_BALANCE'] = $kNormalBalance[$acc['NORMAL_BALANCE']] ? I18N::getSingleToken($kNormalBalance[$acc['NORMAL_BALANCE']]) : '';
            $glaccounts[$key]['ACCOUNT_TYPE'] = $kActType[$acc['ACCOUNT_TYPE']] ? I18N::getSingleToken($kActType[$acc['ACCOUNT_TYPE']]) : '';
            if ($this->requirestatus) {
                $glaccounts[$key]['STATUS'] = I18N::getSingleToken($kAcctstatus[$acc['STATUS']]);
            }
            if (departmentsExist()) {
                $glaccounts[$key]['REQUIREDEPT'] = $kRequire[$acc['REQUIREDEPT']] ? I18N::getSingleToken($kRequire[$acc['REQUIREDEPT']]) : '';
            }
            if (locationsExist()) {
                $glaccounts[$key]['REQUIRELOC'] = $kRequire[$acc['REQUIRELOC']] ? I18N::getSingleToken($kRequire[$acc['REQUIRELOC']]) : '';
            }
            if(!$this->isstatistical) {
                if(!$glaccounts[$key]['NORMAL_BALANCE']) {
                    $glaccounts[$key]['NORMAL_BALANCE'] = '--';
                }
                if(!$glaccounts[$key]['ACCOUNT_TYPE']) {
                    $glaccounts[$key]['ACCOUNT_TYPE'] = '--';
                }
            }

        }

        $this->rawdata = $glaccounts;
        $this->company = $company;
        return true;

    }

    /**
     * @return array
     */
    function DoMap() 
    {
        $lines = array();
        logFL("JPC - params");
        logFL($this->params);

        $lines['report']['0']['report_format'] = $this->params['type'];
        $lines['report']['0']['sess'] = $this->sess;
        $lines['report']['0']['op'] = $this->op;
        $lines['report']['0']['title'] = $this->title;
        $lines['report']['0']['title2'] = $this->title2;
        $lines['report']['0']['titlecomment'] = $this->titlecomment;
        $orientation = 'Portrait';
        if(isset($this->params['ORIENTATION']) && $this->params['ORIENTATION']!='') {
            $orientation = $this->params['ORIENTATION'];
        }

        $lines['report']['0']['orientation'] = $orientation;
        $lines['report']['0']['header_col1'] = I18N::getSingleToken('IA.ACCOUNT_NO');
        $lines['report']['0']['header_col2'] = I18N::getSingleToken('IA.TITLE');

        if(!$this->isstatistical) {
            $lines['report']['0']['isstatistical'] = 0;
            $lines['report']['0']['header_col3'] = I18N::getSingleToken('IA.TYPE');
            $lines['report']['0']['header_col4'] = I18N::getSingleToken('IA.BALANCE');
            $lines['report']['0']['header_col5'] = I18N::getSingleToken('IA.CLOSES_INTO');
        }

        if (departmentsExist()) {
            $lines['report']['0']['requiredept'] = 1;
            $lines['report']['0']['header_col6'] = I18N::getSingleToken('IA.REQUIRE_DEPARTMENT');
        }
        if (locationsExist()) {
            $lines['report']['0']['requireloc'] = 1; 
            $lines['report']['0']['header_col7'] = I18N::getSingleToken('IA.REQUIRE_LOCATION');
        }
        if ($this->requirestatus) {
            $lines['report']['0']['requirestatus'] = 1; 
            $lines['report']['0']['header_col8'] = I18N::getSingleToken('IA.STATUS');
        }
        if ($this->includeTaxCode) {
            $lines['report']['0']['includeTaxCode'] = 1; 
            $lines['report']['0']['header_col5a'] = I18N::getSingleToken('IA.TAX_RETURN_CODE');
            $lines['report']['0']['header_col5b'] = I18N::getSingleToken('IA.M_3_RETURN_CODE');
        }

        //For printing LOGO
        $lines['report']['0']['logo'] = $this->company['LOGO'];
        $lines['report']['0']['co'] = $this->company['NAME'];


        //To display Created Date and Time
        $lines['report']['0']['reportdate'] = GetCurrentDate(IADATE_USRFORMAT);
        if(Profile::getUserCacheProperty('USERPREF', 'TIMEFORMAT') == 'HH24:MI:SS') {
            $lines['report']["0"]['reporttime'] = date("G:i T");
        } else {
            $lines['report']["0"]['reporttime'] = date("g:i A T");
        }

        if(is_countable($this->rawdata) && !count($this->rawdata)) {
            $reportdata = array();
            $reportdata['NODATA']['0'] = array('NODATA'=>'1');
            $lines['report']['0']['ENTITY'] = $reportdata;
            return $lines; 
        }

        //To findout id for general ledger report. It will be used for Drill down Acct no.
        $id = GetOperationId('gl/reports/glledger');
        $values = array();
        
        //to decide whether parameter for statistical account to be added
        $statparam='';
        if ($this->isstatistical) {
            $statparam='&.statAccts=1';
        }
        
        foreach($this->rawdata as $row) {
            //Form URL to drill down
            $row['ACCT_NO_URL'] ='reportor.phtml?.type=html&.op='. $id .'&.ac=' . $row['RECORD#'] .'&.sess='.$this->sess
                . '&.drillfilter=1&_obj__displayAllAccts=' . URLCleanParams::insert('_obj__displayAllAccts', 'Y') . $statparam; 
            $row['ACCT_NO_MOUSEOVER'] = 'window.status="Display General Ledger for Account No :'.$row['ACCT_NO'].'";return true;';
            $values[] = array (
             'ACCOUNTNO' => $row['ACCT_NO'],
             'ACCOUNTNOURL' => $row['ACCT_NO_URL'],
             'ACCOUNTNOMOUSEOVER' => $row['ACCT_NO_MOUSEOVER'],
             'TITLE' => $row['TITLE'],
             'ACCOUNTTYPE' => $row['ACCOUNT_TYPE'],
             'NORMALBALANCE' => $row['NORMAL_BALANCE'],
             'CLOSETOACCTKEY' => $row['CLOSETOACCTKEY'],
             'TAXCODE'        => $row['TAXCODE'],
             'MRCCODE'        => $row['MRCCODE'],
             'REQUIREDEPT' => $row['REQUIREDEPT'],
             'REQUIRELOC' => $row['REQUIRELOC'],
             'STATUS' => $row['STATUS']
            );
        }
        $reportdata = array (
            'GLACCOUNT' => array (
        array (
                    'VALUES' => $values,
        ),
            ),
        );

        $lines['report']['0']['ENTITY'] = $reportdata;
        return $lines;

    }

} 


