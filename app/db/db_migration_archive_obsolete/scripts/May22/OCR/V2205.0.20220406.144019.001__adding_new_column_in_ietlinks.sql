ALTER TABLE IETLINKS ADD ACTUALPAYMENTKEY NUMBER(15,0)
/
--Foriegnkey on prrecord table need to be added
CREATE INDEX IX_IETLINKS_ACTUALPAYMENTKEY ON IETLINKS (CNY#, ACTUALPAYMENTKEY) TABLESPACE ACCTINDX
/

--STEP1 - At first get all the IETLINKS table data as below
--STEP2 - Now from the above query we will fetch paymentkey, payitemkey and amount, based on these values we will try to find the linking
--record for this payment key (i.e if the payment key we were able to find the record in prentrypymtrecs as paymentkey, Now we have to find the resepectinve recordkey and its offset line in ietlinks table)
--then we have to find the type 'S' or 'T' in ietlinks table.
--STEP3 - Now we have to update both the rows, one we got from the prentrypymtrecs and the the paymentkey loop from ieltlins table


DECLARE
    ielrecordkey    NUMBER := 0;
    ielitemkey   NUMBER := 0;
    ielamount   NUMBER := 0;
    CURSOR ieldatas IS SELECT
                           iel.cny#,
                           iel.paymentkey,
                           iel.payitemkey,
                           iel.amount
                       FROM
                           ietlinks iel;

BEGIN
    FOR ieldata IN ieldatas LOOP
        BEGIN
            FOR ielrecordkeydata IN (SELECT
                                pymt.recordkey as recordkey,
                                recordkeyoffset.offsetkey as itemkey,
                                pymt.amount as amount
                            FROM
                                prentrypymtrecs pymt,
                                prentryoffset pymtoffset,
                                prentryoffset recordkeyoffset
                            WHERE
                                pymt.cny# = ieldata.cny#
                                AND pymt.cny# = pymtoffset.cny#
                                AND pymt.cny# = recordkeyoffset.cny#
                                AND pymt.paymentkey = ieldata.paymentkey
                                AND pymtoffset.offsetkey = ieldata.payitemkey
                                AND pymt.payitemkey = pymtoffset.prentrykey
                                AND recordkeyoffset.prentrykey = pymt.paiditemkey)
            LOOP
                UPDATE ietlinks
                SET
                    actualpaymentkey = ieldata.paymentkey
                WHERE
                    cny# = ieldata.cny#
                    AND paymentkey = ielrecordkeydata.recordkey
                    AND payitemkey = ielrecordkeydata.itemkey
                    AND amount = ielrecordkeydata.amount;
            END LOOP;
        END;

        UPDATE ietlinks
        SET
            actualpaymentkey = ieldata.paymentkey
        WHERE
            cny# = ieldata.cny#
            AND paymentkey = ieldata.paymentkey
            AND payitemkey = ieldata.payitemkey
            AND amount = ieldata.amount
            AND actualpaymentkey IS NULL;

    END LOOP;
END;
/