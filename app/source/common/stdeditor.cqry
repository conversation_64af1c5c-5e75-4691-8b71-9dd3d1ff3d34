<?
/**
 *    FILE:
 *    AUTHOR:
 *    DESCRIPTION:
 *
 *    (C) 2000, Intacct Corporation, All Rights Reserved
 *
 *    This document contains trade secret data that belongs to Intacct
 *    Corporation and is protected by the copyright laws.  Information
 *    herein may not be used, copied or disclosed in whole or in part
 *    without prior written consent from Intacct Corporation.
 */


$kStdEditorQueries['QRY_COMPANY_GETALL'] = array(
    'QUERY' => 'select name as title,contactname ,contactphone,
			address1, address2, address3, city, zipcode, state, country, logo,
			message_text, marketing_text, fax, federalid, taxid
	 from company, acctcompany where record# = ? and acctcompany.cny# = ? ',
     'ARGTYPES' => array('integer', 'integer')
);

$kStdEditorQueries['QRY_COMPANY_GETALL_MAILMERGE'] = array(
    'QUERY' => 'select title, name, legalname, legalcountry, created, legalzipcode, type,status,
            record# as recordno, contactemail, howheard, otherapps, authcode,
            legaladdress1, legaladdress2, legaladdress3, legalcity, legalstate, pin, notifycnt, failedauthcnt,
            contactname ,contactphone,
            address1, address2, address3, city, zipcode, state, country, logo,
            message_text, marketing_text, fax, federalid, taxid
            from company, acctcompany where record# = ? and acctcompany.cny# = ? ',
    'ARGTYPES' => array('integer', 'integer')
);

