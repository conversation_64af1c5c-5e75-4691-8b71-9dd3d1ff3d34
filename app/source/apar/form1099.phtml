<?
//=============================================================================
//
//	FILE:			form1099.phtml
//	AUTHOR:			
//	DESCRIPTION:	Generates the form 1099 Report using the criteria
//
//	(C)2000, Intacct Corporation, All Rights Reserved
//
//	Intacct Corporation Proprietary Information.
//	This document contains trade secret data that belongs to Intacct 
//	corporation and is protected by the copyright laws. Information herein 
//	may not be used, copied or disclosed in whole or part without prior 
//	written consent from Intacct Corporation.
//
//=============================================================================

 

//*******  REQUIRE  ************************************************************
require_once 'util.inc';
require_once 'backend_form1099.inc';
require_once 'backend_company.inc';
require_once 'backend_pdf.inc';
require_once 'xmlweb.inc';

Init();
$_pickent = Request::$r->_pickent;
// PHP 5.4 note:  I removed the & from the company parameter but the function signature doesn't even
// come close to matching GetForm1099Arr in backend_form1099.inc so it's not going to work anyway.
$repMap = GetForm1099Arr($_start_vendor, $_end_vendor, $_reporting_year, $_formtype, $company, $_pickent);
if ($repMap) {
    $xml = GetForm1099XML($company, $repMap, array());
    if ($xml) {
        $fo = GetForm1099FO($xml, $_formtype, $_reporting_year);
        if($fo == false) {return false;
        }    
        genPDF($fo);
    }
}
else { 
    ?>
<html>
<head>
  <title>Intacct - Form 1099</title>
</head>
<body><center>No data found for the selected criteria.</center></body>
</html>
    <?
}
exit();

