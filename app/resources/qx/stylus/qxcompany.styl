/**
 * qxcompany.styl
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Intacct Corporation -- All Rights Reserved
 */
/******************************************************************************
 *
 */

.quixote {
  regular_font(14px)
  min-height 100%
  background-color col-co-page-background
  color col-co-page-font-color
  overflow auto

  & .qx-page-container {
    height auto
  }

  & .qx-page-header {
    & .header_title {
      regular_font(26px)
      color col-intacct-page-header-color
    }
  }

  & .qx-co-wrapper {
    min-width 960px
    max-width 1200px
    &.qx-co-subscription-wrapper {
      margin-top 50px
      margin-left 16px
    }
  }

  & .qx-import-delimiter {
      color col-intacct-field-border-color
      padding-left 8px
      padding-right 8px
  }

  & .qx-co-section {
    background col-co-container-background
    border 1px solid col-co-container-border
    width 100%
    display block
    border-radius(radius-section)
    padding 20px
    margin-bottom 20px
    &.qx-co-table-cell-block {
      & > tbody {
        display block
        & > tr {
          display block
          width 100%
          margin-bottom -1px //to overwrite bottom border
          & > td {
            display block
          }
        }
      }
    }
    & select {
      padding 2px 5px
      background col-co-select-background
      border-radius(radius-input)
    }
    & select,
    & .input-group {
      margin-left 10px
    }

    & br {
      display none
    }

    & .qx-icon-done {
      position relative

      & img {
        visibility hidden
      }

      &:before {
        content "\f058"
        font-family 'Font Awesome 5 Pro'
        color col-co-icon-color
        font-size 20px
      }
    }

    & table:not(.qx-co-container-white) .multiline_bg_beige,
    & table:not(.qx-co-container-white) .multiline_bg_white {
      background col-co-container-background
      margin-bottom 5px
      padding 12px
      &:hover {
        background col-co-section-background-highlight
      }

      & td {
        display table-cell
      }
    }

    & table.qx-co-container-white {
      margin-bottom 30px
      margin-left 25px
      & td {
        display table-cell
      }
    }

    & b {
      font-weight normal
    }

    & .header_title {
      & b {
        medium_font(16px)
        color col-co-page-font-color
      }
    }

    & .field_list_data {
      margin-bottom 20px
    }

    &.qx-co-align-input-boxes {
      input[type="checkbox"],
      input[type="radio"] {
        position relative
        top 1px
        right 5px
        accent-color col-radiocheckbox-selected-color
      }
    }
  }

  & .qx-co-import-data {
    & .checkbox {
      padding-left 40px
      padding-top 5px
    }
  }
  //Subscriptions and Permissions from Subscriptions
  & .qx-co-table-list {
    border 0
    &.qx-co-wrapper {
      qx-fit-content()
    }
    & tbody {
      & tr {
        border-bottom 1px solid col-intacct-field-border-color
        background col-co-container-background
        &.Column,
        &.multiline_header_bg {
          border-bottom 1px solid col-intacct-field-border-color
          border-top 1px solid col-intacct-field-border-color
          background col-form-page-background
          & td {
            medium_font(16px)
            & * {
              color col-co-page-font-color
              medium_font(16px)
              font-weight normal
            }
            &.qx-co-perm-radio-controls {
              & > label,
              & > font {
                &.poweredby, * {
                  margin-right 10px
                  margin-left 0
                  regular_font(12px)
                }
              }
              & > b {
                &.poweredby, * {
                  margin-right 5px
                }
              }
            }
          }
        }
        &:hover {
          background col-form-page-background !important
        }
        & td {
          padding 10px
          color col-co-section-title
          display table-cell
          regular_font(14px)
          & * {
            & :not(i.fa) {
              regular_font(14 px)
            }
          }
          & label,
          & > font.poweredby {
            color col-co-section-title
          }
          & input[type="checkbox"] {
            margin 0px
            accent-color col-radiocheckbox-selected-color
            & + label {
              regular_font(12px)
              margin-bottom 2px
            }
          }

        }
      }
    }

    &.qx-co-ID-table {
      & tr.multiline_header_bg {
        margin-bottom 0
        & font {
          margin-left 15px

        }
      }
      & tr.multiline_bg_beige,
      & tr.multiline_bg_white {
        & td {
          &:nth-child(1) {
            padding 0
            & img {
              display none
            }
          }
          &:nth-child(2) {

            & a {
              margin 0

            }
          }
        }
        &.qx-co-ID-dropdown-in-row {
          & td {
            padding 15px
            text-align left
            & .lineitem_title {
              margin-left 10px
            }
          }
          &:hover {
            background col-co-container-background !important
          }
        }
        &.qx-co-ID-has-table-in-row {
          border-bottom none
        }
        &.qx-co-ID-table-in-row {
          & > td {
            text-align left !important
            padding-left 25px
          }

          &:hover {
            background col-co-container-background !important
          }
          & tr {
            margin 0
            border 0
            &:hover {
              background col-co-container-background !important
            }
          }
        }
      }
    }
    &.qx-co-main-first-row {
      & tbody {
        & tr {
          & td {
            &:nth-child(1) {
              width 25%
              text-align left
            }
          }
        }
      }
    }
    &.qx-co-main-second-row {
      & tbody {
        & tr {
          & td {
            &:nth-child(2) {
              width 100%
              text-align left
            }
          }
        }
      }
    }
  }
  & .qx-co-table-navtabs {
    // because inline style
    border 0 !important
    & > tbody {
      & > tr {
        & > td {
          padding 0
          &:not(:first-child) {
            padding-left 20px
          }
        }
      }
    }
    & table > tbody > tr > td {
      display none
    }
    & .layoutTabOn,
    & .layoutTabOff {
      display block
      padding 5px
      background-color transparent
      border-bottom 2px solid transparent
      border-radius 0
      & a {
        text-decoration none
        color col-navtab-link-color
        medium_font(16px)
        //because inline style
        font-size 16px !important
      }
    }

    & .layoutTabOn {
      border-bottom 2px solid col-intacct-tab-active-color
      outline none
      & a {
        color col-navtab-link-color
      }
    }

  }
  & .qx-co-table-title {
    border 0
    background col-form-page-background
    & td {
      padding 5px
    }
    & .lineitem_title {
      medium_font(18px)
      font-weight normal
      color col-co-page-font-color
    }

  }
  & .qx-co-table-permissions {
    & tbody {
      & tr {
        &.multiline_header_bg {
          & td {
            color col-co-page-font-color
            medium_font(14px)
            & * {
              medium_font(14px)
            }
          }
          &:nth-child(2) {
            & td {
              &.poweredby {
                regular_font(12px)
                color col-co-default-text-color !important
                font-size 12px !important
              }
            }
          }
          &.qx-co-multilineheader-1 {
            border-bottom none
          }
          &.qx-co-multilineheader-2 {
            border-top none
          }
        }
      }
    }

  }
  & .qx-co-table-help-link {
    regular_font(12px)
    // because inline style
    font-size 12px !important
    float right
    text-decoration underline !important
  }
  & .qx-co-table-permtemplate {
    & td {
      &.label_cell {
        width 17%
        white-space nowrap
      }
      &.value_cell {
        width 100%
      }
    }
  }
  & .qx-no-border {
    border 0
  }
  & .field_list_data {
    border-radius(radius-section)
  }
  & .label_info {
    background col-co-container-background
    color col-co-section-title
    medium_font(12px)
    &:first-of-type {
      border-radius(radius-section)
    }
  }
  & .value_cell {
    regular_font(12px)
    &:last-child {
      width 48%
    }
  }
  & input {
    height auto
  }
}
