<?php
/**
 * File ARRecordGetAllBatch.cls contains the class ARRecordGetAllBatch
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000-2013 Intacct Corporation
 *
 * This document contains trade secret data that belongs to Intacct
 * Corporation and is protected by the copyright laws.  Information
 * herein may not be used, copied or disclosed in whole or in part
 * without prior written consent from Intacct Corporation.
 */


class CMRecordGetAllBatch extends UnionBaseBatch
{

    /**
     * @param string $entity
     * @param int    $readTimestamp
     * @param int    $pageSize
     * @param array  $params
     */
    function __construct( $entity, $readTimestamp, $pageSize, $params = array() )
    {
        $this->configurationArray = UnionIteratorConfigurator::getBatchConfigurationArray()['cmrecord'];
        parent::__construct($entity, $readTimestamp, $pageSize, $params);
    }

    /**
     * @param array $params
     *
     * @return RecordGetAllReportTypeBatch
     */
    protected function getReportTypeBatch( $params )
    {
        if ( DDS_DEBUG == 1 ) {
            impp('CMRecordGetAllReportTypeBatch', '');
        }
        return new RecordGetAllReportTypeBatch('cmrecord', $this->getReadTimestamp(), $this->getPageSize(), $params);
    }
}