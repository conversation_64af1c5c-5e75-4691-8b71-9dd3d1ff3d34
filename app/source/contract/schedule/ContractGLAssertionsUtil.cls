<?php

/**
 * Contract gl assertions
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2000 Intacct Corporation All, Rights Reserved
 */
class ContractGLAssertionsUtil extends ContractAssertionsUtil
{

    const DEBIT = "1";
    const CREDIT = "-1";

    // specific assertion can be close or open to throw error, along with this TURN_ON_ASSERTIONS should be on to throw error
    const ASSERT_RECOGNITION_ERROR = true;
    const ASSERT_BASE_AMOUNTS_ERROR = true;
    const ASSERT_INVOICE_ERROR = true;
    const ASSERT_PAYMENT_ERROR = true;
    const ASSERT_MEA_ERROR = true;
    const INPUT_ERROR_IN_ASSERTION = true;
    //////////////////////////////////////////////////////
    /** @var array $reclassJEs */
    private static $reclassJEs = [];

    /** @var array $rootTxnJEs */
    private static $rootTxnJEs = [];

    /** @var array $meaScheduleLinks */
    private static $meaScheduleLinks = [];

    /** @var array $meaDistributionCache */
    private static $meaDistributionCache = [];

    /**
     * @var array $contrailDetails;
     */
    private static $contrailDetails = [];

    /**
     * @var array $cacheOriginalPaymentByBSE
     */
    private static $cacheOriginalPaymentByBSE = [];
    
    /**
     * @param float       $amount
     * @param string      $eventType
     * @param string      $journalCode
     * @param string      $lineKey
     * @param int|null    $srcFK1
     * @param int|null    $srcFK2
     *
     * @param null|string $transactionDate
     *
     * @throws IAException
     */
    public static function assert($amount, $eventType, $journalCode, $lineKey, $srcFK1 = null, $srcFK2 = null, $transactionDate = null)
    {
        LogToFile("Method: " . __FUNCTION__ . "(" . json_encode(func_get_args()) . ")");
        
        $lineDetail = self::getContrailDetail($lineKey);
        $THROW_INPUT_ERROR = self::INPUT_ERROR_IN_ASSERTION;
        
        if (empty($lineDetail)) {
            $errMsg = sprintf(
                'We found an internal  issue with the contract line data for  lineKey %d. The contract line was not found. Contact ' .
                  'your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',
                $lineKey
            );
            $errorInfo = new ContractI18nErrorInfo('CN-0818', ['LINE_KEY' => $lineKey], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_INPUT_ERROR);
        }
        
        $reclassJEs = null;
        self::getReclassJEs($eventType, $journalCode, $lineKey, $reclassJEs, $srcFK1, $srcFK2);
        
        // It is possible that are no reclass JEs
        if ($reclassJEs) {
            $isMCP = false;
            if (ContractUtil::isMCPEnabled()) {
                if (!(isset($lineDetail['CURRENCY']) && isset($lineDetail['BASECURR']))) {
                    $errMsg = "CURRENCY or BASECURR is not set";
                    $errorInfo = new ContractI18nErrorInfo('CN-0819', [], $errMsg);
                    self::reportAssertError($errorInfo, func_get_args(), $THROW_INPUT_ERROR);
                    
                    return;
                }
                
                $isMCP = $lineDetail['CURRENCY'] != $lineDetail['BASECURR'];
            }
            
            // Assert exchange rate calc
            self::assertBaseAmounts($reclassJEs, $lineDetail, $isMCP, [$amount, $eventType, $journalCode, $lineKey, $srcFK1, $srcFK2]);
        } else {
            if (
                $eventType == ContractGLReclassEvent::EVENTTYPE_ONPAYMENT
                || $eventType == ContractGLReclassEvent::EVENTTYPE_ONPAYMENTREVERSAL
            ) {
                // if there is no reclass there no need to assert payment je as in payment contract deal with only reclass not AR
                return;
            }
        }
        
        //all account are not unique then by-pass the assertion
        $accountKeys = [
            $lineDetail['DRUNBILLEDACCTKEY'], $lineDetail['DRBILLEDACCTKEY'], $lineDetail['DRPAIDACCTKEY'],
            $lineDetail['SALESUNBILLEDACCTKEY'], $lineDetail['SALESBILLEDACCTKEY'], $lineDetail['SALESPAIDACCTKEY'],
            $lineDetail['ARUNBILLEDACCTKEY'], $lineDetail['ARBILLEDACCTKEY'],
        ];
        if (count(array_unique($accountKeys)) != 8) {
            return;
        }
        
        // Assert JE Debit and Credits
        $txnJEs = null;
        if ($eventType == ContractGLReclassEvent::EVENTTYPE_ONMEA || $eventType == ContractGLReclassEvent::EVENTTYPE_ONKIT) {
            $txnJEs = null; // in case of mea there will be no transaction JE, on reclass JE will be assert again amount
        } else {
            self::getTransactionJEs($eventType, $journalCode, $lineKey, $txnJEs, $srcFK1, $srcFK2);
        }
        
        switch ($eventType) {
            // Assert affected Deferred and Sales balances
            case ContractGLReclassEvent::EVENTTYPE_ONRECOGNITION :
                self::assertRecognitionJEs($amount, $journalCode, $lineKey, $srcFK1, $reclassJEs, $txnJEs, $lineDetail);
                break;
            case ContractGLReclassEvent::SUBEVENTTYPE_ONRECOGNITION :
                self::assertUndoRecognitionJEs($amount, $journalCode, $lineKey, $srcFK1, $reclassJEs, $txnJEs, $lineDetail);
                break;
            case ContractGLReclassEvent::EVENTTYPE_ONINVOICE :
                self::assertInvoiceJEs($amount, $journalCode, $lineKey, $srcFK1, $reclassJEs, $txnJEs, $lineDetail);
                break;
            case ContractGLReclassEvent::EVENTTYPE_ONPAYMENT :
                // self::assertPaymentJEs($amount, $journalCode, $lineKey, $srcFK1, $srcFK2, $reclassJEs, $lineDetail, $transactionDate);
                break;
            case ContractGLReclassEvent::EVENTTYPE_ONPAYMENTREVERSAL;
                // self::assertPaymentReversalJEs($amount, $journalCode, $lineKey, $srcFK1, $srcFK2, $reclassJEs, $lineDetail);
                break;
            case ContractGLReclassEvent::EVENTTYPE_ONMEA:
            case ContractGLReclassEvent::EVENTTYPE_ONKIT:
                self::assertMEAJEs($eventType, $amount, $journalCode, $lineKey, $reclassJEs, $lineDetail);
                break;
        }
    }

    /**
     * @param float      $amount
     * @param string     $journalCode
     * @param int        $lineKey
     * @param int        $rseKey
     * @param array|null $reclassJEs
     * @param array|null $recognitionJE
     * @param array      $lineDetail
     *
     * @throws IAException
     */
    public static function assertRecognitionJEs($amount, $journalCode, $lineKey, $rseKey, $reclassJEs, $recognitionJE, $lineDetail)
    {
        LogToFile("Method: ".__FUNCTION__."(".json_encode(func_get_args()).")");

        $THROW_ERROR = self::ASSERT_RECOGNITION_ERROR; // Turn it ON to throw error in prod.

        if ($recognitionJE == null) {
            $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d. The revenue journal entry could not be found. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.', $lineDetail['CONTRACTID'], $lineDetail['LINENO']);
            $errorInfo = new ContractI18nErrorInfo('CN-0820', ['CONTRACTID' => $lineDetail['CONTRACTID'], 'LINENO' => $lineDetail['LINENO']], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);

            return;
        }

        $duAcctKey = $lineDetail['DRUNBILLEDACCTKEY'];
        $dbAcctKey = $lineDetail['DRBILLEDACCTKEY'];
        $dpAcctKey = $lineDetail['DRPAIDACCTKEY'];

        $suAcctKey = $lineDetail['SALESUNBILLEDACCTKEY'];
        $sbAcctKey = $lineDetail['SALESBILLEDACCTKEY'];
        $spAcctKey = $lineDetail['SALESPAIDACCTKEY'];

        if ($duAcctKey == null || $dbAcctKey == null || $dpAcctKey == null || $suAcctKey == null || $sbAcctKey == null || $spAcctKey == null) {
            $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d. Not all acccount keys are passed. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',$lineDetail['CONTRACTID'],$lineDetail['LINENO']);
            $errorInfo = new ContractI18nErrorInfo('CN-0821', ['CONTRACTID' => $lineDetail['CONTRACTID'], 'LINENO' => $lineDetail['LINENO']], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);

            return;
        }

        $allJEs = $recognitionJE;
        if ($reclassJEs != null) {
            $allJEs = array_merge($recognitionJE, $reclassJEs);
        }

        $reverseSign = false;
        if($amount < 0){
            $reverseSign = true;
        }
        $netBalances = self::findNetJEBalances($allJEs, $reverseSign);

        $totalDebitToDeferred = 0;

        if (self::isNETBalanceAvailableToUse($netBalances, $duAcctKey)) {
            $totalDebitToDeferred = ibcadd($totalDebitToDeferred, $netBalances[$duAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $duAcctKey);
        }

        if (self::isNETBalanceAvailableToUse($netBalances, $dbAcctKey)) {
            $totalDebitToDeferred = ibcadd($totalDebitToDeferred, $netBalances[$dbAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $dbAcctKey);
        }

        if (self::isNETBalanceAvailableToUse($netBalances, $dpAcctKey)) {
            $totalDebitToDeferred = ibcadd($totalDebitToDeferred, $netBalances[$dpAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $dpAcctKey);
        }


        if ( !self::isEqual($totalDebitToDeferred, $amount) ) {
            $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d.  The total debit amount { %3$01.2f } to deferred revenue for revenue schedule entry key { %4$d } does not equal the recognition amount  { %5$01.2f }. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.', $lineDetail['CONTRACTID'],$lineDetail['LINENO'],ibcabs($totalDebitToDeferred),$rseKey,ibcabs($amount));
            $errorInfo = new ContractI18nErrorInfo('CN-0822',
                                                   ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                    'LINENO' => $lineDetail['LINENO'],
                                                    'DEBIT_AMOUNT' => sprintf('%01.2f', ibcabs($totalDebitToDeferred)),
                                                    'SCHEDULE_ENTRY_KEY' => $rseKey,
                                                    'RECOGNITION_AMOUNT' => sprintf('%01.2f', ibcabs($amount))],
                                                   $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);

            return;
        }

        $totalCreditToSales = 0;

        if (self::isNETBalanceAvailableToUse($netBalances, $suAcctKey)) {
            $totalCreditToSales = ibcadd($totalCreditToSales, $netBalances[$suAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $suAcctKey);
        }

        if (self::isNETBalanceAvailableToUse($netBalances, $sbAcctKey)) {
            $totalCreditToSales = ibcadd($totalCreditToSales, $netBalances[$sbAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $sbAcctKey);
        }

        if (self::isNETBalanceAvailableToUse($netBalances, $spAcctKey)) {
            $totalCreditToSales = ibcadd($totalCreditToSales, $netBalances[$spAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $spAcctKey);
        }

        if ( !self::isEqual($totalCreditToSales, $amount) ) {
            $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d.  The total credit amount { %3$01.2f } to sales revenue for revenue schedule entry key { %4$d } does not equal the recognition amount  { %5$01.2f }. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',$lineDetail['CONTRACTID'], $lineDetail['LINENO'],ibcabs($totalCreditToSales),$rseKey,ibcabs($amount));
            $errorInfo = new ContractI18nErrorInfo('CN-0823',
                                                   ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                    'LINENO' => $lineDetail['LINENO'],
                                                    'CREDIT_AMOUNT' => sprintf('%01.2f', ibcabs($totalCreditToSales)),
                                                    'SCHEDULE_ENTRY_KEY' => $rseKey,
                                                    'RECOGNITION_AMOUNT' => sprintf('%01.2f', ibcabs($amount))],
                                                   $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);

            return;
        }
    }

    /**
     * @param float      $amount
     * @param string     $journalCode
     * @param int        $lineKey
     * @param int        $rseKey
     * @param array|null $reclassJEs
     * @param array|null $undoRrecognitionJE
     * @param array      $lineDetail
     *
     * @throws IAException
     */
    public static function assertUndoRecognitionJEs($amount, $journalCode, $lineKey, $rseKey, $reclassJEs, $undoRrecognitionJE, $lineDetail)
    {
        LogToFile("Method: ".__FUNCTION__."(".json_encode(func_get_args()).")");

        $THROW_ERROR = self::ASSERT_RECOGNITION_ERROR; // Turn it ON to throw error in prod.

        if ($undoRrecognitionJE == null) {
            $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d. The revenue journal entry could not be found. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.', $lineDetail['CONTRACTID'], $lineDetail['LINENO']);
            $errorInfo = new ContractI18nErrorInfo('CN-0824', ['CONTRACTID' => $lineDetail['CONTRACTID'], 'LINENO' => $lineDetail['LINENO']], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);

            return;
        }

        $duAcctKey = $lineDetail['DRUNBILLEDACCTKEY'];
        $dbAcctKey = $lineDetail['DRBILLEDACCTKEY'];
        $dpAcctKey = $lineDetail['DRPAIDACCTKEY'];

        $suAcctKey = $lineDetail['SALESUNBILLEDACCTKEY'];
        $sbAcctKey = $lineDetail['SALESBILLEDACCTKEY'];
        $spAcctKey = $lineDetail['SALESPAIDACCTKEY'];

        if ($duAcctKey == null || $dbAcctKey == null || $dpAcctKey == null || $suAcctKey == null || $sbAcctKey == null || $spAcctKey == null) {
            $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d. Not all acccount keys are passed. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',$lineDetail['CONTRACTID'],$lineDetail['LINENO']);
            $errorInfo = new ContractI18nErrorInfo('CN-0825', ['CONTRACTID' => $lineDetail['CONTRACTID'], 'LINENO' => $lineDetail['LINENO']], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);

            return;
        }

        $allJEs = $undoRrecognitionJE;
        if ($reclassJEs != null) {
            $allJEs = array_merge($undoRrecognitionJE, $reclassJEs);
        }

        $reverseSign = false;
        if($amount < 0){
            $reverseSign = true;
        }
        $netBalances = self::findNetJEBalances($allJEs, $reverseSign);;


        $totalDebitToDeferred = 0;

        if (self::isNETBalanceAvailableToUse($netBalances, $suAcctKey)) {
            $totalDebitToDeferred = ibcadd($totalDebitToDeferred, $netBalances[$suAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $suAcctKey);
        }

        if (self::isNETBalanceAvailableToUse($netBalances, $sbAcctKey)) {
            $totalDebitToDeferred = ibcadd($totalDebitToDeferred, $netBalances[$sbAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $sbAcctKey);
        }

        if (self::isNETBalanceAvailableToUse($netBalances, $spAcctKey)) {
            $totalDebitToDeferred = ibcadd($totalDebitToDeferred, $netBalances[$spAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $spAcctKey);
        }


        if ( !self::isEqual($totalDebitToDeferred, $amount) ) {
            $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d.  The total debit amount { %3$01.2f } to sales revenue for revenue schedule entry key { %4$d } does not equal the undo recognition amount  { %5$01.2f }. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',$lineDetail['CONTRACTID'], $lineDetail['LINENO'],ibcabs($totalDebitToDeferred),$rseKey,ibcabs($amount));
            $errorInfo = new ContractI18nErrorInfo('CN-0826',
                                                   ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                    'LINENO' => $lineDetail['LINENO'],
                                                    'DEBIT_AMOUNT' => sprintf('%01.2f', ibcabs($totalDebitToDeferred)),
                                                    'ENTRY_KEY' => $rseKey,
                                                    'RECOGNITION_AMOUNT' => sprintf('%01.2f', ibcabs($amount))],
                                                   $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);

            return;
        }

        $totalCreditToDeferred = 0;

        if (self::isNETBalanceAvailableToUse($netBalances, $duAcctKey)) {
            $totalCreditToDeferred = ibcadd($totalCreditToDeferred, $netBalances[$duAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $duAcctKey);
        }

        if (self::isNETBalanceAvailableToUse($netBalances, $dbAcctKey)) {
            $totalCreditToDeferred = ibcadd($totalCreditToDeferred, $netBalances[$dbAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $dbAcctKey);
        }

        if (self::isNETBalanceAvailableToUse($netBalances, $dpAcctKey)) {
            $totalCreditToDeferred = ibcadd($totalCreditToDeferred, $netBalances[$dpAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $dpAcctKey);
        }

        if ( !self::isEqual($totalCreditToDeferred, $amount) ) {
            $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d.  The total credit  amount { %3$01.2f } to deferred revenue for revenue schedule entry key { %4$d } does not equal the undo recognition amount  { %5$01.2f }. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.', $lineDetail['CONTRACTID'],$lineDetail['LINENO'],ibcabs($totalCreditToDeferred),$rseKey,ibcabs($amount));
            $errorInfo = new ContractI18nErrorInfo('CN-0827',
                                                   ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                    'LINENO' => $lineDetail['LINENO'],
                                                    'CREDIT_AMOUNT' => sprintf('%01.2f', ibcabs($totalCreditToDeferred)),
                                                    'ENTRY_KEY' => $rseKey,
                                                    'RECOGNITION_AMOUNT' => sprintf('%01.2f', ibcabs($amount))],
                                                   $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);

            return;
        }

    }

    /**
     * @param array $allJEs
     *
     * @param bool $reverseSign
     * @return array
     */
    public static function findNetJEBalances(&$allJEs , $reverseSign = false)
    {
        $netBalances = [];

        // CREDIT == Increase and DEBIT == Decrease

        if (isset($allJEs)) {
            foreach ($allJEs as $je) {

                if ( (!$reverseSign && $je['TR_TYPE'] == self::CREDIT) || ($reverseSign && $je['TR_TYPE'] == self::DEBIT) ) {

                    if (!isset($netBalances[$je['ACCOUNT#']])) {
                        $netBalances[$je['ACCOUNT#']]['NET'] = 0;
                        $netBalances[$je['ACCOUNT#']]['CR'] = [];
                    }

                    $netBalances[$je['ACCOUNT#']]['CR'][] = $je;
                    $netBalances[$je['ACCOUNT#']]['NET_CR'] = ibcadd($netBalances[$je['ACCOUNT#']]['NET_CR'], $je['TRX_AMOUNT']);
                    $netBalances[$je['ACCOUNT#']]['NET'] = ibcadd($netBalances[$je['ACCOUNT#']]['NET'], $je['TRX_AMOUNT']);
                    $netBalances[$je['ACCOUNT#']]['ACCOUNTED']['NET'] = false;
                    $netBalances[$je['ACCOUNT#']]['ACCOUNTED']['NET_CR'] = false;
                } else {

                    if (!isset($netBalances[$je['ACCOUNT#']])) {
                        $netBalances[$je['ACCOUNT#']]['NET'] = 0;
                        $netBalances[$je['ACCOUNT#']]['DB'] = [];
                    }

                    $netBalances[$je['ACCOUNT#']]['DB'][] = $je;
                    $netBalances[$je['ACCOUNT#']]['NET_DB'] = ibcsub($netBalances[$je['ACCOUNT#']]['NET_DB'], $je['TRX_AMOUNT']);
                    $netBalances[$je['ACCOUNT#']]['NET'] = ibcsub($netBalances[$je['ACCOUNT#']]['NET'], $je['TRX_AMOUNT']);
                    $netBalances[$je['ACCOUNT#']]['ACCOUNTED']['NET'] = false;
                    $netBalances[$je['ACCOUNT#']]['ACCOUNTED']['NET_DB'] = false;
                }
            }
        }

        return $netBalances;
    }


    /**
     * @param array $glEntries
     * @param array $lineDetail
     * @param bool  $isMCP
     * @param array $eventDetails
     *
     * @throws IAException
     */
    public static function assertBaseAmounts($glEntries, $lineDetail, $isMCP, $eventDetails)
    {
        LogToFile("Method: ".__FUNCTION__."(".json_encode(func_get_args()).")");

        $THROW_ERROR = self::ASSERT_BASE_AMOUNTS_ERROR; // Turn it ON to throw error in prod.
        $TOLERANCE = 0.05; // why not 1 or 2 pennies?
        $TOLERANCE_EXCHANGE_RATE = 0.0005;

        foreach ($glEntries as $glEntry) {

            if ( ! $isMCP ) {
                $eDiff = ibcsub($glEntry['EXCHANGE_RATE'], 1);
                if (abs($eDiff) > $TOLERANCE_EXCHANGE_RATE) {
                    // Exchange rate needs to be 1 if set
                    if ( isset($glEntry['EXCHANGE_RATE']) && $glEntry['EXCHANGE_RATE'] != 1) {
                        $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d. Exchange rate value is other than 1 for non-MCP transaction - { %3$s } . Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.', $lineDetail['CONTRACTID'],$lineDetail['LINENO'],json_encode($eventDetails));
                        $errorInfo = new ContractI18nErrorInfo('CN-0828',
                                                               ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                                'LINENO' => $lineDetail['LINENO'],
                                                                'EVENT_DETAILS' => json_encode($eventDetails)],
                                                               $errMsg);
                        self::reportAssertError($errorInfo, array_merge(func_get_args(), $glEntry), $THROW_ERROR);
                    }

                    // If transaction currency is equal to the base currency then the base amount must be equal to the transaction amount
                    if ($glEntry['TRX_AMOUNT'] != $glEntry['AMOUNT']) {

                        $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d. Transaction and base amounts must match for non-MCP transaction - { %3$s } . Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.', $lineDetail['CONTRACTID'],$lineDetail['LINENO'],json_encode($eventDetails));
                        $errorInfo = new ContractI18nErrorInfo('CN-0829',
                                                               ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                                'LINENO' => $lineDetail['LINENO'],
                                                                'EVENT_DETAILS' => json_encode($eventDetails)],
                                                               $errMsg);
                        self::reportAssertError($errorInfo, array_merge(func_get_args(), $glEntry), $THROW_ERROR);
                    }
                }
            } else {

                // If the transaction currency is different than the exchange rate can't be 1 - Unlikely but possible, commenting for now

                // if ( $glEntry['EXCHANGE_RATE'] == 1 ) {
                //     $errMsg = _(sprintf('Exchange rate value is 1 for a MCP transaction - \'%1$s\'', json_encode($eventDetails)));
                //     self::reportAssertError($errMsg, array_merge(func_get_args(), $glEntry), $THROW_ERROR);
                // }

                // Base amount = transaction amount * exchange rate, we have to allow a tolerance of few pennies
                $calculatedAmount = ibcmul($glEntry['TRX_AMOUNT'], $glEntry['EXCHANGE_RATE'], ContractUtil::AMOUNT_PRECISION, true);
                $diff = ibcsub($glEntry['AMOUNT'] , $calculatedAmount);

                if (abs($diff) > $TOLERANCE) {
                    $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d. The GL entry base amount should be { %3$01.2f * %4$01.12f } but instead is { %5$01.2f } - { %6$s }. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',$lineDetail['CONTRACTID'], $lineDetail['LINENO'],$glEntry['TRX_AMOUNT'],$glEntry['EXCHANGE_RATE'],$glEntry['AMOUNT'],json_encode($eventDetails)) ;
                    $errorInfo = new ContractI18nErrorInfo('CN-0830',
                                                           ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                            'LINENO' => $lineDetail['LINENO'],
                                                            'TRX_AMOUNT' => sprintf('%01.2f', $glEntry['TRX_AMOUNT']),
                                                            'EXCHANGE_RATE' => sprintf('%01.12f', $glEntry['EXCHANGE_RATE']),
                                                            'AMOUNT' => sprintf('%01.2f', $glEntry['AMOUNT']),
                                                            'EVENT_DETAILS' => json_encode($eventDetails)],
                                                           $errMsg);
                    self::reportAssertError($errorInfo, array_merge(func_get_args(), $glEntry), $THROW_ERROR);
                }
            }
        }
    }

    /**
     * @param string $eventType
     * @param string $journalCode
     * @param int    $lineKey
     * @param array  $glEntries
     * @param int|null    $srcFK1
     * @param int|null    $srcFK2
     */
    public static function collectReclassJEs($eventType, $journalCode, $lineKey, $glEntries, $srcFK1 = null,
                                             $srcFK2 = null)
    {

        LogToFile("Method: ".__FUNCTION__."(".json_encode(func_get_args()).")");
        $THROW_INPUT_ERROR = self::INPUT_ERROR_IN_ASSERTION;

        if ($lineKey == null) {
            $errMsg = 'We found an internal  issue with the contract line data. No linekey passed. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.';
            $errorInfo = new ContractI18nErrorInfo('CN-0831', [], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_INPUT_ERROR);
            return;
        }
        if ( $glEntries == null ) {
            $errMsg = sprintf('We found an internal  issue with the contract line data for line key %d. No glentries passed. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',$lineKey);
            $errorInfo = new ContractI18nErrorInfo('CN-0832', ['LINE_KEY' => $lineKey], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_INPUT_ERROR);
            return;
        }

        if ( $srcFK1 != null && $srcFK2 == null ) {
            if (!isset(self::$reclassJEs[$eventType][$journalCode][$lineKey][$srcFK1])) {

                self::$reclassJEs[$eventType][$journalCode][$lineKey][$srcFK1] = $glEntries;

            } else {

                self::$reclassJEs[$eventType][$journalCode][$lineKey][$srcFK1] = array_merge(
                    self::$reclassJEs[$eventType][$journalCode][$lineKey][$srcFK1],
                    $glEntries
                );
            }
        } else if ( $srcFK1 != null && $srcFK2 != null ) {

            if (!isset(self::$reclassJEs[$eventType][$journalCode][$lineKey][$srcFK1][$srcFK2])) {

                self::$reclassJEs[$eventType][$journalCode][$lineKey][$srcFK1][$srcFK2] = $glEntries;

            } else {

                self::$reclassJEs[$eventType][$journalCode][$lineKey][$srcFK1][$srcFK2] = array_merge(
                    self::$reclassJEs[$eventType][$journalCode][$lineKey][$srcFK1][$srcFK2],
                    $glEntries
                );
            }

        } else {

            if (!isset(self::$reclassJEs[$eventType][$journalCode][$lineKey])) {

                self::$reclassJEs[$eventType][$journalCode][$lineKey] = $glEntries;

            } else {

                self::$reclassJEs[$eventType][$journalCode][$lineKey] = array_merge(
                    self::$reclassJEs[$eventType][$journalCode][$lineKey],
                    $glEntries
                );
            }
        }
    }

    /**
     * @param string $eventType
     * @param string $journalCode
     * @param int    $lineKey
     * @param array|null  $glEntries
     * @param int|null    $srcFK1
     * @param int|null    $srcFK2
     *
     * @throws IAException
     */
    public static function collectTransactionJEs($eventType, $journalCode, $lineKey, $glEntries = null,
                                                 $srcFK1 = null, $srcFK2 = null)
    {
        LogToFile("Method: ".__FUNCTION__."(".json_encode(func_get_args()).")");
        $THROW_INPUT_ERROR = self::INPUT_ERROR_IN_ASSERTION;

        if ( $glEntries == null ) {

            $errMsg = sprintf('We found an internal  issue with the contract line data for line key %d. No glentries passed. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',$lineKey);
            $errorInfo = new ContractI18nErrorInfo('CN-0833', ['LINE_KEY' => $lineKey], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_INPUT_ERROR);
            return;
        }

        if ( $srcFK1 != null && $srcFK2 == null ) {

            if (!isset(self::$rootTxnJEs[$eventType][$journalCode][$lineKey][$srcFK1])) {

                self::$rootTxnJEs[$eventType][$journalCode][$lineKey][$srcFK1] = $glEntries;

            } else {

                self::$rootTxnJEs[$eventType][$journalCode][$lineKey][$srcFK1] = array_merge(
                    self::$rootTxnJEs[$eventType][$journalCode][$lineKey][$srcFK1],
                    $glEntries
                );
            }

        } else if ( $srcFK1 != null && $srcFK2 != null ) {

            if (!isset(self::$rootTxnJEs[$eventType][$journalCode][$lineKey][$srcFK1][$srcFK2])) {

                self::$rootTxnJEs[$eventType][$journalCode][$lineKey][$srcFK1][$srcFK2] = $glEntries;

            } else {

                self::$rootTxnJEs[$eventType][$journalCode][$lineKey][$srcFK1][$srcFK2] = array_merge(
                    self::$rootTxnJEs[$eventType][$journalCode][$lineKey][$srcFK1][$srcFK2],
                    $glEntries
                );
            }

        } else {

            if (!isset(self::$rootTxnJEs[$eventType][$journalCode][$lineKey])) {

                self::$rootTxnJEs[$eventType][$journalCode][$lineKey] = $glEntries;

            } else {

                self::$rootTxnJEs[$eventType][$journalCode][$lineKey] = array_merge(
                    self::$rootTxnJEs[$eventType][$journalCode][$lineKey],
                    $glEntries
                );
            }
        }
    }

    /**
     * @param string      $eventType
     * @param string      $journalCode
     * @param int         $lineKey
     * @param array|null  $glEntries
     * @param int|null         $srcFK1
     * @param int|null         $srcFK2
     *
     */
    private static function getTransactionJEs($eventType, $journalCode, $lineKey, &$glEntries, $srcFK1 = null, $srcFK2 = null)
    {
        LogToFile("Method: ".__FUNCTION__."(".json_encode(func_get_args()).")");

        if(self::isSubEventType($eventType)) {
            $eventType = self::getParentEventType($eventType);
        }

        if ( $srcFK1 != null && $srcFK2 == null ) {
            $glEntries = self::$rootTxnJEs[$eventType][$journalCode][$lineKey][$srcFK1];
        } else if ( $srcFK1 != null && $srcFK2 != null ) {
            $glEntries = self::$rootTxnJEs[$eventType][$journalCode][$lineKey][$srcFK1][$srcFK2];
        } else {
            $glEntries = self::$rootTxnJEs[$eventType][$journalCode][$lineKey];
        }

        if ($glEntries == null) {
            $glEntries = self::fetchTransactionJEs($eventType, $journalCode, $lineKey, null, $srcFK1, $srcFK2);

            if ($glEntries != null) {
                self::collectTransactionJEs($eventType, $journalCode, $lineKey, $glEntries, $srcFK1, $srcFK2);
            }
        }
    }

    /**
     * @param string $eventType
     * @param string $journalCode
     * @param int|null    $lineKey
     * @param int|null    $contractKey
     * @param int|null    $srcFK1
     * @param int|null    $srcFK2
     *
     * @return null|array
     *
     */
    public static function fetchTransactionJEs($eventType, $journalCode, $lineKey = null, $contractKey = null, $srcFK1 = null, $srcFK2 = null)
    {
        LogToFile("Method: ".__FUNCTION__."(".json_encode(func_get_args()).")");
        $THROW_INPUT_ERROR = self::INPUT_ERROR_IN_ASSERTION;

        $glEntries = null;

        if ($lineKey == null && $contractKey == null) {

            $errMsg = sprintf('We found an internal  issue with the contract line data. Line key or Contract key must be provided. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.');
            $errorInfo = new ContractI18nErrorInfo('CN-0834', [], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_INPUT_ERROR);
            return null;
        }
    
        switch ($eventType) {
            case ContractGLReclassEvent::EVENTTYPE_ONRECOGNITION:
                $glEntries = self::fetchRecognitionJEs($journalCode, $lineKey, $contractKey, $srcFK1);
                break;
        
            case ContractGLReclassEvent::EVENTTYPE_ONINVOICE:
                $glEntries = self::fetchInvoiceJEs($journalCode, $lineKey, $contractKey, $srcFK1);
                break;
        
            case ContractGLReclassEvent::EVENTTYPE_ONMEA:
            case ContractGLReclassEvent::EVENTTYPE_ONKIT:
                break;
        
            default:
                // TODO
                //$errMsg = _("Not implemented yet.");
                //self::reportAssertError($errMsg, func_get_args());
        }

        return $glEntries;
    }

    /**
     * @param string    $journalCode
     * @param int|null  $lineKey
     * @param int|null  $contractKey
     * @param int|null  $bseKey
     *
     * @return array|null
     *
     * @throws IAException
     */
    public static function fetchInvoiceJEs($journalCode, $lineKey=null, $contractKey=null, $bseKey=null)
    {
        LogToFile("Method: ".__FUNCTION__."(".json_encode(func_get_args()).")");
        $THROW_ERROR = self::ASSERT_INVOICE_ERROR;

        $prentries = null;

        if ($bseKey != null) {
            $query = "select pr.exchange_rate, pr.record#, pr.accountkey, pr.amount,pr.trx_amount,pr.gloffset ,proffset.offsetkey,offsetpr.accountkey as offset_accountkey, offsetpr.record# as offset_record#, offsetpr.amount as offset_amount,offsetpr.trx_amount as offset_trx_amount ,offsetpr.gloffset as offset_gloffset, cs.contractdetailkey  from
            prentry pr inner join prentryoffset proffset on proffset.cny# = pr.CNY# and proffset.prentrykey = pr.RECORD#
            inner join  prentry offsetpr on offsetpr.cny# = proffset.cny# and offsetpr.record# =  proffset.offsetkey
            inner join  contractscheduleentry cse on cse.cny# =  pr.cny#  and cse.record# = pr.billablecontractschentrykey
            inner join contractschedule cs on cs.cny# = cse.cny#  and cs.record# = cse.schedulekey
            where pr.cny# = :1 and pr.billablecontractschentrykey = :2";
            $result = QueryResult(array($query, GetMyCompany(), $bseKey));

            if (!$result) {
                $errMsg = sprintf('We found an internal issue with the contract line data for line key %1$d.  Could not find prentry for billing schedule entry key { %2$d }. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.', $lineKey, $bseKey);
                $errorInfo = new ContractI18nErrorInfo('CN-0835', ['LINE_KEY' => $lineKey, 'ENTRY_KEY' => $bseKey], $errMsg);
                self::reportAssertError($errorInfo, func_get_args(),$THROW_ERROR);
                return null;
            }

            $reverseSign = 1; // for inline credit and credit memo
            if($result[0]['TRX_AMOUNT'] < 0){
                $reverseSign = -1;
            }
            $prentries[] = [
                'ACCOUNT#' => $result[0]['ACCOUNTKEY'],
                'AMOUNT' => ibcmul($result[0]['AMOUNT'], $reverseSign),
                'TRX_AMOUNT' => ibcmul($result[0]['TRX_AMOUNT'],$reverseSign),
                'EXCHANGE_RATE' => $result[0]['EXCHANGE_RATE'],
                'TR_TYPE' => $result[0]['GLOFFSET'] * $reverseSign,
                'CONTRACTDETAILKEY' =>  $result[0]['CONTRACTDETAILKEY']
            ];

            $reverseSign = 1;
            if($result[0]['OFFSET_TRX_AMOUNT'] < 0){
                $reverseSign = -1;
            }
            $prentries[] = [
                'ACCOUNT#' => $result[0]['OFFSET_ACCOUNTKEY'],
                'AMOUNT' => ibcmul($result[0]['OFFSET_AMOUNT'],$reverseSign),
                'TRX_AMOUNT' => ibcmul($result[0]['OFFSET_TRX_AMOUNT'],$reverseSign),
                'EXCHANGE_RATE' => $result[0]['EXCHANGE_RATE'],
                'TR_TYPE' => $result[0]['OFFSET_GLOFFSET'] * $reverseSign,
                'CONTRACTDETAILKEY' =>  $result[0]['CONTRACTDETAILKEY']
            ];
        } else {

            $errMsg = "Not implemented yet. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.";
            $errorInfo = new ContractI18nErrorInfo('CN-0836', ['LINE_KEY' => $lineKey, 'ENTRY_KEY' => $bseKey], $errMsg);
            self::reportAssertError($errorInfo, func_get_args());
        }

        return $prentries;
    }


    /**
     * @param string    $journalCode
     * @param int|null  $lineKey
     * @param int|null  $contractKey
     * @param int|null  $rseKey
     *
     * @return array|null
     *
     * @throws IAException
     */
    public static function fetchRecognitionJEs($journalCode, $lineKey=null, $contractKey=null, $rseKey=null)
    {
        LogToFile("Method: ".__FUNCTION__."(".json_encode(func_get_args()).")");
        $THROW_ERROR = self::ASSERT_RECOGNITION_ERROR;
        $glEntries = null;

        if ($rseKey != null) {

            $stmt = "SELECT RECORD# recordno FROM GLBATCHMST WHERE CNY# = :1 and CONTRACTSCHEDULEENTRYKEY = :2";

            $glBatch = QueryResult([$stmt, GetMyCompany(), $rseKey]);

            if (!isset($glBatch[0]['RECORDNO'])) {
                $errMsg = sprintf('We found an internal issue with the contract line data for line key %1$d.  The GL batch for the revenue schedule entry key { %2$d } could not be found. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.', $lineKey,$rseKey);
                $errorInfo = new ContractI18nErrorInfo('CN-0837', ['LINE_KEY' => $lineKey, 'ENTRY_KEY' => $rseKey], $errMsg);
                self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);

                return null;
            }

            $glEntries = EntityManager::GetListQuick(
                'glentry',
                ['RECORDNO', 'TR_TYPE', 'TRX_AMOUNT', 'AMOUNT', 'EXCHANGE_RATE', 'ACCOUNT#'],
                ['BATCHNO' => $glBatch[0]['RECORDNO']]
            );

        } else {

            $errMsg = "Not implemented yet. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.";
            $errorInfo = new ContractI18nErrorInfo('CN-0838', [], $errMsg);
            self::$errorInfo($errMsg, func_get_args());
        }

        return $glEntries;
    }

    /**
     * @param string     $eventType
     * @param string     $journalCode
     * @param string     $lineKey
     * @param array|null $glEntries
     * @param string     $srcFK1
     * @param string     $srcFK2
     *
     */
    private static function getReclassJEs($eventType, $journalCode, $lineKey, &$glEntries, $srcFK1, $srcFK2)
    {
        LogToFile("Method: ".__FUNCTION__."(".json_encode(func_get_args()).")");

        if(self::isSubEventType($eventType)) {
            $eventType = self::getParentEventType($eventType);
        }

        if ( $srcFK1 != null && $srcFK2 == null ) {
            $glEntries = self::$reclassJEs[$eventType][$journalCode][$lineKey][$srcFK1];
        } else if ( $srcFK1 != null && $srcFK2 != null ) {
            $glEntries = self::$reclassJEs[$eventType][$journalCode][$lineKey][$srcFK1][$srcFK2];
        } else {
            $glEntries = self::$reclassJEs[$eventType][$journalCode][$lineKey];
        }
    }
    
    /**
     * @param float      $amount
     * @param string     $journalCode
     * @param int        $lineKey
     * @param int        $bseKey
     * @param array|null $reclassJEs
     * @param array|null $invoiceJE
     * @param array      $lineDetail
     *
     * @throws IAException
     */
    public static function assertInvoiceJEs($amount, $journalCode, $lineKey, $bseKey, $reclassJEs, $invoiceJE, $lineDetail)
    {
        LogToFile("Method: " . __FUNCTION__ . "(" . json_encode(func_get_args()) . ")");
        
        $THROW_ERROR = self::ASSERT_INVOICE_ERROR; // Turn it ON to throw error in prod.
        
        if ($invoiceJE == null) {
            $errMsg =
                sprintf(
                    'We found an internal issue with the contract line data for %1$s line no %2$d.  The invoice journal entry could ' .
                      'not be found. Contact your Sage Intacct Support resource, tell them you received this error, and they will ' .
                      'resolve the issue.',
                    $lineDetail['CONTRACTID'],
                    $lineDetail['LINENO']);
            $errorInfo = new ContractI18nErrorInfo('CN-0839', ['CONTRACTID' => $lineDetail['CONTRACTID'], 'LINENO' => $lineDetail['LINENO']], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);
            
            return;
        }
        if (self::isBillingSchEntryHasMea($journalCode, $bseKey)) {
            if ($invoiceJE[0]['CONTRACTDETAILKEY'] != $lineKey) {
                // DWNS:- it is possible that invoice je contract detail key not equal to line key in case of MEA, invoice je not require
                $invoiceJE = [];
            }
            self::getAssertedMEADistributionAmount($journalCode, $bseKey, $meaAmount, $lineKey);
            if ($amount != $meaAmount['AMOUNT']) {
                if (ContractDetailManager::getItemType($lineDetail['ITEMID']) === ContractDetailManager::ITEM_TYPE_NON_KIT) {
                    $label = 'MEA';
                    $errorId = 'CN-0840';
                } else {
                    $label = 'Kit';
                    $errorId = 'CN-1212';
                }
                $errMsg = sprintf(
                    'We found an internal issue with the contract line data for %1$s line no %2$d.  The total invoice amount ' .
                      '{ %3$01.2f } for billing schedule entry { %4$d } does not equal the ' . $label . ' amount  { %5$01.2f } ' .
                      'for contract line key { %6$d}.  Contact your Sage Intacct Support resource, tell them you received this error, ' .
                      'and they will resolve the issue.',
                    $lineDetail['CONTRACTID'],
                    $lineDetail['LINENO'],
                    $amount,
                    $bseKey,
                    $meaAmount['AMOUNT'],
                    $lineKey
                );
                $errorInfo = new ContractI18nErrorInfo($errorId,
                                                       ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                        'LINENO' => $lineDetail['LINENO'],
                                                        'AMOUNT' => sprintf('%01.2f', $amount),
                                                        'SCHEDULE_ENTRY_KEY' => $bseKey,
                                                        'MEA_AMOUNT' => sprintf('%01.2f', $meaAmount['AMOUNT']),
                                                        'LINE_KEY' => $lineKey],
                                                       $errMsg);
                self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);
                
                return;
            }
        }
        
        $duAcctKey = $lineDetail['DRUNBILLEDACCTKEY'];
        $dbAcctKey = $lineDetail['DRBILLEDACCTKEY'];
        
        $suAcctKey = $lineDetail['SALESUNBILLEDACCTKEY'];
        $sbAcctKey = $lineDetail['SALESBILLEDACCTKEY'];
        
        if ($duAcctKey == null || $dbAcctKey == null || $suAcctKey == null || $sbAcctKey == null) {
            $errMsg = sprintf(
                'We found an internal issue with the contract line data for %1$s line no %2$d. Not all acccount keys are passed. ' .
                  'Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',
                $lineDetail['CONTRACTID'],
                $lineDetail['LINENO']
            );
            $errorInfo = new ContractI18nErrorInfo('CN-0841', ['CONTRACTID' => $lineDetail['CONTRACTID'], 'LINENO' => $lineDetail['LINENO']], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);
            
            return;
        }
        
        $allJEs = $invoiceJE;
        if ($reclassJEs != null) {
            $allJEs = array_merge($invoiceJE, $reclassJEs);
        }
        
        $reverseSign = false;
        if ($amount < 0) {
            $reverseSign = true;
        }
        $netBalances = self::findNetJEBalances($allJEs, $reverseSign);
        
        //unbilled account balance
        $totalDebitToUnBilled = 0;
        
        //billed balance
        $totalCreditToBilled = 0;
        
        if (self::isNETBalanceAvailableToUse($netBalances, $duAcctKey)) {
            $totalDebitToUnBilled = ibcadd($totalDebitToUnBilled, $netBalances[$duAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $duAcctKey);
        }
        
        if (self::isNETBalanceAvailableToUse($netBalances, $suAcctKey)) {
            $totalDebitToUnBilled = ibcadd($totalDebitToUnBilled, $netBalances[$suAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $suAcctKey);
        }
        
        if (!self::isEqual($totalDebitToUnBilled, $amount)) {
            $errMsg = sprintf(
                'We found an internal issue with the contract line data for %1$s line no %2$d.  The total debit amount { %3$01.2f } ' .
                  'to unbilled revenue for billing schedule entry key { %4$d } does not equal the invoice amount { %5$01.2f }. Contact ' .
                  'your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',
                $lineDetail['CONTRACTID'],
                $lineDetail['LINENO'],
                ibcabs($totalDebitToUnBilled),
                $bseKey,
                ibcabs($amount)
            );
            $errorInfo = new ContractI18nErrorInfo('CN-0842',
                                                   ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                    'LINENO' => $lineDetail['LINENO'],
                                                    'DEBIT_AMOUNT' => sprintf('%01.2f', ibcabs($totalDebitToUnBilled)),
                                                    'SCHEDULE_ENTRY_KEY' => $bseKey,
                                                    'INVOICE_AMOUNT' => sprintf('%01.2f', ibcabs($amount))],
                                                   $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);
            
            return;
        }
        
        if (self::isNETBalanceAvailableToUse($netBalances, $dbAcctKey)) {
            $totalCreditToBilled = ibcadd($totalCreditToBilled, $netBalances[$dbAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $dbAcctKey);
        }
        
        if (self::isNETBalanceAvailableToUse($netBalances, $sbAcctKey)) {
            $totalCreditToBilled = ibcadd($totalCreditToBilled, $netBalances[$sbAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $sbAcctKey);
        }
        
        if (!self::isEqual($totalCreditToBilled, $amount)) {
            $errMsg = sprintf(
                'We found an internal issue with the contract line data for %1$s line no %2$d.  The total credit amount { %3$01.2f } ' .
                  'to billed revenue for billing schedule entry { %4$d } does not equal the invoice amount  { %5$01.2f }. Contact your ' .
                  'Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',
                $lineDetail['CONTRACTID'],
                $lineDetail['LINENO'],
                ibcabs($totalCreditToBilled),
                $bseKey,
                ibcabs($amount)
            );
            $errorInfo = new ContractI18nErrorInfo('CN-0843',
                                                   ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                    'LINENO' => $lineDetail['LINENO'],
                                                    'CREDIT_AMOUNT' => sprintf('%01.2f', ibcabs($totalCreditToBilled)),
                                                    'SCHEDULE_ENTRY_KEY' => $bseKey,
                                                    'INVOICE_AMOUNT' => sprintf('%01.2f', ibcabs($amount))],
                                                   $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);
            
            return;
        }
    }

    /**
     * @param float $amount
     * @param string $journalCode
     * @param int $lineKey
     * @param int $bseKey
     * @param int $paymentPREntrykey
     * @param array $reclassJEs
     * @param array $lineDetail
     *
     * @throws IAException
     */
    public static function assertPaymentReversalJEs($amount, $journalCode, $lineKey, $bseKey, $paymentPREntrykey, $reclassJEs, $lineDetail)
    {

        LogToFile("Method: " . __FUNCTION__ . "(" . json_encode(func_get_args()) . ")");

        $THROW_ERROR = self::ASSERT_PAYMENT_ERROR; // Turn it ON to throw error in prod.

        $dbAcctKey = $lineDetail['DRBILLEDACCTKEY'];
        $dpAcctKey = $lineDetail['DRPAIDACCTKEY'];

        $sbAcctKey = $lineDetail['SALESBILLEDACCTKEY'];
        $spAcctKey = $lineDetail['SALESPAIDACCTKEY'];

        if ($dpAcctKey == null || $dbAcctKey == null || $spAcctKey == null || $sbAcctKey == null) {

            $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d. Not all acccount keys are passed. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',$lineDetail['CONTRACTID'],$lineDetail['LINENO']);
            $errorInfo = new ContractI18nErrorInfo('CN-0844', ['CONTRACTID' => $lineDetail['CONTRACTID'], 'LINENO' => $lineDetail['LINENO']], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);

            return;
        }

        $allJEs = $reclassJEs;

        $reverseSign = false;
        if($amount < 0){
            $reverseSign = true;
        }
        $netBalances = self::findNetJEBalances($allJEs, $reverseSign);

        //paid account balance
        $totalDebitToBilled = 0;

        //billed account balance
        $totalCreditToPaid = 0;

        if (self::isNETBalanceAvailableToUse($netBalances, $dbAcctKey)){
            $totalDebitToBilled = ibcadd($totalDebitToBilled, $netBalances[$dbAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $dbAcctKey);
        }


        if (self::isNETBalanceAvailableToUse($netBalances, $sbAcctKey)){
            $totalDebitToBilled = ibcadd($totalDebitToBilled, $netBalances[$sbAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $sbAcctKey);
        }


        if ( !self::isEqual($totalDebitToBilled, $amount) ) {
            $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d.  The total debit amount { %3$01.2f } to billed revenue for { %4$d } does not equal the reverse payment amount  { %5$01.2f } ). Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',$lineDetail['CONTRACTID'], $lineDetail['LINENO'],ibcabs($totalDebitToBilled),$bseKey,ibcabs($amount));
            $errorInfo = new ContractI18nErrorInfo('CN-0845',
                                                   ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                    'LINENO' => $lineDetail['LINENO'],
                                                    'DEBIT_AMOUNT' => sprintf('%01.2f', ibcabs($totalDebitToBilled)),
                                                    'SCHEDULE_ENTRY_KEY' => $bseKey,
                                                    'PAYMENT_AMOUNT' => sprintf('%01.2f', ibcabs($amount))],
                                                   $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);
            return;
        }



        if (self::isNETBalanceAvailableToUse($netBalances, $dpAcctKey)){
            $totalCreditToPaid = ibcadd($totalCreditToPaid, $netBalances[$dpAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $dpAcctKey);
        }


        if (self::isNETBalanceAvailableToUse($netBalances, $spAcctKey)){
            $totalCreditToPaid = ibcadd($totalCreditToPaid, $netBalances[$spAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $spAcctKey);
        }

        if ( !self::isEqual($totalCreditToPaid, $amount) ) {

            $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d.  The total credit amount { %3$01.2f  } to paid revenue for billing schedule entry key { %4$d } does not equal the reverse payment amount  { %5$01.2f } . Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',$lineDetail['CONTRACTID'], $lineDetail['LINENO'],ibcabs($totalCreditToPaid),$bseKey,ibcabs($amount));
            $errorInfo = new ContractI18nErrorInfo('CN-0846',
                                                   ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                    'LINENO' => $lineDetail['LINENO'],
                                                    'CREDIT_AMOUNT' => sprintf('%01.2f', ibcabs($totalCreditToPaid)),
                                                    'SCHEDULE_ENTRY_KEY' => $bseKey,
                                                    'PAYMENT_AMOUNT' => sprintf('%01.2f', ibcabs($amount))],
                                                   $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);

            return;
        }


    }

    /**
     * @param float $amount
     * @param string $journalCode
     * @param int $lineKey
     * @param int $bseKey
     * @param int $paymentPREntrykey
     * @param array $reclassJEs
     * @param array $lineDetail
     *
     * @param string $transactionDate
     * @throws IAException
     */
    public static function assertPaymentJEs($amount, $journalCode, $lineKey, $bseKey, $paymentPREntrykey, $reclassJEs, $lineDetail, $transactionDate)
    {

        LogToFile("Method: " . __FUNCTION__ . "(" . json_encode(func_get_args()) . ")");

        $THROW_ERROR = self::ASSERT_PAYMENT_ERROR; // Turn it ON to throw error in prod.

        if(self::isBillingSchEntryHasMea($journalCode,$bseKey)){

            self::getPorportionAmount($paymentPREntrykey , $bseKey, $journalCode, $lineKey, $meaAmount, $transactionDate );

            if( !self::isEqual( $amount, $meaAmount[0]['AMOUNT']) ){

                $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d.  The total payment amount  { %3$01.2f }  for billing schedule entry key { %4$d } does not equal the paid amount  { %5$01.2f }  for contract line key { %6$d  }.  Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',$lineDetail['CONTRACTID'],$lineDetail['LINENO'],$amount,$bseKey,$meaAmount[0]['AMOUNT'],$lineKey);
                $errorInfo = new ContractI18nErrorInfo('CN-0847',
                                                       ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                        'LINENO' => $lineDetail['LINENO'],
                                                        'AMOUNT' => sprintf('%01.2f', $amount),
                                                        'ENTRY_KEY' => $bseKey,
                                                        'MEA_AMOUNT' => sprintf('%01.2f', $meaAmount[0]['AMOUNT']),
                                                        'LINE_KEY' => $lineKey],
                                                       $errMsg);
                self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);
                return;
            }

        }


        $dbAcctKey = $lineDetail['DRBILLEDACCTKEY'];
        $dpAcctKey = $lineDetail['DRPAIDACCTKEY'];

        $sbAcctKey = $lineDetail['SALESBILLEDACCTKEY'];
        $spAcctKey = $lineDetail['SALESPAIDACCTKEY'];


        if ($dpAcctKey == null || $dbAcctKey == null || $spAcctKey == null || $sbAcctKey == null) {
            $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d. Not all acccount keys are passed. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',$lineDetail['CONTRACTID'],$lineDetail['LINENO']);
            $errorInfo = new ContractI18nErrorInfo('CN-0848', ['CONTRACTID' => $lineDetail['CONTRACTID'], 'LINENO' => $lineDetail['LINENO']], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);

            return;
        }

        // we do not require to merge payment JEs as paymen JE deal weal with ar side not reclass revenue.
        $allJEs = $reclassJEs;


        $reverseSign = false;
        if($amount < 0){
            $reverseSign = true;
        }
        $netBalances = self::findNetJEBalances($allJEs, $reverseSign);

        //billed bucket
        $totalDebitToBilled = 0;

        //paid bucket
        $totalCreditToPaid = 0;

        if (self::isNETBalanceAvailableToUse($netBalances, $dbAcctKey)) {
            $totalDebitToBilled = ibcadd($totalDebitToBilled, $netBalances[$dbAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $dbAcctKey);
        }


        if (self::isNETBalanceAvailableToUse($netBalances, $sbAcctKey)) {
            $totalDebitToBilled = ibcadd($totalDebitToBilled, $netBalances[$sbAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $sbAcctKey);
        }


        if ( !self::isEqual($totalDebitToBilled, $amount) ) {
            $errMsg = sprintf('We found an internal issue with the contract line data for %1$s line no %2$d.  The total debit amount { %3$01.2f } to billed revenue for billing schedule entry key { %4$d } does not equal the payment amount  { %5$01.2f }. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',$lineDetail['CONTRACTID'] , $lineDetail['LINENO'],ibcabs($totalDebitToBilled),$bseKey,ibcabs($amount));
            $errorInfo = new ContractI18nErrorInfo('CN-0849',
                                                   ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                    'LINENO' => $lineDetail['LINENO'],
                                                    'DEBIT_AMOUNT' => sprintf('%01.2f', ibcabs($totalDebitToBilled)),
                                                    'ENTRY_KEY' => $bseKey,
                                                    'PAYMENT_AMOUNT' => sprintf('%01.2f', ibcabs($amount))],
                                                   $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);
            return;
        }


        if (self::isNETBalanceAvailableToUse($netBalances, $dpAcctKey)) {
            $totalCreditToPaid = ibcadd($totalCreditToPaid, $netBalances[$dpAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $dpAcctKey);
        }


        if (self::isNETBalanceAvailableToUse($netBalances, $spAcctKey)) {
            $totalCreditToPaid = ibcadd($totalCreditToPaid, $netBalances[$spAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $spAcctKey);
        }

        if ( !self::isEqual($totalCreditToPaid, $amount) ) {

            $errMsg =  sprintf('We found an internal issue with the contract line data for %1$s line no %2$d.  The total credit amount { %3$01.2f } to paid revenue for billing schedule entry key { %4$d } does not equal the payment amount  { %5$01.2f }. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',$lineDetail['CONTRACTID'],$lineDetail['LINENO'],ibcabs($totalCreditToPaid),$bseKey,ibcabs($amount));
            $errorInfo = new ContractI18nErrorInfo('CN-0850',
                                                   ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                    'LINENO' => $lineDetail['LINENO'],
                                                    'CREDIT_AMOUNT' => sprintf('%01.2f', ibcabs($totalCreditToPaid)),
                                                    'ENTRY_KEY' => $bseKey,
                                                    'PAYMENT_AMOUNT' => sprintf('%01.2f', ibcabs($amount))],
                                                   $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);

            return;
        }

    }
    /**
     * @param string $eventType
     * @param string $journalCode
     * @param int    $lineKey
     * @param int|null    $srcFK1
     * @param int|null    $srcFK2
     *
     * @throws IAException
     */
    public static function clearTransactionJEs($eventType, $journalCode, $lineKey,
                                                 $srcFK1 = null, $srcFK2 = null)
    {

        LogToFile("Method: ".__FUNCTION__."(".json_encode(func_get_args()).")");

        if ( $srcFK1 != null && $srcFK2 == null ) {

            if (isset(self::$reclassJEs[$eventType][$journalCode][$lineKey][$srcFK1])) {
                        unset(self::$reclassJEs[$eventType][$journalCode][$lineKey][$srcFK1]);
            }
        } else if ( $srcFK1 != null && $srcFK2 != null ) {

            if (isset(self::$reclassJEs[$eventType][$journalCode][$lineKey][$srcFK1][$srcFK2])) {

                unset(self::$reclassJEs[$eventType][$journalCode][$lineKey][$srcFK1][$srcFK2]);

            }

        } else {

            if (isset(self::$reclassJEs[$eventType][$journalCode][$lineKey])) {
                unset(self::$reclassJEs[$eventType][$journalCode][$lineKey]);
            }
        }
    }

    /**
     * @param int $eventType
     * @param string $journalCode
     * @param int $contractKey
     * @param array $meaScheduleLink
     */
    public static function collectMEAScheduleLinks($eventType, $journalCode, $contractKey, $meaScheduleLink)
    {
        self::$meaScheduleLinks[$eventType][$contractKey][$journalCode] = $meaScheduleLink;
    }
    
    /**
     * @param int        $eventType
     * @param string     $journalCode
     * @param int        $contractKey
     * @param array|null $meaScheduleLink
     */
    private static function getMEAScheduleLinks($eventType, $journalCode, $contractKey, &$meaScheduleLink)
    {
        $THROW_ERROR = self::ASSERT_MEA_ERROR;
        // it can be possible that link is empty , so isset ceck is there
        if (!isset(self::$meaScheduleLinks[$eventType][$contractKey][$journalCode])) {
            if ($eventType === ContractGLReclassEvent::EVENTTYPE_ONKIT) {
                $label = 'Kit';
                $errorId = 'CN-1213';
            } else {
                $label = 'MEA';
                $errorId = 'CN-0851';
            }
            $errMsg = sprintf(
                "We found an internal issue with the contract. The $label schedule link could not be found. Contact your Sage Intacct " .
                 'Support resource, tell them you received this error, and they will resolve the issue.'
            );
            $errorInfo = new ContractI18nErrorInfo($errorId, [], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);
            return;
        }
        $meaScheduleLink = self::$meaScheduleLinks[$eventType][$contractKey][$journalCode];
    }

    /**
     * @param int $eventType
     * @param string $journalCode
     * @param int $contractKey
     * @param array $contractDetailKeys
     */
    public static function assertMEA($eventType, $journalCode, $contractKey, $contractDetailKeys)
    {

        LogToFile("Method: " . __FUNCTION__ . "(" . json_encode(func_get_args()) . ")");


        $meaScheduleLink = null;
        self::getMEAScheduleLinks($eventType, $journalCode, $contractKey, $meaScheduleLink);



        if($meaScheduleLink) {

            $meaAmountMovement = [];
            foreach ($meaScheduleLink as $link) {
                //destination total recieve amount
                $meaAmountMovement[$link['DESTDETAILKEY']]['AMOUNT'] = $meaAmountMovement[$link['DESTDETAILKEY']] ? ibcadd($meaAmountMovement[$link['DESTDETAILKEY']]['AMOUNT'], $link['AMOUNT']) : $link['AMOUNT'];
                $meaAmountMovement[$link['DESTDETAILKEY']]['BASEAMOUNT'] = $meaAmountMovement[$link['DESTDETAILKEY']] ? ibcadd($meaAmountMovement[$link['DESTDETAILKEY']]['BASEAMOUNT'], $link['BASEAMOUNT']) : $link['BASEAMOUNT'];
                $meaAmountMovement[$link['DESTDETAILKEY']]['BILLBASEAMOUNT'] = $meaAmountMovement[$link['DESTDETAILKEY']] ? ibcadd($meaAmountMovement[$link['DESTDETAILKEY']]['BILLBASEAMOUNT'], $link['BILLBASEAMOUNT']) : $link['BILLBASEAMOUNT'];
                // source total given away amount
                $meaAmountMovement[$link['SRCDETAILKEY']]['AMOUNT'] = $meaAmountMovement[$link['SRCDETAILKEY']] ? ibcsub($meaAmountMovement[$link['SRCDETAILKEY']]['AMOUNT'], $link['AMOUNT']) : ibcsub(0, $link['AMOUNT']);
                $meaAmountMovement[$link['SRCDETAILKEY']]['BASEAMOUNT'] = $meaAmountMovement[$link['SRCDETAILKEY']] ? ibcsub($meaAmountMovement[$link['SRCDETAILKEY']]['BASEAMOUNT'], $link['BASEAMOUNT']) : ibcsub(0, $link['BASEAMOUNT']);
                $meaAmountMovement[$link['SRCDETAILKEY']]['BILLBASEAMOUNT'] = $meaAmountMovement[$link['SRCDETAILKEY']] ? ibcsub($meaAmountMovement[$link['SRCDETAILKEY']]['BILLBASEAMOUNT'], $link['BILLBASEAMOUNT']) : ibcsub(0, $link['BILLBASEAMOUNT']);
            }
            foreach ($contractDetailKeys as $lineKey) { // assertion will be done at line lable
                if(isset($meaAmountMovement[$lineKey])) {
                    self::assert($meaAmountMovement[$lineKey]['AMOUNT'], $eventType, $journalCode, $lineKey);
                }
            }

        }

    }
    
    /**
     * @param string     $eventType
     * @param float      $amount
     * @param string     $journalCode
     * @param int        $lineKey
     * @param array|null $reclassJEs
     * @param array      $lineDetail
     */
    private static function assertMEAJEs($eventType, $amount, $journalCode, $lineKey, $reclassJEs, $lineDetail)
    {
        LogToFile("Method: " . __FUNCTION__ . "(" . json_encode(func_get_args()) . ")");
        
        $THROW_ERROR = self::ASSERT_MEA_ERROR; // Turn it ON to throw error in prod.
        
        $duAcctKey = $lineDetail['DRUNBILLEDACCTKEY'];
        $dbAcctKey = $lineDetail['DRBILLEDACCTKEY'];
        $dpAcctKey = $lineDetail['DRPAIDACCTKEY'];
        
        $suAcctKey = $lineDetail['SALESUNBILLEDACCTKEY'];
        $sbAcctKey = $lineDetail['SALESBILLEDACCTKEY'];
        $spAcctKey = $lineDetail['SALESPAIDACCTKEY'];
        
        if (
            $dpAcctKey == null || $dbAcctKey == null || $spAcctKey == null || $sbAcctKey == null || $duAcctKey == null
            || $sbAcctKey == null
        ) {
            $errMsg = sprintf(
                'We found an internal issue with the contract line data for %1$s line no %2$d. Not all acccount keys are passed. ' .
                  'Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',
                $lineDetail['CONTRACTID'],
                $lineDetail['LINENO']
            );
            $errorInfo = new ContractI18nErrorInfo('CN-0852', ['CONTRACTID' => $lineDetail['CONTRACTID'], 'LINENO' => $lineDetail['LINENO']], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);
            
            return;
        }
        
        $netBalances = self::findNetJEBalances($reclassJEs);
        
        $du = 0.0;
        $db = 0.0;
        $dp = 0.0;
        $su = 0.0;
        $sb = 0.0;
        $sp = 0.0;
        
        if (self::isNETBalanceAvailableToUse($netBalances, $duAcctKey)) {
            $du = ibcadd($du, $netBalances[$duAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $duAcctKey);
        }
        if (self::isNETBalanceAvailableToUse($netBalances, $dbAcctKey)) {
            $db = ibcadd($db, $netBalances[$dbAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $dbAcctKey);
        }
        if (self::isNETBalanceAvailableToUse($netBalances, $dpAcctKey)) {
            $dp = ibcadd($dp, $netBalances[$dpAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $dpAcctKey);
        }
        
        if (self::isNETBalanceAvailableToUse($netBalances, $suAcctKey)) {
            $su = ibcadd($su, $netBalances[$suAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $suAcctKey);
        }
        if (self::isNETBalanceAvailableToUse($netBalances, $sbAcctKey)) {
            $sb = ibcadd($sb, $netBalances[$sbAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $sbAcctKey);
        }
        if (self::isNETBalanceAvailableToUse($netBalances, $spAcctKey)) {
            $sp = ibcadd($sp, $netBalances[$spAcctKey]['NET']);
            self::setNETAccountedTrue($netBalances, $spAcctKey);
        }
        
        $totaldefferedCreditRevenue = bcadd(bcadd($du, $db), $dp);
        $totalSalesCreditRevenue = bcadd(bcadd($su, $sb), $sp);
        $totalCreditRevenue = ibcadd($totaldefferedCreditRevenue, $totalSalesCreditRevenue);
        
        if (!self::isEqual($totalCreditRevenue, $amount)) {
            if ($eventType === ContractGLReclassEvent::EVENTTYPE_ONKIT) {
                $label = 'Kit';
                $errorId = 'CN-1214';
            } else {
                $label = 'MEA';
                $errorId = 'CN-0853';
            }
            
            $errMsg = sprintf(
                'We found an internal issue with the contract line data for %1$s line no %2$d.  The net credit amount { %3$01.2f } for ' .
                  'contract line key { %4$d } does not equal the ' . $label . ' amount { %5$01.2f }. Contact your Sage Intacct Support ' .
                  'resource, tell them you received this error, and they will resolve the issue.',
                $lineDetail['CONTRACTID'],
                $lineDetail['LINENO'],
                $totalCreditRevenue,
                $lineKey,
                $amount
            );
            $errorInfo = new ContractI18nErrorInfo($errorId,
                                                   ['CONTRACTID' => $lineDetail['CONTRACTID'],
                                                    'LINENO' => $lineDetail['LINENO'],
                                                    'TOTAL_CREDIT_REVENUE' => sprintf('%01.2f', $totalCreditRevenue),
                                                    'LINE_KEY' => $lineKey,
                                                    'AMOUNT' => sprintf('%01.2f', $amount)],
                                                   $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);
            
            return;
        }
    }

    /**
     * @param array $meaDistributionCache
     */
    public static function collectAssertedMEADistributionCache($meaDistributionCache)
    {
        self::$meaDistributionCache = $meaDistributionCache;
    }
    
    /**
     */
    public static function clearAssertedMEADistributionCache()
    {
        self::$meaDistributionCache = [];
    }
    
    /**
     * @param string      $journalCode
     * @param int         $billingScheduleEntry
     * @param float|array $meaDistributionAmount
     * @param int|null    $contractDetailkey
     */
    private static function getAssertedMEADistributionAmount(
        $journalCode,
        $billingScheduleEntry,
        &$meaDistributionAmount,
        $contractDetailkey = null
    ) {
        $meaDistributionAmount = 0.0;
        if (isset(self::$meaDistributionCache[$journalCode][$billingScheduleEntry . '_map'])) {
            if ($contractDetailkey) {
                $meaDistributionAmount = self::$meaDistributionCache[$journalCode][$billingScheduleEntry . '_map'][$contractDetailkey];
            } else {
                $meaDistributionAmount = self::$meaDistributionCache[$journalCode][$billingScheduleEntry];
            }
        }
    }

    /**
     * @param string $journalCode
     * @param int $billingScheduleEntry
     * @return bool
     */
    private static function isBillingSchEntryHasMea($journalCode, $billingScheduleEntry)
    {
        if (isset(self::$meaDistributionCache[$journalCode][$billingScheduleEntry . '_map'])) {
            return true;
        }
        return false;
    }


    /**
     * @param int $contractDetailKey
     * @param array $contractDetail
     */
    public static function collectContrailDetails($contractDetailKey, $contractDetail)
    {
        self::$contrailDetails[$contractDetailKey] = $contractDetail;
    }

    /**
     * @param int $contractDetailKey
     * @return mixed
     */
    public static function getContrailDetail($contractDetailKey)
    {

        if (self::$contrailDetails[$contractDetailKey]) {
            return self::$contrailDetails[$contractDetailKey];
        } else {
            $slect = ['CONTRACTID', 'LINENO', 'LINETYPE','ITEMID',
                'CURRENCY', 'BASECURR', 'REVENUEJOURNALKEY', 'REVENUE2JOURNALKEY', 'ARUNBILLEDACCTKEY', 'ARBILLEDACCTKEY', 'DRUNBILLEDACCTKEY',
                'DRBILLEDACCTKEY', 'DRPAIDACCTKEY', 'SALESUNBILLEDACCTKEY', 'SALESBILLEDACCTKEY', 'SALESPAIDACCTKEY','BILLINGMETHOD'];
            $result = EntityManager::GetListQuick('contractdetail', $slect, ['RECORDNO' => $contractDetailKey]);
            return $result[0];
        }
    }

    /**
     * @param int $paymentPREntrykey
     * @param int $billingScheduleEntryKey
     * @param string $journalCode
     * @param int $lineKey
     * @param array $meaAmount
     * @param string $paymentDate
     * @throws IAException
     */
    public static function getPorportionAmount($paymentPREntrykey, $billingScheduleEntryKey, $journalCode, $lineKey, &$meaAmount, $paymentDate)
    {
        $THROW_ERROR = self::ASSERT_PAYMENT_ERROR;
        $cacheKey = "{$billingScheduleEntryKey}_{$paymentPREntrykey}_{$paymentDate}";
        $orignalPaidAmount =  self::getOriginalPaidAmount($cacheKey);
        
        if (!$orignalPaidAmount) {

            $errMsg = sprintf('We found an internal issue with the contract line data for line key %1$d and billing schedule entry key { %2$d }. OriginalPayment Not Found. Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.',$lineKey, $billingScheduleEntryKey);
            $errorInfo = new ContractI18nErrorInfo('CN-0854', ['LINE_KEY' => $lineKey, 'BILLING_SCHEDULE_ENTRY_KEY' => $billingScheduleEntryKey], $errMsg);
            self::reportAssertError($errorInfo, func_get_args(), $THROW_ERROR);
        }


        self::getAssertedMEADistributionAmount($journalCode, $billingScheduleEntryKey, $amountsByCNLine);
        $totalAmount = 0;
        foreach ($amountsByCNLine as $amountDetails) {
            $totalAmount = ibcadd($totalAmount, $amountDetails['AMOUNT']);
        }


        $newAmounts = [];
        $runningTotal = 0;
        $arr_size = count($amountsByCNLine);
        foreach($amountsByCNLine as $idx => $amountDetails) {
            $cnDetailKey = $amountDetails['CONTRACTDETAILKEY'];

            if ($idx == $arr_size-1) {

                $newAmount = ibcsub($orignalPaidAmount, $runningTotal);
                $newAmounts[$cnDetailKey] = $newAmount;

            } else {

                $newAmount = ContractUtil::computeAmountRatio(
                    $orignalPaidAmount,
                    $amountDetails['AMOUNT'],
                    $totalAmount
                );

                $newAmounts[$cnDetailKey] = $newAmount;
                $runningTotal = ibcadd($runningTotal, $newAmount);
            }
        }

        $meaAmount[0]['AMOUNT'] = $newAmounts[$lineKey];
    }


    /**
     * @param string $eventType
     * @param string $journalCode
     * @param array $contractDetailKeys
     * @throws IAException
     */
    public static function clearMEATransactionJEs($eventType, $journalCode, $contractDetailKeys)
    {
        foreach ($contractDetailKeys as $lineKey) {
            self::clearTransactionJEs($eventType, $journalCode, $lineKey);
        }
    }

    /**
     * @param string|float $leftAmount
     * @param string|float $rightAmount
     * @return bool
     * @throws Exception
     */
    private  static function isEqual($leftAmount, $rightAmount){
        $lefAmount = (float)ibcabs($leftAmount);
        $rightAmount = (float)ibcabs($rightAmount);
        if(ibccomp($lefAmount,$rightAmount) === 0){
            return true;
        }
        return false;

    }

    /**
     * @param array $netBalances
     * @param int $accountKey
     * @return bool
     */
    private static function isNETBalanceAvailableToUse($netBalances, $accountKey){
        if (isset($netBalances[$accountKey]) && !$netBalances[$accountKey]['ACCOUNTED']['NET']) {
                    return true;
        }
        return false;
    }

    /**
     * @param array $netBalances
     * @param int $accountKey
     */
    private static function setNETAccountedTrue(&$netBalances, $accountKey){
        $netBalances[$accountKey]['ACCOUNTED']['NET'] = true;
    }

    
    /**
     * @param string $eventType
     * @return bool
     */
    private static function isSubEventType($eventType)
    {

        if ($eventType === ContractGLReclassEvent::SUBEVENTTYPE_ONRECOGNITION) {
            return true;
        }
        return false;

    }

    /**
     * @param string $subEventType
     * @return string
     */
    private static function getParentEventType($subEventType)
    {
        if ($subEventType === ContractGLReclassEvent::SUBEVENTTYPE_ONRECOGNITION) {
            return ContractGLReclassEvent::EVENTTYPE_ONRECOGNITION;
        }
        return $subEventType;

    }

    /**
     * @param string $cacheKey
     * @param float $amount
     */
    public static function collectOriginalPaidAmount($cacheKey, $amount){
        self::$cacheOriginalPaymentByBSE[$cacheKey] = $amount;
    }

    /**
     * @param string $cacheKey
     * @return mixed
     */
    private static function getOriginalPaidAmount($cacheKey){
        return self::$cacheOriginalPaymentByBSE[$cacheKey];
    }


    /**
     * @param array $glbatchKeys
     * @throws IAException
     */
    public static function assertUniquenessOfGlBatchKeyInCN($glbatchKeys)
    {

        if(!empty($glbatchKeys)) {
            /**
             * validate is this GL batch key is unique in resolve table ( this check is because addGLBatch may does not return
             * unique gl batch key  in some condition in case of sequence gl batch enable  which functionality re use orphan batch)
             **/

            $mgr = Globals::$g->gManagerFactory->getManager('contractresolve');
            $results = $mgr->GetList([
                'selects' => ['GLBATCHKEY'],
                'distinct' => true,
                'filters' => [[['GLBATCHKEY', 'in', $glbatchKeys]]],
            ]);


            if (count($results) > 0) {

                $string =  'glbatchkey' ; // singular
                if($results > 1){
                    $string =  'glbatchkeys'; // plural
                }
                $glbatchKeysString = implode(',', array_column($results,'GLBATCHKEY'));

                $msg = 'We found an internal issue with %1$s %2$s. There is a duplicate GL batch key in the contract resolve table. ' .
                    'Contact your Sage Intacct Support resource, tell them you received this error, and they will resolve the issue.';
    
                throw IAException::newIAException('CN-1772', $msg,
                    ['STRING' => $string, 'GLBATCH_KEYS_STRING' => $glbatchKeysString]);
            }
        }
    }
    
    /**
     * @param string|int|null $contractKey
     * @param string $journalCode
     * @param string|null $fromDate
     *
     * @return array|mixed|string[]
     */
    public static function getInvoiceAmountEqualsReclassAmount($contractKey, $journalCode, $fromDate = '')
    {
        $contractQry = null;
    
        if ($contractKey != null) {
            $contractQry = ' cnd.contractkey = :3';
        } else if ($fromDate != null) {
            $contractQry = ' cnd.contractkey IN (
                SELECT DISTINCT contractdimkey
                  FROM glentry
                 WHERE cny# = :1 AND contractdimkey IS NOT NULL AND whencreated >= :3
           )';
        }
        
        $adjustQry = "
            (
                SELECT NVL(MAX(transactiondate), to_date('01/01/1900', 'mm/dd/yyyy'))
                FROM contractresolve cr2
                WHERE cr2.cny# = :1 AND cr2.eventtype = 'J' AND cr2.contractkey = cn.record# AND NVL(cr2.amount, 0) > 0
           )
        ";
    
        $resQry = "
                    (
                        SELECT  SUM(cr.amount * cr.tr_type) amount
                          FROM  contractresolve cr
                         WHERE  cr.cny# = :1
                           AND  cr.billingschentrykey = bse.record# AND cr.balancetype = 'R' AND cr.journaltype = :2
                           AND  cr.type in ('F' ,'T') AND cr.eventtype = 'I' AND NVL(cr.INACTIVE,'F') != 'T'
                           AND  cr.classification = CASE cr.historical WHEN 'T' THEN 'P' ELSE 'B' END
                           AND  cr.transactiondate > $adjustQry
                    )
        ";
        
        $qry = "
            SELECT cn.contractid, cnd.contractkey, cn.melocationkey, cnd.lineno, bs.contractdetailkey, bse.record# billingscheduleentrykey,
                   bse.postingdate, bse.actualpostingdate, bse.amount entry_amount, $resQry resolve_amount
              FROM contractscheduleentry bse, contractschedule bs, contractdetail cnd, contract cn
             WHERE bse.schedulekey = bs.record# AND bs.type = 'B' AND bs.contractdetailkey = cnd.record# AND cnd.contractkey = cn.record#
               AND cn.cny# = :1 AND cnd.cny# = cn.cny# AND bs.cny# = cnd.cny# AND bse.cny# = bs.cny#
               AND bse.postingdate > $adjustQry
        ";
        
        if($contractQry != null) {
            $qry .= " AND $contractQry ";
        }
        
               
        $qry .=     " AND $resQry <> bse.amount
          ORDER BY cn.contractid, cnd.lineno
        ";
    
        $qparams = [$qry, GetMyCompany(), $journalCode];
        
        if ($contractKey != null) {
            $qparams[] = $contractKey;
        } else if ($fromDate != null) {
            $qparams[] = $fromDate;
        }
        
        $results = QueryResult($qparams);
        
        return $results;
    }
    
    public static function assertInvoiceAmountEqualsReclassAmount(int $contractKey, string $journalCode): void
    {
        $assertionEnabled = ContractUtil::isInvoiceAmountEqualsReclassAmountAssertionEnabled();

        if (!$assertionEnabled) {
            return;
        }

        $results = self::getInvoiceAmountEqualsReclassAmount($contractKey, $journalCode);
        
        if (!empty($results)) {
            $errMsg = sprintf(
                'We found an internal issue with contract %1$s for %2$s. The Invoice amount does not match the journal amount.',
                $contractKey,
                $journalCode
            );
            $errorInfo = new ContractI18nErrorInfo('CN-0855', ['CONTRACT_KEY' => $contractKey, 'JOURNAL_CODE' => $journalCode], $errMsg);

            // Do not throw error (for now).  Just log it and add to metrics.
            self::reportAssertError($errorInfo, func_get_args(), false);
        }
    }
}